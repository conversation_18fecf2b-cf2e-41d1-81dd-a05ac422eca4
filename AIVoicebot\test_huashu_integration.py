#!/usr/bin/env python3
"""
测试话术文件集成

验证言犀复贷话术和零犀复贷AI话术文件是否正确集成到系统中
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    from src.components.scripts.huashu_parser import HuashuParser, HuashuParserConfig
    from src.components.scripts.script_manager import ScriptManager, ScriptManagerConfig
    from src.core.config_manager import ConfigManager
except ImportError as e:
    try:
        # 尝试不同的导入路径
        sys.path.append(str(project_root / "src"))
        from components.scripts.huashu_parser import HuashuParser, HuashuParserConfig
        from components.scripts.script_manager import ScriptManager, ScriptManagerConfig
        from core.config_manager import ConfigManager
    except ImportError as e2:
        print(f"❌ 导入错误: {e}")
        print(f"❌ 备用导入也失败: {e2}")
        print("请确保项目结构正确")
        sys.exit(1)


async def test_huashu_parser():
    """测试话术解析器"""
    print("🔧 测试话术解析器...")
    
    try:
        config_manager = ConfigManager()
        config = HuashuParserConfig()
        parser = HuashuParser(config, config_manager)
        
        await parser.initialize()
        await parser.start()
        
        # 测试解析目录
        results = parser.parse_directory("docs")
        
        print(f"✅ 话术解析器测试成功")
        print(f"📊 解析结果:")
        
        total_scripts = 0
        for file_path, scripts in results.items():
            print(f"  📄 {file_path}: {len(scripts)} 个脚本")
            total_scripts += len(scripts)
            
            # 显示前几个脚本的详情
            for i, script in enumerate(scripts[:3]):
                print(f"    📝 脚本 {i+1}: {script.script_id}")
                print(f"       场景: {script.scenario}")
                print(f"       交换数: {len(script.exchanges)}")
                if script.exchanges:
                    exchange = script.exchanges[0]
                    print(f"       用户输入: {exchange.user_input[:50]}...")
                    print(f"       系统回复: {exchange.system_response[:50]}...")
        
        print(f"📈 总计: {total_scripts} 个脚本")
        
        # 获取解析错误
        errors = parser.get_parsing_errors()
        if errors:
            print(f"⚠️  解析错误: {len(errors)} 个")
            for error in errors[:3]:  # 只显示前3个错误
                print(f"  ❌ {error['location']}: {error['error']}")
        
        await parser.stop()
        await parser.cleanup()
        
        return len(results) > 0 and total_scripts > 0
        
    except Exception as e:
        print(f"❌ 话术解析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_script_manager_integration():
    """测试脚本管理器集成"""
    print("\n🎛️  测试脚本管理器集成...")
    
    try:
        config_manager = ConfigManager()
        config = ScriptManagerConfig(
            script_directory="docs",
            preload_scripts=True,
            enable_hot_reload=False
        )
        
        manager = ScriptManager(config, config_manager)
        
        await manager.initialize()
        await manager.start()
        
        # 加载脚本
        await manager.load_scripts()
        
        # 获取统计信息
        stats = manager.get_script_statistics()
        print(f"✅ 脚本管理器集成测试成功")
        print(f"📊 统计信息:")
        print(f"  📄 总文件数: {stats['total_files']}")
        print(f"  📝 总脚本数: {stats['total_scripts']}")
        print(f"  ⚠️  总错误数: {stats['total_errors']}")
        
        # 获取所有脚本
        all_scripts = manager.get_all_scripts()
        print(f"📋 获取到 {len(all_scripts)} 个脚本")
        
        # 显示一些脚本示例
        huashu_scripts = [s for s in all_scripts.values() if '话术' in s.script_id or '复贷' in s.script_id]
        print(f"🎯 话术相关脚本: {len(huashu_scripts)} 个")
        
        for i, script in enumerate(list(huashu_scripts)[:3]):
            print(f"  📝 脚本 {i+1}: {script.script_id}")
            print(f"     场景: {script.scenario}")
            if script.exchanges:
                exchange = script.exchanges[0]
                print(f"     示例交换: {exchange.user_input} -> {exchange.system_response[:30]}...")
        
        await manager.stop()
        await manager.cleanup()
        
        return len(all_scripts) > 0 and len(huashu_scripts) > 0
        
    except Exception as e:
        print(f"❌ 脚本管理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_specific_files():
    """测试特定的话术文件"""
    print("\n📄 测试特定话术文件...")
    
    files_to_test = [
        "docs/言犀复贷话术.xlsx",
        "docs/零犀复贷AI话术调优240914.xlsx"
    ]
    
    results = {}
    
    for file_path in files_to_test:
        print(f"\n📋 测试文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在")
            results[file_path] = False
            continue
        
        try:
            config_manager = ConfigManager()
            config = HuashuParserConfig()
            parser = HuashuParser(config, config_manager)
            
            await parser.initialize()
            await parser.start()
            
            # 解析单个文件
            scripts = parser.parse_huashu_file(file_path)
            
            print(f"  ✅ 解析成功: {len(scripts)} 个脚本")
            
            if scripts:
                # 显示第一个脚本的详情
                script = scripts[0]
                print(f"    📝 示例脚本ID: {script.script_id}")
                print(f"    🎯 场景: {script.scenario}")
                print(f"    💬 交换数: {len(script.exchanges)}")
                
                if script.exchanges:
                    exchange = script.exchanges[0]
                    print(f"    👤 用户输入: {exchange.user_input}")
                    print(f"    🤖 系统回复: {exchange.system_response[:100]}...")
                    print(f"    🎯 意图: {exchange.intent}")
                    print(f"    📊 置信度: {exchange.confidence}")
            
            await parser.stop()
            await parser.cleanup()
            
            results[file_path] = len(scripts) > 0
            
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")
            results[file_path] = False
    
    return all(results.values())


async def main():
    """主测试函数"""
    print("🚀 开始话术文件集成测试...")
    print("=" * 60)
    
    # 测试话术解析器
    parser_success = await test_huashu_parser()
    
    # 测试脚本管理器集成
    manager_success = await test_script_manager_integration()
    
    # 测试特定文件
    files_success = await test_specific_files()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    print(f"🔧 话术解析器: {'✅ 通过' if parser_success else '❌ 失败'}")
    print(f"🎛️  脚本管理器集成: {'✅ 通过' if manager_success else '❌ 失败'}")
    print(f"📄 特定文件解析: {'✅ 通过' if files_success else '❌ 失败'}")
    
    overall_success = parser_success and manager_success and files_success
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if overall_success else '❌ 存在失败'}")
    
    if overall_success:
        print("\n🎉 话术文件已成功集成到系统中！")
        print("系统现在可以使用以下话术文件:")
        print("  - 言犀复贷话术.xlsx")
        print("  - 零犀复贷AI话术调优240914.xlsx")
        print("\n这些话术将在对话过程中被自动使用。")
    else:
        print("\n⚠️  话术文件集成存在问题，请检查上述错误信息。")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
