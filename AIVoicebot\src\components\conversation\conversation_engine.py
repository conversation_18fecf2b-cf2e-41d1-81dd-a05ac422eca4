"""
Conversation engine for orchestrating AI voice customer service interactions.

This module provides the main conversation coordinator that integrates ASR, LLM, TTS,
and script management components to deliver seamless voice interactions.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import json

from ...core.base_component import BaseComponent
from ...core.interfaces import AudioProcessingError, ScriptResponse
from ..scripts.script_manager import ScriptManager
from ..llm.qwen_client import QwenLLMClient, QwenResponse
from ..llm.prompt_manager import PromptManager, PromptContext
from ..llm.response_processor import ResponseProcessor, ProcessedResponse
from ..tts.voice_manager import VoiceManager, SpeechContext as TTSSpeechContext
from ...services.conversation_logger import ConversationLogger
from ...services.performance_monitor import PerformanceMonitor


class ConversationState(Enum):
    """Conversation state enumeration."""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    SPEAKING = "speaking"
    WAITING_FOR_INPUT = "waiting_for_input"
    TRANSFERRING = "transferring"
    ENDING = "ending"
    ERROR = "error"


class ConversationFlow(Enum):
    """Conversation flow types."""
    GREETING = "greeting"
    INFORMATION_GATHERING = "information_gathering"
    SERVICE_DELIVERY = "service_delivery"
    PROBLEM_SOLVING = "problem_solving"
    CLOSING = "closing"
    ESCALATION = "escalation"


class IntentType(Enum):
    """Intent classification types."""
    GREETING = "greeting"
    INQUIRY = "inquiry"
    REQUEST = "request"
    COMPLAINT = "complaint"
    CONFIRMATION = "confirmation"
    GOODBYE = "goodbye"
    UNKNOWN = "unknown"


@dataclass
class ConversationTurn:
    """Represents a single conversation turn."""
    turn_id: str
    user_input: str
    user_intent: Optional[IntentType] = None
    
    # Processing results
    matched_scripts: List[Any] = field(default_factory=list)
    llm_response: Optional[QwenResponse] = None
    processed_response: Optional[ProcessedResponse] = None
    final_response: str = ""
    
    # Audio data
    input_audio: Optional[bytes] = None
    output_audio: Optional[bytes] = None
    
    # Timing information
    start_time: datetime = field(default_factory=datetime.now)
    processing_time_ms: float = 0.0
    
    # Quality metrics
    confidence_score: float = 0.0
    user_satisfaction: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert turn to dictionary for logging."""
        return {
            "turn_id": self.turn_id,
            "user_input": self.user_input,
            "user_intent": self.user_intent.value if self.user_intent else None,
            "final_response": self.final_response,
            "processing_time_ms": self.processing_time_ms,
            "confidence_score": self.confidence_score,
            "start_time": self.start_time.isoformat(),
            "matched_scripts_count": len(self.matched_scripts),
            "llm_used": self.llm_response is not None,
            "user_satisfaction": self.user_satisfaction
        }


@dataclass
class ConversationSession:
    """Represents a complete conversation session."""
    session_id: str
    customer_id: Optional[str] = None
    
    # Session state
    current_state: ConversationState = ConversationState.IDLE
    current_flow: ConversationFlow = ConversationFlow.GREETING
    
    # Conversation history
    turns: List[ConversationTurn] = field(default_factory=list)
    
    # Context information
    customer_info: Dict[str, Any] = field(default_factory=dict)
    session_context: Dict[str, Any] = field(default_factory=dict)
    
    # Timing
    start_time: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    # Quality metrics
    overall_satisfaction: Optional[float] = None
    resolution_status: Optional[str] = None
    
    @property
    def duration_seconds(self) -> float:
        """Get session duration in seconds."""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()
    
    @property
    def turn_count(self) -> int:
        """Get number of conversation turns."""
        return len(self.turns)
    
    def get_recent_turns(self, count: int = 5) -> List[ConversationTurn]:
        """Get recent conversation turns."""
        return self.turns[-count:] if self.turns else []


@dataclass
class ConversationEngineConfig:
    """Configuration for conversation engine."""
    # Flow management
    max_session_duration_minutes: int = 30
    max_turns_per_session: int = 50
    idle_timeout_seconds: int = 300
    
    # Intent classification
    enable_intent_classification: bool = True
    intent_confidence_threshold: float = 0.7
    
    # Response generation
    prefer_scripts_over_llm: bool = True
    script_confidence_threshold: float = 0.8
    llm_fallback_enabled: bool = True
    
    # Context management
    context_window_turns: int = 5
    preserve_customer_info: bool = True
    
    # Quality control
    enable_response_validation: bool = True
    min_response_confidence: float = 0.6
    
    # Performance settings
    max_processing_time_seconds: float = 5.0
    enable_parallel_processing: bool = True
    
    # Integration settings
    enable_script_integration: bool = True
    enable_llm_integration: bool = True
    enable_tts_integration: bool = True


class ConversationEngine(BaseComponent):
    """
    Main conversation coordinator for AI voice customer service.
    
    Orchestrates ASR, LLM, TTS, and script management components to deliver
    seamless voice interactions with conversation flow management and state tracking.
    """
    
    def __init__(
        self,
        config: ConversationEngineConfig,
        script_manager: Optional[ScriptManager] = None,
        llm_client: Optional[QwenLLMClient] = None,
        prompt_manager: Optional[PromptManager] = None,
        response_processor: Optional[ResponseProcessor] = None,
        voice_manager: Optional[VoiceManager] = None,
        conversation_logger: Optional[ConversationLogger] = None,
        performance_monitor: Optional[PerformanceMonitor] = None,
        config_manager=None,
        logger=None
    ):
        """
        Initialize conversation engine.
        
        Args:
            config: Engine configuration
            script_manager: Script management component
            llm_client: LLM client for AI responses
            prompt_manager: Prompt management component
            response_processor: Response processing component
            voice_manager: Voice management component
            conversation_logger: Logger for conversation events
            performance_monitor: Monitor for performance metrics
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("conversation_engine", config_manager, logger)
        
        self.config = config
        
        # AI Components
        self.script_manager = script_manager
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
        self.response_processor = response_processor
        self.voice_manager = voice_manager
        self.conversation_logger = conversation_logger
        self.performance_monitor = performance_monitor
        
        # Active sessions
        self.active_sessions: Dict[str, ConversationSession] = {}
        
        # Event handlers
        self._state_change_handlers: List[Callable] = []
        self._turn_complete_handlers: List[Callable] = []
        
        # Statistics
        self._total_sessions = 0
        self._total_turns = 0
        self._average_session_duration = 0.0
        self._success_rate = 0.0
        
        # Flow management
        self._flow_transitions: Dict[ConversationFlow, List[ConversationFlow]] = {}
        self._setup_flow_transitions()   
 
    async def _initialize_impl(self) -> None:
        """Initialize conversation engine."""
        self._log.info("Initializing conversation engine...")
        
        # Validate required components
        if self.config.enable_script_integration and not self.script_manager:
            self._log.warning("Script integration enabled but no script manager provided")
        
        if self.config.enable_llm_integration and not self.llm_client:
            self._log.warning("LLM integration enabled but no LLM client provided")
        
        if self.config.enable_tts_integration and not self.voice_manager:
            self._log.warning("TTS integration enabled but no voice manager provided")
        
        # Reset statistics
        self._total_sessions = 0
        self._total_turns = 0
        
        self._log.info("Conversation engine initialized")
    
    async def _start_impl(self) -> None:
        """Start conversation engine."""
        self._log.info("Starting conversation engine...")
        
        # Start session cleanup task
        asyncio.create_task(self._session_cleanup_loop())
        
        self._log.info("Conversation engine started")
    
    async def _stop_impl(self) -> None:
        """Stop conversation engine."""
        self._log.info("Stopping conversation engine...")
        
        # End all active sessions
        for session in list(self.active_sessions.values()):
            await self.end_session(session.session_id, "system_shutdown")
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("Conversation engine stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup conversation engine resources."""
        self._log.info("Cleaning up conversation engine...")
        
        # Clear active sessions
        self.active_sessions.clear()
        
        # Clear event handlers
        self._state_change_handlers.clear()
        self._turn_complete_handlers.clear()
        
        self._log.info("Conversation engine cleanup completed")
    
    def _setup_flow_transitions(self) -> None:
        """Setup valid conversation flow transitions."""
        self._flow_transitions = {
            ConversationFlow.GREETING: [
                ConversationFlow.INFORMATION_GATHERING,
                ConversationFlow.SERVICE_DELIVERY,
                ConversationFlow.CLOSING
            ],
            ConversationFlow.INFORMATION_GATHERING: [
                ConversationFlow.SERVICE_DELIVERY,
                ConversationFlow.PROBLEM_SOLVING,
                ConversationFlow.ESCALATION,
                ConversationFlow.CLOSING
            ],
            ConversationFlow.SERVICE_DELIVERY: [
                ConversationFlow.INFORMATION_GATHERING,
                ConversationFlow.PROBLEM_SOLVING,
                ConversationFlow.CLOSING
            ],
            ConversationFlow.PROBLEM_SOLVING: [
                ConversationFlow.SERVICE_DELIVERY,
                ConversationFlow.ESCALATION,
                ConversationFlow.CLOSING
            ],
            ConversationFlow.ESCALATION: [
                ConversationFlow.CLOSING
            ],
            ConversationFlow.CLOSING: []  # Terminal state
        }
    
    async def start_session(
        self,
        customer_id: Optional[str] = None,
        customer_info: Optional[Dict[str, Any]] = None,
        session_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Start a new conversation session.
        
        Args:
            customer_id: Optional customer identifier
            customer_info: Customer information
            session_context: Initial session context
            
        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())
        
        session = ConversationSession(
            session_id=session_id,
            customer_id=customer_id,
            customer_info=customer_info or {},
            session_context=session_context or {}
        )
        
        self.active_sessions[session_id] = session
        self._total_sessions += 1
        
        # Notify state change
        await self._notify_state_change(session, ConversationState.IDLE)
        
        self._log.info(f"Started conversation session: {session_id}")
        return session_id
    
    async def end_session(
        self,
        session_id: str,
        reason: str = "completed",
        satisfaction_score: Optional[float] = None
    ) -> bool:
        """
        End a conversation session.
        
        Args:
            session_id: Session to end
            reason: Reason for ending
            satisfaction_score: Optional satisfaction score
            
        Returns:
            Success status
        """
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        session.end_time = datetime.now()
        session.overall_satisfaction = satisfaction_score
        session.resolution_status = reason
        
        # Update state
        await self._update_session_state(session, ConversationState.ENDING)
        
        # Remove from active sessions
        del self.active_sessions[session_id]
        
        self._log.info(f"Ended conversation session: {session_id} (reason: {reason})")
        return True
    
    async def process_user_input(
        self,
        session_id: str,
        user_input: str,
        input_audio: Optional[bytes] = None
    ) -> Dict[str, Any]:
        """
        Process user input and generate response.
        
        Args:
            session_id: Session ID
            user_input: User's text input
            input_audio: Optional audio data
            
        Returns:
            Processing result with response
        """
        if session_id not in self.active_sessions:
            raise AudioProcessingError(f"Session not found: {session_id}")
        
        session = self.active_sessions[session_id]
        start_time = datetime.now()
        
        # Update session state
        await self._update_session_state(session, ConversationState.PROCESSING)
        
        try:
            # Create conversation turn
            turn = ConversationTurn(
                turn_id=str(uuid.uuid4()),
                user_input=user_input,
                input_audio=input_audio,
                start_time=start_time
            )
            
            # Classify intent
            if self.config.enable_intent_classification:
                turn.user_intent = await self._classify_intent(user_input, session)
            
            # Generate response
            response_result = await self._generate_response(turn, session)
            
            # Update turn with results
            turn.matched_scripts = response_result.get("matched_scripts", [])
            turn.llm_response = response_result.get("llm_response")
            turn.processed_response = response_result.get("processed_response")
            turn.final_response = response_result.get("final_response", "")
            turn.confidence_score = response_result.get("confidence", 0.0)
            
            # Generate audio if TTS is enabled
            if self.config.enable_tts_integration and self.voice_manager:
                audio_result = await self._generate_audio_response(turn, session)
                turn.output_audio = audio_result.get("audio_data")
            
            # Calculate processing time
            turn.processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            # Record performance metric
            if self.performance_monitor:
                self.performance_monitor.record_response_time("conversation_turn", turn.processing_time_ms)

            # Add turn to session
            session.turns.append(turn)
            session.last_activity = datetime.now()
            self._total_turns += 1
            
            # Update conversation flow
            await self._update_conversation_flow(session, turn)
            
            # Update session state
            await self._update_session_state(session, ConversationState.WAITING_FOR_INPUT)
            
            # Notify turn completion
            await self._notify_turn_complete(session, turn)
            
            # Log the turn
            if self.conversation_logger:
                await self.conversation_logger.log_turn(session_id, turn)

            # Prepare response
            result = {
                "session_id": session_id,
                "turn_id": turn.turn_id,
                "response_text": turn.final_response,
                "response_audio": turn.output_audio,
                "confidence": turn.confidence_score,
                "intent": turn.user_intent.value if turn.user_intent else None,
                "processing_time_ms": turn.processing_time_ms,
                "session_state": session.current_state.value,
                "conversation_flow": session.current_flow.value
            }
            
            return result
            
        except Exception as e:
            self._log.error(f"Error processing user input: {e}")
            if self.performance_monitor:
                self.performance_monitor.record_error("process_user_input")
            await self._update_session_state(session, ConversationState.ERROR)
            raise AudioProcessingError(f"Failed to process user input: {e}")
    
    async def _classify_intent(
        self,
        user_input: str,
        session: ConversationSession
    ) -> IntentType:
        """Classify user intent from input."""
        # Simple rule-based intent classification
        # In a real implementation, this could use ML models
        
        input_lower = user_input.lower()
        
        # Greeting patterns
        greeting_patterns = ["你好", "您好", "hello", "hi", "早上好", "下午好", "晚上好"]
        if any(pattern in input_lower for pattern in greeting_patterns):
            return IntentType.GREETING
        
        # Goodbye patterns
        goodbye_patterns = ["再见", "拜拜", "谢谢", "结束", "挂断", "goodbye", "bye"]
        if any(pattern in input_lower for pattern in goodbye_patterns):
            return IntentType.GOODBYE
        
        # Inquiry patterns
        inquiry_patterns = ["什么", "如何", "怎么", "为什么", "哪里", "什么时候", "多少"]
        if any(pattern in input_lower for pattern in inquiry_patterns):
            return IntentType.INQUIRY
        
        # Request patterns
        request_patterns = ["我要", "我想", "帮我", "请", "申请", "办理"]
        if any(pattern in input_lower for pattern in request_patterns):
            return IntentType.REQUEST
        
        # Complaint patterns
        complaint_patterns = ["投诉", "不满意", "问题", "错误", "失误", "抱怨"]
        if any(pattern in input_lower for pattern in complaint_patterns):
            return IntentType.COMPLAINT
        
        # Confirmation patterns
        confirmation_patterns = ["是的", "对", "确认", "好的", "可以", "同意"]
        if any(pattern in input_lower for pattern in confirmation_patterns):
            return IntentType.CONFIRMATION
        
        return IntentType.UNKNOWN
    
    async def _generate_response(
        self,
        turn: ConversationTurn,
        session: ConversationSession
    ) -> Dict[str, Any]:
        """Generate response for user input."""
        result = {
            "matched_scripts": [],
            "llm_response": None,
            "processed_response": None,
            "final_response": "",
            "confidence": 0.0
        }
        
        try:
            # Try script-based response first if enabled
            if self.config.enable_script_integration and self.script_manager:
                script_response = await self._try_script_response(turn, session)
                if script_response:
                    result["matched_scripts"] = script_response.get("scripts", [])
                    result["final_response"] = script_response.get("response", "")
                    result["confidence"] = script_response.get("confidence", 0.0)
                    
                    # If script confidence is high enough, use it
                    if result["confidence"] >= self.config.script_confidence_threshold:
                        return result
            
            # Try LLM response if script confidence is low or LLM is preferred
            if self.config.enable_llm_integration and self.llm_client:
                llm_response = await self._try_llm_response(turn, session)
                if llm_response:
                    result["llm_response"] = llm_response.get("llm_response")
                    result["processed_response"] = llm_response.get("processed_response")
                    
                    # Use LLM response if it's better than script or no script available
                    if (llm_response.get("confidence", 0.0) > result["confidence"] or 
                        not result["final_response"]):
                        result["final_response"] = llm_response.get("response", "")
                        result["confidence"] = llm_response.get("confidence", 0.0)
            
            # Fallback to default response if nothing worked
            if not result["final_response"]:
                result["final_response"] = self._get_fallback_response(turn, session)
                result["confidence"] = 0.3
            
            return result
            
        except Exception as e:
            self._log.error(f"Error generating response: {e}")
            result["final_response"] = "抱歉，我暂时无法处理您的请求。请稍后再试。"
            result["confidence"] = 0.1
            return result
    
    async def _try_script_response(
        self,
        turn: ConversationTurn,
        session: ConversationSession
    ) -> Optional[Dict[str, Any]]:
        """Try to generate response using scripts."""
        try:
            # Find matching scripts
            script_response = await self.script_manager.find_response(
                turn.user_input,
                context=self._build_script_context(session)
            )
            
            if script_response:
                return {
                    "scripts": [script_response],
                    "response": script_response.text,
                    "confidence": script_response.confidence
                }
            
            return None
            
        except Exception as e:
            self._log.warning(f"Script response generation failed: {e}")
            return None
    
    async def _try_llm_response(
        self,
        turn: ConversationTurn,
        session: ConversationSession
    ) -> Optional[Dict[str, Any]]:
        """Try to generate response using LLM."""
        try:
            if not self.prompt_manager or not self.response_processor:
                return None
            
            # Create prompt context
            prompt_context = self._build_prompt_context(turn, session)
            
            # Generate prompt
            prompt = self.prompt_manager.generate_prompt(prompt_context)
            
            # Get LLM response
            llm_response = await self.llm_client.generate_simple_response(prompt)
            
            if llm_response.is_success:
                # Process response
                processed = await self.response_processor.process_llm_response(
                    llm_response,
                    fallback_scripts=turn.matched_scripts
                )
                
                return {
                    "llm_response": llm_response,
                    "processed_response": processed,
                    "response": processed.text,
                    "confidence": processed.confidence
                }
            
            return None
            
        except Exception as e:
            self._log.warning(f"LLM response generation failed: {e}")
            return None
    
    def _build_script_context(self, session: ConversationSession) -> Dict[str, Any]:
        """Build context for script matching."""
        return {
            "customer_info": session.customer_info,
            "conversation_flow": session.current_flow.value,
            "turn_count": len(session.turns),
            "session_duration": session.duration_seconds
        }
    
    def _build_prompt_context(
        self,
        turn: ConversationTurn,
        session: ConversationSession
    ) -> PromptContext:
        """Build context for prompt generation."""
        # Get recent conversation history
        recent_turns = session.get_recent_turns(self.config.context_window_turns)
        
        # Convert to conversation history format
        conversation_history = []
        for prev_turn in recent_turns:
            conversation_history.append({
                "user": prev_turn.user_input,
                "assistant": prev_turn.final_response,
                "confidence": prev_turn.confidence_score
            })
        
        return PromptContext(
            user_query=turn.user_input,
            user_intent=turn.user_intent.value if turn.user_intent else None,
            conversation_history=conversation_history,
            matched_scripts=turn.matched_scripts,
            customer_info=session.customer_info,
            system_state={
                "conversation_flow": session.current_flow.value,
                "session_duration": session.duration_seconds,
                "turn_count": len(session.turns)
            }
        )
    
    def _get_fallback_response(
        self,
        turn: ConversationTurn,
        session: ConversationSession
    ) -> str:
        """Get fallback response when other methods fail."""
        # Intent-based fallback responses
        fallback_responses = {
            IntentType.GREETING: "您好！欢迎致电我们银行，我是您的智能客服助手。",
            IntentType.GOODBYE: "感谢您的咨询，祝您生活愉快，再见！",
            IntentType.INQUIRY: "我理解您的问题，让我为您查询相关信息。",
            IntentType.REQUEST: "我会尽力帮助您处理这个请求。",
            IntentType.COMPLAINT: "非常抱歉给您带来不便，我会认真处理您的问题。",
            IntentType.CONFIRMATION: "好的，我已经记录了您的确认。",
            IntentType.UNKNOWN: "抱歉，我没有完全理解您的意思，能否请您再详细说明一下？"
        }
        
        return fallback_responses.get(
            turn.user_intent or IntentType.UNKNOWN,
            "抱歉，我暂时无法处理您的请求。请稍后再试或联系人工客服。"
        )
    
    async def _generate_audio_response(
        self,
        turn: ConversationTurn,
        session: ConversationSession
    ) -> Dict[str, Any]:
        """Generate audio response using TTS."""
        try:
            # Determine speech context based on intent and flow
            speech_context = self._map_to_speech_context(turn.user_intent, session.current_flow)
            
            # Generate audio
            tts_result = await self.voice_manager.generate_speech(
                turn.final_response,
                context=speech_context
            )
            
            return {
                "audio_data": tts_result.audio_data,
                "duration_ms": tts_result.duration_ms,
                "voice_used": tts_result.voice_used
            }
            
        except Exception as e:
            self._log.warning(f"Audio generation failed: {e}")
            return {"audio_data": None}
    
    def _map_to_speech_context(
        self,
        intent: Optional[IntentType],
        flow: ConversationFlow
    ) -> TTSSpeechContext:
        """Map conversation intent/flow to TTS speech context."""
        # Intent-based mapping
        if intent == IntentType.GREETING:
            return TTSSpeechContext.GREETING
        elif intent == IntentType.GOODBYE:
            return TTSSpeechContext.CLOSING
        elif intent == IntentType.COMPLAINT:
            return TTSSpeechContext.SENSITIVE
        
        # Flow-based mapping
        if flow == ConversationFlow.GREETING:
            return TTSSpeechContext.GREETING
        elif flow == ConversationFlow.CLOSING:
            return TTSSpeechContext.CLOSING
        elif flow == ConversationFlow.ESCALATION:
            return TTSSpeechContext.URGENT
        
        # Default to explanation
        return TTSSpeechContext.EXPLANATION  
  
    async def _update_conversation_flow(
        self,
        session: ConversationSession,
        turn: ConversationTurn
    ) -> None:
        """Update conversation flow based on turn."""
        current_flow = session.current_flow
        new_flow = current_flow
        
        # Flow transition logic based on intent
        if turn.user_intent == IntentType.GREETING and current_flow == ConversationFlow.GREETING:
            new_flow = ConversationFlow.INFORMATION_GATHERING
        elif turn.user_intent == IntentType.REQUEST:
            new_flow = ConversationFlow.SERVICE_DELIVERY
        elif turn.user_intent == IntentType.COMPLAINT:
            new_flow = ConversationFlow.PROBLEM_SOLVING
        elif turn.user_intent == IntentType.GOODBYE:
            new_flow = ConversationFlow.CLOSING
        
        # Validate transition
        if new_flow != current_flow:
            valid_transitions = self._flow_transitions.get(current_flow, [])
            if new_flow in valid_transitions or new_flow == ConversationFlow.CLOSING:
                session.current_flow = new_flow
                self._log.debug(f"Flow transition: {current_flow.value} -> {new_flow.value}")
    
    async def _update_session_state(
        self,
        session: ConversationSession,
        new_state: ConversationState
    ) -> None:
        """Update session state and notify handlers."""
        old_state = session.current_state
        session.current_state = new_state
        
        if old_state != new_state:
            await self._notify_state_change(session, new_state)
    
    async def _notify_state_change(
        self,
        session: ConversationSession,
        new_state: ConversationState
    ) -> None:
        """Notify state change handlers."""
        for handler in self._state_change_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(session, new_state)
                else:
                    handler(session, new_state)
            except Exception as e:
                self._log.warning(f"State change handler error: {e}")
    
    async def _notify_turn_complete(
        self,
        session: ConversationSession,
        turn: ConversationTurn
    ) -> None:
        """Notify turn completion handlers."""
        for handler in self._turn_complete_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(session, turn)
                else:
                    handler(session, turn)
            except Exception as e:
                self._log.warning(f"Turn complete handler error: {e}")
    
    async def _session_cleanup_loop(self) -> None:
        """Background task to cleanup idle sessions."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = datetime.now()
                idle_sessions = []
                
                for session_id, session in self.active_sessions.items():
                    # Check for idle timeout
                    idle_time = (current_time - session.last_activity).total_seconds()
                    if idle_time > self.config.idle_timeout_seconds:
                        idle_sessions.append(session_id)
                    
                    # Check for max duration
                    session_duration = session.duration_seconds / 60  # Convert to minutes
                    if session_duration > self.config.max_session_duration_minutes:
                        idle_sessions.append(session_id)
                    
                    # Check for max turns
                    if session.turn_count > self.config.max_turns_per_session:
                        idle_sessions.append(session_id)
                
                # Cleanup idle sessions
                for session_id in idle_sessions:
                    await self.end_session(session_id, "timeout_or_limit")
                
                if idle_sessions:
                    self._log.info(f"Cleaned up {len(idle_sessions)} idle sessions")
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Session cleanup error: {e}")
    
    def add_state_change_handler(self, handler: Callable) -> None:
        """Add state change event handler."""
        self._state_change_handlers.append(handler)
    
    def add_turn_complete_handler(self, handler: Callable) -> None:
        """Add turn completion event handler."""
        self._turn_complete_handlers.append(handler)
    
    def remove_state_change_handler(self, handler: Callable) -> None:
        """Remove state change event handler."""
        if handler in self._state_change_handlers:
            self._state_change_handlers.remove(handler)
    
    def remove_turn_complete_handler(self, handler: Callable) -> None:
        """Remove turn completion event handler."""
        if handler in self._turn_complete_handlers:
            self._turn_complete_handlers.remove(handler)
    
    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """Get session by ID."""
        return self.active_sessions.get(session_id)
    
    def get_active_sessions(self) -> List[ConversationSession]:
        """Get all active sessions."""
        return list(self.active_sessions.values())
    
    def get_session_count(self) -> int:
        """Get number of active sessions."""
        return len(self.active_sessions)
    
    async def transfer_session(
        self,
        session_id: str,
        target_agent: str,
        reason: str = "user_request"
    ) -> bool:
        """
        Transfer session to another agent.
        
        Args:
            session_id: Session to transfer
            target_agent: Target agent identifier
            reason: Transfer reason
            
        Returns:
            Success status
        """
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        
        # Update session state
        await self._update_session_state(session, ConversationState.TRANSFERRING)
        
        # Add transfer information to session context
        session.session_context["transfer_target"] = target_agent
        session.session_context["transfer_reason"] = reason
        session.session_context["transfer_time"] = datetime.now().isoformat()
        
        self._log.info(f"Session {session_id} transferred to {target_agent} (reason: {reason})")
        return True
    
    def get_conversation_statistics(self) -> Dict[str, Any]:
        """Get conversation engine statistics."""
        # Calculate average session duration
        if self._total_sessions > 0:
            total_duration = sum(
                session.duration_seconds for session in self.active_sessions.values()
            )
            self._average_session_duration = total_duration / len(self.active_sessions) if self.active_sessions else 0
        
        # Calculate success rate (sessions that ended normally)
        completed_sessions = self._total_sessions - len(self.active_sessions)
        self._success_rate = completed_sessions / max(1, self._total_sessions)
        
        return {
            "total_sessions": self._total_sessions,
            "active_sessions": len(self.active_sessions),
            "total_turns": self._total_turns,
            "average_session_duration_seconds": self._average_session_duration,
            "success_rate": self._success_rate,
            "average_turns_per_session": self._total_turns / max(1, self._total_sessions),
            "component_status": {
                "script_manager": self.script_manager is not None,
                "llm_client": self.llm_client is not None,
                "prompt_manager": self.prompt_manager is not None,
                "response_processor": self.response_processor is not None,
                "voice_manager": self.voice_manager is not None
            }
        }
    
    def get_session_statistics(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for specific session."""
        session = self.get_session(session_id)
        if not session:
            return None
        
        # Calculate turn statistics
        turn_times = [turn.processing_time_ms for turn in session.turns]
        confidence_scores = [turn.confidence_score for turn in session.turns]
        
        return {
            "session_id": session_id,
            "duration_seconds": session.duration_seconds,
            "turn_count": session.turn_count,
            "current_state": session.current_state.value,
            "current_flow": session.current_flow.value,
            "average_processing_time_ms": sum(turn_times) / len(turn_times) if turn_times else 0,
            "average_confidence": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
            "intent_distribution": self._get_intent_distribution(session),
            "customer_info": session.customer_info,
            "overall_satisfaction": session.overall_satisfaction
        }
    
    def _get_intent_distribution(self, session: ConversationSession) -> Dict[str, int]:
        """Get intent distribution for session."""
        intent_counts = {}
        for turn in session.turns:
            if turn.user_intent:
                intent = turn.user_intent.value
                intent_counts[intent] = intent_counts.get(intent, 0) + 1
        return intent_counts
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_conversation_statistics()
        
        self._log.info(f"Conversation engine statistics:")
        self._log.info(f"  Total sessions: {stats['total_sessions']}")
        self._log.info(f"  Total turns: {stats['total_turns']}")
        self._log.info(f"  Success rate: {stats['success_rate']:.2%}")
        self._log.info(f"  Average session duration: {stats['average_session_duration_seconds']:.1f}s")
        self._log.info(f"  Average turns per session: {stats['average_turns_per_session']:.1f}")
    
    async def simulate_conversation(
        self,
        test_inputs: List[str],
        customer_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Simulate a conversation for testing purposes.
        
        Args:
            test_inputs: List of user inputs to simulate
            customer_info: Optional customer information
            
        Returns:
            Simulation results
        """
        # Start test session
        session_id = await self.start_session(
            customer_id="test_customer",
            customer_info=customer_info or {}
        )
        
        results = {
            "session_id": session_id,
            "turns": [],
            "total_processing_time": 0.0,
            "average_confidence": 0.0
        }
        
        try:
            # Process each input
            for i, user_input in enumerate(test_inputs):
                self._log.info(f"Simulating turn {i+1}: {user_input}")
                
                result = await self.process_user_input(session_id, user_input)
                results["turns"].append({
                    "input": user_input,
                    "response": result["response_text"],
                    "confidence": result["confidence"],
                    "intent": result["intent"],
                    "processing_time_ms": result["processing_time_ms"]
                })
                
                results["total_processing_time"] += result["processing_time_ms"]
            
            # Calculate averages
            if results["turns"]:
                confidences = [turn["confidence"] for turn in results["turns"]]
                results["average_confidence"] = sum(confidences) / len(confidences)
            
            # Get final session statistics
            results["session_stats"] = self.get_session_statistics(session_id)
            
        finally:
            # End test session
            await self.end_session(session_id, "simulation_complete")
        
        return results
    
    def reset_statistics(self) -> None:
        """Reset conversation engine statistics."""
        self._total_sessions = 0
        self._total_turns = 0
        self._average_session_duration = 0.0
        self._success_rate = 0.0
        
        self._log.info("Conversation engine statistics reset")


# Utility functions for conversation engine

async def create_conversation_engine(
    script_manager: Optional[ScriptManager] = None,
    llm_client: Optional[QwenLLMClient] = None,
    prompt_manager: Optional[PromptManager] = None,
    response_processor: Optional[ResponseProcessor] = None,
    voice_manager: Optional[VoiceManager] = None,
    max_session_duration_minutes: int = 30,
    enable_intent_classification: bool = True,
    config_manager=None,
    **kwargs
) -> ConversationEngine:
    """
    Create and initialize conversation engine.
    
    Args:
        script_manager: Script management component
        llm_client: LLM client component
        prompt_manager: Prompt management component
        response_processor: Response processing component
        voice_manager: Voice management component
        max_session_duration_minutes: Maximum session duration
        enable_intent_classification: Enable intent classification
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized conversation engine
    """
    config = ConversationEngineConfig(
        max_session_duration_minutes=max_session_duration_minutes,
        enable_intent_classification=enable_intent_classification,
        **kwargs
    )
    
    engine = ConversationEngine(
        config=config,
        script_manager=script_manager,
        llm_client=llm_client,
        prompt_manager=prompt_manager,
        response_processor=response_processor,
        voice_manager=voice_manager,
        config_manager=config_manager
    )
    
    await engine.initialize()
    await engine.start()
    
    return engine


def create_banking_conversation_config() -> ConversationEngineConfig:
    """Create configuration optimized for banking conversations."""
    return ConversationEngineConfig(
        max_session_duration_minutes=45,  # Longer for complex banking queries
        max_turns_per_session=30,
        idle_timeout_seconds=600,  # 10 minutes for banking
        enable_intent_classification=True,
        intent_confidence_threshold=0.6,
        prefer_scripts_over_llm=True,  # Banking prefers scripted responses
        script_confidence_threshold=0.7,
        llm_fallback_enabled=True,
        context_window_turns=7,  # More context for banking
        preserve_customer_info=True,
        enable_response_validation=True,
        min_response_confidence=0.7,  # Higher confidence for banking
        max_processing_time_seconds=8.0,  # Allow more time for complex queries
        enable_parallel_processing=True
    )


def create_quick_support_config() -> ConversationEngineConfig:
    """Create configuration optimized for quick support."""
    return ConversationEngineConfig(
        max_session_duration_minutes=15,  # Shorter for quick support
        max_turns_per_session=20,
        idle_timeout_seconds=180,  # 3 minutes
        enable_intent_classification=True,
        intent_confidence_threshold=0.8,
        prefer_scripts_over_llm=True,
        script_confidence_threshold=0.8,
        llm_fallback_enabled=True,
        context_window_turns=3,  # Less context for speed
        preserve_customer_info=False,
        enable_response_validation=True,
        min_response_confidence=0.6,
        max_processing_time_seconds=3.0,  # Fast response
        enable_parallel_processing=True
    )