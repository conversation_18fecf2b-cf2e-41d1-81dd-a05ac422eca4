"""
Integration Test Scenarios

This module provides specific integration test scenarios for the
AI voice customer service system, covering various real-world use cases.
"""

import pytest
import asyncio
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from tests.integration.test_e2e_conversation_flow import E2ETestFramework, E2ETestScenario
from tests.performance.test_load_testing import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from tests.performance.test_benchmarks import PerformanceBenchmark
from tests.test_utils import <PERSON>ckConfigManager, MockLogger


class IntegrationTestSuite:
    """Integration test suite with predefined scenarios."""
    
    def __init__(self):
        self.config_manager = MockConfigManager()
        self.logger = MockLogger()
        self.e2e_framework = None
        self.load_test_runner = None
        self.benchmark_runner = None
    
    async def setup(self):
        """Set up the integration test suite."""
        self.e2e_framework = E2ETestFramework(self.config_manager, self.logger)
        self.load_test_runner = LoadTestRunner(self.config_manager, self.logger)
        self.benchmark_runner = PerformanceBenchmark(self.config_manager, self.logger)
        
        await self.e2e_framework.initialize()
        await self.load_test_runner.initialize()
    
    async def teardown(self):
        """Clean up the integration test suite."""
        if self.e2e_framework:
            await self.e2e_framework.cleanup()
        if self.load_test_runner:
            await self.load_test_runner.cleanup()
    
    def create_account_inquiry_scenario(self) -> E2ETestScenario:
        """Create account inquiry test scenario."""
        return E2ETestScenario(
            scenario_id="account_inquiry_001",
            name="Account Balance Inquiry",
            description="Customer calls to inquire about account balance",
            steps=[
                {
                    "type": "initiate_call",
                    "name": "Customer calls",
                    "data": {
                        "caller_number": "+*********0",
                        "callee_number": "+**********"
                    }
                },
                {
                    "type": "send_audio",
                    "name": "Customer greeting",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "你好，我想查询我的账户余额"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI assistant responds",
                    "data": {
                        "expected_keywords": ["账户", "余额", "查询"],
                        "timeout_seconds": 5.0
                    }
                },
                {
                    "type": "send_audio",
                    "name": "Provide account number",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "我的账户号码是*********"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI processes request",
                    "data": {
                        "expected_keywords": ["查询", "余额", "请稍等"],
                        "timeout_seconds": 10.0
                    }
                },
                {
                    "type": "validate_state",
                    "name": "Verify conversation state",
                    "data": {
                        "component": "conversation_manager",
                        "expected_state": True
                    }
                },
                {
                    "type": "end_call",
                    "name": "End call",
                    "data": {}
                }
            ],
            expected_outcomes={
                "max_duration_seconds": 45.0,
                "min_step_success_rate": 0.9
            },
            timeout_seconds=60.0
        )
    
    def create_complaint_handling_scenario(self) -> E2ETestScenario:
        """Create complaint handling test scenario."""
        return E2ETestScenario(
            scenario_id="complaint_handling_001",
            name="Customer Complaint Handling",
            description="Customer calls to file a complaint",
            steps=[
                {
                    "type": "initiate_call",
                    "name": "Customer calls",
                    "data": {
                        "caller_number": "+1987654321",
                        "callee_number": "+**********"
                    }
                },
                {
                    "type": "send_audio",
                    "name": "Customer complaint",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "我要投诉你们的服务，昨天的转账没有到账"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI acknowledges complaint",
                    "data": {
                        "expected_keywords": ["投诉", "抱歉", "帮助"],
                        "timeout_seconds": 5.0
                    }
                },
                {
                    "type": "send_audio",
                    "name": "Provide details",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "转账金额是5000元，收款人是张三"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI requests more info",
                    "data": {
                        "expected_keywords": ["转账", "查询", "记录"],
                        "timeout_seconds": 8.0
                    }
                },
                {
                    "type": "wait",
                    "name": "Processing time",
                    "data": {
                        "duration_seconds": 2.0
                    }
                },
                {
                    "type": "end_call",
                    "name": "End call",
                    "data": {}
                }
            ],
            expected_outcomes={
                "max_duration_seconds": 60.0,
                "min_step_success_rate": 0.85
            },
            timeout_seconds=90.0
        )
    
    def create_multi_turn_conversation_scenario(self) -> E2ETestScenario:
        """Create multi-turn conversation test scenario."""
        return E2ETestScenario(
            scenario_id="multi_turn_001",
            name="Multi-turn Conversation",
            description="Extended conversation with multiple exchanges",
            steps=[
                {
                    "type": "initiate_call",
                    "name": "Start call",
                    "data": {
                        "caller_number": "+1555666777",
                        "callee_number": "+**********"
                    }
                },
                # Turn 1
                {
                    "type": "send_audio",
                    "name": "Initial inquiry",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "你好，我想了解一下理财产品"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI responds to inquiry",
                    "data": {
                        "expected_keywords": ["理财", "产品", "了解"],
                        "timeout_seconds": 5.0
                    }
                },
                # Turn 2
                {
                    "type": "send_audio",
                    "name": "Ask about risk",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "这些产品的风险怎么样？"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI explains risk",
                    "data": {
                        "expected_keywords": ["风险", "投资", "收益"],
                        "timeout_seconds": 8.0
                    }
                },
                # Turn 3
                {
                    "type": "send_audio",
                    "name": "Ask about minimum amount",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "最低投资金额是多少？"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI provides amount info",
                    "data": {
                        "expected_keywords": ["金额", "投资", "最低"],
                        "timeout_seconds": 5.0
                    }
                },
                # Turn 4
                {
                    "type": "send_audio",
                    "name": "Request consultation",
                    "data": {
                        "audio_type": "speech",
                        "text_content": "我想预约一个理财顾问咨询"
                    }
                },
                {
                    "type": "expect_response",
                    "name": "AI handles appointment",
                    "data": {
                        "expected_keywords": ["预约", "顾问", "咨询"],
                        "timeout_seconds": 10.0
                    }
                },
                {
                    "type": "validate_state",
                    "name": "Check conversation context",
                    "data": {
                        "component": "conversation_manager",
                        "expected_state": True
                    }
                },
                {
                    "type": "end_call",
                    "name": "End call",
                    "data": {}
                }
            ],
            expected_outcomes={
                "max_duration_seconds": 120.0,
                "min_step_success_rate": 0.8
            },
            timeout_seconds=150.0
        )
    
    async def run_basic_integration_tests(self) -> Dict[str, Any]:
        """Run basic integration test scenarios."""
        self.logger.info("Starting basic integration tests")
        
        scenarios = [
            self.create_account_inquiry_scenario(),
            self.create_complaint_handling_scenario(),
            self.create_multi_turn_conversation_scenario()
        ]
        
        results = {}
        
        for scenario in scenarios:
            self.logger.info(f"Running scenario: {scenario.name}")
            
            success = await self.e2e_framework.run_scenario(scenario)
            report = self.e2e_framework.get_scenario_report(scenario)
            
            results[scenario.scenario_id] = {
                "success": success,
                "report": report
            }
            
            self.logger.info(f"Scenario {scenario.name} {'PASSED' if success else 'FAILED'}")
        
        # Generate summary
        total_scenarios = len(scenarios)
        passed_scenarios = sum(1 for r in results.values() if r["success"])
        
        summary = {
            "total_scenarios": total_scenarios,
            "passed_scenarios": passed_scenarios,
            "failed_scenarios": total_scenarios - passed_scenarios,
            "success_rate": (passed_scenarios / total_scenarios) * 100 if total_scenarios > 0 else 0,
            "scenario_results": results
        }
        
        self.logger.info(f"Basic integration tests completed: {passed_scenarios}/{total_scenarios} passed")
        return summary
    
    async def run_performance_integration_tests(self) -> Dict[str, Any]:
        """Run performance-focused integration tests."""
        self.logger.info("Starting performance integration tests")
        
        results = {}
        
        # Run load tests
        self.logger.info("Running concurrent call load test")
        load_test_result = await self.load_test_runner.run_concurrent_call_test(
            concurrent_calls=10,
            duration_seconds=30.0
        )
        
        results["load_test"] = self.load_test_runner.generate_performance_report(load_test_result)
        
        # Run benchmarks
        self.logger.info("Running performance benchmarks")
        benchmark_results = await self.benchmark_runner.run_comprehensive_benchmark()
        
        results["benchmarks"] = self.benchmark_runner.generate_benchmark_report()
        
        # Run stress test
        self.logger.info("Running stress test")
        stress_results = await self.load_test_runner.run_stress_test(
            max_calls=20,
            increment_step=5,
            step_duration=15.0
        )
        
        results["stress_test"] = {
            "steps": len(stress_results),
            "max_stable_load": max(
                (i + 1) * 5 for i, result in enumerate(stress_results)
                if result.success_rate >= 90
            ) if stress_results else 0,
            "step_results": [
                self.load_test_runner.generate_performance_report(result)
                for result in stress_results
            ]
        }
        
        self.logger.info("Performance integration tests completed")
        return results
    
    async def run_regression_tests(self, baseline_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """Run regression tests against baseline performance."""
        self.logger.info("Starting regression tests")
        
        # Run current benchmarks
        current_benchmarks = await self.benchmark_runner.run_comprehensive_benchmark()
        
        regression_results = {}
        
        if baseline_results:
            # Compare with baseline
            for benchmark_name, current_result in current_benchmarks.items():
                if benchmark_name in baseline_results:
                    comparison = self.benchmark_runner.compare_with_baseline(
                        benchmark_name,
                        current_result
                    )
                    regression_results[benchmark_name] = comparison
        
        # Run basic scenarios for functional regression
        functional_results = await self.run_basic_integration_tests()
        
        summary = {
            "performance_regression": regression_results,
            "functional_regression": functional_results,
            "overall_regression_detected": any(
                comp.get("performance_regression", False)
                for comp in regression_results.values()
            ) or functional_results["success_rate"] < 90
        }
        
        self.logger.info("Regression tests completed")
        return summary


# Pytest integration
class TestIntegrationScenarios:
    """Pytest integration test class."""
    
    @pytest.fixture
    async def integration_suite(self):
        """Create and setup integration test suite."""
        suite = IntegrationTestSuite()
        await suite.setup()
        yield suite
        await suite.teardown()
    
    @pytest.mark.asyncio
    async def test_account_inquiry_scenario(self, integration_suite):
        """Test account inquiry scenario."""
        scenario = integration_suite.create_account_inquiry_scenario()
        success = await integration_suite.e2e_framework.run_scenario(scenario)
        assert success, f"Account inquiry scenario failed: {scenario.results}"
    
    @pytest.mark.asyncio
    async def test_complaint_handling_scenario(self, integration_suite):
        """Test complaint handling scenario."""
        scenario = integration_suite.create_complaint_handling_scenario()
        success = await integration_suite.e2e_framework.run_scenario(scenario)
        assert success, f"Complaint handling scenario failed: {scenario.results}"
    
    @pytest.mark.asyncio
    async def test_multi_turn_conversation_scenario(self, integration_suite):
        """Test multi-turn conversation scenario."""
        scenario = integration_suite.create_multi_turn_conversation_scenario()
        success = await integration_suite.e2e_framework.run_scenario(scenario)
        assert success, f"Multi-turn conversation scenario failed: {scenario.results}"
    
    @pytest.mark.asyncio
    async def test_basic_integration_suite(self, integration_suite):
        """Test complete basic integration suite."""
        results = await integration_suite.run_basic_integration_tests()
        assert results["success_rate"] >= 80, f"Integration test success rate too low: {results['success_rate']}%"
    
    @pytest.mark.asyncio
    async def test_performance_integration_suite(self, integration_suite):
        """Test performance integration suite."""
        results = await integration_suite.run_performance_integration_tests()
        
        # Check load test results
        load_test = results["load_test"]
        assert load_test["success_rate_percent"] >= 90, f"Load test success rate too low: {load_test['success_rate_percent']}%"
        
        # Check stress test results
        stress_test = results["stress_test"]
        assert stress_test["max_stable_load"] >= 10, f"System cannot handle minimum load: {stress_test['max_stable_load']}"
