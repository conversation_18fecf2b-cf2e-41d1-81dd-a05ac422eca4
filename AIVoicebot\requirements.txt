
# AI Voice Customer Service System - Production Dependencies

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
starlette==0.27.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis
redis==5.0.1
aioredis==2.0.1

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Audio Processing
numpy==1.24.4
scipy==1.11.4
librosa==0.10.1
soundfile==0.12.1
PyAudio==0.2.14  # Installed successfully
# webrtcvad==2.0.10  # Requires Visual C++ Build Tools - optional for voice activity detection

# Machine Learning / AI
torch==2.1.1
torchaudio==2.1.1
transformers==4.35.2
onnxruntime==1.16.3
openai==1.3.7

# WebSocket
websockets==12.0

# Configuration Management
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0
PyYAML==6.0.1

# Logging and Monitoring
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# Async Support
asyncio==3.4.3
aiofiles==23.2.1
anyio>=3.7.1,<4.0.0

# Date and Time
python-dateutil==2.8.2
pytz==2023.3

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
jinja2==3.1.2

# Security
cryptography>=3.4.8,<42.0.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# Validation and Serialization
marshmallow==3.20.1
cerberus==1.3.5

# Background Tasks
celery==5.3.4
kombu==5.3.4

# File Processing
Pillow==10.1.0
python-magic==0.4.27

# Network and Protocol
dnspython==2.4.2
paramiko==3.4.0

# Performance
cachetools==5.3.2
lru-dict==1.3.0

# Telephony and Communication
pydub==0.25.1
SpeechRecognition==3.10.0

# Data Processing
pandas==2.1.4
openpyxl==3.1.2

# Environment and Deployment
python-decouple==3.8
watchdog>=2.0.0