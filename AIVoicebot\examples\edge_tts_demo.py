"""
Demo script for EdgeTTS generator.

This script demonstrates how to use the EdgeTTS generator for text-to-speech
conversion with Chinese voices and telephony optimization.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.tts.edge_tts_generator import (
    EdgeTTSGenerator, EdgeTTSConfig, VoiceGender, AudioFormat,
    create_edge_tts_generator, get_telephony_optimized_config,
    get_high_quality_config, create_customer_service_voice_config,
    select_best_chinese_voice, recommend_voice_for_banking,
    get_voice_parameters_for_context, EDGE_TTS_AVAILABLE
)
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_basic_tts():
    """Demonstrate basic TTS functionality."""
    logger.info("=== Basic TTS Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available. Install with: pip install edge-tts pydub")
        return
    
    try:
        # Create TTS generator
        generator = await create_edge_tts_generator(
            default_voice="zh-CN-XiaoxiaoNeural",
            sample_rate=16000,
            enable_optimization=True,
            config_manager=Mock()
        )
        
        # Test text
        test_text = "您好！欢迎致电我们银行的客服热线。我是您的专属客服助手，很高兴为您服务。"
        
        logger.info(f"Generating speech for: {test_text}")
        
        # Generate speech
        result = await generator.generate_speech(test_text)
        
        logger.info("Generation Results:")
        logger.info(f"  Voice used: {result.voice_used}")
        logger.info(f"  Duration: {result.duration_ms:.0f}ms")
        logger.info(f"  File size: {result.file_size_bytes} bytes")
        logger.info(f"  Generation time: {result.generation_time_ms:.2f}ms")
        logger.info(f"  Sample rate: {result.sample_rate}Hz")
        logger.info(f"  Channels: {result.channels}")
        logger.info(f"  Format: {result.format.value}")
        
        # Save to file
        output_file = "demo_basic_tts.wav"
        if result.save_to_file(output_file):
            logger.info(f"Audio saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"Basic TTS demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def demo_voice_selection():
    """Demonstrate voice selection and testing."""
    logger.info("=== Voice Selection Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available")
        return
    
    try:
        generator = await create_edge_tts_generator(config_manager=Mock())
        
        # Get available Chinese voices
        chinese_voices = generator.get_chinese_voices()
        logger.info(f"Available Chinese voices: {len(chinese_voices)}")
        
        for voice in chinese_voices[:5]:  # Show first 5
            logger.info(f"  {voice.short_name} - {voice.gender.value} - Quality: N{voice.naturalness}/C{voice.clarity}/E{voice.expressiveness}")
        
        # Test different voices
        test_text = "我们的贷款利率根据您的信用状况而定，一般在年化3.5%到8%之间。"
        
        # Test female voice
        female_voice = generator.get_recommended_voice(VoiceGender.FEMALE)
        if female_voice:
            logger.info(f"\nTesting female voice: {female_voice}")
            result = await generator.test_voice(female_voice, test_text)
            logger.info(f"  Duration: {result.duration_ms:.0f}ms, Size: {result.file_size_bytes} bytes")
        
        # Test male voice
        male_voice = generator.get_recommended_voice(VoiceGender.MALE)
        if male_voice:
            logger.info(f"\nTesting male voice: {male_voice}")
            result = await generator.test_voice(male_voice, test_text)
            logger.info(f"  Duration: {result.duration_ms:.0f}ms, Size: {result.file_size_bytes} bytes")
        
        # Banking recommendation
        banking_voice = recommend_voice_for_banking(chinese_voices, "vip")
        if banking_voice:
            logger.info(f"\nRecommended banking voice for VIP: {banking_voice}")
    
    except Exception as e:
        logger.error(f"Voice selection demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def demo_voice_parameters():
    """Demonstrate voice parameter adjustment."""
    logger.info("=== Voice Parameters Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available")
        return
    
    try:
        generator = await create_edge_tts_generator(config_manager=Mock())
        
        test_text = "感谢您的耐心等待，我已经为您查询到相关信息。"
        
        # Test different parameter combinations
        parameter_tests = [
            {"name": "Normal", "params": {}},
            {"name": "Fast", "params": {"speed": "+20%"}},
            {"name": "Slow", "params": {"speed": "-20%"}},
            {"name": "High Pitch", "params": {"pitch": "+50Hz"}},
            {"name": "Low Pitch", "params": {"pitch": "-50Hz"}},
            {"name": "Loud", "params": {"volume": "+20%"}},
            {"name": "Soft", "params": {"volume": "-20%"}},
        ]
        
        for test in parameter_tests:
            logger.info(f"\nTesting {test['name']} parameters: {test['params']}")
            
            result = await generator.generate_speech(
                test_text,
                voice="zh-CN-XiaoxiaoNeural",
                **test['params']
            )
            
            logger.info(f"  Duration: {result.duration_ms:.0f}ms")
            logger.info(f"  Generation time: {result.generation_time_ms:.2f}ms")
            
            # Save with descriptive filename
            filename = f"demo_voice_{test['name'].lower().replace(' ', '_')}.wav"
            if result.save_to_file(filename):
                logger.info(f"  Saved to: {filename}")
    
    except Exception as e:
        logger.error(f"Voice parameters demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def demo_context_based_speech():
    """Demonstrate context-based speech generation."""
    logger.info("=== Context-Based Speech Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available")
        return
    
    try:
        generator = await create_edge_tts_generator(config_manager=Mock())
        
        # Different contexts with appropriate texts
        context_tests = [
            {
                "context": "greeting",
                "text": "您好！欢迎致电我们银行，我是您的客服助手小雅。"
            },
            {
                "context": "explanation", 
                "text": "根据您的信用评估结果，我们可以为您提供最高50万元的贷款额度。"
            },
            {
                "context": "closing",
                "text": "感谢您选择我们银行，祝您生活愉快，再见！"
            },
            {
                "context": "urgent",
                "text": "请注意，您的账户出现异常交易，请立即联系我们确认。"
            },
            {
                "context": "apologetic",
                "text": "非常抱歉让您久等了，由于系统升级导致的延误，我们深表歉意。"
            }
        ]
        
        for test in context_tests:
            context = test["context"]
            text = test["text"]
            
            logger.info(f"\nGenerating {context} speech:")
            logger.info(f"Text: {text}")
            
            # Get context-appropriate parameters
            params = get_voice_parameters_for_context(context)
            logger.info(f"Parameters: {params}")
            
            # Generate speech
            result = await generator.generate_speech(
                text,
                voice="zh-CN-XiaoxiaoNeural",
                **params
            )
            
            logger.info(f"Duration: {result.duration_ms:.0f}ms")
            
            # Save with context name
            filename = f"demo_context_{context}.wav"
            if result.save_to_file(filename):
                logger.info(f"Saved to: {filename}")
    
    except Exception as e:
        logger.error(f"Context-based speech demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def demo_telephony_optimization():
    """Demonstrate telephony optimization."""
    logger.info("=== Telephony Optimization Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available")
        return
    
    try:
        # Create generator with telephony optimization
        telephony_config = get_telephony_optimized_config()
        generator = EdgeTTSGenerator(telephony_config, Mock())
        await generator.initialize()
        await generator.start()
        
        test_text = "您的贷款申请已经通过初步审核，请准备相关材料到网点办理后续手续。"
        
        logger.info("Generating telephony-optimized speech...")
        logger.info(f"Config: {telephony_config.sample_rate}Hz, {telephony_config.channels}ch, {telephony_config.target_bitrate_kbps}kbps")
        
        result = await generator.generate_speech(test_text)
        
        logger.info("Telephony Results:")
        logger.info(f"  Sample rate: {result.sample_rate}Hz")
        logger.info(f"  Channels: {result.channels}")
        logger.info(f"  File size: {result.file_size_bytes} bytes")
        logger.info(f"  Duration: {result.duration_ms:.0f}ms")
        logger.info(f"  Optimized: {result.optimized}")
        logger.info(f"  Compressed: {result.compressed}")
        
        # Save telephony version
        if result.save_to_file("demo_telephony.wav"):
            logger.info("Telephony audio saved to: demo_telephony.wav")
        
        # Compare with high quality version
        logger.info("\nGenerating high-quality version for comparison...")
        
        hq_config = get_high_quality_config()
        hq_generator = EdgeTTSGenerator(hq_config, Mock())
        await hq_generator.initialize()
        await hq_generator.start()
        
        hq_result = await hq_generator.generate_speech(test_text)
        
        logger.info("High Quality Results:")
        logger.info(f"  Sample rate: {hq_result.sample_rate}Hz")
        logger.info(f"  File size: {hq_result.file_size_bytes} bytes")
        logger.info(f"  Duration: {hq_result.duration_ms:.0f}ms")
        
        # Save high quality version
        if hq_result.save_to_file("demo_high_quality.wav"):
            logger.info("High quality audio saved to: demo_high_quality.wav")
        
        # Compare file sizes
        size_ratio = result.file_size_bytes / hq_result.file_size_bytes
        logger.info(f"\nCompression ratio: {size_ratio:.2f} (telephony vs high quality)")
        
        await hq_generator.stop()
        await hq_generator.cleanup()
    
    except Exception as e:
        logger.error(f"Telephony optimization demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def demo_performance_testing():
    """Demonstrate performance testing."""
    logger.info("=== Performance Testing Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available")
        return
    
    try:
        generator = await create_edge_tts_generator(
            enable_caching=True,
            config_manager=Mock()
        )
        
        # Test texts of different lengths
        test_texts = [
            "您好。",  # Very short
            "感谢您的咨询，请稍等。",  # Short
            "根据您的信用状况，我们可以为您提供个人贷款服务。",  # Medium
            "非常感谢您选择我们银行的服务。根据您提供的信息，我们已经为您准备了最适合的贷款方案。请您携带身份证、收入证明等相关材料到就近的网点办理。",  # Long
        ]
        
        voices_to_test = ["zh-CN-XiaoxiaoNeural", "zh-CN-YunxiNeural"]
        
        logger.info(f"Testing {len(test_texts)} texts with {len(voices_to_test)} voices...")
        
        total_time = 0
        total_audio_duration = 0
        
        for i, text in enumerate(test_texts):
            for voice in voices_to_test:
                logger.info(f"\nTest {i+1}: {len(text)} chars, voice: {voice}")
                
                result = await generator.generate_speech(text, voice)
                
                total_time += result.generation_time_ms
                total_audio_duration += result.duration_ms
                
                logger.info(f"  Generation time: {result.generation_time_ms:.2f}ms")
                logger.info(f"  Audio duration: {result.duration_ms:.0f}ms")
                logger.info(f"  Real-time factor: {result.generation_time_ms / result.duration_ms:.2f}")
        
        # Test caching performance
        logger.info("\nTesting cache performance...")
        cache_test_text = test_texts[1]  # Use medium length text
        
        # First generation (no cache)
        start_time = asyncio.get_event_loop().time()
        result1 = await generator.generate_speech(cache_test_text, voices_to_test[0])
        first_time = (asyncio.get_event_loop().time() - start_time) * 1000
        
        # Second generation (should use cache)
        start_time = asyncio.get_event_loop().time()
        result2 = await generator.generate_speech(cache_test_text, voices_to_test[0])
        second_time = (asyncio.get_event_loop().time() - start_time) * 1000
        
        logger.info(f"First generation: {first_time:.2f}ms")
        logger.info(f"Second generation (cached): {second_time:.2f}ms")
        logger.info(f"Cache speedup: {first_time / second_time:.1f}x")
        
        # Show final statistics
        stats = generator.get_generator_stats()
        logger.info(f"\nFinal Statistics:")
        logger.info(f"  Total generations: {stats['total_generations']}")
        logger.info(f"  Cache hit rate: {stats['cache_hit_rate']:.2%}")
        logger.info(f"  Average generation time: {stats['average_generation_time_ms']:.2f}ms")
        logger.info(f"  Total audio duration: {stats['total_audio_duration_ms']:.0f}ms")
    
    except Exception as e:
        logger.error(f"Performance testing demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def demo_customer_service_scenarios():
    """Demonstrate customer service scenarios."""
    logger.info("=== Customer Service Scenarios Demo ===")
    
    if not EDGE_TTS_AVAILABLE:
        logger.warning("EdgeTTS dependencies not available")
        return
    
    try:
        # Create generator with customer service configuration
        cs_config = create_customer_service_voice_config(
            gender=VoiceGender.FEMALE,
            tone="professional"
        )
        
        generator = await create_edge_tts_generator(config_manager=Mock())
        
        # Customer service scenarios
        scenarios = [
            {
                "name": "Welcome",
                "text": "您好！欢迎致电中国银行客服热线，我是智能客服助手小雅，很高兴为您服务。",
                "context": "greeting"
            },
            {
                "name": "Product Introduction",
                "text": "我们目前推出的个人贷款产品包括信用贷款、抵押贷款和消费贷款三种类型。",
                "context": "explanation"
            },
            {
                "name": "Interest Rate Inquiry",
                "text": "根据您的信用评级，我们可以为您提供年化利率3.5%到6.8%的优惠贷款。",
                "context": "explanation"
            },
            {
                "name": "Application Process",
                "text": "申请流程很简单：首先在线填写申请表，然后提交相关材料，最后等待审核结果。",
                "context": "explanation"
            },
            {
                "name": "Apology",
                "text": "非常抱歉让您久等了，由于业务繁忙造成的延误，我们深表歉意。",
                "context": "apologetic"
            },
            {
                "name": "Closing",
                "text": "感谢您选择我们的服务，如有其他问题请随时联系我们。祝您生活愉快！",
                "context": "closing"
            }
        ]
        
        for scenario in scenarios:
            logger.info(f"\nScenario: {scenario['name']}")
            logger.info(f"Context: {scenario['context']}")
            logger.info(f"Text: {scenario['text']}")
            
            # Get context-appropriate parameters
            params = get_voice_parameters_for_context(scenario['context'])
            
            # Generate speech
            result = await generator.generate_speech(
                scenario['text'],
                voice="zh-CN-XiaoxiaoNeural",
                **params
            )
            
            logger.info(f"Generated: {result.duration_ms:.0f}ms audio")
            
            # Save scenario audio
            filename = f"demo_scenario_{scenario['name'].lower().replace(' ', '_')}.wav"
            if result.save_to_file(filename):
                logger.info(f"Saved to: {filename}")
    
    except Exception as e:
        logger.error(f"Customer service scenarios demo failed: {e}")
    
    finally:
        if 'generator' in locals():
            await generator.stop()
            await generator.cleanup()


async def main():
    """Run all demos."""
    logger.info("Starting EdgeTTS Demo")
    
    if not EDGE_TTS_AVAILABLE:
        logger.error("EdgeTTS dependencies not available. Please install with:")
        logger.error("pip install edge-tts pydub")
        return
    
    try:
        await demo_basic_tts()
        await demo_voice_selection()
        await demo_voice_parameters()
        await demo_context_based_speech()
        await demo_telephony_optimization()
        await demo_performance_testing()
        await demo_customer_service_scenarios()
        
        logger.info("All demos completed successfully!")
        logger.info("Check the generated .wav files to hear the results.")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())