"""
Example Audio Processor Plugin

This is an example plugin that demonstrates how to create
audio processing plugins for the AI Voice Customer Service system.
"""

import asyncio
import numpy as np
from typing import Dict, Any
import logging

# Import plugin interfaces from the main system
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.core.plugin_system import AudioProcessorPlugin, PluginMetadata, PluginType


class ExampleAudioProcessor(AudioProcessorPlugin):
    """Example audio processor plugin implementation."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = {}
        self.is_initialized = False
        self.is_running = False
        
        # Plugin metadata
        self._metadata = PluginMetadata(
            name="example_audio_processor",
            version="1.0.0",
            description="Example audio processor plugin that demonstrates the plugin system",
            author="AI Voice Team",
            plugin_type=PluginType.AUDIO_PROCESSOR,
            dependencies=[],
            entry_point="ExampleAudioProcessor"
        )
        
        # Processing state
        self.processing_stats = {
            "total_processed": 0,
            "total_bytes": 0,
            "average_processing_time": 0.0,
            "error_count": 0
        }
    
    @property
    def metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        return self._metadata
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin with configuration."""
        try:
            self.config = config
            
            # Validate configuration
            self.processing_mode = config.get("processing_mode", "enhance")
            self.gain_factor = config.get("gain_factor", 1.0)
            self.enable_noise_reduction = config.get("enable_noise_reduction", True)
            self.sample_rate = config.get("sample_rate", 16000)
            
            # Validate configuration values
            if self.processing_mode not in ["enhance", "filter", "normalize"]:
                raise ValueError(f"Invalid processing mode: {self.processing_mode}")
            
            if not (0.1 <= self.gain_factor <= 5.0):
                raise ValueError(f"Invalid gain factor: {self.gain_factor}")
            
            if self.sample_rate not in [8000, 16000, 22050, 44100, 48000]:
                raise ValueError(f"Invalid sample rate: {self.sample_rate}")
            
            self.is_initialized = True
            self.logger.info(f"Example Audio Processor initialized with mode: {self.processing_mode}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Example Audio Processor: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the plugin."""
        if not self.is_initialized:
            self.logger.error("Plugin not initialized")
            return False
        
        try:
            self.is_running = True
            self.logger.info("Example Audio Processor started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start Example Audio Processor: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the plugin."""
        try:
            self.is_running = False
            self.logger.info("Example Audio Processor stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop Example Audio Processor: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self.is_running = False
        self.is_initialized = False
        self.config.clear()
        self.processing_stats = {
            "total_processed": 0,
            "total_bytes": 0,
            "average_processing_time": 0.0,
            "error_count": 0
        }
        self.logger.info("Example Audio Processor cleanup completed")
    
    async def process_audio(self, audio_data: bytes, context: Dict[str, Any]) -> bytes:
        """Process audio data."""
        if not self.is_running:
            raise RuntimeError("Plugin is not running")
        
        import time
        start_time = time.time()
        
        try:
            # Convert bytes to numpy array for processing
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Apply processing based on mode
            if self.processing_mode == "enhance":
                processed_array = await self._enhance_audio(audio_array, context)
            elif self.processing_mode == "filter":
                processed_array = await self._filter_audio(audio_array, context)
            elif self.processing_mode == "normalize":
                processed_array = await self._normalize_audio(audio_array, context)
            else:
                processed_array = audio_array  # No processing
            
            # Apply gain
            if self.gain_factor != 1.0:
                processed_array = processed_array * self.gain_factor
                # Clip to prevent overflow
                processed_array = np.clip(processed_array, -32768, 32767)
            
            # Convert back to bytes
            processed_bytes = processed_array.astype(np.int16).tobytes()
            
            # Update statistics
            processing_time = time.time() - start_time
            self.processing_stats["total_processed"] += 1
            self.processing_stats["total_bytes"] += len(audio_data)
            
            # Update average processing time
            total_time = (self.processing_stats["average_processing_time"] * 
                         (self.processing_stats["total_processed"] - 1) + processing_time)
            self.processing_stats["average_processing_time"] = total_time / self.processing_stats["total_processed"]
            
            self.logger.debug(f"Processed {len(audio_data)} bytes in {processing_time:.3f}s")
            
            return processed_bytes
            
        except Exception as e:
            self.processing_stats["error_count"] += 1
            self.logger.error(f"Error processing audio: {e}")
            # Return original audio on error
            return audio_data
    
    async def _enhance_audio(self, audio_array: np.ndarray, context: Dict[str, Any]) -> np.ndarray:
        """Enhance audio quality."""
        # Simple enhancement: apply a mild high-pass filter
        if len(audio_array) < 2:
            return audio_array
        
        # Simple high-pass filter (difference filter)
        enhanced = np.copy(audio_array).astype(np.float32)
        
        # Apply noise reduction if enabled
        if self.enable_noise_reduction:
            # Simple noise gate
            noise_threshold = np.std(enhanced) * 0.1
            enhanced[np.abs(enhanced) < noise_threshold] *= 0.5
        
        # Mild amplification of higher frequencies
        for i in range(1, len(enhanced)):
            enhanced[i] = enhanced[i] + 0.1 * (enhanced[i] - enhanced[i-1])
        
        return enhanced.astype(np.int16)
    
    async def _filter_audio(self, audio_array: np.ndarray, context: Dict[str, Any]) -> np.ndarray:
        """Apply audio filtering."""
        # Simple low-pass filter
        if len(audio_array) < 3:
            return audio_array
        
        filtered = np.copy(audio_array).astype(np.float32)
        
        # 3-point moving average (simple low-pass filter)
        for i in range(1, len(filtered) - 1):
            filtered[i] = (filtered[i-1] + filtered[i] + filtered[i+1]) / 3.0
        
        return filtered.astype(np.int16)
    
    async def _normalize_audio(self, audio_array: np.ndarray, context: Dict[str, Any]) -> np.ndarray:
        """Normalize audio levels."""
        if len(audio_array) == 0:
            return audio_array
        
        # Find peak amplitude
        peak = np.max(np.abs(audio_array))
        
        if peak == 0:
            return audio_array
        
        # Normalize to 80% of maximum range to prevent clipping
        target_peak = 32767 * 0.8
        normalization_factor = target_peak / peak
        
        normalized = audio_array.astype(np.float32) * normalization_factor
        
        return normalized.astype(np.int16)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get plugin statistics."""
        return {
            "plugin_name": self.metadata.name,
            "plugin_version": self.metadata.version,
            "is_running": self.is_running,
            "configuration": {
                "processing_mode": self.processing_mode,
                "gain_factor": self.gain_factor,
                "enable_noise_reduction": self.enable_noise_reduction,
                "sample_rate": self.sample_rate
            },
            "statistics": self.processing_stats.copy()
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get plugin health status."""
        error_rate = 0.0
        if self.processing_stats["total_processed"] > 0:
            error_rate = self.processing_stats["error_count"] / self.processing_stats["total_processed"]
        
        health_status = "healthy"
        if error_rate > 0.1:  # More than 10% errors
            health_status = "unhealthy"
        elif error_rate > 0.05:  # More than 5% errors
            health_status = "warning"
        
        return {
            "status": health_status,
            "error_rate": error_rate,
            "total_processed": self.processing_stats["total_processed"],
            "average_processing_time": self.processing_stats["average_processing_time"],
            "is_running": self.is_running,
            "is_initialized": self.is_initialized
        }


# Plugin factory function (optional)
def create_plugin() -> ExampleAudioProcessor:
    """Factory function to create plugin instance."""
    return ExampleAudioProcessor()


# Plugin information (optional)
PLUGIN_INFO = {
    "name": "example_audio_processor",
    "version": "1.0.0",
    "description": "Example audio processor plugin",
    "author": "AI Voice Team",
    "type": "audio_processor"
}
