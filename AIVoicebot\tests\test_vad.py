"""
Tests for Voice Activity Detection (VAD) components.
"""

import asyncio
import pytest
import numpy as np
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.interfaces import AudioChunk, AudioFormat
from src.components.vad import (
    SileroVADDetector, VADConfig, VADResult,
    SpeechSegmenter, SegmentationConfig, SpeechSegment
)


class TestSileroVADDetector:
    """Test SileroVAD detector functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def vad_config(self):
        """Create test VAD configuration."""
        return VADConfig(
            model_path="models/snakers4_silero-vad",
            threshold=0.5,
            min_speech_duration_ms=250,
            min_silence_duration_ms=100,
            enable_adaptive_threshold=True
        )
    
    @pytest.fixture
    def mock_silero_model(self):
        """Create mock SileroVAD model."""
        model = Mock()
        model.return_value = Mock()
        model.return_value.item.return_value = 0.7  # Mock confidence score
        
        utils = (Mock(), Mock(), Mock(), Mock(), Mock())  # Mock utils tuple
        
        return model, utils
    
    def create_test_audio_chunk(self, duration_ms: int = 32, sample_rate: int = 16000) -> AudioChunk:
        """Create test audio chunk."""
        samples = int(duration_ms * sample_rate / 1000)
        audio_data = np.random.randint(-32768, 32767, samples, dtype=np.int16).tobytes()
        
        return AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=duration_ms,
            sample_rate=sample_rate
        )
    
    @pytest.mark.asyncio
    async def test_vad_detector_creation(self, vad_config, mock_config_manager):
        """Test VAD detector creation."""
        detector = SileroVADDetector(vad_config, mock_config_manager)
        
        assert detector.config == vad_config
        assert detector.model is None  # Not initialized yet
        assert not detector.is_initialized
    
    @pytest.mark.asyncio
    @patch('src.components.vad.silero_vad_detector.silero_vad')
    async def test_vad_detector_initialization(self, mock_silero_vad, vad_config, 
                                             mock_config_manager, mock_silero_model):
        """Test VAD detector initialization."""
        mock_silero_vad.return_value = mock_silero_model
        
        detector = SileroVADDetector(vad_config, mock_config_manager)
        
        # Mock the import
        with patch.dict('sys.modules', {'hubconf': Mock(silero_vad=mock_silero_vad)}):
            await detector.initialize()
        
        assert detector.is_initialized
        assert detector.model is not None
    
    @pytest.mark.asyncio
    @patch('src.components.vad.silero_vad_detector.silero_vad')
    async def test_voice_activity_detection(self, mock_silero_vad, vad_config, 
                                          mock_config_manager, mock_silero_model):
        """Test voice activity detection."""
        mock_silero_vad.return_value = mock_silero_model
        
        detector = SileroVADDetector(vad_config, mock_config_manager)
        
        # Mock initialization
        with patch.dict('sys.modules', {'hubconf': Mock(silero_vad=mock_silero_vad)}):
            await detector.initialize()
            await detector.start()
        
        # Create test audio
        audio_chunk = self.create_test_audio_chunk()
        
        # Test detection
        confidence = await detector.detect_voice_activity(audio_chunk)
        
        assert isinstance(confidence, float)
        assert 0.0 <= confidence <= 1.0
        
        await detector.stop()
        await detector.cleanup()
    
    @pytest.mark.asyncio
    @patch('src.components.vad.silero_vad_detector.silero_vad')
    async def test_detailed_voice_activity_detection(self, mock_silero_vad, vad_config, 
                                                   mock_config_manager, mock_silero_model):
        """Test detailed voice activity detection."""
        mock_silero_vad.return_value = mock_silero_model
        
        detector = SileroVADDetector(vad_config, mock_config_manager)
        
        with patch.dict('sys.modules', {'hubconf': Mock(silero_vad=mock_silero_vad)}):
            await detector.initialize()
            await detector.start()
        
        audio_chunk = self.create_test_audio_chunk()
        
        # Test detailed detection
        result = await detector.detect_voice_activity_detailed(audio_chunk)
        
        assert isinstance(result, VADResult)
        assert isinstance(result.has_speech, bool)
        assert isinstance(result.confidence, float)
        assert isinstance(result.timestamp, datetime)
        
        await detector.stop()
        await detector.cleanup()
    
    @pytest.mark.asyncio
    @patch('src.components.vad.silero_vad_detector.silero_vad')
    async def test_adaptive_threshold(self, mock_silero_vad, vad_config, 
                                    mock_config_manager, mock_silero_model):
        """Test adaptive threshold functionality."""
        vad_config.enable_adaptive_threshold = True
        mock_silero_vad.return_value = mock_silero_model
        
        detector = SileroVADDetector(vad_config, mock_config_manager)
        
        with patch.dict('sys.modules', {'hubconf': Mock(silero_vad=mock_silero_vad)}):
            await detector.initialize()
            await detector.start()
        
        initial_threshold = detector.current_threshold
        
        # Process several audio chunks to trigger adaptation
        for i in range(10):
            audio_chunk = self.create_test_audio_chunk()
            await detector.detect_voice_activity(audio_chunk)
        
        # Threshold should potentially change with adaptive mode
        assert detector.is_adaptive_threshold_enabled
        
        await detector.stop()
        await detector.cleanup()
    
    @pytest.mark.asyncio
    @patch('src.components.vad.silero_vad_detector.silero_vad')
    async def test_vad_statistics(self, mock_silero_vad, vad_config, 
                                mock_config_manager, mock_silero_model):
        """Test VAD statistics collection."""
        mock_silero_vad.return_value = mock_silero_model
        
        detector = SileroVADDetector(vad_config, mock_config_manager)
        
        with patch.dict('sys.modules', {'hubconf': Mock(silero_vad=mock_silero_vad)}):
            await detector.initialize()
            await detector.start()
        
        # Process some audio chunks
        for i in range(5):
            audio_chunk = self.create_test_audio_chunk()
            await detector.detect_voice_activity(audio_chunk)
        
        # Get statistics
        stats = detector.get_detection_stats()
        
        assert stats["total_detections"] == 5
        assert "speech_detected" in stats
        assert "speech_rate" in stats
        assert "average_processing_time_ms" in stats
        assert "current_threshold" in stats
        
        # Reset statistics
        detector.reset_stats()
        stats_after_reset = detector.get_detection_stats()
        assert stats_after_reset["total_detections"] == 0
        
        await detector.stop()
        await detector.cleanup()


class TestSpeechSegmenter:
    """Test speech segmentation functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def segmentation_config(self):
        """Create test segmentation configuration."""
        return SegmentationConfig(
            min_speech_duration_ms=250,
            min_silence_duration_ms=300,
            speech_start_pad_ms=100,
            speech_end_pad_ms=200,
            enable_adaptive_threshold=True
        )
    
    @pytest.fixture
    def mock_vad_detector(self):
        """Create mock VAD detector."""
        detector = Mock(spec=SileroVADDetector)
        detector.is_initialized = True
        detector.is_running = True
        
        # Mock VAD results
        async def mock_detect_detailed(audio_chunk):
            return VADResult(
                has_speech=True,
                confidence=0.7,
                timestamp=datetime.now(),
                background_noise_level=0.1,
                adapted_threshold=0.5
            )
        
        detector.detect_voice_activity_detailed = mock_detect_detailed
        return detector
    
    def create_test_audio_chunk(self, duration_ms: int = 32) -> AudioChunk:
        """Create test audio chunk."""
        samples = int(duration_ms * 16000 / 1000)
        audio_data = np.random.randint(-32768, 32767, samples, dtype=np.int16).tobytes()
        
        return AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=duration_ms,
            sample_rate=16000
        )
    
    @pytest.mark.asyncio
    async def test_segmenter_creation(self, segmentation_config, mock_vad_detector, mock_config_manager):
        """Test speech segmenter creation."""
        segmenter = SpeechSegmenter(segmentation_config, mock_vad_detector, mock_config_manager)
        
        assert segmenter.config == segmentation_config
        assert segmenter.vad_detector == mock_vad_detector
        assert not segmenter.is_initialized
    
    @pytest.mark.asyncio
    async def test_segmenter_initialization(self, segmentation_config, mock_vad_detector, mock_config_manager):
        """Test speech segmenter initialization."""
        segmenter = SpeechSegmenter(segmentation_config, mock_vad_detector, mock_config_manager)
        
        await segmenter.initialize()
        await segmenter.start()
        
        assert segmenter.is_initialized
        assert segmenter.is_running
        
        await segmenter.stop()
        await segmenter.cleanup()
    
    @pytest.mark.asyncio
    async def test_single_audio_segmentation(self, segmentation_config, mock_vad_detector, mock_config_manager):
        """Test segmentation of a single audio sequence."""
        segmenter = SpeechSegmenter(segmentation_config, mock_vad_detector, mock_config_manager)
        
        await segmenter.initialize()
        await segmenter.start()
        
        # Create test audio chunks
        audio_chunks = [self.create_test_audio_chunk() for _ in range(20)]
        
        # Segment audio
        segments = await segmenter.segment_single_audio(audio_chunks)
        
        # Should detect at least one segment
        assert len(segments) >= 0  # Might be 0 if mock doesn't trigger speech detection
        
        for segment in segments:
            assert isinstance(segment, SpeechSegment)
            assert len(segment.audio_chunks) > 0
            assert segment.duration_ms > 0
            assert 0.0 <= segment.average_confidence <= 1.0
        
        await segmenter.stop()
        await segmenter.cleanup()
    
    @pytest.mark.asyncio
    async def test_stream_segmentation(self, segmentation_config, mock_vad_detector, mock_config_manager):
        """Test segmentation of audio stream."""
        segmenter = SpeechSegmenter(segmentation_config, mock_vad_detector, mock_config_manager)
        
        await segmenter.initialize()
        await segmenter.start()
        
        # Create audio stream generator
        async def audio_stream():
            for i in range(15):
                yield self.create_test_audio_chunk()
                await asyncio.sleep(0.01)  # Small delay to simulate real-time
        
        # Collect segments
        segments = []
        async for segment in segmenter.segment_audio_stream(audio_stream()):
            segments.append(segment)
        
        # Verify segments
        for segment in segments:
            assert isinstance(segment, SpeechSegment)
            assert segment.duration_ms >= segmentation_config.min_speech_duration_ms
        
        await segmenter.stop()
        await segmenter.cleanup()
    
    @pytest.mark.asyncio
    async def test_segmentation_statistics(self, segmentation_config, mock_vad_detector, mock_config_manager):
        """Test segmentation statistics."""
        segmenter = SpeechSegmenter(segmentation_config, mock_vad_detector, mock_config_manager)
        
        await segmenter.initialize()
        await segmenter.start()
        
        # Process some audio
        audio_chunks = [self.create_test_audio_chunk() for _ in range(10)]
        await segmenter.segment_single_audio(audio_chunks)
        
        # Get statistics
        stats = segmenter.get_segmentation_stats()
        
        assert "segments_detected" in stats
        assert "total_speech_duration_ms" in stats
        assert "average_segment_duration_ms" in stats
        assert "current_adaptive_threshold" in stats
        assert "background_noise_level" in stats
        
        # Reset statistics
        segmenter.reset_stats()
        stats_after_reset = segmenter.get_segmentation_stats()
        assert stats_after_reset["segments_detected"] == 0
        
        await segmenter.stop()
        await segmenter.cleanup()
    
    def test_speech_segment_properties(self):
        """Test SpeechSegment properties and methods."""
        # Create test audio chunks
        audio_chunks = [self.create_test_audio_chunk() for _ in range(3)]
        
        segment = SpeechSegment(
            audio_chunks=audio_chunks,
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration_ms=100.0,
            confidence_scores=[0.7, 0.8, 0.6],
            average_confidence=0.7,
            segment_id="test_segment"
        )
        
        # Test properties
        assert len(segment.audio_chunks) == 3
        assert segment.average_confidence == 0.7
        assert segment.segment_id == "test_segment"
        
        # Test methods
        total_samples = segment.get_total_samples()
        assert total_samples > 0
        
        audio_data = segment.get_audio_data()
        assert isinstance(audio_data, bytes)
        assert len(audio_data) > 0


class TestVADUtilities:
    """Test VAD utility functions."""
    
    def create_test_vad_results(self, count: int = 10) -> list:
        """Create test VAD results."""
        results = []
        for i in range(count):
            result = VADResult(
                has_speech=i % 2 == 0,  # Alternate speech/no speech
                confidence=0.5 + (i % 5) * 0.1,  # Varying confidence
                timestamp=datetime.now()
            )
            results.append(result)
        return results
    
    def test_speech_statistics_calculation(self):
        """Test speech statistics calculation."""
        from src.components.vad.silero_vad_detector import calculate_speech_statistics
        
        # Test with empty results
        empty_stats = calculate_speech_statistics([])
        assert empty_stats["total_frames"] == 0
        assert empty_stats["speech_ratio"] == 0.0
        
        # Test with actual results
        vad_results = self.create_test_vad_results(10)
        stats = calculate_speech_statistics(vad_results)
        
        assert stats["total_frames"] == 10
        assert stats["speech_frames"] == 5  # Half have speech
        assert stats["speech_ratio"] == 0.5
        assert 0.0 <= stats["average_confidence"] <= 1.0
        assert 0.0 <= stats["min_confidence"] <= 1.0
        assert 0.0 <= stats["max_confidence"] <= 1.0
    
    def test_segment_analysis(self):
        """Test speech segment analysis."""
        from src.components.vad.speech_segmenter import analyze_speech_segments
        
        # Create test segments
        segments = []
        for i in range(3):
            audio_chunks = [AudioChunk(
                data=b'\x00' * 1024,
                format=AudioFormat.PCM_16KHZ_MONO,
                timestamp=datetime.now(),
                duration_ms=32,
                sample_rate=16000
            )]
            
            segment = SpeechSegment(
                audio_chunks=audio_chunks,
                start_time=datetime.now(),
                end_time=datetime.now(),
                duration_ms=100.0 + i * 50,
                confidence_scores=[0.7],
                average_confidence=0.7 + i * 0.1,
                segment_id=f"segment_{i}"
            )
            segments.append(segment)
        
        # Analyze segments
        analysis = analyze_speech_segments(segments)
        
        assert analysis["total_segments"] == 3
        assert analysis["total_duration_ms"] == 300.0  # 100 + 150 + 200
        assert analysis["average_duration_ms"] == 150.0
        assert analysis["min_duration_ms"] == 100.0
        assert analysis["max_duration_ms"] == 200.0
        assert 0.0 <= analysis["average_confidence"] <= 1.0


# Integration test
@pytest.mark.asyncio
async def test_vad_integration():
    """Integration test for VAD components."""
    # Mock configuration manager
    config_manager = Mock()
    
    # Create VAD configuration
    vad_config = VADConfig(
        model_path="models/snakers4_silero-vad",
        threshold=0.5,
        enable_adaptive_threshold=True
    )
    
    # Create segmentation configuration
    seg_config = SegmentationConfig(
        min_speech_duration_ms=200,
        min_silence_duration_ms=250
    )
    
    # Mock SileroVAD model
    with patch('src.components.vad.silero_vad_detector.silero_vad') as mock_silero:
        model = Mock()
        model.return_value = Mock()
        model.return_value.item.return_value = 0.8
        utils = (Mock(), Mock(), Mock(), Mock(), Mock())
        mock_silero.return_value = (model, utils)
        
        # Create VAD detector
        with patch.dict('sys.modules', {'hubconf': Mock(silero_vad=mock_silero)}):
            vad_detector = SileroVADDetector(vad_config, config_manager)
            await vad_detector.initialize()
            await vad_detector.start()
            
            # Create speech segmenter
            segmenter = SpeechSegmenter(seg_config, vad_detector, config_manager)
            await segmenter.initialize()
            await segmenter.start()
            
            # Test integration
            audio_chunks = []
            for i in range(20):
                samples = np.random.randint(-32768, 32767, 512, dtype=np.int16)
                chunk = AudioChunk(
                    data=samples.tobytes(),
                    format=AudioFormat.PCM_16KHZ_MONO,
                    timestamp=datetime.now(),
                    duration_ms=32,
                    sample_rate=16000
                )
                audio_chunks.append(chunk)
            
            # Segment audio
            segments = await segmenter.segment_single_audio(audio_chunks)
            
            # Get statistics
            vad_stats = vad_detector.get_detection_stats()
            seg_stats = segmenter.get_segmentation_stats()
            
            assert vad_stats["total_detections"] > 0
            assert isinstance(seg_stats["segments_detected"], int)
            
            # Cleanup
            await segmenter.stop()
            await segmenter.cleanup()
            await vad_detector.stop()
            await vad_detector.cleanup()


if __name__ == "__main__":
    # Run basic tests
    asyncio.run(test_vad_integration())