"""
Comprehensive Performance Monitoring Service

This module provides a comprehensive service for monitoring system performance,
including real-time metrics, alerting, resource optimization, and performance analytics.
"""

import time
import logging
import asyncio
import statistics
from typing import Dict, Any, Optional, List, Callable, Set
from collections import deque, defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
import json

from ..core.base_component import BaseComponent


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MetricType(Enum):
    """Performance metric types."""
    RESPONSE_TIME = "response_time"
    ERROR_RATE = "error_rate"
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    DISK_USAGE = "disk_usage"
    NETWORK_LATENCY = "network_latency"
    THROUGHPUT = "throughput"
    CONCURRENT_USERS = "concurrent_users"
    QUEUE_SIZE = "queue_size"


@dataclass
class PerformanceMetric:
    """Performance metric data point."""
    metric_type: MetricType
    component: str
    value: float
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "metric_type": self.metric_type.value,
            "component": self.component,
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }


@dataclass
class PerformanceAlert:
    """Performance alert."""
    alert_id: str
    metric_type: MetricType
    component: str
    severity: AlertSeverity
    message: str
    current_value: float
    threshold_value: float
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "alert_id": self.alert_id,
            "metric_type": self.metric_type.value,
            "component": self.component,
            "severity": self.severity.value,
            "message": self.message,
            "current_value": self.current_value,
            "threshold_value": self.threshold_value,
            "timestamp": self.timestamp.isoformat(),
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None
        }


@dataclass
class AlertThreshold:
    """Alert threshold configuration."""
    metric_type: MetricType
    component: str
    warning_threshold: float
    critical_threshold: float
    comparison_operator: str = ">"  # >, <, >=, <=, ==
    duration_seconds: int = 60  # How long threshold must be exceeded
    enabled: bool = True


@dataclass
class ComponentHealth:
    """Component health status."""
    component: str
    status: str  # healthy, warning, critical, unknown
    last_check: datetime = field(default_factory=datetime.now)
    response_time_ms: float = 0.0
    error_rate_percent: float = 0.0
    cpu_usage_percent: float = 0.0
    memory_usage_percent: float = 0.0
    active_alerts: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "component": self.component,
            "status": self.status,
            "last_check": self.last_check.isoformat(),
            "response_time_ms": self.response_time_ms,
            "error_rate_percent": self.error_rate_percent,
            "cpu_usage_percent": self.cpu_usage_percent,
            "memory_usage_percent": self.memory_usage_percent,
            "active_alerts": self.active_alerts
        }


@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation."""
    component: str
    issue_type: str
    severity: AlertSeverity
    description: str
    recommendation: str
    estimated_impact: str
    implementation_effort: str  # low, medium, high
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "component": self.component,
            "issue_type": self.issue_type,
            "severity": self.severity.value,
            "description": self.description,
            "recommendation": self.recommendation,
            "estimated_impact": self.estimated_impact,
            "implementation_effort": self.implementation_effort,
            "timestamp": self.timestamp.isoformat()
        }


class PerformanceMonitor(BaseComponent):
    """
    Comprehensive performance monitoring system with real-time metrics,
    alerting, resource optimization, and performance analytics.
    """

    def __init__(self, config_manager, logger=None):
        super().__init__("performance_monitor", config_manager, logger)

        # Configuration
        self.monitoring_interval = self.config_manager.get_config("performance.monitoring_interval_seconds", 30)
        self.metrics_retention_hours = self.config_manager.get_config("performance.metrics_retention_hours", 24)
        self.enable_alerting = self.config_manager.get_config("performance.enable_alerting", True)
        self.enable_optimization_recommendations = self.config_manager.get_config("performance.enable_recommendations", True)

        # Metrics storage
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.component_metrics: Dict[str, Dict[str, deque]] = defaultdict(lambda: defaultdict(lambda: deque(maxlen=1000)))

        # Alert management
        self.alert_thresholds: Dict[str, AlertThreshold] = {}
        self.active_alerts: Dict[str, PerformanceAlert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []

        # Component health tracking
        self.component_health: Dict[str, ComponentHealth] = {}
        self.registered_components: Set[str] = set()

        # Optimization recommendations
        self.recommendations: List[OptimizationRecommendation] = []

        # Background tasks
        self._monitor_task: Optional[asyncio.Task] = None
        self._alert_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None

        # Performance tracking
        self._last_collection_time = time.time()
        self._collection_count = 0

        # Thread safety
        self._lock = threading.RLock()

        # Initialize default thresholds
        self._setup_default_thresholds()

    async def _initialize_impl(self) -> None:
        self._log.info("Comprehensive Performance Monitor initialized")
        self._log.info(f"Monitoring interval: {self.monitoring_interval}s")
        self._log.info(f"Metrics retention: {self.metrics_retention_hours}h")
        self._log.info(f"Alerting enabled: {self.enable_alerting}")
        self._log.info(f"Recommendations enabled: {self.enable_optimization_recommendations}")

    async def _start_impl(self) -> None:
        self._log.info("Starting Performance Monitor...")

        # Start monitoring tasks
        self._monitor_task = asyncio.create_task(self._monitor_loop())

        if self.enable_alerting:
            self._alert_task = asyncio.create_task(self._alert_loop())

        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        self._log.info("Performance Monitor started")

    async def _stop_impl(self) -> None:
        self._log.info("Stopping Performance Monitor...")

        # Cancel all tasks
        tasks = [self._monitor_task, self._alert_task, self._cleanup_task]
        for task in tasks:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self._log.info("Performance Monitor stopped")

    async def _cleanup_impl(self) -> None:
        self._log.info("Cleaning up Performance Monitor...")

        with self._lock:
            self.metrics.clear()
            self.component_metrics.clear()
            self.active_alerts.clear()
            self.component_health.clear()
            self.recommendations.clear()

        self._log.info("Performance Monitor cleanup completed")

    # Core Monitoring Methods

    def register_component(self, component_name: str) -> None:
        """Register a component for monitoring."""
        with self._lock:
            self.registered_components.add(component_name)
            if component_name not in self.component_health:
                self.component_health[component_name] = ComponentHealth(
                    component=component_name,
                    status="unknown"
                )
        self._log.info(f"Registered component for monitoring: {component_name}")

    def record_metric(
        self,
        metric_type: MetricType,
        component: str,
        value: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Record a performance metric."""
        metric = PerformanceMetric(
            metric_type=metric_type,
            component=component,
            value=value,
            metadata=metadata or {}
        )

        with self._lock:
            # Store in general metrics
            self.metrics[metric_type.value].append(metric)

            # Store in component-specific metrics
            self.component_metrics[component][metric_type.value].append(metric)

            # Update component health
            self._update_component_health(component, metric_type, value)

    def record_response_time(self, component: str, response_time_ms: float, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record response time for a component."""
        self.record_metric(MetricType.RESPONSE_TIME, component, response_time_ms, metadata)

    def record_error(self, component: str, error_type: str = "general", metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record an error for a component."""
        error_metadata = {"error_type": error_type}
        if metadata:
            error_metadata.update(metadata)
        self.record_metric(MetricType.ERROR_RATE, component, 1.0, error_metadata)

    def record_throughput(self, component: str, requests_per_second: float, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record throughput for a component."""
        self.record_metric(MetricType.THROUGHPUT, component, requests_per_second, metadata)

    def record_queue_size(self, component: str, queue_size: int, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record queue size for a component."""
        self.record_metric(MetricType.QUEUE_SIZE, component, float(queue_size), metadata)

    def add_alert_threshold(self, threshold: AlertThreshold) -> None:
        """Add an alert threshold."""
        key = f"{threshold.component}_{threshold.metric_type.value}"
        with self._lock:
            self.alert_thresholds[key] = threshold
        self._log.info(f"Added alert threshold for {threshold.component} {threshold.metric_type.value}")

    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]) -> None:
        """Add a callback function to be called when alerts are triggered."""
        self.alert_callbacks.append(callback)

    def get_component_health(self, component: str) -> Optional[ComponentHealth]:
        """Get health status for a component."""
        with self._lock:
            return self.component_health.get(component)

    def get_all_component_health(self) -> Dict[str, ComponentHealth]:
        """Get health status for all components."""
        with self._lock:
            return dict(self.component_health)

    def get_active_alerts(self) -> List[PerformanceAlert]:
        """Get all active alerts."""
        with self._lock:
            return list(self.active_alerts.values())

    def get_recommendations(self) -> List[OptimizationRecommendation]:
        """Get optimization recommendations."""
        return list(self.recommendations)

    # Analysis and Statistics Methods

    def get_metric_statistics(
        self,
        metric_type: MetricType,
        component: Optional[str] = None,
        time_window_minutes: int = 60
    ) -> Dict[str, float]:
        """Get statistics for a specific metric."""
        cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)

        with self._lock:
            if component:
                metrics_deque = self.component_metrics.get(component, {}).get(metric_type.value, deque())
            else:
                metrics_deque = self.metrics.get(metric_type.value, deque())

            # Filter by time window
            recent_metrics = [
                m for m in metrics_deque
                if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
            ]

            if not recent_metrics:
                return {
                    "count": 0,
                    "min": 0.0,
                    "max": 0.0,
                    "mean": 0.0,
                    "median": 0.0,
                    "std_dev": 0.0,
                    "p95": 0.0,
                    "p99": 0.0
                }

            values = [m.value for m in recent_metrics]

            return {
                "count": len(values),
                "min": min(values),
                "max": max(values),
                "mean": statistics.mean(values),
                "median": statistics.median(values),
                "std_dev": statistics.stdev(values) if len(values) > 1 else 0.0,
                "p95": self._percentile(values, 95),
                "p99": self._percentile(values, 99)
            }

    def get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system performance overview."""
        with self._lock:
            overview = {
                "timestamp": datetime.now().isoformat(),
                "monitoring_duration_minutes": (time.time() - self._last_collection_time) / 60,
                "total_components": len(self.registered_components),
                "healthy_components": len([h for h in self.component_health.values() if h.status == "healthy"]),
                "active_alerts": len(self.active_alerts),
                "total_recommendations": len(self.recommendations),
                "system_metrics": {},
                "component_summary": {}
            }

            # System-wide metrics
            for metric_type in MetricType:
                stats = self.get_metric_statistics(metric_type, time_window_minutes=60)
                if stats["count"] > 0:
                    overview["system_metrics"][metric_type.value] = stats

            # Component summary
            for component in self.registered_components:
                health = self.component_health.get(component)
                if health:
                    overview["component_summary"][component] = health.to_dict()

            return overview

    def get_performance_trends(self, time_window_hours: int = 24) -> Dict[str, Any]:
        """Get performance trends over time."""
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        trends = {
            "time_window_hours": time_window_hours,
            "trends": {}
        }

        with self._lock:
            for metric_type in MetricType:
                metrics_deque = self.metrics.get(metric_type.value, deque())
                recent_metrics = [
                    m for m in metrics_deque
                    if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
                ]

                if len(recent_metrics) >= 2:
                    # Calculate trend (simple linear regression slope)
                    values = [m.value for m in recent_metrics]
                    timestamps = [(m.timestamp - cutoff_time).total_seconds() for m in recent_metrics]

                    if len(set(timestamps)) > 1:  # Avoid division by zero
                        slope = self._calculate_trend_slope(timestamps, values)
                        trends["trends"][metric_type.value] = {
                            "slope": slope,
                            "direction": "increasing" if slope > 0 else "decreasing" if slope < 0 else "stable",
                            "data_points": len(recent_metrics)
                        }

        return trends

    # Background Tasks

    async def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while True:
            try:
                await asyncio.sleep(self.monitoring_interval)
                await self._collect_system_metrics()
                self._collection_count += 1

                if self._collection_count % 10 == 0:  # Log every 10 collections
                    self._log.debug(f"Performance metrics collected (count: {self._collection_count})")

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in monitoring loop: {e}")

    async def _alert_loop(self) -> None:
        """Alert checking loop."""
        while True:
            try:
                await asyncio.sleep(30)  # Check alerts every 30 seconds
                await self._check_alerts()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in alert loop: {e}")

    async def _cleanup_loop(self) -> None:
        """Cleanup old metrics loop."""
        while True:
            try:
                await asyncio.sleep(3600)  # Cleanup every hour
                self._cleanup_old_metrics()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in cleanup loop: {e}")

    async def _collect_system_metrics(self) -> None:
        """Collect system-wide metrics."""
        try:
            # Collect system resource usage
            resource_usage = await self._get_resource_usage()

            if resource_usage.get("cpu_percent", -1) >= 0:
                self.record_metric(MetricType.CPU_USAGE, "system", resource_usage["cpu_percent"])

            if resource_usage.get("memory_percent", -1) >= 0:
                self.record_metric(MetricType.MEMORY_USAGE, "system", resource_usage["memory_percent"])

            if resource_usage.get("disk_percent", -1) >= 0:
                self.record_metric(MetricType.DISK_USAGE, "system", resource_usage["disk_percent"])

            # Generate optimization recommendations
            if self.enable_optimization_recommendations:
                await self._generate_recommendations()

        except Exception as e:
            self._log.error(f"Error collecting system metrics: {e}")

    async def _check_alerts(self) -> None:
        """Check for alert conditions."""
        if not self.enable_alerting:
            return

        current_time = datetime.now()

        with self._lock:
            for threshold_key, threshold in self.alert_thresholds.items():
                if not threshold.enabled:
                    continue

                try:
                    # Get recent metrics for this threshold
                    recent_metrics = self._get_recent_metrics_for_threshold(threshold)

                    if not recent_metrics:
                        continue

                    # Check if threshold is exceeded
                    current_value = statistics.mean([m.value for m in recent_metrics])

                    if self._is_threshold_exceeded(current_value, threshold):
                        # Create or update alert
                        alert_id = f"{threshold.component}_{threshold.metric_type.value}_{threshold.critical_threshold}"

                        if alert_id not in self.active_alerts:
                            severity = AlertSeverity.CRITICAL if current_value >= threshold.critical_threshold else AlertSeverity.MEDIUM

                            alert = PerformanceAlert(
                                alert_id=alert_id,
                                metric_type=threshold.metric_type,
                                component=threshold.component,
                                severity=severity,
                                message=f"{threshold.component} {threshold.metric_type.value} is {current_value:.2f}, exceeding threshold {threshold.critical_threshold}",
                                current_value=current_value,
                                threshold_value=threshold.critical_threshold
                            )

                            self.active_alerts[alert_id] = alert
                            self.alert_history.append(alert)

                            # Trigger callbacks
                            for callback in self.alert_callbacks:
                                try:
                                    callback(alert)
                                except Exception as e:
                                    self._log.error(f"Error in alert callback: {e}")

                            self._log.warning(f"Alert triggered: {alert.message}")

                    else:
                        # Check if we should resolve an existing alert
                        alert_id = f"{threshold.component}_{threshold.metric_type.value}_{threshold.critical_threshold}"
                        if alert_id in self.active_alerts:
                            alert = self.active_alerts[alert_id]
                            alert.resolved = True
                            alert.resolved_at = current_time
                            del self.active_alerts[alert_id]
                            self._log.info(f"Alert resolved: {alert.message}")

                except Exception as e:
                    self._log.error(f"Error checking alert for {threshold_key}: {e}")

    # Helper Methods

    def _setup_default_thresholds(self) -> None:
        """Setup default alert thresholds."""
        default_thresholds = [
            AlertThreshold(MetricType.CPU_USAGE, "system", 70.0, 90.0),
            AlertThreshold(MetricType.MEMORY_USAGE, "system", 80.0, 95.0),
            AlertThreshold(MetricType.DISK_USAGE, "system", 85.0, 95.0),
            AlertThreshold(MetricType.RESPONSE_TIME, "asr", 3000.0, 5000.0),
            AlertThreshold(MetricType.RESPONSE_TIME, "tts", 2000.0, 4000.0),
            AlertThreshold(MetricType.RESPONSE_TIME, "llm", 5000.0, 10000.0),
            AlertThreshold(MetricType.ERROR_RATE, "system", 5.0, 10.0, comparison_operator=">="),
        ]

        for threshold in default_thresholds:
            self.add_alert_threshold(threshold)

    def _update_component_health(self, component: str, metric_type: MetricType, value: float) -> None:
        """Update component health based on new metric."""
        if component not in self.component_health:
            self.component_health[component] = ComponentHealth(component=component, status="unknown")

        health = self.component_health[component]
        health.last_check = datetime.now()

        # Update specific metrics
        if metric_type == MetricType.RESPONSE_TIME:
            health.response_time_ms = value
        elif metric_type == MetricType.ERROR_RATE:
            # Calculate error rate as percentage
            recent_errors = self._get_recent_error_count(component)
            recent_total = self._get_recent_request_count(component)
            health.error_rate_percent = (recent_errors / recent_total * 100) if recent_total > 0 else 0
        elif metric_type == MetricType.CPU_USAGE:
            health.cpu_usage_percent = value
        elif metric_type == MetricType.MEMORY_USAGE:
            health.memory_usage_percent = value

        # Determine overall health status
        health.status = self._calculate_health_status(health)

    def _calculate_health_status(self, health: ComponentHealth) -> str:
        """Calculate overall health status for a component."""
        if len(health.active_alerts) > 0:
            return "critical"

        # Check various thresholds
        if (health.response_time_ms > 5000 or
            health.error_rate_percent > 10 or
            health.cpu_usage_percent > 90 or
            health.memory_usage_percent > 95):
            return "critical"

        if (health.response_time_ms > 3000 or
            health.error_rate_percent > 5 or
            health.cpu_usage_percent > 70 or
            health.memory_usage_percent > 80):
            return "warning"

        return "healthy"

    def _get_recent_metrics_for_threshold(self, threshold: AlertThreshold) -> List[PerformanceMetric]:
        """Get recent metrics for alert threshold checking."""
        cutoff_time = datetime.now() - timedelta(seconds=threshold.duration_seconds)

        metrics_deque = self.component_metrics.get(threshold.component, {}).get(threshold.metric_type.value, deque())

        return [
            m for m in metrics_deque
            if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
        ]

    def _is_threshold_exceeded(self, current_value: float, threshold: AlertThreshold) -> bool:
        """Check if threshold is exceeded."""
        comparison_value = threshold.critical_threshold

        if threshold.comparison_operator == ">":
            return current_value > comparison_value
        elif threshold.comparison_operator == ">=":
            return current_value >= comparison_value
        elif threshold.comparison_operator == "<":
            return current_value < comparison_value
        elif threshold.comparison_operator == "<=":
            return current_value <= comparison_value
        elif threshold.comparison_operator == "==":
            return current_value == comparison_value

        return False

    def _get_recent_error_count(self, component: str, minutes: int = 5) -> int:
        """Get recent error count for a component."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        error_metrics = self.component_metrics.get(component, {}).get(MetricType.ERROR_RATE.value, deque())

        return len([
            m for m in error_metrics
            if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
        ])

    def _get_recent_request_count(self, component: str, minutes: int = 5) -> int:
        """Get recent request count for a component (approximated from response times)."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        response_metrics = self.component_metrics.get(component, {}).get(MetricType.RESPONSE_TIME.value, deque())

        return len([
            m for m in response_metrics
            if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
        ])

    def _cleanup_old_metrics(self) -> None:
        """Clean up old metrics to prevent memory issues."""
        cutoff_time = datetime.now() - timedelta(hours=self.metrics_retention_hours)

        with self._lock:
            # Clean up general metrics
            for metric_type, metrics_deque in self.metrics.items():
                # Convert to list, filter, and recreate deque
                filtered_metrics = [
                    m for m in metrics_deque
                    if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
                ]
                self.metrics[metric_type] = deque(filtered_metrics, maxlen=10000)

            # Clean up component metrics
            for component, component_metrics in self.component_metrics.items():
                for metric_type, metrics_deque in component_metrics.items():
                    filtered_metrics = [
                        m for m in metrics_deque
                        if isinstance(m, PerformanceMetric) and m.timestamp >= cutoff_time
                    ]
                    self.component_metrics[component][metric_type] = deque(filtered_metrics, maxlen=1000)

        self._log.debug("Cleaned up old performance metrics")

    async def _generate_recommendations(self) -> None:
        """Generate optimization recommendations based on current metrics."""
        try:
            new_recommendations = []

            # Check system resource usage
            cpu_stats = self.get_metric_statistics(MetricType.CPU_USAGE, "system", 30)
            memory_stats = self.get_metric_statistics(MetricType.MEMORY_USAGE, "system", 30)

            if cpu_stats["mean"] > 80:
                new_recommendations.append(OptimizationRecommendation(
                    component="system",
                    issue_type="high_cpu_usage",
                    severity=AlertSeverity.HIGH,
                    description=f"CPU usage is consistently high at {cpu_stats['mean']:.1f}%",
                    recommendation="Consider scaling horizontally or optimizing CPU-intensive operations",
                    estimated_impact="20-40% performance improvement",
                    implementation_effort="medium"
                ))

            if memory_stats["mean"] > 85:
                new_recommendations.append(OptimizationRecommendation(
                    component="system",
                    issue_type="high_memory_usage",
                    severity=AlertSeverity.HIGH,
                    description=f"Memory usage is consistently high at {memory_stats['mean']:.1f}%",
                    recommendation="Implement memory optimization or increase available memory",
                    estimated_impact="15-30% performance improvement",
                    implementation_effort="medium"
                ))

            # Check component response times
            for component in self.registered_components:
                response_stats = self.get_metric_statistics(MetricType.RESPONSE_TIME, component, 30)
                if response_stats["mean"] > 3000:  # 3 seconds
                    new_recommendations.append(OptimizationRecommendation(
                        component=component,
                        issue_type="slow_response_time",
                        severity=AlertSeverity.MEDIUM,
                        description=f"{component} response time is {response_stats['mean']:.0f}ms",
                        recommendation="Optimize processing pipeline or implement caching",
                        estimated_impact="30-50% response time improvement",
                        implementation_effort="low"
                    ))

            # Update recommendations (keep only recent ones)
            cutoff_time = datetime.now() - timedelta(hours=1)
            self.recommendations = [
                r for r in self.recommendations
                if r.timestamp >= cutoff_time
            ] + new_recommendations

        except Exception as e:
            self._log.error(f"Error generating recommendations: {e}")

    async def _get_resource_usage(self) -> Dict[str, Any]:
        """Get system resource usage."""
        try:
            import psutil

            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100

            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_percent,
                "memory_available_gb": memory.available / (1024**3),
                "disk_free_gb": disk.free / (1024**3)
            }
        except ImportError:
            self._log.warning("psutil not available, using mock resource data")
            return {
                "cpu_percent": 25.0,
                "memory_percent": 45.0,
                "disk_percent": 60.0,
                "memory_available_gb": 4.0,
                "disk_free_gb": 50.0
            }
        except Exception as e:
            self._log.error(f"Error getting resource usage: {e}")
            return {}

    @staticmethod
    def _percentile(data: List[float], percentile: int) -> float:
        """Calculate percentile of data."""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))

    @staticmethod
    def _calculate_trend_slope(x_values: List[float], y_values: List[float]) -> float:
        """Calculate trend slope using simple linear regression."""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0

        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x_squared = sum(x * x for x in x_values)

        denominator = n * sum_x_squared - sum_x * sum_x
        if denominator == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope

    # Legacy Methods (for backward compatibility)

    async def collect_metrics(self):
        """Legacy method - collect metrics from various system components."""
        await self._collect_system_metrics()

    def get_summary(self) -> Dict[str, Any]:
        """Legacy method - get a summary of the current performance metrics."""
        try:
            overview = self.get_system_overview()

            # Convert to legacy format
            summary = {
                "avg_response_time_ms": 0,
                "error_rate_percent": 0,
                "resource_usage": {},
                "component_avg_response_time": {}
            }

            # Extract response time average
            if "response_time" in overview.get("system_metrics", {}):
                summary["avg_response_time_ms"] = overview["system_metrics"]["response_time"].get("mean", 0)

            # Extract error rate
            if "error_rate" in overview.get("system_metrics", {}):
                summary["error_rate_percent"] = overview["system_metrics"]["error_rate"].get("mean", 0)

            # Extract resource usage
            system_health = overview.get("component_summary", {}).get("system", {})
            summary["resource_usage"] = {
                "cpu_percent": system_health.get("cpu_usage_percent", 0),
                "memory_percent": system_health.get("memory_usage_percent", 0)
            }

            # Extract component response times
            for component, health in overview.get("component_summary", {}).items():
                if health.get("response_time_ms", 0) > 0:
                    summary["component_avg_response_time"][component] = health["response_time_ms"]

            return summary

        except Exception as e:
            self._log.error(f"Error generating legacy summary: {e}")
            return {
                "avg_response_time_ms": 0,
                "error_rate_percent": 0,
                "resource_usage": {},
                "component_avg_response_time": {}
            }
