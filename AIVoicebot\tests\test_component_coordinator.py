"""
Tests for component coordination system.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock
import time
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.component_coordinator import (
    ComponentCoordinator, EventHandler, CoordinationEvent, ComponentInfo,
    ComponentState, EventType, Priority, ResourceRequest
)


class MockEventHandler(EventHandler):
    """Mock event handler for testing."""
    
    def __init__(self, component_name: str, handled_events: set = None):
        self.component_name = component_name
        self.handled_events = handled_events or {EventType.AUDIO_DATA}
        self.received_events = []
        self.should_fail = False
    
    async def handle_event(self, event: CoordinationEvent) -> bool:
        """Handle a coordination event."""
        self.received_events.append(event)
        if self.should_fail:
            raise Exception("Mock handler failure")
        return True
    
    def get_handled_events(self) -> set:
        """Get handled event types."""
        return self.handled_events
    
    def get_component_name(self) -> str:
        """Get component name."""
        return self.component_name


class TestComponentCoordinator:
    """Test component coordination system."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        config_manager = Mock()
        config_manager.get_config.side_effect = lambda key, default: {
            "coordination.max_queue_size": 100,
            "coordination.event_timeout_seconds": 5.0,
            "coordination.conflict_resolution_timeout": 2.0
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def coordinator(self, mock_config_manager):
        """Create component coordinator instance."""
        return ComponentCoordinator(mock_config_manager)
    
    @pytest.mark.asyncio
    async def test_coordinator_lifecycle(self, coordinator):
        """Test coordinator lifecycle."""
        await coordinator.initialize()
        await coordinator.start()
        
        assert coordinator.is_initialized
        assert coordinator.is_running
        
        await coordinator.stop()
        await coordinator.cleanup()
    
    def test_component_registration(self, coordinator):
        """Test component registration."""
        # Register a component
        coordinator.register_component(
            component_name="test_vad",
            component_type="vad",
            capabilities={"voice_detection", "silence_detection"},
            dependencies={"audio_input"},
            conflicts_with={"tts"}
        )
        
        assert "test_vad" in coordinator.components
        assert coordinator.components["test_vad"].component_type == "vad"
        assert "voice_detection" in coordinator.components["test_vad"].capabilities
        assert "audio_input" in coordinator.components["test_vad"].dependencies
        assert "tts" in coordinator.components["test_vad"].conflicts_with
        
        # Check initial state
        assert coordinator.get_component_state("test_vad") == ComponentState.IDLE
    
    def test_event_handler_registration(self, coordinator):
        """Test event handler registration."""
        handler = MockEventHandler("test_component", {EventType.SPEECH_DETECTED, EventType.AUDIO_DATA})
        coordinator.register_event_handler(handler)
        
        assert "test_component" in coordinator.event_handlers
        assert coordinator.event_handlers["test_component"] == handler
        
        # Check subscriptions
        assert "test_component" in coordinator.event_subscriptions[EventType.SPEECH_DETECTED]
        assert "test_component" in coordinator.event_subscriptions[EventType.AUDIO_DATA]
    
    @pytest.mark.asyncio
    async def test_event_publishing_and_processing(self, coordinator):
        """Test event publishing and processing."""
        await coordinator.initialize()
        await coordinator.start()
        
        # Register handler
        handler = MockEventHandler("test_component", {EventType.SPEECH_DETECTED})
        coordinator.register_event_handler(handler)
        
        # Publish event
        event = CoordinationEvent(
            event_type=EventType.SPEECH_DETECTED,
            source_component="vad",
            data={"confidence": 0.95}
        )
        
        success = await coordinator.publish_event(event)
        assert success
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Check handler received event
        assert len(handler.received_events) == 1
        assert handler.received_events[0].event_type == EventType.SPEECH_DETECTED
        assert handler.received_events[0].data["confidence"] == 0.95
        
        await coordinator.stop()
    
    @pytest.mark.asyncio
    async def test_simple_event_publishing(self, coordinator):
        """Test simplified event publishing."""
        await coordinator.initialize()
        await coordinator.start()
        
        handler = MockEventHandler("test_component", {EventType.AUDIO_DATA})
        coordinator.register_event_handler(handler)
        
        # Publish simple event
        success = await coordinator.publish_event_simple(
            event_type=EventType.AUDIO_DATA,
            source_component="audio_input",
            data={"sample_rate": 16000},
            priority=Priority.HIGH
        )
        
        assert success
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Check handler received event
        assert len(handler.received_events) == 1
        received_event = handler.received_events[0]
        assert received_event.event_type == EventType.AUDIO_DATA
        assert received_event.priority == Priority.HIGH
        assert received_event.data["sample_rate"] == 16000
        
        await coordinator.stop()
    
    def test_state_management(self, coordinator):
        """Test component state management."""
        # Register component
        coordinator.register_component("test_component", "test")
        
        # Test state updates
        assert coordinator.update_component_state("test_component", ComponentState.ACTIVE)
        assert coordinator.get_component_state("test_component") == ComponentState.ACTIVE
        
        assert coordinator.update_component_state("test_component", ComponentState.BUSY)
        assert coordinator.get_component_state("test_component") == ComponentState.BUSY
        
        # Test invalid component
        assert not coordinator.update_component_state("nonexistent", ComponentState.ACTIVE)
        
        # Test getting components by state
        coordinator.register_component("test_component2", "test")
        coordinator.update_component_state("test_component2", ComponentState.ACTIVE)
        
        active_components = coordinator.get_components_by_state(ComponentState.ACTIVE)
        assert "test_component2" in active_components
        
        busy_components = coordinator.get_components_by_state(ComponentState.BUSY)
        assert "test_component" in busy_components
    
    @pytest.mark.asyncio
    async def test_resource_management(self, coordinator):
        """Test resource allocation and management."""
        await coordinator.initialize()
        
        # Register component
        coordinator.register_component("test_component", "test")
        
        # Request resource that's available
        success = await coordinator.request_resource("test_component", "cpu_intensive", 2.0)
        assert success
        
        # Check allocation
        usage = coordinator.get_resource_usage()
        assert "test_component" in usage["allocations"]
        assert usage["allocations"]["test_component"]["cpu_intensive"] == 2.0
        
        # Request resource that's not available (should queue)
        success = await coordinator.request_resource("test_component", "cpu_intensive", 5.0)
        assert not success  # Should be queued
        
        # Release resource
        success = coordinator.release_resource("test_component", "cpu_intensive", 1.0)
        assert success
        
        # Check updated allocation
        usage = coordinator.get_resource_usage()
        assert usage["allocations"]["test_component"]["cpu_intensive"] == 1.0
    
    def test_conflict_detection(self, coordinator):
        """Test conflict detection."""
        # Register conflicting components
        coordinator.register_component(
            "vad_component",
            "vad",
            conflicts_with={"tts_component"}
        )
        coordinator.register_component(
            "tts_component",
            "tts",
            conflicts_with={"vad_component"}
        )
        
        # Set TTS as active
        coordinator.update_component_state("tts_component", ComponentState.ACTIVE)
        
        # Detect conflicts for VAD
        conflicts = coordinator.detect_conflicts("vad_component", "activate")
        assert "tts_component" in conflicts
    
    @pytest.mark.asyncio
    async def test_conflict_resolution(self, coordinator):
        """Test conflict resolution."""
        await coordinator.initialize()
        
        # Register conflicting components
        coordinator.register_component("vad_component", "vad")
        coordinator.register_component("tts_component", "tts")
        
        # Set both as active (conflict situation)
        coordinator.update_component_state("vad_component", ComponentState.ACTIVE)
        coordinator.update_component_state("tts_component", ComponentState.ACTIVE)
        
        # Resolve conflict
        success = await coordinator.resolve_conflict("test_conflict", ["vad_component", "tts_component"])
        assert success
        
        # Check that one component was suspended/waiting
        vad_state = coordinator.get_component_state("vad_component")
        tts_state = coordinator.get_component_state("tts_component")
        
        # TTS should win in duplex conflicts
        assert tts_state == ComponentState.ACTIVE
        assert vad_state in [ComponentState.WAITING, ComponentState.SUSPENDED]
    
    def test_event_subscriptions(self, coordinator):
        """Test event subscription management."""
        coordinator.register_component("test_component", "test")
        
        # Subscribe to events
        event_types = {EventType.SPEECH_DETECTED, EventType.AUDIO_DATA}
        coordinator.subscribe_to_events("test_component", event_types)
        
        # Check subscriptions
        assert "test_component" in coordinator.event_subscriptions[EventType.SPEECH_DETECTED]
        assert "test_component" in coordinator.event_subscriptions[EventType.AUDIO_DATA]
        
        # Unsubscribe from events
        coordinator.unsubscribe_from_events("test_component", {EventType.SPEECH_DETECTED})
        
        # Check updated subscriptions
        assert "test_component" not in coordinator.event_subscriptions[EventType.SPEECH_DETECTED]
        assert "test_component" in coordinator.event_subscriptions[EventType.AUDIO_DATA]
    
    def test_component_unregistration(self, coordinator):
        """Test component unregistration."""
        # Register component and handler
        coordinator.register_component("test_component", "test")
        handler = MockEventHandler("test_component")
        coordinator.register_event_handler(handler)
        
        # Allocate resource
        asyncio.run(coordinator.request_resource("test_component", "cpu_intensive", 1.0))
        
        # Verify registration
        assert "test_component" in coordinator.components
        assert "test_component" in coordinator.event_handlers
        
        # Unregister
        coordinator.unregister_component("test_component")
        
        # Verify cleanup
        assert "test_component" not in coordinator.components
        assert "test_component" not in coordinator.event_handlers
        assert "test_component" not in coordinator.resource_allocations
    
    def test_coordination_statistics(self, coordinator):
        """Test coordination statistics."""
        # Register some components
        coordinator.register_component("vad", "vad")
        coordinator.register_component("asr", "asr")
        coordinator.register_component("tts", "tts")
        
        # Set different states
        coordinator.update_component_state("vad", ComponentState.ACTIVE)
        coordinator.update_component_state("asr", ComponentState.BUSY)
        coordinator.update_component_state("tts", ComponentState.SUSPENDED)
        
        # Get statistics
        stats = coordinator.get_coordination_statistics()
        
        assert stats["registered_components"] == 3
        assert stats["active_components"] == 1
        assert stats["busy_components"] == 1
        assert stats["suspended_components"] == 1
        assert "component_states" in stats
        assert stats["component_states"]["vad"] == "active"
        assert stats["component_states"]["asr"] == "busy"
        assert stats["component_states"]["tts"] == "suspended"
    
    def test_component_coordination_info(self, coordinator):
        """Test getting component coordination info."""
        # Register component
        coordinator.register_component(
            "test_component",
            "test",
            capabilities={"test_capability"},
            dependencies={"dependency1"},
            conflicts_with={"conflict1"}
        )
        
        # Subscribe to events
        coordinator.subscribe_to_events("test_component", {EventType.AUDIO_DATA})
        
        # Get info
        info = coordinator.get_component_coordination_info("test_component")
        
        assert info["component_info"]["component_name"] == "test_component"
        assert info["component_info"]["component_type"] == "test"
        assert "test_capability" in info["component_info"]["capabilities"]
        assert "dependency1" in info["component_info"]["dependencies"]
        assert "conflict1" in info["component_info"]["conflicts_with"]
        assert "audio_data" in info["subscribed_events"]
        
        # Test nonexistent component
        info = coordinator.get_component_coordination_info("nonexistent")
        assert "error" in info


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestComponentCoordinator().test_coordinator_lifecycle(ComponentCoordinator(Mock())))
