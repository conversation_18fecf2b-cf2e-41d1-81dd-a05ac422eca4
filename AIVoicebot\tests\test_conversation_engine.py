"""
Tests for conversation engine.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.conversation.conversation_engine import (
    ConversationEngine, ConversationEngineConfig, ConversationSession,
    ConversationTurn, ConversationState, ConversationFlow, IntentType,
    create_conversation_engine, create_banking_conversation_config
)
from src.core.interfaces import ScriptResponse


class TestConversationEngine:
    """Test conversation engine functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def engine_config(self):
        """Create test engine configuration."""
        return ConversationEngineConfig(
            max_session_duration_minutes=10,
            max_turns_per_session=20,
            idle_timeout_seconds=60,
            enable_intent_classification=True
        )
    
    @pytest.fixture
    def mock_components(self):
        """Create mock AI components."""
        # Mock script manager
        script_manager = Mock()
        script_response = ScriptResponse(
            response_id="test_script",
            text="这是脚本回复",
            confidence=0.8,
            source="script"
        )
        script_manager.find_response = AsyncMock(return_value=script_response)
        
        # Mock LLM client
        llm_client = Mock()
        llm_response = Mock()
        llm_response.is_success = True
        llm_response.text = "这是LLM回复"
        llm_client.generate_simple_response = AsyncMock(return_value=llm_response)
        
        # Mock prompt manager
        prompt_manager = Mock()
        prompt_manager.generate_prompt = Mock(return_value="测试提示")
        
        # Mock response processor
        response_processor = Mock()
        processed_response = Mock()
        processed_response.text = "处理后的回复"
        processed_response.confidence = 0.9
        response_processor.process_llm_response = AsyncMock(return_value=processed_response)
        
        # Mock voice manager
        voice_manager = Mock()
        tts_result = Mock()
        tts_result.audio_data = b"mock_audio"
        tts_result.duration_ms = 1000
        tts_result.voice_used = "zh-CN-XiaoxiaoNeural"
        voice_manager.generate_speech = AsyncMock(return_value=tts_result)
        
        return {
            "script_manager": script_manager,
            "llm_client": llm_client,
            "prompt_manager": prompt_manager,
            "response_processor": response_processor,
            "voice_manager": voice_manager
        }
    
    def test_engine_creation(self, engine_config, mock_components, mock_config_manager):
        """Test conversation engine creation."""
        engine = ConversationEngine(
            config=engine_config,
            script_manager=mock_components["script_manager"],
            llm_client=mock_components["llm_client"],
            config_manager=mock_config_manager
        )
        
        assert engine.config == engine_config
        assert engine.script_manager == mock_components["script_manager"]
        assert engine.llm_client == mock_components["llm_client"]
        assert len(engine.active_sessions) == 0
    
    @pytest.mark.asyncio
    async def test_engine_lifecycle(self, engine_config, mock_components, mock_config_manager):
        """Test engine lifecycle."""
        engine = ConversationEngine(
            config=engine_config,
            config_manager=mock_config_manager
        )
        
        await engine.initialize()
        await engine.start()
        
        assert engine.is_initialized
        assert engine.is_running
        
        await engine.stop()
        await engine.cleanup()
    
    @pytest.mark.asyncio
    async def test_session_management(self, engine_config, mock_config_manager):
        """Test session management."""
        engine = ConversationEngine(
            config=engine_config,
            config_manager=mock_config_manager
        )
        
        await engine.initialize()
        await engine.start()
        
        try:
            # Start session
            session_id = await engine.start_session(
                customer_id="test_customer",
                customer_info={"name": "张先生", "level": "VIP"}
            )
            
            assert session_id is not None
            assert len(engine.active_sessions) == 1
            
            # Get session
            session = engine.get_session(session_id)
            assert session is not None
            assert session.customer_id == "test_customer"
            assert session.customer_info["name"] == "张先生"
            assert session.current_state == ConversationState.IDLE
            
            # End session
            success = await engine.end_session(session_id, "test_complete")
            assert success
            assert len(engine.active_sessions) == 0
            
        finally:
            await engine.stop()
            await engine.cleanup()
    
    def test_intent_classification(self, engine_config, mock_config_manager):
        """Test intent classification."""
        engine = ConversationEngine(
            config=engine_config,
            config_manager=mock_config_manager
        )
        
        session = ConversationSession(session_id="test")
        
        # Test different intents
        test_cases = [
            ("你好", IntentType.GREETING),
            ("我想申请贷款", IntentType.REQUEST),
            ("利率是多少？", IntentType.INQUIRY),
            ("我要投诉", IntentType.COMPLAINT),
            ("是的，确认", IntentType.CONFIRMATION),
            ("再见", IntentType.GOODBYE),
            ("随机文本", IntentType.UNKNOWN)
        ]
        
        for text, expected_intent in test_cases:
            result = asyncio.run(engine._classify_intent(text, session))
            assert result == expected_intent
    
    @pytest.mark.asyncio
    async def test_user_input_processing(self, engine_config, mock_components, mock_config_manager):
        """Test user input processing."""
        engine = ConversationEngine(
            config=engine_config,
            script_manager=mock_components["script_manager"],
            llm_client=mock_components["llm_client"],
            prompt_manager=mock_components["prompt_manager"],
            response_processor=mock_components["response_processor"],
            voice_manager=mock_components["voice_manager"],
            config_manager=mock_config_manager
        )
        
        await engine.initialize()
        await engine.start()
        
        try:
            # Start session
            session_id = await engine.start_session()
            
            # Process user input
            result = await engine.process_user_input(
                session_id=session_id,
                user_input="你好，我想了解贷款产品"
            )
            
            # Verify result
            assert result["session_id"] == session_id
            assert "turn_id" in result
            assert "response_text" in result
            assert "confidence" in result
            assert "intent" in result
            assert result["intent"] == IntentType.GREETING.value
            
            # Verify session state
            session = engine.get_session(session_id)
            assert len(session.turns) == 1
            assert session.current_state == ConversationState.WAITING_FOR_INPUT
            
            # Verify turn details
            turn = session.turns[0]
            assert turn.user_input == "你好，我想了解贷款产品"
            assert turn.user_intent == IntentType.GREETING
            assert turn.final_response != ""
            
        finally:
            await engine.stop()
            await engine.cleanup()
    
    @pytest.mark.asyncio
    async def test_conversation_flow(self, engine_config, mock_components, mock_config_manager):
        """Test conversation flow management."""
        engine = ConversationEngine(
            config=engine_config,
            script_manager=mock_components["script_manager"],
            config_manager=mock_config_manager
        )
        
        await engine.initialize()
        await engine.start()
        
        try:
            session_id = await engine.start_session()
            session = engine.get_session(session_id)
            
            # Initial flow should be greeting
            assert session.current_flow == ConversationFlow.GREETING
            
            # Process greeting
            await engine.process_user_input(session_id, "你好")
            
            # Flow should transition after greeting
            # (Exact transition depends on implementation logic)
            assert session.current_flow in [
                ConversationFlow.GREETING,
                ConversationFlow.INFORMATION_GATHERING
            ]
            
            # Process request
            await engine.process_user_input(session_id, "我想申请贷款")
            
            # Should transition to service delivery
            assert session.current_flow in [
                ConversationFlow.SERVICE_DELIVERY,
                ConversationFlow.INFORMATION_GATHERING
            ]
            
        finally:
            await engine.stop()
            await engine.cleanup()
    
    @pytest.mark.asyncio
    async def test_script_response_generation(self, engine_config, mock_components, mock_config_manager):
        """Test script-based response generation."""
        engine = ConversationEngine(
            config=engine_config,
            script_manager=mock_components["script_manager"],
            config_manager=mock_config_manager
        )
        
        session = ConversationSession(session_id="test")
        turn = ConversationTurn(turn_id="turn1", user_input="测试输入")
        
        # Test script response
        result = await engine._try_script_response(turn, session)
        
        assert result is not None
        assert result["response"] == "这是脚本回复"
        assert result["confidence"] == 0.8
        
        # Verify script manager was called
        mock_components["script_manager"].find_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_llm_response_generation(self, engine_config, mock_components, mock_config_manager):
        """Test LLM-based response generation."""
        engine = ConversationEngine(
            config=engine_config,
            llm_client=mock_components["llm_client"],
            prompt_manager=mock_components["prompt_manager"],
            response_processor=mock_components["response_processor"],
            config_manager=mock_config_manager
        )
        
        session = ConversationSession(session_id="test")
        turn = ConversationTurn(turn_id="turn1", user_input="测试输入")
        
        # Test LLM response
        result = await engine._try_llm_response(turn, session)
        
        assert result is not None
        assert result["response"] == "处理后的回复"
        assert result["confidence"] == 0.9
        
        # Verify components were called
        mock_components["prompt_manager"].generate_prompt.assert_called_once()
        mock_components["llm_client"].generate_simple_response.assert_called_once()
        mock_components["response_processor"].process_llm_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_audio_generation(self, engine_config, mock_components, mock_config_manager):
        """Test audio response generation."""
        engine = ConversationEngine(
            config=engine_config,
            voice_manager=mock_components["voice_manager"],
            config_manager=mock_config_manager
        )
        
        session = ConversationSession(session_id="test")
        turn = ConversationTurn(
            turn_id="turn1",
            user_input="测试输入",
            final_response="测试回复",
            user_intent=IntentType.GREETING
        )
        
        # Test audio generation
        result = await engine._generate_audio_response(turn, session)
        
        assert result["audio_data"] == b"mock_audio"
        assert result["duration_ms"] == 1000
        assert result["voice_used"] == "zh-CN-XiaoxiaoNeural"
        
        # Verify voice manager was called
        mock_components["voice_manager"].generate_speech.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_session_transfer(self, engine_config, mock_config_manager):
        """Test session transfer functionality."""
        engine = ConversationEngine(
            config=engine_config,
            config_manager=mock_config_manager
        )
        
        await engine.initialize()
        await engine.start()
        
        try:
            # Start session
            session_id = await engine.start_session()
            
            # Transfer session
            success = await engine.transfer_session(
                session_id=session_id,
                target_agent="human_agent_001",
                reason="complex_query"
            )
            
            assert success
            
            # Verify session state
            session = engine.get_session(session_id)
            assert session.current_state == ConversationState.TRANSFERRING
            assert session.session_context["transfer_target"] == "human_agent_001"
            assert session.session_context["transfer_reason"] == "complex_query"
            
        finally:
            await engine.stop()
            await engine.cleanup()
    
    def test_statistics_tracking(self, engine_config, mock_config_manager):
        """Test statistics tracking."""
        engine = ConversationEngine(
            config=engine_config,
            config_manager=mock_config_manager
        )
        
        # Initial stats
        stats = engine.get_conversation_statistics()
        assert stats["total_sessions"] == 0
        assert stats["total_turns"] == 0
        
        # Simulate some activity
        engine._total_sessions = 5
        engine._total_turns = 25
        
        # Check updated stats
        stats = engine.get_conversation_statistics()
        assert stats["total_sessions"] == 5
        assert stats["total_turns"] == 25
        assert stats["average_turns_per_session"] == 5.0
    
    @pytest.mark.asyncio
    async def test_conversation_simulation(self, engine_config, mock_components, mock_config_manager):
        """Test conversation simulation."""
        engine = ConversationEngine(
            config=engine_config,
            script_manager=mock_components["script_manager"],
            config_manager=mock_config_manager
        )
        
        await engine.initialize()
        await engine.start()
        
        try:
            # Simulate conversation
            test_inputs = [
                "你好",
                "我想了解贷款产品",
                "利率是多少？",
                "谢谢，再见"
            ]
            
            results = await engine.simulate_conversation(
                test_inputs=test_inputs,
                customer_info={"name": "测试客户"}
            )
            
            # Verify results
            assert "session_id" in results
            assert len(results["turns"]) == len(test_inputs)
            assert "total_processing_time" in results
            assert "average_confidence" in results
            assert "session_stats" in results
            
            # Verify each turn
            for i, turn in enumerate(results["turns"]):
                assert turn["input"] == test_inputs[i]
                assert "response" in turn
                assert "confidence" in turn
                assert "intent" in turn
                
        finally:
            await engine.stop()
            await engine.cleanup()


class TestUtilityFunctions:
    """Test utility functions."""
    
    @pytest.mark.asyncio
    async def test_create_conversation_engine(self):
        """Test conversation engine factory function."""
        # Create mock components
        script_manager = Mock()
        llm_client = Mock()
        
        engine = await create_conversation_engine(
            script_manager=script_manager,
            llm_client=llm_client,
            max_session_duration_minutes=20,
            config_manager=Mock()
        )
        
        assert engine.is_initialized
        assert engine.is_running
        assert engine.script_manager == script_manager
        assert engine.llm_client == llm_client
        assert engine.config.max_session_duration_minutes == 20
        
        await engine.stop()
        await engine.cleanup()
    
    def test_banking_conversation_config(self):
        """Test banking conversation configuration."""
        config = create_banking_conversation_config()
        
        assert config.max_session_duration_minutes == 45
        assert config.prefer_scripts_over_llm == True
        assert config.script_confidence_threshold == 0.7
        assert config.context_window_turns == 7
        assert config.min_response_confidence == 0.7
        assert config.preserve_customer_info == True


class TestConversationDataStructures:
    """Test conversation data structures."""
    
    def test_conversation_turn(self):
        """Test ConversationTurn data structure."""
        turn = ConversationTurn(
            turn_id="test_turn",
            user_input="测试输入",
            user_intent=IntentType.INQUIRY
        )
        
        assert turn.turn_id == "test_turn"
        assert turn.user_input == "测试输入"
        assert turn.user_intent == IntentType.INQUIRY
        assert turn.confidence_score == 0.0
        
        # Test to_dict conversion
        turn_dict = turn.to_dict()
        assert turn_dict["turn_id"] == "test_turn"
        assert turn_dict["user_input"] == "测试输入"
        assert turn_dict["user_intent"] == "inquiry"
    
    def test_conversation_session(self):
        """Test ConversationSession data structure."""
        session = ConversationSession(
            session_id="test_session",
            customer_id="customer_123"
        )
        
        assert session.session_id == "test_session"
        assert session.customer_id == "customer_123"
        assert session.current_state == ConversationState.IDLE
        assert session.current_flow == ConversationFlow.GREETING
        assert session.turn_count == 0
        
        # Add a turn
        turn = ConversationTurn(turn_id="turn1", user_input="test")
        session.turns.append(turn)
        
        assert session.turn_count == 1
        
        # Test recent turns
        recent = session.get_recent_turns(5)
        assert len(recent) == 1
        assert recent[0] == turn


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestUtilityFunctions().test_create_conversation_engine())