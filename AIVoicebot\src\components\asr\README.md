# Automatic Speech Recognition (ASR) Components

This module provides automatic speech recognition capabilities using SenseVoiceSmall model for the AI Voice Customer Service system, including real-time transcription, processing pipeline, and result buffering.

## Components

### SenseVoiceRecognizer
- **File**: `sensevoice_recognizer.py`
- **Purpose**: SenseVoiceSmall model wrapper for speech recognition
- **Features**:
  - High-quality Chinese speech recognition
  - Multi-language support with auto-detection
  - Confidence scoring and quality control
  - Real-time and batch processing modes
  - GPU/CPU inference support

### ASRProcessor
- **File**: `asr_processor.py`
- **Purpose**: ASR processing pipeline for workflow management
- **Features**:
  - Asynchronous processing pipeline
  - Quality filtering and retry logic
  - Result aggregation and statistics
  - Error handling and recovery
  - Performance monitoring

### TranscriptionBuffer
- **File**: `transcription_buffer.py`
- **Purpose**: Buffer for managing transcription results
- **Features**:
  - Partial and final result management
  - Result merging and aggregation
  - Timeout handling and cleanup
  - Event-driven notifications
  - Statistics and monitoring

## Key Features

### Speech Recognition
- **Model**: SenseVoiceSmall (PyTorch)
- **Languages**: Chinese (primary), English, auto-detection
- **Sample Rate**: 16kHz (required)
- **Quality**: High accuracy with confidence scoring
- **Performance**: Real-time processing capability

### Processing Pipeline
- **Asynchronous**: Non-blocking processing
- **Quality Control**: Confidence-based filtering
- **Retry Logic**: Automatic retry for failed recognition
- **Statistics**: Comprehensive performance tracking
- **Error Handling**: Robust error recovery

### Result Management
- **Buffering**: Intelligent result buffering
- **Merging**: Automatic partial result merging
- **Timeout**: Configurable result timeouts
- **Events**: Real-time result notifications
- **Quality**: Confidence-weighted processing##
 Usage Examples

### Basic Speech Recognition

```python
from src.components.asr import SenseVoiceRecognizer, SenseVoiceConfig

# Create configuration
config = SenseVoiceConfig(
    model_path="models/SenseVoiceSmall/model.pt",
    language="zh",
    confidence_threshold=0.3,
    use_gpu=True
)

# Create and initialize recognizer
recognizer = SenseVoiceRecognizer(config, config_manager)
await recognizer.initialize()
await recognizer.start()

# Transcribe audio
result = await recognizer.transcribe(audio_chunk)
print(f"Transcription: {result.text}")
print(f"Confidence: {result.confidence:.3f}")
```

### ASR Processing Pipeline

```python
from src.components.asr import ASRProcessor, ASRProcessorConfig

# Create processor configuration
processor_config = ASRProcessorConfig(
    chunk_duration_ms=1000,
    min_confidence_threshold=0.3,
    enable_confidence_filtering=True,
    max_retry_attempts=2
)

# Create processor
processor = ASRProcessor(processor_config, recognizer, config_manager)
await processor.initialize()
await processor.start()

# Add result handler
async def handle_result(result):
    print(f"Recognized: {result.transcription.text}")

processor.add_result_handler(handle_result)

# Process audio chunks
await processor.process_audio_chunk(audio_chunk)
```

### Transcription Buffer

```python
from src.components.asr import TranscriptionBuffer, BufferConfig

# Create buffer
buffer_config = BufferConfig(
    max_size=100,
    timeout_ms=5000,
    enable_result_merging=True
)

buffer = TranscriptionBuffer(buffer_config)
await buffer.initialize()
await buffer.start()

# Add results
await buffer.add_result(transcription_result)

# Get final result
final_result = await buffer.get_final_result(timeout_ms=2000)
```

## Configuration

### SenseVoice Configuration

| Parameter | Default | Description |
|-----------|---------|-------------|
| `model_path` | "models/SenseVoiceSmall/model.pt" | Path to model file |
| `language` | "zh" | Primary language (zh/en) |
| `confidence_threshold` | 0.3 | Minimum confidence threshold |
| `use_gpu` | true | Use GPU if available |
| `sample_rate` | 16000 | Required sample rate |
| `chunk_duration_ms` | 1000 | Processing chunk duration |
| `beam_size` | 5 | Beam search size |
| `enable_language_detection` | true | Auto-detect language |
| `return_timestamps` | true | Include word timestamps |

### ASR Processor Configuration

| Parameter | Default | Description |
|-----------|---------|-------------|
| `chunk_duration_ms` | 1000 | Audio chunk duration |
| `min_confidence_threshold` | 0.3 | Minimum confidence |
| `enable_confidence_filtering` | true | Filter low confidence |
| `enable_duplicate_filtering` | true | Filter duplicates |
| `max_retry_attempts` | 3 | Maximum retry attempts |
| `retry_delay_ms` | 100 | Delay between retries |
| `enable_parallel_processing` | false | Parallel processing |

### Buffer Configuration

| Parameter | Default | Description |
|-----------|---------|-------------|
| `max_size` | 100 | Maximum buffer size |
| `timeout_ms` | 5000 | Result timeout |
| `enable_result_merging` | true | Enable result merging |
| `merge_threshold_ms` | 500 | Merge time threshold |
| `min_confidence_for_final` | 0.5 | Min confidence for final |

## Performance Monitoring

### Recognition Statistics

```python
# Get recognition statistics
stats = recognizer.get_recognition_stats()

print(f"Total recognitions: {stats['total_recognitions']}")
print(f"Average processing time: {stats['average_processing_time_s']:.3f}s")
print(f"Real-time factor: {stats['real_time_factor']:.1f}x")
print(f"Error rate: {stats['error_rate']:.2%}")
```

### Processing Statistics

```python
# Get processing statistics
stats = processor.get_processing_stats()

print(f"Total processed: {stats['total_processed']}")
print(f"Success rate: {stats['success_rate']:.2%}")
print(f"Average processing time: {stats['average_processing_time_ms']:.1f}ms")
print(f"Queue size: {stats['queue_size']}")
```

### Buffer Statistics

```python
# Get buffer statistics
stats = buffer.get_buffer_stats()

print(f"Total added: {stats['total_added']}")
print(f"Merge rate: {stats['merge_rate']:.2%}")
print(f"Finalization rate: {stats['finalization_rate']:.2%}")
```

## Integration Examples

### With VAD System

```python
# Complete VAD -> ASR pipeline
async def vad_asr_pipeline(audio_stream, vad_segmenter, asr_processor):
    async for speech_segment in vad_segmenter.segment_audio_stream(audio_stream):
        # Process speech segment with ASR
        results = await asr_processor.process_speech_segments([speech_segment])
        
        for result in results:
            if result.confidence_score > 0.5:
                print(f"Transcription: {result.transcription.text}")
```

### With Audio Streaming

```python
# Real-time audio transcription
async def real_time_transcription(audio_manager, asr_processor):
    async for audio_chunk in audio_manager.get_stream_generator("voice_input"):
        await asr_processor.process_audio_chunk(audio_chunk)
        
        # Get results
        final_result = await asr_processor.get_final_transcription(timeout_ms=1000)
        if final_result:
            print(f"Final: {final_result.text}")
```

## Model Requirements

### SenseVoiceSmall Model

1. **Model File**: Place model.pt in `models/SenseVoiceSmall/`
2. **Dependencies**: PyTorch, torchaudio
3. **Hardware**: GPU recommended for real-time processing
4. **Memory**: ~2GB GPU memory or 4GB RAM for CPU

### Installation

```bash
# Install PyTorch dependencies
pip install torch torchaudio

# For GPU support (optional)
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## Error Handling

### Common Issues

1. **Model Loading Errors**
   - Verify model file exists and is valid
   - Check PyTorch version compatibility
   - Ensure sufficient memory

2. **Audio Format Issues**
   - SenseVoice requires 16kHz sample rate
   - Audio should be mono (single channel)
   - Input should be 16-bit PCM format

3. **Performance Issues**
   - Use GPU for better performance
   - Adjust batch size and chunk duration
   - Monitor memory usage

### Error Recovery

```python
try:
    result = await recognizer.transcribe(audio_chunk)
except AudioProcessingError as e:
    logger.error(f"ASR processing failed: {e}")
    # Implement fallback or retry logic
```

## Testing

### Unit Tests

```bash
# Run ASR tests
python -m pytest tests/test_asr.py -v
```

### Example Usage

```bash
# Run ASR examples
python examples/asr_example.py
```

This ASR system provides high-quality speech recognition capabilities essential for voice interaction systems, with robust error handling and performance monitoring.