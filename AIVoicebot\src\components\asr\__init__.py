"""
Automatic Speech Recognition components for AI Voice Customer Service System

This package provides ASR functionality including:
- ASR processing pipeline and workflow management
- Transcription result buffering and handling
- Error handling and retry logic for speech recognition
"""

from .asr_processor import (
    ASRProcessor,
    TranscriptionBuffer,
    TranscriptionResult,
    ASRProcessorConfig,
    TranscriptionStatus,
    ASRError,
    create_asr_processor,
    process_audio_batch
)

__all__ = [
    "ASRProcessor",
    "TranscriptionBuffer",
    "TranscriptionResult",
    "ASRProcessorConfig",
    "TranscriptionStatus",
    "ASRError",
    "create_asr_processor",
    "process_audio_batch"
]