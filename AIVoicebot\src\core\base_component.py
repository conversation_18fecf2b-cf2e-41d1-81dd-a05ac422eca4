"""
Base component class providing common functionality for all system components.

This module provides a base class that all components can inherit from to get
common functionality like logging, configuration access, and lifecycle management.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

from .interfaces import IConfigManager, ILogger


class BaseComponent(ABC):
    """Base class for all system components."""
    
    def __init__(self, 
                 name: str,
                 config_manager: IConfigManager,
                 logger: Optional[ILogger] = None):
        """
        Initialize base component.
        
        Args:
            name: Component name for logging and identification
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        self.name = name
        self.config_manager = config_manager
        self.logger = logger
        self._is_initialized = False
        self._is_running = False
        
        # Set up Python logging
        self._log = logging.getLogger(f"aivoice.{name}")
        
    async def initialize(self) -> None:
        """Initialize the component."""
        if self._is_initialized:
            return
            
        self._log.info(f"Initializing component: {self.name}")
        
        try:
            await self._initialize_impl()
            self._is_initialized = True
            self._log.info(f"Component initialized successfully: {self.name}")
        except Exception as e:
            self._log.error(f"Failed to initialize component {self.name}: {e}")
            raise
    
    async def start(self) -> None:
        """Start the component."""
        if not self._is_initialized:
            await self.initialize()
            
        if self._is_running:
            return
            
        self._log.info(f"Starting component: {self.name}")
        
        try:
            await self._start_impl()
            self._is_running = True
            self._log.info(f"Component started successfully: {self.name}")
        except Exception as e:
            self._log.error(f"Failed to start component {self.name}: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the component."""
        if not self._is_running:
            return
            
        self._log.info(f"Stopping component: {self.name}")
        
        try:
            await self._stop_impl()
            self._is_running = False
            self._log.info(f"Component stopped successfully: {self.name}")
        except Exception as e:
            self._log.error(f"Failed to stop component {self.name}: {e}")
            raise
    
    async def cleanup(self) -> None:
        """Cleanup component resources."""
        if self._is_running:
            await self.stop()
            
        self._log.info(f"Cleaning up component: {self.name}")
        
        try:
            await self._cleanup_impl()
            self._is_initialized = False
            self._log.info(f"Component cleaned up successfully: {self.name}")
        except Exception as e:
            self._log.error(f"Failed to cleanup component {self.name}: {e}")
            raise
    
    @property
    def is_initialized(self) -> bool:
        """Check if component is initialized."""
        return self._is_initialized
    
    @property
    def is_running(self) -> bool:
        """Check if component is running."""
        return self._is_running
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value with component name prefix."""
        component_key = f"{self.name}.{key}"
        return self.config_manager.get_config(component_key, default)
    
    async def log_info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self._log.info(message)
        if self.logger:
            await self.logger.log_info(self.name, message, **kwargs)
    
    async def log_error(self, message: str, error: Optional[Exception] = None, **kwargs) -> None:
        """Log error message."""
        self._log.error(message)
        if self.logger and error:
            await self.logger.log_error(error, {"component": self.name, "message": message, **kwargs})
    
    async def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self._log.warning(message)
        if self.logger:
            await self.logger.log_warning(self.name, message, **kwargs)
    
    # Abstract methods that subclasses must implement
    
    @abstractmethod
    async def _initialize_impl(self) -> None:
        """Component-specific initialization logic."""
        pass
    
    @abstractmethod
    async def _start_impl(self) -> None:
        """Component-specific start logic."""
        pass
    
    @abstractmethod
    async def _stop_impl(self) -> None:
        """Component-specific stop logic."""
        pass
    
    @abstractmethod
    async def _cleanup_impl(self) -> None:
        """Component-specific cleanup logic."""
        pass


class ComponentManager:
    """Manages lifecycle of multiple components."""
    
    def __init__(self):
        self.components: Dict[str, BaseComponent] = {}
        self._log = logging.getLogger("aivoice.component_manager")
    
    def register_component(self, component: BaseComponent) -> None:
        """Register a component for management."""
        self.components[component.name] = component
        self._log.info(f"Registered component: {component.name}")
    
    async def initialize_all(self) -> None:
        """Initialize all registered components."""
        self._log.info("Initializing all components...")
        
        for name, component in self.components.items():
            try:
                await component.initialize()
            except Exception as e:
                self._log.error(f"Failed to initialize component {name}: {e}")
                raise
        
        self._log.info("All components initialized successfully")
    
    async def start_all(self) -> None:
        """Start all registered components."""
        self._log.info("Starting all components...")
        
        for name, component in self.components.items():
            try:
                await component.start()
            except Exception as e:
                self._log.error(f"Failed to start component {name}: {e}")
                # Stop any components that were already started
                await self.stop_all()
                raise
        
        self._log.info("All components started successfully")
    
    async def stop_all(self) -> None:
        """Stop all registered components."""
        self._log.info("Stopping all components...")
        
        # Stop components in reverse order
        for name, component in reversed(list(self.components.items())):
            try:
                await component.stop()
            except Exception as e:
                self._log.error(f"Failed to stop component {name}: {e}")
                # Continue stopping other components even if one fails
        
        self._log.info("All components stopped")
    
    async def cleanup_all(self) -> None:
        """Cleanup all registered components."""
        self._log.info("Cleaning up all components...")
        
        # Cleanup components in reverse order
        for name, component in reversed(list(self.components.items())):
            try:
                await component.cleanup()
            except Exception as e:
                self._log.error(f"Failed to cleanup component {name}: {e}")
                # Continue cleaning up other components even if one fails
        
        self._log.info("All components cleaned up")
    
    def get_component(self, name: str) -> Optional[BaseComponent]:
        """Get component by name."""
        return self.components.get(name)
    
    def list_components(self) -> Dict[str, bool]:
        """List all components and their running status."""
        return {name: component.is_running for name, component in self.components.items()}