# Default configuration for AI Voice Customer Service System
# This file contains the base configuration that can be overridden by
# environment-specific files or environment variables

# Logging configuration
logging:
  level: INFO
  dir: logs
  enable_console: true
  enable_file: true
  enable_json: false
  max_file_size: 10485760  # 10MB
  backup_count: 5

# Audio processing configuration
audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  format: pcm_16khz_mono
  buffer_size: 4096

# AI Model configurations
models:
  # SenseVoiceSmall speech recognition model
  sensevoice_path: models/SenseVoiceSmall/model.pt
  
  # SileroVAD voice activity detection model
  silero_vad_path: models/snakers4_silero-vad
  
  # Model loading configuration
  device: auto  # auto, cpu, cuda
  batch_size: 1

# Qwen LLM configuration (通义千问)
qwen:
  api_key: "sk-457eeca4aa744530843bf697c2c7d83a"  # Your Qwen API key
  api_base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"  # Compatible OpenAI API
  model: "qwen-turbo"
  max_tokens: 2000
  temperature: 0.7
  timeout: 30
  max_retries: 3

# EdgeTTS configuration
edge_tts:
  voice: zh-CN-XiaoxiaoNeural
  rate: "+0%"
  pitch: "+0Hz"
  volume: "+0%"
  timeout: 30

# Telephony system configuration
telephony:
  provider: custom
  config_path: config/telephony.yaml
  max_concurrent_calls: 10
  call_timeout: 300  # 5 minutes
  esp32:
    enabled: true
    host: "0.0.0.0"
    port: 8765
    max_connections: 50
  
# Conversation management
conversation:
  # Excel files containing conversation scripts
  script_files:
    - docs/言犀复贷话术.xlsx
    - docs/零犀复贷AI话术调优240914.xlsx
  
  default_script_id: default
  max_conversation_turns: 50
  response_timeout: 30
  
  # Script reloading
  auto_reload_scripts: true
  script_check_interval: 60  # seconds

# Performance and optimization settings
performance:
  # Audio processing
  max_audio_buffer_size: 1048576  # 1MB
  audio_processing_threads: 2
  
  # Voice Activity Detection
  vad_threshold: 0.5
  vad_window_size: 512
  
  # Speech recognition
  speech_timeout: 3.0
  silence_timeout: 1.0
  min_speech_duration: 0.3
  
  # Response generation
  llm_timeout: 15.0
  tts_timeout: 10.0
  
  # Memory management
  max_session_history: 100
  cleanup_interval: 300  # 5 minutes

# Monitoring and health checks
monitoring:
  enable_metrics: true
  metrics_port: 8080
  health_check_interval: 30
  
  # Performance thresholds
  max_response_time: 5.0
  max_error_rate: 0.05
  
# Development settings
development:
  debug_mode: false
  mock_telephony: false
  mock_models: false
  save_audio_files: false
  audio_debug_dir: debug/audio

# Security settings
security:
  # API rate limiting
  rate_limit_calls_per_minute: 60
  rate_limit_calls_per_hour: 1000
  
  # Data retention
  conversation_retention_days: 30
  audio_retention_days: 7
  log_retention_days: 90