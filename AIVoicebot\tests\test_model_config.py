"""
Tests for Model Configuration Manager

This module tests the model configuration system including:
- Model path validation and management
- Configuration loading for different AI models
- Model availability checking
- Configuration updates and validation
"""

import pytest
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.config.model_config import (
    ModelConfigManager,
    ModelPath,
    SenseVoiceConfig,
    SileroVADConfig,
    EdgeTTSConfig,
    QwenConfig,
    ModelType,
    ModelFormat,
    get_model_config,
    initialize_model_config,
    setup_model_environment
)


class TestModelPath:
    """Test cases for ModelPath class"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_model_file = Path(self.temp_dir) / "test_model.pt"
        self.test_config_file = Path(self.temp_dir) / "config.json"
        
        # Create test files
        self.test_model_file.touch()
        self.test_config_file.touch()
    
    def teardown_method(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_model_path_creation(self):
        """Test ModelPath creation and path resolution"""
        model_path = ModelPath(
            model_file=str(self.test_model_file),
            config_file=str(self.test_config_file)
        )
        
        assert Path(model_path.model_file).exists()
        assert Path(model_path.config_file).exists()
        assert model_path.exists()
    
    def test_model_path_missing_files(self):
        """Test ModelPath with missing files"""
        missing_file = Path(self.temp_dir) / "missing.pt"
        
        model_path = ModelPath(
            model_file=str(missing_file),
            config_file=str(self.test_config_file)
        )
        
        assert not model_path.exists()
    
    def test_model_path_size_calculation(self):
        """Test model file size calculation"""
        # Write some data to test file
        self.test_model_file.write_bytes(b"0" * 1024)  # 1KB
        
        model_path = ModelPath(model_file=str(self.test_model_file))
        size_mb = model_path.get_size_mb()
        
        assert size_mb > 0
        assert size_mb < 1  # Should be less than 1MB


class TestModelConfigurations:
    """Test cases for model configuration classes"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_model_file = Path(self.temp_dir) / "model.pt"
        self.test_model_file.touch()
    
    def teardown_method(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_sense_voice_config(self):
        """Test SenseVoiceConfig creation and validation"""
        model_path = ModelPath(model_file=str(self.test_model_file))
        
        config = SenseVoiceConfig(
            model_path=model_path,
            sample_rate=16000,
            beam_size=1,
            use_gpu=True
        )
        
        assert config.model_type == "SenseVoiceSmall"
        assert config.sample_rate == 16000
        assert config.beam_size == 1
        assert config.use_gpu is True
    
    def test_sense_voice_config_invalid_sample_rate(self):
        """Test SenseVoiceConfig with invalid sample rate"""
        model_path = ModelPath(model_file=str(self.test_model_file))
        
        with pytest.raises(ValueError):
            SenseVoiceConfig(
                model_path=model_path,
                sample_rate=999  # Invalid sample rate
            )
    
    def test_silero_vad_config(self):
        """Test SileroVADConfig creation and validation"""
        model_path = ModelPath(model_file=str(self.test_model_file))
        
        config = SileroVADConfig(
            model_path=model_path,
            threshold=0.5,
            min_speech_duration_ms=100
        )
        
        assert config.model_type == "SileroVAD"
        assert config.threshold == 0.5
        assert config.min_speech_duration_ms == 100
    
    def test_silero_vad_config_invalid_threshold(self):
        """Test SileroVADConfig with invalid threshold"""
        model_path = ModelPath(model_file=str(self.test_model_file))
        
        with pytest.raises(ValueError):
            SileroVADConfig(
                model_path=model_path,
                threshold=1.5  # Invalid threshold > 1.0
            )
    
    def test_edge_tts_config(self):
        """Test EdgeTTSConfig creation"""
        config = EdgeTTSConfig(
            voice="zh-CN-XiaoxiaoNeural",
            rate="+10%",
            volume="+5%",
            cache_enabled=True
        )
        
        assert config.model_type == "EdgeTTS"
        assert config.voice == "zh-CN-XiaoxiaoNeural"
        assert config.rate == "+10%"
        assert config.cache_enabled is True
    
    def test_qwen_config(self):
        """Test QwenConfig creation and validation"""
        config = QwenConfig(
            api_key="test_key",
            model_name="qwen-turbo",
            temperature=0.7,
            max_tokens=1000
        )
        
        assert config.model_type == "Qwen-turbo"
        assert config.api_key == "test_key"
        assert config.temperature == 0.7
        assert config.max_tokens == 1000
    
    def test_qwen_config_invalid_temperature(self):
        """Test QwenConfig with invalid temperature"""
        with pytest.raises(ValueError):
            QwenConfig(temperature=3.0)  # Invalid temperature > 2.0
    
    def test_qwen_config_invalid_top_p(self):
        """Test QwenConfig with invalid top_p"""
        with pytest.raises(ValueError):
            QwenConfig(top_p=1.5)  # Invalid top_p > 1.0


class TestModelConfigManager:
    """Test cases for ModelConfigManager class"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.models_dir = Path(self.temp_dir) / "models"
        self.cache_dir = Path(self.temp_dir) / "cache"
        
        # Create test model files
        self.models_dir.mkdir(parents=True, exist_ok=True)
        (self.models_dir / "sensevoice" / "model.pt").parent.mkdir(parents=True, exist_ok=True)
        (self.models_dir / "sensevoice" / "model.pt").touch()
        (self.models_dir / "silero_vad" / "model.pt").parent.mkdir(parents=True, exist_ok=True)
        (self.models_dir / "silero_vad" / "model.pt").touch()
    
    def teardown_method(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_model_config_manager_initialization(self):
        """Test ModelConfigManager initialization"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        assert manager.models_dir == self.models_dir
        assert manager.cache_dir == self.cache_dir
        assert manager.sense_voice is not None
        assert manager.silero_vad is not None
        assert manager.edge_tts is not None
        assert manager.qwen is not None
    
    def test_get_model_config(self):
        """Test getting model configurations"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        sense_voice_config = manager.get_model_config("sensevoice")
        assert isinstance(sense_voice_config, SenseVoiceConfig)
        
        vad_config = manager.get_model_config("silero_vad")
        assert isinstance(vad_config, SileroVADConfig)
        
        tts_config = manager.get_model_config("edge_tts")
        assert isinstance(tts_config, EdgeTTSConfig)
        
        qwen_config = manager.get_model_config("qwen")
        assert isinstance(qwen_config, QwenConfig)
        
        # Test non-existent model
        assert manager.get_model_config("nonexistent") is None
    
    def test_model_availability_checking(self):
        """Test model availability checking"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        # Models with files should be available
        assert manager.is_model_available("sensevoice")
        assert manager.is_model_available("silero_vad")
        
        # API-based models should be available
        assert manager.is_model_available("edge_tts")
        
        # Qwen availability depends on API key
        qwen_available = manager.is_model_available("qwen")
        # Should be False since no API key is set in test
        assert qwen_available is False
    
    def test_get_available_models(self):
        """Test getting list of available models"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        available_models = manager.get_available_models()
        
        # Should include models with files and API-based models
        assert "sensevoice" in available_models
        assert "silero_vad" in available_models
        assert "edge_tts" in available_models
        # qwen might not be available without API key
    
    def test_validate_model_files(self):
        """Test model file validation"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        validation_results = manager.validate_model_files()
        
        assert validation_results["sensevoice"] is True
        assert validation_results["silero_vad"] is True
        assert validation_results["edge_tts"] is True  # API-based
        assert validation_results["qwen"] is True  # API-based
    
    def test_get_total_model_size(self):
        """Test total model size calculation"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        total_size = manager.get_total_model_size()
        assert total_size >= 0  # Should be non-negative
    
    def test_update_model_config(self):
        """Test updating model configuration"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        # Update SenseVoice configuration
        success = manager.update_model_config("sensevoice", beam_size=5, temperature=0.5)
        assert success is True
        
        updated_config = manager.get_model_config("sensevoice")
        assert updated_config.beam_size == 5
        assert updated_config.temperature == 0.5
        
        # Try to update non-existent model
        success = manager.update_model_config("nonexistent", param=123)
        assert success is False
    
    def test_export_model_configs(self):
        """Test exporting model configurations"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        exported_configs = manager.export_model_configs()
        
        assert "sensevoice" in exported_configs
        assert "silero_vad" in exported_configs
        assert "edge_tts" in exported_configs
        assert "qwen" in exported_configs
        
        # Check structure
        assert "model_path" in exported_configs["sensevoice"]
        assert "sample_rate" in exported_configs["sensevoice"]
        assert "threshold" in exported_configs["silero_vad"]
        assert "voice" in exported_configs["edge_tts"]
    
    def test_get_model_status_report(self):
        """Test model status report generation"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        report = manager.get_model_status_report()
        
        assert "Model Configuration Status Report" in report
        assert "SENSEVOICE:" in report
        assert "SILERO_VAD:" in report
        assert "EDGE_TTS:" in report
        assert "QWEN:" in report
        assert "Summary:" in report
    
    def test_create_model_directories(self):
        """Test model directory creation"""
        manager = ModelConfigManager(
            models_dir=str(self.models_dir),
            cache_dir=str(self.cache_dir)
        )
        
        manager.create_model_directories()
        
        # Check that directories were created
        assert (self.models_dir / "sensevoice").exists()
        assert (self.models_dir / "silero_vad").exists()
        assert (self.cache_dir / "tts").exists()
        assert (self.cache_dir / "asr").exists()
        assert (self.cache_dir / "vad").exists()
    
    def test_environment_variable_overrides(self):
        """Test environment variable configuration overrides"""
        with patch.dict(os.environ, {
            'SENSEVOICE_MODEL_PATH': '/custom/path/model.pt',
            'SENSEVOICE_SAMPLE_RATE': '22050',
            'VAD_THRESHOLD': '0.8',
            'QWEN_API_KEY': 'test_api_key',
            'TTS_VOICE': 'zh-CN-YunxiNeural'
        }):
            manager = ModelConfigManager(
                models_dir=str(self.models_dir),
                cache_dir=str(self.cache_dir)
            )
            
            # Check environment variable overrides
            assert manager.sense_voice.model_path.model_file == '/custom/path/model.pt'
            assert manager.sense_voice.sample_rate == 22050
            assert manager.silero_vad.threshold == 0.8
            assert manager.qwen.api_key == 'test_api_key'
            assert manager.edge_tts.voice == 'zh-CN-YunxiNeural'


class TestGlobalModelConfigManager:
    """Test cases for global model configuration manager functions"""
    
    def test_get_model_config_singleton(self):
        """Test global model configuration manager singleton"""
        # Reset global config
        import src.config.model_config as model_config_module
        model_config_module.model_config_manager = None
        
        # Get config instances
        config1 = get_model_config()
        config2 = get_model_config()
        
        # Should be the same instance
        assert config1 is config2
    
    def test_initialize_model_config(self):
        """Test global model configuration manager initialization"""
        # Reset global config
        import src.config.model_config as model_config_module
        model_config_module.model_config_manager = None
        
        # Initialize with custom settings
        config = initialize_model_config(
            models_dir="custom_models",
            cache_dir="custom_cache"
        )
        
        assert str(config.models_dir).endswith("custom_models")
        assert str(config.cache_dir).endswith("custom_cache")
        
        # Get config should return the same instance
        assert get_model_config() is config
    
    def test_setup_model_environment(self):
        """Test model environment setup"""
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Initialize with temp directory
            initialize_model_config(
                models_dir=str(Path(temp_dir) / "models"),
                cache_dir=str(Path(temp_dir) / "cache")
            )
            
            # Setup environment
            setup_model_environment()
            
            # Check that directories were created
            assert (Path(temp_dir) / "models" / "sensevoice").exists()
            assert (Path(temp_dir) / "cache" / "tts").exists()
            
        finally:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    pytest.main([__file__])