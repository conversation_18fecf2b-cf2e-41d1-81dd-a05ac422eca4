"""
Tests for conversation context management system.
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.conversation.conversation_context import (
    ConversationContext, ConversationContextConfig, ContextItem, ContextScope,
    ContextPriority, ConversationIntent, ConversationTopic, create_conversation_context,
    ConversationTurn, ConversationState, ConversationFlow, IntentType
)


class TestConversationContext:
    """Test conversation context functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def context_config(self):
        """Create test context configuration."""
        return ConversationContextConfig(
            max_conversation_history=10,
            max_context_items=50,
            default_context_ttl_minutes=15,
            context_cleanup_interval_minutes=1,
            enable_context_persistence=False
        )
    
    @pytest.fixture
    def sample_turn(self):
        """Create sample conversation turn."""
        return ConversationTurn(
            turn_id="turn_1",
            user_input="我想了解贷款产品",
            user_intent=IntentType.INQUIRY,
            final_response="我们有多种贷款产品可供选择"
        )
    
    def test_context_creation(self, context_config, mock_config_manager):
        """Test conversation context creation."""
        session_id = "test_session_123"
        context = ConversationContext(session_id, context_config, mock_config_manager)
        
        assert context.session_id == session_id
        assert context.config == context_config
        assert context.conversation_state == ConversationState.IDLE
        assert context.conversation_flow == ConversationFlow.GREETING
        assert len(context.context_items) == 0
        assert len(context.conversation_history) == 0
    
    @pytest.mark.asyncio
    async def test_context_lifecycle(self, context_config, mock_config_manager):
        """Test context lifecycle."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        await context.initialize()
        await context.start()
        
        assert context.is_initialized
        assert context.is_running
        
        await context.stop()
        await context.cleanup()
    
    def test_context_item_management(self, context_config, mock_config_manager):
        """Test context item management."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Add context item
        context.add_context_item(
            key="customer_name",
            value="张先生",
            scope=ContextScope.SESSION,
            priority=ContextPriority.HIGH
        )
        
        # Get context item
        name = context.get_context_item("customer_name")
        assert name == "张先生"
        
        # Get non-existent item
        missing = context.get_context_item("non_existent", "default")
        assert missing == "default"
        
        # Remove context item
        success = context.remove_context_item("customer_name")
        assert success
        
        # Verify removal
        name = context.get_context_item("customer_name")
        assert name is None
    
    def test_context_scopes_and_priorities(self, context_config, mock_config_manager):
        """Test context scopes and priorities."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Add items with different scopes and priorities
        context.add_context_item("session_info", "session_data", ContextScope.SESSION, ContextPriority.CRITICAL)
        context.add_context_item("topic_info", "topic_data", ContextScope.TOPIC, ContextPriority.HIGH)
        context.add_context_item("turn_info", "turn_data", ContextScope.TURN, ContextPriority.MEDIUM)
        context.add_context_item("temp_info", "temp_data", ContextScope.IMMEDIATE, ContextPriority.LOW)
        
        # Get by scope
        session_context = context.get_context_by_scope(ContextScope.SESSION)
        assert "session_info" in session_context
        assert session_context["session_info"] == "session_data"
        
        topic_context = context.get_context_by_scope(ContextScope.TOPIC)
        assert "topic_info" in topic_context
        
        # Get by priority
        high_priority = context.get_context_by_priority(ContextPriority.HIGH)
        assert "session_info" in high_priority  # CRITICAL >= HIGH
        assert "topic_info" in high_priority    # HIGH >= HIGH
        assert "turn_info" not in high_priority # MEDIUM < HIGH
    
    def test_conversation_history(self, context_config, mock_config_manager, sample_turn):
        """Test conversation history management."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Add conversation turn
        context.add_conversation_turn(sample_turn)
        
        # Get history
        history = context.get_conversation_history()
        assert len(history) == 1
        assert history[0].turn_id == "turn_1"
        
        # Get recent history
        recent = context.get_conversation_history(count=1)
        assert len(recent) == 1
        
        # Get conversation summary
        summary = context.get_conversation_summary()
        assert summary["session_id"] == "test_session"
        assert summary["total_turns"] == 1
        assert "duration_seconds" in summary
    
    def test_intent_management(self, context_config, mock_config_manager):
        """Test intent management."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Update intent
        context.update_intent(
            intent_type=IntentType.INQUIRY,
            confidence=0.9,
            entities={"loan_type": "personal"},
            sub_intents=["rate_inquiry"]
        )

        # Check current intent
        assert context.current_intent is not None
        assert context.current_intent.intent_type == IntentType.INQUIRY
        assert context.current_intent.confidence == 0.9

        # Check intent history
        history = context.get_intent_history()
        assert len(history) == 1

        # Add more intents
        context.update_intent(IntentType.REQUEST, 0.8)
        context.update_intent(IntentType.INQUIRY, 0.85)

        # Get dominant intent
        dominant = context.get_dominant_intent(window_size=3)
        assert dominant == IntentType.INQUIRY  # Most frequent
    
    def test_topic_management(self, context_config, mock_config_manager):
        """Test topic management."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Start topic
        topic_id = context.start_topic("贷款咨询", {"loan_amount": 100000})
        
        assert topic_id in context.topics
        assert context.current_topic_id == topic_id
        assert len(context.active_topics) == 1
        
        # Get current topic
        current_topic = context.get_current_topic()
        assert current_topic is not None
        assert current_topic.name == "贷款咨询"
        assert current_topic.is_active
        
        # Get active topics
        active_topics = context.get_active_topics()
        assert len(active_topics) == 1
        
        # End topic
        success = context.end_topic(topic_id, "resolved")
        assert success
        assert not context.get_current_topic() or not context.get_current_topic().is_active
    
    def test_state_management(self, context_config, mock_config_manager):
        """Test conversation state management."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Update conversation state
        context.update_conversation_state(ConversationState.LISTENING)
        assert context.conversation_state == ConversationState.LISTENING
        
        # Update conversation flow
        context.update_conversation_flow(ConversationFlow.INFORMATION_GATHERING)
        assert context.conversation_flow == ConversationFlow.INFORMATION_GATHERING
        
        # Update customer info
        customer_info = {"name": "李先生", "phone": "13800138000"}
        context.update_customer_info(customer_info)
        
        assert context.customer_info["name"] == "李先生"
        assert context.get_context_item("customer_name") == "李先生"
    
    def test_context_expiration(self, context_config, mock_config_manager):
        """Test context item expiration."""
        context = ConversationContext("test_session", context_config, mock_config_manager)

        # Create an already expired item by setting expires_at in the past
        from datetime import timedelta
        past_time = datetime.now() - timedelta(minutes=1)

        # Manually create expired context item
        expired_item = ContextItem(
            key="expired_data",
            value="expired",
            scope=ContextScope.TURN,
            priority=ContextPriority.LOW,
            expires_at=past_time
        )

        context.context_items["expired_data"] = expired_item

        # Item should be expired and return None
        assert context.get_context_item("expired_data") is None

        # Verify item was removed from context
        assert "expired_data" not in context.context_items
    
    def test_context_export(self, context_config, mock_config_manager, sample_turn):
        """Test context export functionality."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Add some data
        context.add_context_item("test_key", "test_value")
        context.add_conversation_turn(sample_turn)
        context.update_intent(IntentType.INQUIRY, 0.9)
        context.start_topic("测试主题")
        
        # Export context
        exported = context.export_context()
        
        assert exported["session_id"] == "test_session"
        assert "context_items" in exported
        assert "conversation_history" in exported
        assert "intent_history" in exported
        assert "topics" in exported
        assert len(exported["context_items"]) > 0
        assert len(exported["conversation_history"]) == 1
    
    def test_statistics(self, context_config, mock_config_manager):
        """Test statistics collection."""
        context = ConversationContext("test_session", context_config, mock_config_manager)
        
        # Perform some operations
        context.add_context_item("key1", "value1")
        context.get_context_item("key1")
        context.get_context_item("non_existent")
        
        # Get statistics
        stats = context.get_statistics()
        
        assert stats["session_id"] == "test_session"
        assert stats["context_access_count"] == 2
        assert stats["context_updates"] == 1
        assert stats["total_context_items"] == 1
        assert "session_duration_seconds" in stats
    
    @pytest.mark.asyncio
    async def test_factory_function(self, mock_config_manager):
        """Test factory function."""
        context = await create_conversation_context(
            session_id="factory_test",
            config_manager=mock_config_manager,
            max_conversation_history=15,
            max_context_items=75
        )
        
        assert context.is_initialized
        assert context.is_running
        assert context.session_id == "factory_test"
        assert context.config.max_conversation_history == 15
        assert context.config.max_context_items == 75
        
        await context.stop()
        await context.cleanup()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestConversationContext().test_factory_function(Mock()))
