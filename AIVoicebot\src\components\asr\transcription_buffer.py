"""
Transcription buffer for handling partial and final ASR results.

This module provides buffering capabilities for transcription results,
managing partial results, final results, and result aggregation.
"""

import asyncio
import threading
import time
from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import logging

from ...core.interfaces import TranscriptionResult, AudioProcessingError
from ...core.base_component import BaseComponent


@dataclass
class BufferConfig:
    """Configuration for transcription buffer."""
    max_size: int = 100  # Maximum number of results to buffer
    timeout_ms: int = 5000  # Timeout for partial results
    
    # Result management
    enable_result_merging: bool = True  # Merge consecutive partial results
    merge_threshold_ms: int = 500  # Time threshold for merging results
    
    # Quality control
    min_confidence_for_final: float = 0.5  # Minimum confidence for final result
    enable_confidence_weighting: bool = True  # Weight results by confidence
    
    # Performance settings
    cleanup_interval_ms: int = 1000  # Cleanup interval for expired results
    max_partial_results: int = 50  # Maximum partial results to keep


@dataclass
class BufferedResult:
    """Buffered transcription result with metadata."""
    result: TranscriptionResult
    buffer_time: datetime
    is_processed: bool = False
    merge_count: int = 1
    
    @property
    def age_ms(self) -> float:
        """Get age of buffered result in milliseconds."""
        return (datetime.now() - self.buffer_time).total_seconds() * 1000


class TranscriptionBuffer(BaseComponent):
    """
    Buffer for managing transcription results.
    
    Handles partial and final transcription results, provides result merging,
    timeout management, and quality control for ASR output.
    """
    
    def __init__(self, config: BufferConfig, config_manager=None, logger=None):
        """
        Initialize transcription buffer.
        
        Args:
            config: Buffer configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("transcription_buffer", config_manager, logger)
        
        self.config = config
        
        # Result storage
        self._partial_results: deque = deque(maxlen=config.max_partial_results)
        self._final_results: deque = deque(maxlen=config.max_size)
        self._pending_results: List[BufferedResult] = []
        
        # Synchronization
        self._buffer_lock = threading.Lock()
        self._result_event = asyncio.Event()
        self._final_result_event = asyncio.Event()
        
        # Statistics
        self._total_added = 0
        self._total_merged = 0
        self._total_expired = 0
        self._total_finalized = 0
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # Event handlers
        self._result_handlers: List[Callable] = []
        self._final_handlers: List[Callable] = []
    
    async def _initialize_impl(self) -> None:
        """Initialize transcription buffer."""
        self._log.info("Initializing transcription buffer...")
        
        # Reset statistics
        self._total_added = 0
        self._total_merged = 0
        self._total_expired = 0
        self._total_finalized = 0
        
        self._log.info("Transcription buffer initialized")
    
    async def _start_impl(self) -> None:
        """Start transcription buffer."""
        self._log.info("Starting transcription buffer...")
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self._log.info("Transcription buffer started")
    
    async def _stop_impl(self) -> None:
        """Stop transcription buffer."""
        self._log.info("Stopping transcription buffer...")
        
        # Cancel cleanup task
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("Transcription buffer stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup transcription buffer resources."""
        self._log.info("Cleaning up transcription buffer...")
        
        # Clear all buffers
        with self._buffer_lock:
            self._partial_results.clear()
            self._final_results.clear()
            self._pending_results.clear()
        
        # Clear handlers
        self._result_handlers.clear()
        self._final_handlers.clear()
        
        self._log.info("Transcription buffer cleanup completed")
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop for expired results."""
        self._log.debug("Starting transcription buffer cleanup loop")
        
        try:
            while True:
                await asyncio.sleep(self.config.cleanup_interval_ms / 1000)
                await self._cleanup_expired_results()
        
        except asyncio.CancelledError:
            self._log.debug("Cleanup loop cancelled")
        except Exception as e:
            self._log.error(f"Error in cleanup loop: {e}")
    
    async def _cleanup_expired_results(self) -> None:
        """Clean up expired partial results."""
        current_time = datetime.now()
        timeout_threshold = timedelta(milliseconds=self.config.timeout_ms)
        
        with self._buffer_lock:
            # Clean up expired pending results
            expired_results = []
            remaining_results = []
            
            for buffered_result in self._pending_results:
                if current_time - buffered_result.buffer_time > timeout_threshold:
                    expired_results.append(buffered_result)
                    self._total_expired += 1
                else:
                    remaining_results.append(buffered_result)
            
            self._pending_results = remaining_results
            
            if expired_results:
                self._log.debug(f"Cleaned up {len(expired_results)} expired results")
    
    async def add_result(self, result: TranscriptionResult) -> None:
        """
        Add transcription result to buffer.
        
        Args:
            result: Transcription result to add
        """
        buffered_result = BufferedResult(
            result=result,
            buffer_time=datetime.now()
        )
        
        with self._buffer_lock:
            if result.is_final:
                # Add to final results
                self._final_results.append(buffered_result)
                self._total_finalized += 1
                
                # Set final result event
                self._final_result_event.set()
                
                # Notify final handlers
                await self._notify_final_handlers(result)
                
                self._log.debug(f"Added final result: '{result.text}' (confidence: {result.confidence:.3f})")
            
            else:
                # Handle partial result
                if self.config.enable_result_merging:
                    merged = await self._try_merge_result(buffered_result)
                    if not merged:
                        self._partial_results.append(buffered_result)
                        self._pending_results.append(buffered_result)
                else:
                    self._partial_results.append(buffered_result)
                    self._pending_results.append(buffered_result)
                
                self._log.debug(f"Added partial result: '{result.text}' (confidence: {result.confidence:.3f})")
            
            self._total_added += 1
            
            # Set result available event
            self._result_event.set()
            
            # Notify result handlers
            await self._notify_result_handlers(result)
    
    async def _try_merge_result(self, new_result: BufferedResult) -> bool:
        """
        Try to merge new result with existing partial results.
        
        Args:
            new_result: New buffered result to merge
            
        Returns:
            True if merged, False otherwise
        """
        merge_threshold = timedelta(milliseconds=self.config.merge_threshold_ms)
        
        # Look for recent partial results to merge with
        for i, existing in enumerate(self._pending_results):
            if existing.is_processed:
                continue
            
            time_diff = new_result.buffer_time - existing.buffer_time
            
            if time_diff <= merge_threshold:
                # Merge results
                merged_text = self._merge_transcription_text(
                    existing.result.text,
                    new_result.result.text
                )
                
                # Calculate weighted confidence
                if self.config.enable_confidence_weighting:
                    total_weight = existing.merge_count + 1
                    merged_confidence = (
                        (existing.result.confidence * existing.merge_count + 
                         new_result.result.confidence) / total_weight
                    )
                else:
                    merged_confidence = max(existing.result.confidence, new_result.result.confidence)
                
                # Update existing result
                existing.result = TranscriptionResult(
                    text=merged_text,
                    confidence=merged_confidence,
                    is_final=new_result.result.is_final,
                    timestamp=existing.result.timestamp,
                    language=existing.result.language
                )
                
                existing.merge_count += 1
                existing.buffer_time = new_result.buffer_time
                
                self._total_merged += 1
                
                self._log.debug(f"Merged results: '{merged_text}' (confidence: {merged_confidence:.3f})")
                
                return True
        
        return False
    
    def _merge_transcription_text(self, existing_text: str, new_text: str) -> str:
        """
        Merge two transcription texts intelligently.
        
        Args:
            existing_text: Existing transcription text
            new_text: New transcription text
            
        Returns:
            Merged transcription text
        """
        # Simple merging strategy - can be enhanced
        if not existing_text:
            return new_text
        
        if not new_text:
            return existing_text
        
        # If new text is longer and contains existing text, use new text
        if len(new_text) > len(existing_text) and existing_text in new_text:
            return new_text
        
        # If existing text is longer and contains new text, keep existing
        if len(existing_text) > len(new_text) and new_text in existing_text:
            return existing_text
        
        # Otherwise, concatenate with space
        return f"{existing_text} {new_text}".strip()
    
    async def get_next_result(self, timeout_ms: Optional[int] = None) -> Optional[TranscriptionResult]:
        """
        Get next available transcription result.
        
        Args:
            timeout_ms: Optional timeout in milliseconds
            
        Returns:
            Next transcription result or None if timeout
        """
        timeout_seconds = timeout_ms / 1000 if timeout_ms else None
        
        try:
            # Wait for result to be available
            await asyncio.wait_for(self._result_event.wait(), timeout=timeout_seconds)
            
            with self._buffer_lock:
                # Check for final results first
                if self._final_results:
                    buffered_result = self._final_results.popleft()
                    self._result_event.clear()
                    return buffered_result.result
                
                # Check for partial results
                if self._partial_results:
                    buffered_result = self._partial_results.popleft()
                    
                    # Remove from pending if present
                    self._pending_results = [
                        r for r in self._pending_results 
                        if r.result.timestamp != buffered_result.result.timestamp
                    ]
                    
                    # Clear event if no more results
                    if not self._partial_results and not self._final_results:
                        self._result_event.clear()
                    
                    return buffered_result.result
                
                # No results available
                self._result_event.clear()
                return None
        
        except asyncio.TimeoutError:
            return None
    
    async def get_final_result(self, timeout_ms: Optional[int] = None) -> Optional[TranscriptionResult]:
        """
        Get final transcription result.
        
        Args:
            timeout_ms: Optional timeout in milliseconds
            
        Returns:
            Final transcription result or None if timeout
        """
        timeout_seconds = timeout_ms / 1000 if timeout_ms else None
        
        try:
            # Wait for final result to be available
            await asyncio.wait_for(self._final_result_event.wait(), timeout=timeout_seconds)
            
            with self._buffer_lock:
                if self._final_results:
                    buffered_result = self._final_results.popleft()
                    
                    # Clear event if no more final results
                    if not self._final_results:
                        self._final_result_event.clear()
                    
                    return buffered_result.result
                
                return None
        
        except asyncio.TimeoutError:
            return None
    
    async def get_partial_results(self) -> List[TranscriptionResult]:
        """
        Get all current partial results.
        
        Returns:
            List of partial transcription results
        """
        with self._buffer_lock:
            return [br.result for br in self._partial_results]
    
    def get_partial_results_sync(self) -> List[TranscriptionResult]:
        """
        Get all current partial results (synchronous).
        
        Returns:
            List of partial transcription results
        """
        with self._buffer_lock:
            return [br.result for br in self._partial_results]
    
    async def get_recent_results(self, count: int) -> List[TranscriptionResult]:
        """
        Get recent transcription results.
        
        Args:
            count: Number of recent results to return
            
        Returns:
            List of recent transcription results
        """
        with self._buffer_lock:
            # Combine final and partial results, sorted by timestamp
            all_results = []
            
            # Add final results
            all_results.extend([br.result for br in self._final_results])
            
            # Add partial results
            all_results.extend([br.result for br in self._partial_results])
            
            # Sort by timestamp and return most recent
            all_results.sort(key=lambda r: r.timestamp, reverse=True)
            
            return all_results[:count]
    
    def has_final_result(self) -> bool:
        """Check if buffer has final result available."""
        with self._buffer_lock:
            return len(self._final_results) > 0
    
    def has_partial_results(self) -> bool:
        """Check if buffer has partial results available."""
        with self._buffer_lock:
            return len(self._partial_results) > 0
    
    def __len__(self) -> int:
        """Get total number of buffered results."""
        with self._buffer_lock:
            return len(self._final_results) + len(self._partial_results)
    
    def add_result_handler(self, handler: Callable) -> None:
        """Add result notification handler."""
        self._result_handlers.append(handler)
    
    def remove_result_handler(self, handler: Callable) -> None:
        """Remove result notification handler."""
        if handler in self._result_handlers:
            self._result_handlers.remove(handler)
    
    def add_final_handler(self, handler: Callable) -> None:
        """Add final result notification handler."""
        self._final_handlers.append(handler)
    
    def remove_final_handler(self, handler: Callable) -> None:
        """Remove final result notification handler."""
        if handler in self._final_handlers:
            self._final_handlers.remove(handler)
    
    async def _notify_result_handlers(self, result: TranscriptionResult) -> None:
        """Notify result handlers."""
        for handler in self._result_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(result)
                else:
                    handler(result)
            except Exception as e:
                self._log.error(f"Error in result handler: {e}")
    
    async def _notify_final_handlers(self, result: TranscriptionResult) -> None:
        """Notify final result handlers."""
        for handler in self._final_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(result)
                else:
                    handler(result)
            except Exception as e:
                self._log.error(f"Error in final result handler: {e}")
    
    def _log_final_statistics(self) -> None:
        """Log final buffer statistics."""
        self._log.info(f"Transcription Buffer Statistics:")
        self._log.info(f"  Total added: {self._total_added}")
        self._log.info(f"  Total merged: {self._total_merged}")
        self._log.info(f"  Total expired: {self._total_expired}")
        self._log.info(f"  Total finalized: {self._total_finalized}")
        
        with self._buffer_lock:
            self._log.info(f"  Final results remaining: {len(self._final_results)}")
            self._log.info(f"  Partial results remaining: {len(self._partial_results)}")
    
    def get_buffer_stats(self) -> Dict[str, Any]:
        """
        Get buffer statistics.
        
        Returns:
            Dictionary with buffer statistics
        """
        with self._buffer_lock:
            return {
                "total_added": self._total_added,
                "total_merged": self._total_merged,
                "total_expired": self._total_expired,
                "total_finalized": self._total_finalized,
                "final_results_count": len(self._final_results),
                "partial_results_count": len(self._partial_results),
                "pending_results_count": len(self._pending_results),
                "merge_rate": self._total_merged / self._total_added if self._total_added > 0 else 0.0,
                "finalization_rate": self._total_finalized / self._total_added if self._total_added > 0 else 0.0
            }
    
    def reset_stats(self) -> None:
        """Reset buffer statistics."""
        self._total_added = 0
        self._total_merged = 0
        self._total_expired = 0
        self._total_finalized = 0
        
        self._log.info("Buffer statistics reset")
    
    def clear_buffer(self) -> None:
        """Clear all buffered results."""
        with self._buffer_lock:
            self._partial_results.clear()
            self._final_results.clear()
            self._pending_results.clear()
        
        # Clear events
        self._result_event.clear()
        self._final_result_event.clear()
        
        self._log.info("Buffer cleared")


# Utility functions for transcription buffer

async def create_transcription_buffer(
    max_size: int = 100,
    timeout_ms: int = 5000,
    enable_merging: bool = True,
    config_manager=None,
    **kwargs
) -> TranscriptionBuffer:
    """
    Create and initialize a transcription buffer.
    
    Args:
        max_size: Maximum buffer size
        timeout_ms: Timeout for partial results
        enable_merging: Enable result merging
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized transcription buffer
    """
    config = BufferConfig(
        max_size=max_size,
        timeout_ms=timeout_ms,
        enable_result_merging=enable_merging,
        **kwargs
    )
    
    buffer = TranscriptionBuffer(config, config_manager)
    await buffer.initialize()
    await buffer.start()
    
    return buffer