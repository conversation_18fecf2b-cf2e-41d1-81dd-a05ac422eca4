"""
Automatic Speech Recognition (ASR) Service
==========================================

This module provides speech recognition capabilities for the AI Voice Customer Service System.
It supports multiple ASR engines and provides a unified interface for speech-to-text conversion.
"""

import asyncio
import logging
import speech_recognition as sr
import io
import wave
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from ...core.base_component import BaseComponent

# Try to import SenseVoice components
try:
    from ..asr.sensevoice_recognizer import SenseVoiceRecognizer, SenseVoiceConfig
    SENSEVOICE_AVAILABLE = True
except ImportError:
    SENSEVOICE_AVAILABLE = False

class ASREngine(Enum):
    """Supported ASR engines."""
    GOOGLE = "google"
    WHISPER = "whisper"
    AZURE = "azure"
    AWS = "aws"
    SENSEVOICE = "sensevoice"

@dataclass
class ASRConfig:
    """Configuration for ASR service."""
    engine: ASREngine = ASREngine.GOOGLE
    language: str = "en-US"
    timeout: float = 10.0
    phrase_timeout: float = 1.0
    energy_threshold: int = 300
    dynamic_energy_threshold: bool = True
    pause_threshold: float = 0.8

@dataclass
class ASRResult:
    """Result from ASR processing."""
    text: str
    confidence: float
    language: str
    processing_time: float
    engine_used: str
    alternatives: List[Dict[str, Any]] = None

class ASRService(BaseComponent):
    """Automatic Speech Recognition service."""
    
    def __init__(self, config: ASRConfig, logger: Optional[logging.Logger] = None):
        super().__init__("asr_service", logger)
        self.config = config
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.sensevoice_recognizer = None

        # Configure recognizer
        self.recognizer.energy_threshold = config.energy_threshold
        self.recognizer.dynamic_energy_threshold = config.dynamic_energy_threshold
        self.recognizer.pause_threshold = config.pause_threshold

        self._log.info(f"ASR Service initialized with engine: {config.engine.value}")
    
    # Implement abstract methods from BaseComponent
    async def _initialize_impl(self) -> bool:
        """Implementation of initialization logic."""
        try:
            # Initialize SenseVoice if selected
            if self.config.engine == ASREngine.SENSEVOICE:
                if not SENSEVOICE_AVAILABLE:
                    self._log.error("SenseVoice not available but selected as ASR engine")
                    return False

                # Create SenseVoice configuration
                sensevoice_config = SenseVoiceConfig(
                    model_path="models/SenseVoiceSmall/model.pt",
                    language="zh",  # Chinese
                    confidence_threshold=0.3,
                    use_gpu=True
                )

                # Initialize SenseVoice recognizer
                self.sensevoice_recognizer = SenseVoiceRecognizer(sensevoice_config, None)
                await self.sensevoice_recognizer.initialize()
                await self.sensevoice_recognizer.start()
                self._log.info("SenseVoice recognizer initialized successfully")

            # Test microphone availability (optional)
            try:
                self.microphone = sr.Microphone()
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=0.5)
                self._log.info("Microphone initialized and calibrated")
            except Exception as e:
                self._log.warning(f"Microphone not available: {e}")
                self.microphone = None

            self._log.info("ASR Service initialized successfully")
            return True

        except Exception as e:
            self._log.error(f"Failed to initialize ASR Service: {e}")
            return False

    async def _start_impl(self) -> bool:
        """Implementation of start logic."""
        self._log.info("ASR Service started")
        return True

    async def _stop_impl(self) -> bool:
        """Implementation of stop logic."""
        self._log.info("ASR Service stopped")
        return True
    
    async def recognize_speech_from_audio_data(self, audio_data: bytes, sample_rate: int = 16000) -> ASRResult:
        """
        Recognize speech from raw audio data.
        
        Args:
            audio_data: Raw audio bytes
            sample_rate: Sample rate of the audio
            
        Returns:
            ASRResult with recognition results
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Convert raw audio data to AudioData object
            audio_data_obj = sr.AudioData(audio_data, sample_rate, 2)  # 2 bytes per sample (16-bit)
            
            # Perform recognition based on configured engine
            if self.config.engine == ASREngine.GOOGLE:
                text = await self._recognize_google(audio_data_obj)
            elif self.config.engine == ASREngine.WHISPER:
                text = await self._recognize_whisper(audio_data_obj)
            elif self.config.engine == ASREngine.SENSEVOICE:
                text = await self._recognize_sensevoice(audio_data_obj)
            else:
                raise ValueError(f"Unsupported ASR engine: {self.config.engine}")
            
            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            return ASRResult(
                text=text,
                confidence=0.95,  # Would be provided by actual ASR engine
                language=self.config.language,
                processing_time=processing_time,
                engine_used=self.config.engine.value
            )
            
        except sr.UnknownValueError:
            self._log.warning("Could not understand audio")
            return ASRResult(
                text="",
                confidence=0.0,
                language=self.config.language,
                processing_time=asyncio.get_event_loop().time() - start_time,
                engine_used=self.config.engine.value
            )
        except Exception as e:
            self._log.error(f"ASR recognition failed: {e}")
            raise
    
    async def _recognize_google(self, audio_data: sr.AudioData) -> str:
        """Recognize speech using Google Speech Recognition."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.recognizer.recognize_google(
                audio_data,
                language=self.config.language
            )
        )
    
    async def _recognize_whisper(self, audio_data: sr.AudioData) -> str:
        """Recognize speech using OpenAI Whisper."""
        # This would require whisper library installation
        # For now, we'll use Google as fallback
        self._log.warning("Whisper not implemented, falling back to Google")
        return await self._recognize_google(audio_data)

    async def _recognize_sensevoice(self, audio_data: sr.AudioData) -> str:
        """Recognize speech using SenseVoice."""
        if not self.sensevoice_recognizer:
            raise RuntimeError("SenseVoice recognizer not initialized")

        try:
            # Convert sr.AudioData to the format expected by SenseVoice
            # SenseVoice expects raw audio bytes
            audio_bytes = audio_data.get_raw_data()

            # Create AudioChunk for SenseVoice (using interfaces AudioChunk)
            from ...core.interfaces import AudioChunk, AudioFormat
            from datetime import datetime

            # Calculate duration in milliseconds
            duration_ms = int(len(audio_bytes) / (audio_data.sample_rate * 2) * 1000)  # 2 bytes per sample for 16-bit

            audio_chunk = AudioChunk(
                data=audio_bytes,
                format=AudioFormat.PCM_16KHZ_MONO,
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                sample_rate=audio_data.sample_rate
            )

            # Use SenseVoice to transcribe
            result = await self.sensevoice_recognizer.transcribe(audio_chunk)
            return result.text

        except Exception as e:
            self._log.error(f"SenseVoice recognition failed: {e}")
            # Fallback to Google if SenseVoice fails
            self._log.warning("Falling back to Google ASR")
            return await self._recognize_google(audio_data)
    
    async def recognize_from_microphone(self, duration: Optional[float] = None) -> ASRResult:
        """
        Recognize speech from microphone input.
        
        Args:
            duration: Maximum duration to listen (None for phrase-based)
            
        Returns:
            ASRResult with recognition results
        """
        if not self.microphone:
            raise RuntimeError("Microphone not available")
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            with self.microphone as source:
                self._log.info("Listening for speech...")
                if duration:
                    audio = self.recognizer.listen(source, timeout=self.config.timeout, phrase_time_limit=duration)
                else:
                    audio = self.recognizer.listen(source, timeout=self.config.timeout)
            
            # Perform recognition
            if self.config.engine == ASREngine.GOOGLE:
                text = await self._recognize_google(audio)
            elif self.config.engine == ASREngine.SENSEVOICE:
                text = await self._recognize_sensevoice(audio)
            else:
                raise ValueError(f"Unsupported ASR engine: {self.config.engine}")
            
            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            return ASRResult(
                text=text,
                confidence=0.95,
                language=self.config.language,
                processing_time=processing_time,
                engine_used=self.config.engine.value
            )
            
        except sr.WaitTimeoutError:
            self._log.warning("Listening timeout")
            return ASRResult(
                text="",
                confidence=0.0,
                language=self.config.language,
                processing_time=asyncio.get_event_loop().time() - start_time,
                engine_used=self.config.engine.value
            )
        except Exception as e:
            self._log.error(f"Microphone recognition failed: {e}")
            raise
    

    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return [
            "en-US", "en-GB", "es-ES", "fr-FR", "de-DE", 
            "it-IT", "pt-BR", "ru-RU", "ja-JP", "ko-KR", "zh-CN"
        ]
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about the current ASR engine."""
        return {
            "engine": self.config.engine.value,
            "language": self.config.language,
            "timeout": self.config.timeout,
            "energy_threshold": self.recognizer.energy_threshold,
            "dynamic_energy_threshold": self.recognizer.dynamic_energy_threshold,
            "pause_threshold": self.recognizer.pause_threshold
        }

    async def _cleanup_impl(self) -> bool:
        """Implementation of cleanup logic."""
        self.microphone = None
        self._log.info("ASR Service cleanup completed")
        return True
