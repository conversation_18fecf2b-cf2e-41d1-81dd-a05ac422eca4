#!/usr/bin/env python3
"""
Configuration Optimization Script

This script analyzes system performance and automatically optimizes
configuration parameters for better performance.
"""

import asyncio
import logging
import yaml
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.config_manager import ConfigManager
from src.optimization.performance_optimizer import PerformanceOptimizer, OptimizationType
from src.optimization.model_tuner import ModelTuner, OptimizationTechnique


class ConfigOptimizer:
    """Configuration optimizer for AI Voice Customer Service."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.original_config = {}
        self.optimized_config = {}
        self.optimization_results = {}
        
    async def optimize_configuration(self, config_path: str, environment: str = "production") -> Dict[str, Any]:
        """Optimize configuration for the specified environment."""
        self.logger.info(f"Starting configuration optimization for {environment} environment...")
        
        # Load current configuration
        self.original_config = self._load_config(config_path)
        self.optimized_config = self.original_config.copy()
        
        # Run optimization steps
        optimization_steps = [
            ("server_optimization", self._optimize_server_config),
            ("database_optimization", self._optimize_database_config),
            ("audio_optimization", self._optimize_audio_config),
            ("model_optimization", self._optimize_model_config),
            ("monitoring_optimization", self._optimize_monitoring_config),
            ("performance_optimization", self._optimize_performance_config)
        ]
        
        for step_name, step_func in optimization_steps:
            try:
                self.logger.info(f"Running {step_name}...")
                result = await step_func(environment)
                self.optimization_results[step_name] = result
                self.logger.info(f"Completed {step_name}: {result.get('improvements', 0)} improvements")
            except Exception as e:
                self.logger.error(f"Error in {step_name}: {e}")
                self.optimization_results[step_name] = {"error": str(e)}
        
        # Generate optimization report
        report = self._generate_optimization_report()
        
        self.logger.info("Configuration optimization completed")
        return report
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    return yaml.safe_load(f)
                elif config_path.endswith('.json'):
                    return json.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_path}")
        except Exception as e:
            self.logger.error(f"Failed to load config from {config_path}: {e}")
            return {}
    
    async def _optimize_server_config(self, environment: str) -> Dict[str, Any]:
        """Optimize server configuration."""
        improvements = 0
        recommendations = []
        
        server_config = self.optimized_config.setdefault("server", {})
        
        # Optimize based on environment
        if environment == "production":
            # Production optimizations
            if server_config.get("workers", 1) < 4:
                server_config["workers"] = 4
                improvements += 1
                recommendations.append("Increased worker count for production")
            
            if server_config.get("host") != "0.0.0.0":
                server_config["host"] = "0.0.0.0"
                improvements += 1
                recommendations.append("Set host to 0.0.0.0 for production")
            
            # Add production-specific settings
            server_config.setdefault("keepalive_timeout", 65)
            server_config.setdefault("max_requests", 1000)
            server_config.setdefault("max_requests_jitter", 100)
            improvements += 3
            recommendations.append("Added production server settings")
            
        elif environment == "development":
            # Development optimizations
            if server_config.get("workers", 1) > 1:
                server_config["workers"] = 1
                improvements += 1
                recommendations.append("Set single worker for development")
            
            server_config.setdefault("reload", True)
            improvements += 1
            recommendations.append("Enabled auto-reload for development")
        
        return {
            "improvements": improvements,
            "recommendations": recommendations
        }
    
    async def _optimize_database_config(self, environment: str) -> Dict[str, Any]:
        """Optimize database configuration."""
        improvements = 0
        recommendations = []
        
        db_config = self.optimized_config.setdefault("database", {})
        
        # Connection pool optimization
        if environment == "production":
            if db_config.get("pool_size", 5) < 20:
                db_config["pool_size"] = 20
                improvements += 1
                recommendations.append("Increased database pool size for production")
            
            if db_config.get("max_overflow", 10) < 30:
                db_config["max_overflow"] = 30
                improvements += 1
                recommendations.append("Increased max overflow for production")
            
            db_config.setdefault("pool_timeout", 30)
            db_config.setdefault("pool_recycle", 3600)
            improvements += 2
            recommendations.append("Added production database settings")
        
        # Query optimization
        db_config.setdefault("echo", False)  # Disable SQL logging in production
        db_config.setdefault("pool_pre_ping", True)  # Enable connection health checks
        improvements += 2
        recommendations.append("Optimized database query settings")
        
        return {
            "improvements": improvements,
            "recommendations": recommendations
        }
    
    async def _optimize_audio_config(self, environment: str) -> Dict[str, Any]:
        """Optimize audio configuration."""
        improvements = 0
        recommendations = []
        
        audio_config = self.optimized_config.setdefault("audio", {})
        
        # Optimize audio parameters for performance
        if audio_config.get("sample_rate") != 16000:
            audio_config["sample_rate"] = 16000
            improvements += 1
            recommendations.append("Set optimal sample rate to 16kHz")
        
        if audio_config.get("channels") != 1:
            audio_config["channels"] = 1
            improvements += 1
            recommendations.append("Set mono audio for efficiency")
        
        # Buffer size optimization
        if environment == "production":
            if audio_config.get("buffer_size", 1024) < 4096:
                audio_config["buffer_size"] = 4096
                improvements += 1
                recommendations.append("Increased buffer size for production stability")
        else:
            if audio_config.get("buffer_size", 1024) > 2048:
                audio_config["buffer_size"] = 2048
                improvements += 1
                recommendations.append("Reduced buffer size for development responsiveness")
        
        # Enable compression for production
        if environment == "production":
            audio_config.setdefault("enable_compression", True)
            improvements += 1
            recommendations.append("Enabled audio compression for production")
        
        return {
            "improvements": improvements,
            "recommendations": recommendations
        }
    
    async def _optimize_model_config(self, environment: str) -> Dict[str, Any]:
        """Optimize model configuration."""
        improvements = 0
        recommendations = []
        
        models_config = self.optimized_config.setdefault("models", {})
        
        # ASR optimization
        asr_config = models_config.setdefault("asr", {})
        if asr_config.get("confidence_threshold", 0.5) < 0.8:
            asr_config["confidence_threshold"] = 0.8
            improvements += 1
            recommendations.append("Increased ASR confidence threshold for accuracy")
        
        # LLM optimization
        llm_config = models_config.setdefault("llm", {})
        if environment == "production":
            if llm_config.get("max_tokens", 500) > 1000:
                llm_config["max_tokens"] = 1000
                improvements += 1
                recommendations.append("Limited max tokens for production efficiency")
            
            if llm_config.get("temperature", 1.0) > 0.7:
                llm_config["temperature"] = 0.7
                improvements += 1
                recommendations.append("Reduced temperature for more consistent responses")
        
        # TTS optimization
        tts_config = models_config.setdefault("tts", {})
        tts_config.setdefault("voice", "zh-CN-XiaoxiaoNeural")
        tts_config.setdefault("rate", "+0%")
        tts_config.setdefault("volume", "+0%")
        improvements += 3
        recommendations.append("Optimized TTS settings")
        
        return {
            "improvements": improvements,
            "recommendations": recommendations
        }
    
    async def _optimize_monitoring_config(self, environment: str) -> Dict[str, Any]:
        """Optimize monitoring configuration."""
        improvements = 0
        recommendations = []
        
        monitoring_config = self.optimized_config.setdefault("monitoring", {})
        
        if environment == "production":
            monitoring_config.setdefault("enabled", True)
            monitoring_config.setdefault("metrics_retention_hours", 168)  # 7 days
            monitoring_config.setdefault("alert_check_interval", 30)
            improvements += 3
            recommendations.append("Enabled comprehensive monitoring for production")
        else:
            monitoring_config.setdefault("enabled", True)
            monitoring_config.setdefault("metrics_retention_hours", 24)  # 1 day
            monitoring_config.setdefault("alert_check_interval", 60)
            improvements += 3
            recommendations.append("Enabled basic monitoring for development")
        
        return {
            "improvements": improvements,
            "recommendations": recommendations
        }
    
    async def _optimize_performance_config(self, environment: str) -> Dict[str, Any]:
        """Optimize performance configuration."""
        improvements = 0
        recommendations = []
        
        performance_config = self.optimized_config.setdefault("optimization", {})
        
        if environment == "production":
            performance_config.setdefault("target_latency_ms", 500)
            performance_config.setdefault("target_throughput_rps", 100)
            performance_config.setdefault("target_cpu_utilization", 70)
            performance_config.setdefault("target_memory_utilization", 80)
            performance_config.setdefault("auto_optimize", True)
            performance_config.setdefault("optimization_level", "balanced")
            improvements += 6
            recommendations.append("Configured performance optimization for production")
        else:
            performance_config.setdefault("target_latency_ms", 1000)
            performance_config.setdefault("target_throughput_rps", 10)
            performance_config.setdefault("auto_optimize", False)
            performance_config.setdefault("optimization_level", "conservative")
            improvements += 4
            recommendations.append("Configured performance optimization for development")
        
        return {
            "improvements": improvements,
            "recommendations": recommendations
        }
    
    def _generate_optimization_report(self) -> Dict[str, Any]:
        """Generate optimization report."""
        total_improvements = sum(
            result.get("improvements", 0) 
            for result in self.optimization_results.values()
            if isinstance(result, dict)
        )
        
        all_recommendations = []
        for result in self.optimization_results.values():
            if isinstance(result, dict) and "recommendations" in result:
                all_recommendations.extend(result["recommendations"])
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_improvements": total_improvements,
            "optimization_steps": len(self.optimization_results),
            "successful_steps": len([
                r for r in self.optimization_results.values() 
                if isinstance(r, dict) and "error" not in r
            ]),
            "recommendations": all_recommendations,
            "step_results": self.optimization_results,
            "config_changes": self._get_config_changes()
        }
        
        return report
    
    def _get_config_changes(self) -> Dict[str, Any]:
        """Get configuration changes."""
        changes = {}
        
        def compare_configs(original, optimized, path=""):
            for key, value in optimized.items():
                current_path = f"{path}.{key}" if path else key
                
                if key not in original:
                    changes[current_path] = {"action": "added", "value": value}
                elif isinstance(value, dict) and isinstance(original[key], dict):
                    compare_configs(original[key], value, current_path)
                elif original[key] != value:
                    changes[current_path] = {
                        "action": "modified",
                        "old_value": original[key],
                        "new_value": value
                    }
        
        compare_configs(self.original_config, self.optimized_config)
        return changes
    
    def save_optimized_config(self, output_path: str) -> str:
        """Save optimized configuration to file."""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            if output_path.endswith('.yaml') or output_path.endswith('.yml'):
                yaml.dump(self.optimized_config, f, default_flow_style=False, allow_unicode=True)
            elif output_path.endswith('.json'):
                json.dump(self.optimized_config, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Optimized configuration saved to: {output_path}")
        return output_path
    
    def save_optimization_report(self, report_path: str, report: Dict[str, Any]) -> str:
        """Save optimization report to file."""
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Optimization report saved to: {report_path}")
        return report_path


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Optimize AI Voice Customer Service configuration")
    parser.add_argument("--config", required=True, help="Path to configuration file")
    parser.add_argument("--environment", default="production", choices=["development", "testing", "production"],
                       help="Target environment")
    parser.add_argument("--output", help="Output path for optimized configuration")
    parser.add_argument("--report", help="Output path for optimization report")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Create optimizer
        optimizer = ConfigOptimizer()
        
        # Run optimization
        report = await optimizer.optimize_configuration(args.config, args.environment)
        
        # Save optimized configuration
        if args.output:
            optimizer.save_optimized_config(args.output)
        else:
            # Generate default output path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_name = os.path.splitext(os.path.basename(args.config))[0]
            output_path = f"config/optimized_{config_name}_{args.environment}_{timestamp}.yml"
            optimizer.save_optimized_config(output_path)
        
        # Save optimization report
        if args.report:
            optimizer.save_optimization_report(args.report, report)
        else:
            # Generate default report path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = f"logs/optimization/config_optimization_report_{timestamp}.json"
            optimizer.save_optimization_report(report_path, report)
        
        # Print summary
        print("\n" + "="*60)
        print("CONFIGURATION OPTIMIZATION SUMMARY")
        print("="*60)
        print(f"Environment: {args.environment}")
        print(f"Total Improvements: {report['total_improvements']}")
        print(f"Successful Steps: {report['successful_steps']}/{report['optimization_steps']}")
        print(f"Configuration Changes: {len(report['config_changes'])}")
        
        if report['recommendations']:
            print("\nKey Recommendations:")
            for i, rec in enumerate(report['recommendations'][:5], 1):
                print(f"  {i}. {rec}")
        
        print("\n🎉 Configuration optimization completed successfully!")
        return 0
        
    except Exception as e:
        logging.error(f"Configuration optimization failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
