#!/usr/bin/env python3
"""
AI Voice Customer Service System - Voice Functionality Test
==========================================================

This file tests the newly integrated voice functionality including
ASR (Automatic Speech Recognition) and TTS (Text-to-Speech) services.

Prerequisites:
- System running on http://localhost:8000 with voice components
- Python with requests library installed
"""

import requests
import json
import time
from typing import Dict, Any

# Base URL for the API
BASE_URL = "http://localhost:8000"

class VoiceFunctionalityTester:
    """Client for testing voice functionality."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status including voice components."""
        response = self.session.get(f"{self.base_url}/status")
        response.raise_for_status()
        return response.json()
    
    def test_voice_components_status(self) -> Dict[str, Any]:
        """Test if voice components are properly loaded."""
        status = self.get_system_status()
        
        # Check if voice processor is in components
        components = status.get('components', {})
        voice_status = {
            'voice_processor_loaded': 'voice_processor' in components,
            'total_components': len(components),
            'system_state': status['system_status']['state'],
            'components_running': status['system_status']['components_running']
        }
        
        return voice_status

def main():
    """Test voice functionality integration."""
    print("🎙️ AI Voice System - Voice Functionality Test")
    print("=" * 55)
    
    tester = VoiceFunctionalityTester()
    
    try:
        # 1. Check voice components status
        print("\n1. 🔍 Voice Components Status Check:")
        voice_status = tester.test_voice_components_status()
        
        print(f"   System state: {voice_status['system_state']}")
        print(f"   Total components: {voice_status['total_components']}")
        print(f"   Components running: {voice_status['components_running']}")
        print(f"   Voice processor loaded: {'✅' if voice_status['voice_processor_loaded'] else '❌'}")
        
        # 2. System health with voice components
        print("\n2. 🏥 System Health with Voice Components:")
        health_response = tester.session.get(f"{tester.base_url}/health")
        health_response.raise_for_status()
        health = health_response.json()
        
        print(f"   Overall status: {health['status']}")
        print(f"   Running components: {health.get('running_components', 'N/A')}")
        print(f"   Active calls: {health['active_calls']}")
        
        # 3. Voice integration verification
        print("\n3. 🎵 Voice Integration Verification:")
        if voice_status['voice_processor_loaded']:
            print("   ✅ Voice Processor successfully integrated")
            print("   ✅ ASR Service (Speech Recognition) available")
            print("   ✅ TTS Service (Text-to-Speech) available")
            print("   ✅ Voice processing pipeline ready")
        else:
            print("   ❌ Voice Processor not found in system")
            print("   ⚠️  Voice functionality may not be available")
        
        # 4. Performance impact assessment
        print("\n4. 📊 Performance Impact Assessment:")
        status = tester.get_system_status()
        sys_status = status['system_status']
        
        print(f"   System uptime: {sys_status['uptime']:.2f} seconds")
        print(f"   Components initialized: {sys_status['components_initialized']}")
        print(f"   Components running: {sys_status['components_running']}")
        print(f"   Components with errors: {sys_status['components_error']}")
        
        if sys_status['components_error'] == 0:
            print("   ✅ No component errors detected")
        else:
            print(f"   ⚠️  {sys_status['components_error']} components have errors")
        
        # 5. Voice functionality readiness
        print("\n5. 🚀 Voice Functionality Readiness:")
        
        readiness_score = 0
        max_score = 5
        
        # Check system state
        if voice_status['system_state'] == 'running':
            print("   ✅ System is running")
            readiness_score += 1
        else:
            print("   ❌ System is not running")
        
        # Check voice processor
        if voice_status['voice_processor_loaded']:
            print("   ✅ Voice processor loaded")
            readiness_score += 1
        else:
            print("   ❌ Voice processor not loaded")
        
        # Check component count (should be 10 with voice processor)
        if voice_status['total_components'] >= 10:
            print("   ✅ All components loaded (including voice)")
            readiness_score += 1
        else:
            print(f"   ⚠️  Only {voice_status['total_components']} components loaded")
        
        # Check running components
        if voice_status['components_running'] >= 10:
            print("   ✅ All components running")
            readiness_score += 1
        else:
            print(f"   ⚠️  Only {voice_status['components_running']} components running")
        
        # Check health
        if health['status'] == 'healthy':
            print("   ✅ System health is good")
            readiness_score += 1
        else:
            print("   ❌ System health issues detected")
        
        # Overall readiness assessment
        print(f"\n   📊 Readiness Score: {readiness_score}/{max_score}")
        
        if readiness_score == max_score:
            print("   🟢 EXCELLENT: Voice functionality is fully ready!")
            print("   🎯 Next step: Test actual voice processing")
        elif readiness_score >= 4:
            print("   🟡 GOOD: Voice functionality is mostly ready")
            print("   🔧 Minor issues need attention")
        else:
            print("   🔴 NEEDS WORK: Voice functionality needs more setup")
            print("   🛠️  Significant issues need to be resolved")
        
        # 6. Next steps recommendations
        print("\n6. 📝 Next Steps Recommendations:")
        print("   🎯 Voice System Integration Complete!")
        print("   📋 Ready for:")
        print("      • Real-time voice processing tests")
        print("      • ASR accuracy testing")
        print("      • TTS quality testing")
        print("      • Voice conversation flow testing")
        print("      • Integration with AI conversation engine")
        
        print("\n✅ Voice functionality testing completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Cannot connect to the AI Voice System.")
        print("   Make sure the system is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
