"""
Example usage of Voice Activity Detection (VAD) components.

This example demonstrates how to use SileroVAD for voice activity detection
and speech segmentation in the AI voice customer service system.
"""

import asyncio
import numpy as np
import logging
from typing import Optional
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import VAD components
from src.core.interfaces import AudioChunk, AudioFormat
from src.components.vad import (
    SileroVADDetector, VADConfig, VADResult,
    SpeechSegmenter, SegmentationConfig, SpeechSegment,
    create_vad_detector, create_speech_segmenter,
    calculate_speech_statistics, analyze_speech_segments
)


class MockConfigManager:
    """Mock configuration manager for example."""
    
    def get(self, key: str, default=None):
        return default


def create_test_audio_chunk(duration_ms: int = 32, sample_rate: int = 16000, 
                          noise_level: float = 0.1, has_speech: bool = False) -> AudioChunk:
    """
    Create test audio chunk with optional speech simulation.
    
    Args:
        duration_ms: Duration in milliseconds
        sample_rate: Sample rate in Hz
        noise_level: Background noise level (0.0 to 1.0)
        has_speech: Whether to simulate speech content
        
    Returns:
        Test audio chunk
    """
    samples = int(duration_ms * sample_rate / 1000)
    
    if has_speech:
        # Simulate speech with higher amplitude and some periodicity
        t = np.linspace(0, duration_ms / 1000, samples)
        speech_signal = np.sin(2 * np.pi * 200 * t) * 0.5  # 200Hz tone
        speech_signal += np.sin(2 * np.pi * 400 * t) * 0.3  # 400Hz harmonic
        
        # Add some randomness to make it more speech-like
        speech_signal += np.random.normal(0, 0.2, samples)
        
        # Add background noise
        noise = np.random.normal(0, noise_level, samples)
        audio_signal = speech_signal + noise
    else:
        # Just background noise
        audio_signal = np.random.normal(0, noise_level, samples)
    
    # Convert to int16 and clip
    audio_int16 = np.clip(audio_signal * 32767, -32768, 32767).astype(np.int16)
    
    return AudioChunk(
        data=audio_int16.tobytes(),
        format=AudioFormat.PCM_16KHZ_MONO,
        timestamp=datetime.now(),
        duration_ms=duration_ms,
        sample_rate=sample_rate
    )


async def basic_vad_example():
    """Demonstrate basic VAD functionality."""
    logger.info("=== Basic VAD Example ===")
    
    # Create mock config manager
    config_manager = MockConfigManager()
    
    # Create VAD configuration
    vad_config = VADConfig(
        model_path="models/snakers4_silero-vad",
        threshold=0.5,
        enable_adaptive_threshold=True,
        min_speech_duration_ms=200,
        min_silence_duration_ms=150
    )
    
    try:
        # Create VAD detector using utility function
        vad_detector = await create_vad_detector(
            model_path=vad_config.model_path,
            threshold=vad_config.threshold,
            config_manager=config_manager,
            enable_adaptive_threshold=True
        )
        
        logger.info(f"VAD detector created with threshold: {vad_detector.current_threshold}")
        
        # Test with different types of audio
        test_scenarios = [
            ("Background noise", False, 0.1),
            ("Quiet speech", True, 0.05),
            ("Loud speech", True, 0.02),
            ("Noisy speech", True, 0.3),
            ("Silence", False, 0.01)
        ]
        
        vad_results = []
        
        for scenario_name, has_speech, noise_level in test_scenarios:
            logger.info(f"\nTesting: {scenario_name}")
            
            # Create test audio chunks for this scenario
            for i in range(5):
                audio_chunk = create_test_audio_chunk(
                    duration_ms=64,  # 64ms chunks
                    has_speech=has_speech,
                    noise_level=noise_level
                )
                
                # Detect voice activity
                confidence = await vad_detector.detect_voice_activity(audio_chunk)
                
                # Get detailed result
                detailed_result = await vad_detector.detect_voice_activity_detailed(audio_chunk)
                vad_results.append(detailed_result)
                
                logger.info(f"  Chunk {i+1}: confidence={confidence:.3f}, "
                          f"has_speech={detailed_result.has_speech}, "
                          f"threshold={detailed_result.adapted_threshold:.3f}")
        
        # Show final statistics
        stats = vad_detector.get_detection_stats()
        logger.info(f"\nVAD Statistics:")
        logger.info(f"  Total detections: {stats['total_detections']}")
        logger.info(f"  Speech detected: {stats['speech_detected']}")
        logger.info(f"  Speech rate: {stats['speech_rate']:.2%}")
        logger.info(f"  Average processing time: {stats['average_processing_time_ms']:.2f}ms")
        logger.info(f"  Final threshold: {stats['current_threshold']:.3f}")
        logger.info(f"  Background noise level: {stats['background_noise_level']:.3f}")
        
        # Analyze VAD results
        speech_stats = calculate_speech_statistics(vad_results)
        logger.info(f"\nSpeech Analysis:")
        logger.info(f"  Speech ratio: {speech_stats['speech_ratio']:.2%}")
        logger.info(f"  Average confidence: {speech_stats['average_confidence']:.3f}")
        logger.info(f"  Confidence range: {speech_stats['min_confidence']:.3f} - {speech_stats['max_confidence']:.3f}")
        
        # Cleanup
        await vad_detector.stop()
        await vad_detector.cleanup()
        
    except Exception as e:
        logger.error(f"VAD example failed: {e}")
        logger.info("Note: This example requires the SileroVAD model to be properly installed")


async def speech_segmentation_example():
    """Demonstrate speech segmentation functionality."""
    logger.info("\n=== Speech Segmentation Example ===")
    
    config_manager = MockConfigManager()
    
    # Create VAD detector
    vad_config = VADConfig(
        model_path="models/snakers4_silero-vad",
        threshold=0.4,
        enable_adaptive_threshold=True
    )
    
    try:
        vad_detector = SileroVADDetector(vad_config, config_manager)
        await vad_detector.initialize()
        await vad_detector.start()
        
        # Create speech segmenter
        seg_config = SegmentationConfig(
            min_speech_duration_ms=300,
            min_silence_duration_ms=200,
            speech_start_pad_ms=50,
            speech_end_pad_ms=100,
            enable_adaptive_threshold=True
        )
        
        segmenter = SpeechSegmenter(seg_config, vad_detector, config_manager)
        await segmenter.initialize()
        await segmenter.start()
        
        logger.info(f"Segmenter created - min speech: {seg_config.min_speech_duration_ms}ms, "
                   f"min silence: {seg_config.min_silence_duration_ms}ms")
        
        # Create a sequence of audio chunks simulating a conversation
        audio_sequence = []
        
        # Pattern: silence -> speech -> silence -> speech -> silence
        patterns = [
            (10, False, 0.05),  # 10 chunks of silence
            (15, True, 0.1),    # 15 chunks of speech
            (8, False, 0.05),   # 8 chunks of silence
            (20, True, 0.08),   # 20 chunks of speech
            (12, False, 0.05),  # 12 chunks of silence
            (18, True, 0.12),   # 18 chunks of speech
            (10, False, 0.05)   # 10 chunks of silence
        ]
        
        for chunk_count, has_speech, noise_level in patterns:
            for i in range(chunk_count):
                chunk = create_test_audio_chunk(
                    duration_ms=32,
                    has_speech=has_speech,
                    noise_level=noise_level
                )
                audio_sequence.append(chunk)
        
        logger.info(f"Created audio sequence with {len(audio_sequence)} chunks "
                   f"({len(audio_sequence) * 32}ms total)")
        
        # Segment the audio
        segments = await segmenter.segment_single_audio(audio_sequence)
        
        logger.info(f"\nDetected {len(segments)} speech segments:")
        
        for i, segment in enumerate(segments):
            logger.info(f"  Segment {i+1}:")
            logger.info(f"    Duration: {segment.duration_ms:.1f}ms")
            logger.info(f"    Chunks: {len(segment.audio_chunks)}")
            logger.info(f"    Average confidence: {segment.average_confidence:.3f}")
            logger.info(f"    Speech energy: {segment.speech_energy:.3f}")
            logger.info(f"    Start time: {segment.start_time.strftime('%H:%M:%S.%f')[:-3]}")
        
        # Get segmentation statistics
        seg_stats = segmenter.get_segmentation_stats()
        logger.info(f"\nSegmentation Statistics:")
        logger.info(f"  Segments detected: {seg_stats['segments_detected']}")
        logger.info(f"  Total speech duration: {seg_stats['total_speech_duration_ms']:.1f}ms")
        logger.info(f"  Average segment duration: {seg_stats['average_segment_duration_ms']:.1f}ms")
        logger.info(f"  Current adaptive threshold: {seg_stats['current_adaptive_threshold']:.3f}")
        logger.info(f"  Background noise level: {seg_stats['background_noise_level']:.3f}")
        
        # Analyze segments
        if segments:
            analysis = analyze_speech_segments(segments)
            logger.info(f"\nSegment Analysis:")
            logger.info(f"  Total segments: {analysis['total_segments']}")
            logger.info(f"  Duration range: {analysis['min_duration_ms']:.1f} - {analysis['max_duration_ms']:.1f}ms")
            logger.info(f"  Average confidence: {analysis['average_confidence']:.3f}")
            logger.info(f"  Segments per minute: {analysis['segments_per_minute']:.1f}")
        
        # Cleanup
        await segmenter.stop()
        await segmenter.cleanup()
        await vad_detector.stop()
        await vad_detector.cleanup()
        
    except Exception as e:
        logger.error(f"Segmentation example failed: {e}")
        logger.info("Note: This example requires the SileroVAD model to be properly installed")


async def streaming_vad_example():
    """Demonstrate real-time streaming VAD."""
    logger.info("\n=== Streaming VAD Example ===")
    
    config_manager = MockConfigManager()
    
    try:
        # Create VAD detector
        vad_detector = await create_vad_detector(
            threshold=0.5,
            config_manager=config_manager,
            enable_adaptive_threshold=True
        )
        
        # Create speech segmenter
        segmenter = await create_speech_segmenter(
            vad_detector=vad_detector,
            min_speech_duration_ms=250,
            min_silence_duration_ms=200,
            config_manager=config_manager
        )
        
        logger.info("Starting real-time audio stream simulation...")
        
        # Simulate real-time audio stream
        async def audio_stream():
            """Simulate real-time audio stream."""
            # Simulate a conversation with varying speech patterns
            for phase in range(3):
                logger.info(f"  Phase {phase + 1}: Generating audio...")
                
                # Each phase has different characteristics
                if phase == 0:
                    # Mostly silence with some speech
                    speech_probability = 0.3
                elif phase == 1:
                    # Balanced speech and silence
                    speech_probability = 0.6
                else:
                    # Mostly speech
                    speech_probability = 0.8
                
                for i in range(30):  # 30 chunks per phase
                    has_speech = np.random.random() < speech_probability
                    noise_level = 0.05 + np.random.random() * 0.1
                    
                    chunk = create_test_audio_chunk(
                        duration_ms=32,
                        has_speech=has_speech,
                        noise_level=noise_level
                    )
                    
                    yield chunk
                    
                    # Simulate real-time delay
                    await asyncio.sleep(0.01)
        
        # Process stream and collect segments
        segments = []
        segment_count = 0
        
        async for segment in segmenter.segment_audio_stream(audio_stream()):
            segment_count += 1
            segments.append(segment)
            
            logger.info(f"  Detected segment {segment_count}: "
                       f"{segment.duration_ms:.1f}ms, "
                       f"confidence: {segment.average_confidence:.3f}")
        
        logger.info(f"\nStreaming completed - detected {len(segments)} segments")
        
        # Final statistics
        vad_stats = vad_detector.get_detection_stats()
        seg_stats = segmenter.get_segmentation_stats()
        
        logger.info(f"Final VAD stats: {vad_stats['speech_rate']:.1%} speech rate")
        logger.info(f"Final segmentation: {seg_stats['segments_detected']} segments, "
                   f"{seg_stats['total_speech_duration_ms']:.1f}ms total speech")
        
        # Cleanup
        await segmenter.stop()
        await segmenter.cleanup()
        await vad_detector.stop()
        await vad_detector.cleanup()
        
    except Exception as e:
        logger.error(f"Streaming example failed: {e}")
        logger.info("Note: This example requires the SileroVAD model to be properly installed")


async def adaptive_threshold_example():
    """Demonstrate adaptive threshold functionality."""
    logger.info("\n=== Adaptive Threshold Example ===")
    
    config_manager = MockConfigManager()
    
    try:
        # Create VAD detector with adaptive threshold
        vad_config = VADConfig(
            threshold=0.5,
            enable_adaptive_threshold=True,
            noise_level_adaptation=True,
            threshold_adaptation_rate=0.2
        )
        
        vad_detector = SileroVADDetector(vad_config, config_manager)
        await vad_detector.initialize()
        await vad_detector.start()
        
        logger.info(f"Initial threshold: {vad_detector.current_threshold:.3f}")
        
        # Simulate changing noise conditions
        noise_scenarios = [
            ("Quiet environment", 0.02, 20),
            ("Normal environment", 0.08, 20),
            ("Noisy environment", 0.25, 20),
            ("Very noisy environment", 0.4, 20),
            ("Back to quiet", 0.03, 20)
        ]
        
        for scenario_name, noise_level, chunk_count in noise_scenarios:
            logger.info(f"\n{scenario_name} (noise level: {noise_level:.2f}):")
            
            threshold_history = []
            
            for i in range(chunk_count):
                # Mix of speech and non-speech
                has_speech = i % 3 == 0  # Every 3rd chunk has speech
                
                chunk = create_test_audio_chunk(
                    duration_ms=64,
                    has_speech=has_speech,
                    noise_level=noise_level
                )
                
                result = await vad_detector.detect_voice_activity_detailed(chunk)
                threshold_history.append(result.adapted_threshold)
                
                if i % 5 == 0:  # Log every 5th chunk
                    logger.info(f"  Chunk {i+1}: confidence={result.confidence:.3f}, "
                              f"threshold={result.adapted_threshold:.3f}, "
                              f"noise={result.background_noise_level:.3f}")
            
            # Show threshold adaptation
            initial_threshold = threshold_history[0] if threshold_history else 0
            final_threshold = threshold_history[-1] if threshold_history else 0
            logger.info(f"  Threshold adaptation: {initial_threshold:.3f} -> {final_threshold:.3f}")
        
        # Final statistics
        stats = vad_detector.get_detection_stats()
        logger.info(f"\nFinal Statistics:")
        logger.info(f"  Total detections: {stats['total_detections']}")
        logger.info(f"  Speech rate: {stats['speech_rate']:.2%}")
        logger.info(f"  Final threshold: {stats['current_threshold']:.3f}")
        logger.info(f"  Background noise: {stats['background_noise_level']:.3f}")
        
        # Cleanup
        await vad_detector.stop()
        await vad_detector.cleanup()
        
    except Exception as e:
        logger.error(f"Adaptive threshold example failed: {e}")
        logger.info("Note: This example requires the SileroVAD model to be properly installed")


async def performance_benchmark():
    """Benchmark VAD performance."""
    logger.info("\n=== Performance Benchmark ===")
    
    config_manager = MockConfigManager()
    
    try:
        # Create VAD detector
        vad_detector = await create_vad_detector(
            threshold=0.5,
            config_manager=config_manager
        )
        
        # Benchmark parameters
        chunk_count = 1000
        chunk_duration_ms = 32
        
        logger.info(f"Benchmarking with {chunk_count} chunks of {chunk_duration_ms}ms each...")
        
        # Create test audio chunks
        test_chunks = []
        for i in range(chunk_count):
            has_speech = i % 4 < 2  # 50% speech
            chunk = create_test_audio_chunk(
                duration_ms=chunk_duration_ms,
                has_speech=has_speech,
                noise_level=0.1
            )
            test_chunks.append(chunk)
        
        # Benchmark detection
        start_time = asyncio.get_event_loop().time()
        
        for chunk in test_chunks:
            await vad_detector.detect_voice_activity(chunk)
        
        end_time = asyncio.get_event_loop().time()
        
        # Calculate performance metrics
        total_time = end_time - start_time
        total_audio_duration = chunk_count * chunk_duration_ms / 1000  # seconds
        real_time_factor = total_audio_duration / total_time
        
        stats = vad_detector.get_detection_stats()
        
        logger.info(f"Benchmark Results:")
        logger.info(f"  Total processing time: {total_time:.3f}s")
        logger.info(f"  Total audio duration: {total_audio_duration:.3f}s")
        logger.info(f"  Real-time factor: {real_time_factor:.1f}x")
        logger.info(f"  Average processing time per chunk: {stats['average_processing_time_ms']:.3f}ms")
        logger.info(f"  Chunks per second: {chunk_count / total_time:.1f}")
        
        if real_time_factor > 1.0:
            logger.info(f"  ✓ Can process audio faster than real-time!")
        else:
            logger.info(f"  ⚠ Processing slower than real-time")
        
        # Cleanup
        await vad_detector.stop()
        await vad_detector.cleanup()
        
    except Exception as e:
        logger.error(f"Performance benchmark failed: {e}")
        logger.info("Note: This example requires the SileroVAD model to be properly installed")


async def main():
    """Run all VAD examples."""
    logger.info("Voice Activity Detection (VAD) Examples")
    logger.info("=" * 50)
    
    try:
        await basic_vad_example()
        await speech_segmentation_example()
        await streaming_vad_example()
        await adaptive_threshold_example()
        await performance_benchmark()
        
        logger.info("\n" + "=" * 50)
        logger.info("All VAD examples completed!")
        logger.info("\nNote: These examples use simulated audio data.")
        logger.info("For real audio processing, ensure SileroVAD model is properly installed.")
        
    except Exception as e:
        logger.error(f"Examples failed: {e}")
        raise


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())