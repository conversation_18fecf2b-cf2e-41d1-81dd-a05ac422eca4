# Production environment configuration
# Optimized settings for production deployment

logging:
  level: "WARNING"
  conversation_log_enabled: true
  include_audio: false  # Disable audio logging in production for privacy

performance:
  max_concurrent_calls: 50
  memory_limit_mb: 4096
  cpu_limit_percent: 70.0
  metrics_enabled: true
  model_cache_size: 5

audio:
  # More conservative VAD threshold for production
  vad_threshold: 0.6
  vad_max_silence_duration: 1.5
  
llm:
  # Production LLM settings
  timeout: 20
  max_retries: 5
  
tts:
  # Production TTS optimization
  cache_size: 200
  quality: "high"
  
script:
  # Less frequent reloading in production
  reload_interval: 30.0
  
telephony:
  # Production telephony settings
  max_call_duration: 900  # 15 minutes max
  call_timeout: 45