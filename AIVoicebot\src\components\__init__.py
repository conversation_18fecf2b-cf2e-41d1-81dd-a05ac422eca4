"""Components for the AI Voice Customer Service system."""

# Audio processing components
from .audio import (
    AudioPipeline,
    AudioBuffer,
    AudioProcessor,
    AudioConfig,
    AudioFormat,
    create_audio_pipeline,
    create_simple_processor
)

# Voice Activity Detection components (to be implemented)
# from .vad import (
#     SileroVADDetector, VADConfig, VADResult,
#     SpeechSegmenter, SegmentationConfig, SpeechSegment
# )

# Automatic Speech Recognition components (to be implemented)
# from .asr import (
#     SenseVoiceRecognizer, SenseVoiceConfig,
#     ASRProcessor, ASRProcessorConfig, ASRResult,
#     TranscriptionBuffer, BufferConfig
# )

__all__ = [
    # Audio processing
    'AudioPipeline',
    'AudioBuffer',
    'AudioProcessor',
    'AudioConfig',
    'AudioFormat',
    'create_audio_pipeline',
    'create_simple_processor',
    
    # Voice Activity Detection (to be implemented)
    # 'SileroVADDetector',
    # 'VADConfig',
    # 'VADResult',
    
    # Automatic Speech Recognition (to be implemented)
    # 'SenseVoiceRecognizer',
    # 'SenseVoiceConfig',
    # 'ASRProcessor',
]