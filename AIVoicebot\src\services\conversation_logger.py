"""
Comprehensive Conversation Logging Service

This module provides a comprehensive service for logging conversation transcripts,
metadata, analytics, and quality metrics to structured log files with correlation IDs.
"""

import logging
import json
import uuid
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import statistics

from ..core.base_component import BaseComponent
from ..core.interfaces import CallSession, ConversationTurn, CallSummary


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class EventType(Enum):
    """Event type enumeration."""
    SESSION_START = "session_start"
    SESSION_END = "session_end"
    CONVERSATION_TURN = "conversation_turn"
    AUDIO_RECEIVED = "audio_received"
    AUDIO_SENT = "audio_sent"
    TRANSCRIPTION = "transcription"
    INTENT_DETECTED = "intent_detected"
    RESPONSE_GENERATED = "response_generated"
    TTS_GENERATED = "tts_generated"
    ERROR_OCCURRED = "error_occurred"
    QUALITY_METRIC = "quality_metric"
    SYSTEM_EVENT = "system_event"


@dataclass
class LogEntry:
    """Structured log entry."""
    correlation_id: str
    session_id: str
    event_type: EventType
    timestamp: datetime
    level: LogLevel = LogLevel.INFO
    message: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "correlation_id": self.correlation_id,
            "session_id": self.session_id,
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "level": self.level.value,
            "message": self.message,
            "data": self.data,
            "metadata": self.metadata
        }


@dataclass
class QualityMetrics:
    """Quality metrics for conversation analysis."""
    session_id: str
    correlation_id: str
    timestamp: datetime = field(default_factory=datetime.now)

    # Response quality metrics
    response_time_ms: float = 0.0
    transcription_accuracy: float = 0.0
    intent_confidence: float = 0.0
    response_relevance: float = 0.0

    # Audio quality metrics
    audio_clarity: float = 0.0
    background_noise_level: float = 0.0
    volume_level: float = 0.0

    # Conversation flow metrics
    turn_taking_smoothness: float = 0.0
    interruption_count: int = 0
    silence_duration_ms: float = 0.0

    # System performance metrics
    cpu_usage_percent: float = 0.0
    memory_usage_mb: float = 0.0
    network_latency_ms: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "session_id": self.session_id,
            "correlation_id": self.correlation_id,
            "timestamp": self.timestamp.isoformat(),
            "response_time_ms": self.response_time_ms,
            "transcription_accuracy": self.transcription_accuracy,
            "intent_confidence": self.intent_confidence,
            "response_relevance": self.response_relevance,
            "audio_clarity": self.audio_clarity,
            "background_noise_level": self.background_noise_level,
            "volume_level": self.volume_level,
            "turn_taking_smoothness": self.turn_taking_smoothness,
            "interruption_count": self.interruption_count,
            "silence_duration_ms": self.silence_duration_ms,
            "cpu_usage_percent": self.cpu_usage_percent,
            "memory_usage_mb": self.memory_usage_mb,
            "network_latency_ms": self.network_latency_ms
        }


@dataclass
class ConversationAnalytics:
    """Analytics data for conversation analysis."""
    session_id: str
    total_turns: int = 0
    total_duration_seconds: float = 0.0
    average_response_time_ms: float = 0.0
    intent_distribution: Dict[str, int] = field(default_factory=dict)
    sentiment_scores: List[float] = field(default_factory=list)
    topic_changes: int = 0
    escalation_triggers: List[str] = field(default_factory=list)
    customer_satisfaction_score: Optional[float] = None
    resolution_status: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "session_id": self.session_id,
            "total_turns": self.total_turns,
            "total_duration_seconds": self.total_duration_seconds,
            "average_response_time_ms": self.average_response_time_ms,
            "intent_distribution": self.intent_distribution,
            "sentiment_scores": self.sentiment_scores,
            "average_sentiment": statistics.mean(self.sentiment_scores) if self.sentiment_scores else 0.0,
            "topic_changes": self.topic_changes,
            "escalation_triggers": self.escalation_triggers,
            "customer_satisfaction_score": self.customer_satisfaction_score,
            "resolution_status": self.resolution_status
        }


class ConversationLogger(BaseComponent):
    """
    Comprehensive conversation logging service with structured logging,
    correlation IDs, quality metrics, and analytics.
    """

    def __init__(self, config_manager, logger=None):
        super().__init__("conversation_logger", config_manager, logger)

        # Configuration
        self.log_dir = Path(self.config_manager.get_config("logging.conversation_log_path", "logs/conversations"))
        self.metrics_dir = Path(self.config_manager.get_config("logging.metrics_log_path", "logs/metrics"))
        self.analytics_dir = Path(self.config_manager.get_config("logging.analytics_log_path", "logs/analytics"))

        # Create directories
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.metrics_dir.mkdir(parents=True, exist_ok=True)
        self.analytics_dir.mkdir(parents=True, exist_ok=True)

        # In-memory storage for analytics
        self.session_analytics: Dict[str, ConversationAnalytics] = {}
        self.session_metrics: Dict[str, List[QualityMetrics]] = defaultdict(list)
        self.correlation_map: Dict[str, str] = {}  # correlation_id -> session_id

        # Configuration options
        self.enable_structured_logging = self.config_manager.get_config("logging.enable_structured", True)
        self.enable_quality_metrics = self.config_manager.get_config("logging.enable_quality_metrics", True)
        self.enable_analytics = self.config_manager.get_config("logging.enable_analytics", True)
        self.max_log_file_size_mb = self.config_manager.get_config("logging.max_file_size_mb", 100)
        self.log_retention_days = self.config_manager.get_config("logging.retention_days", 30)

        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._analytics_task: Optional[asyncio.Task] = None

    async def _initialize_impl(self) -> None:
        self._log.info(f"Comprehensive conversation logger initialized")
        self._log.info(f"Conversation logs: {self.log_dir}")
        self._log.info(f"Quality metrics: {self.metrics_dir}")
        self._log.info(f"Analytics: {self.analytics_dir}")
        self._log.info(f"Structured logging: {self.enable_structured_logging}")
        self._log.info(f"Quality metrics: {self.enable_quality_metrics}")
        self._log.info(f"Analytics: {self.enable_analytics}")

    async def _start_impl(self) -> None:
        """Start background tasks."""
        if self.log_retention_days > 0:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

        if self.enable_analytics:
            self._analytics_task = asyncio.create_task(self._periodic_analytics_processing())

    async def _stop_impl(self) -> None:
        """Stop background tasks."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        if self._analytics_task:
            self._analytics_task.cancel()
            try:
                await self._analytics_task
            except asyncio.CancelledError:
                pass

        # Final analytics processing
        if self.enable_analytics:
            await self._process_pending_analytics()

    async def _cleanup_impl(self) -> None:
        """Cleanup resources."""
        self.session_analytics.clear()
        self.session_metrics.clear()
        self.correlation_map.clear()

    # Core Logging Methods

    def generate_correlation_id(self, session_id: str) -> str:
        """Generate a new correlation ID for tracking related events."""
        correlation_id = str(uuid.uuid4())
        self.correlation_map[correlation_id] = session_id
        return correlation_id

    async def log_structured_event(
        self,
        correlation_id: str,
        session_id: str,
        event_type: EventType,
        message: str = "",
        level: LogLevel = LogLevel.INFO,
        data: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a structured event with correlation ID."""
        if not self.enable_structured_logging:
            return

        log_entry = LogEntry(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=event_type,
            timestamp=datetime.now(),
            level=level,
            message=message,
            data=data or {},
            metadata=metadata or {}
        )

        await self._write_structured_log(log_entry)

    async def log_quality_metrics(self, metrics: QualityMetrics) -> None:
        """Log quality metrics for analysis."""
        if not self.enable_quality_metrics:
            return

        # Store in memory for analytics
        self.session_metrics[metrics.session_id].append(metrics)

        # Write to metrics log
        await self._write_metrics_log(metrics)

    async def log_transcription(
        self,
        correlation_id: str,
        session_id: str,
        audio_duration_ms: float,
        transcribed_text: str,
        confidence: float,
        processing_time_ms: float,
        language: str = "zh-CN"
    ) -> None:
        """Log transcription event with detailed metadata."""
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.TRANSCRIPTION,
            message=f"Transcribed {audio_duration_ms}ms of audio",
            data={
                "transcribed_text": transcribed_text,
                "confidence": confidence,
                "audio_duration_ms": audio_duration_ms,
                "processing_time_ms": processing_time_ms,
                "language": language,
                "text_length": len(transcribed_text),
                "words_per_minute": len(transcribed_text.split()) * 60000 / audio_duration_ms if audio_duration_ms > 0 else 0
            }
        )

    async def log_intent_detection(
        self,
        correlation_id: str,
        session_id: str,
        user_input: str,
        detected_intent: str,
        confidence: float,
        entities: Dict[str, Any],
        processing_time_ms: float
    ) -> None:
        """Log intent detection event."""
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.INTENT_DETECTED,
            message=f"Detected intent: {detected_intent} (confidence: {confidence:.2f})",
            data={
                "user_input": user_input,
                "detected_intent": detected_intent,
                "confidence": confidence,
                "entities": entities,
                "processing_time_ms": processing_time_ms,
                "input_length": len(user_input)
            }
        )

    async def log_response_generation(
        self,
        correlation_id: str,
        session_id: str,
        user_input: str,
        generated_response: str,
        processing_time_ms: float,
        model_used: str,
        prompt_tokens: int = 0,
        completion_tokens: int = 0
    ) -> None:
        """Log response generation event."""
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.RESPONSE_GENERATED,
            message=f"Generated response using {model_used}",
            data={
                "user_input": user_input,
                "generated_response": generated_response,
                "processing_time_ms": processing_time_ms,
                "model_used": model_used,
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": prompt_tokens + completion_tokens,
                "response_length": len(generated_response)
            }
        )

    async def log_tts_generation(
        self,
        correlation_id: str,
        session_id: str,
        text: str,
        audio_duration_ms: float,
        processing_time_ms: float,
        voice_model: str,
        audio_format: str
    ) -> None:
        """Log TTS generation event."""
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.TTS_GENERATED,
            message=f"Generated {audio_duration_ms}ms of audio using {voice_model}",
            data={
                "text": text,
                "audio_duration_ms": audio_duration_ms,
                "processing_time_ms": processing_time_ms,
                "voice_model": voice_model,
                "audio_format": audio_format,
                "text_length": len(text),
                "synthesis_speed": len(text) / (processing_time_ms / 1000) if processing_time_ms > 0 else 0
            }
        )

    async def log_error(
        self,
        correlation_id: str,
        session_id: str,
        error_type: str,
        error_message: str,
        stack_trace: Optional[str] = None,
        component: Optional[str] = None,
        recovery_action: Optional[str] = None
    ) -> None:
        """Log error event with detailed information."""
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.ERROR_OCCURRED,
            message=f"Error in {component or 'unknown'}: {error_message}",
            level=LogLevel.ERROR,
            data={
                "error_type": error_type,
                "error_message": error_message,
                "stack_trace": stack_trace,
                "component": component,
                "recovery_action": recovery_action
            }
        )

    # Analytics Methods

    def start_session_analytics(self, session_id: str) -> None:
        """Initialize analytics tracking for a session."""
        if not self.enable_analytics:
            return

        self.session_analytics[session_id] = ConversationAnalytics(session_id=session_id)

    def update_session_analytics(
        self,
        session_id: str,
        intent: Optional[str] = None,
        sentiment_score: Optional[float] = None,
        response_time_ms: Optional[float] = None,
        topic_change: bool = False,
        escalation_trigger: Optional[str] = None
    ) -> None:
        """Update analytics data for a session."""
        if not self.enable_analytics or session_id not in self.session_analytics:
            return

        analytics = self.session_analytics[session_id]

        if intent:
            analytics.intent_distribution[intent] = analytics.intent_distribution.get(intent, 0) + 1

        if sentiment_score is not None:
            analytics.sentiment_scores.append(sentiment_score)

        if response_time_ms is not None:
            # Update average response time
            total_time = analytics.average_response_time_ms * analytics.total_turns
            analytics.total_turns += 1
            analytics.average_response_time_ms = (total_time + response_time_ms) / analytics.total_turns

        if topic_change:
            analytics.topic_changes += 1

        if escalation_trigger:
            analytics.escalation_triggers.append(escalation_trigger)

    def finalize_session_analytics(
        self,
        session_id: str,
        duration_seconds: float,
        customer_satisfaction_score: Optional[float] = None,
        resolution_status: Optional[str] = None
    ) -> Optional[ConversationAnalytics]:
        """Finalize analytics for a completed session."""
        if not self.enable_analytics or session_id not in self.session_analytics:
            return None

        analytics = self.session_analytics[session_id]
        analytics.total_duration_seconds = duration_seconds
        analytics.customer_satisfaction_score = customer_satisfaction_score
        analytics.resolution_status = resolution_status

        return analytics

    async def get_session_analytics(self, session_id: str) -> Optional[ConversationAnalytics]:
        """Get analytics data for a session."""
        return self.session_analytics.get(session_id)

    async def get_quality_metrics_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get quality metrics summary for a session."""
        if session_id not in self.session_metrics:
            return None

        metrics_list = self.session_metrics[session_id]
        if not metrics_list:
            return None

        # Calculate averages
        avg_response_time = statistics.mean([m.response_time_ms for m in metrics_list])
        avg_transcription_accuracy = statistics.mean([m.transcription_accuracy for m in metrics_list if m.transcription_accuracy > 0])
        avg_intent_confidence = statistics.mean([m.intent_confidence for m in metrics_list if m.intent_confidence > 0])
        avg_audio_clarity = statistics.mean([m.audio_clarity for m in metrics_list if m.audio_clarity > 0])

        return {
            "session_id": session_id,
            "total_metrics_count": len(metrics_list),
            "average_response_time_ms": avg_response_time,
            "average_transcription_accuracy": avg_transcription_accuracy if avg_transcription_accuracy else 0.0,
            "average_intent_confidence": avg_intent_confidence if avg_intent_confidence else 0.0,
            "average_audio_clarity": avg_audio_clarity if avg_audio_clarity else 0.0,
            "total_interruptions": sum([m.interruption_count for m in metrics_list]),
            "average_cpu_usage": statistics.mean([m.cpu_usage_percent for m in metrics_list if m.cpu_usage_percent > 0]),
            "average_memory_usage": statistics.mean([m.memory_usage_mb for m in metrics_list if m.memory_usage_mb > 0]),
            "average_network_latency": statistics.mean([m.network_latency_ms for m in metrics_list if m.network_latency_ms > 0])
        }

    async def log_session_start(self, session: CallSession) -> str:
        """Log the start of a new conversation session."""
        correlation_id = self.generate_correlation_id(session.session_id)

        # Start analytics tracking
        self.start_session_analytics(session.session_id)

        # Log structured event
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session.session_id,
            event_type=EventType.SESSION_START,
            message=f"Session started for {session.phone_number}",
            data={
                "phone_number": session.phone_number,
                "script_id": session.script_id,
                "customer_info": session.conversation_context.customer_info if hasattr(session, 'conversation_context') else {}
            },
            metadata={
                "start_time": session.start_time.isoformat(),
                "call_id": getattr(session, 'call_id', None)
            }
        )

        # Legacy format for backward compatibility
        if not self.enable_structured_logging:
            log_entry = {
                "event": "session_start",
                "session_id": session.session_id,
                "timestamp": session.start_time.isoformat(),
                "phone_number": session.phone_number,
                "script_id": session.script_id,
                "customer_info": session.conversation_context.customer_info if hasattr(session, 'conversation_context') else {}
            }
            await self._write_log(session.session_id, log_entry)

        return correlation_id

    async def log_turn(self, session_id: str, turn: ConversationTurn, correlation_id: Optional[str] = None) -> None:
        """Log a single conversation turn."""
        if not correlation_id:
            correlation_id = self.generate_correlation_id(session_id)

        # Update analytics
        self.update_session_analytics(
            session_id=session_id,
            intent=turn.user_intent.value if turn.user_intent else None,
            response_time_ms=getattr(turn, 'processing_time_ms', 0)
        )

        # Log structured event
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.CONVERSATION_TURN,
            message=f"Conversation turn {turn.turn_id}",
            data={
                "turn_id": turn.turn_id,
                "speaker": getattr(turn, 'speaker', 'user'),
                "user_input": turn.user_input,
                "final_response": turn.final_response,
                "intent": turn.user_intent.value if turn.user_intent else None,
                "confidence": turn.confidence_score,
                "processing_time_ms": getattr(turn, 'processing_time_ms', 0)
            },
            metadata={
                "start_time": getattr(turn, 'start_time', datetime.now()).isoformat(),
                "turn_duration_ms": getattr(turn, 'duration_ms', 0)
            }
        )

        # Legacy format for backward compatibility
        if not self.enable_structured_logging:
            log_entry = {
                "event": "conversation_turn",
                "session_id": session_id,
                "turn_id": turn.turn_id,
                "timestamp": getattr(turn, 'start_time', datetime.now()).isoformat(),
                "speaker": getattr(turn, 'speaker', 'user'),
                "user_input": turn.user_input,
                "final_response": turn.final_response,
                "intent": turn.user_intent.value if turn.user_intent else None,
                "confidence": turn.confidence_score,
                "processing_time_ms": getattr(turn, 'processing_time_ms', 0)
            }
            await self._write_log(session_id, log_entry)

    async def log_session_end(self, summary: CallSummary, correlation_id: Optional[str] = None) -> None:
        """Log the end of a conversation session with its summary."""
        if not correlation_id:
            correlation_id = self.generate_correlation_id(summary.session_id)

        # Finalize analytics
        analytics = self.finalize_session_analytics(
            session_id=summary.session_id,
            duration_seconds=summary.duration,
            customer_satisfaction_score=getattr(summary, 'customer_satisfaction_score', None),
            resolution_status=getattr(summary, 'resolution_status', None)
        )

        # Log structured event
        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=summary.session_id,
            event_type=EventType.SESSION_END,
            message=f"Session ended after {summary.duration:.1f}s with {summary.total_turns} turns",
            data={
                "duration_seconds": summary.duration,
                "total_turns": summary.total_turns,
                "final_intent": summary.final_intent,
                "success": summary.success,
                "analytics": analytics.to_dict() if analytics else None
            },
            metadata={
                "end_time": summary.end_time.isoformat()
            }
        )

        # Write analytics to separate file
        if analytics:
            await self._write_analytics_log(analytics)

        # Legacy format for backward compatibility
        if not self.enable_structured_logging:
            log_entry = {
                "event": "session_end",
                "session_id": summary.session_id,
                "timestamp": summary.end_time.isoformat(),
                "duration_seconds": summary.duration,
                "total_turns": summary.total_turns,
                "final_intent": summary.final_intent,
                "success": summary.success
            }
            await self._write_log(summary.session_id, log_entry)

        # Clean up session data
        self.session_analytics.pop(summary.session_id, None)
        # Keep metrics for a while for analysis
        # self.session_metrics.pop(summary.session_id, None)

    async def log_event(self, session_id: str, event_name: str, details: Dict[str, Any], correlation_id: Optional[str] = None) -> None:
        """Log a generic event related to a session."""
        if not correlation_id:
            correlation_id = self.generate_correlation_id(session_id)

        await self.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.SYSTEM_EVENT,
            message=f"System event: {event_name}",
            data=details
        )

        # Legacy format for backward compatibility
        if not self.enable_structured_logging:
            log_entry = {
                "event": event_name,
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "details": details
            }
            await self._write_log(session_id, log_entry)

    # Private Helper Methods

    async def _write_structured_log(self, log_entry: LogEntry) -> None:
        """Write a structured log entry to file."""
        try:
            log_file = self.log_dir / f"{log_entry.session_id}_structured.jsonl"
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry.to_dict(), ensure_ascii=False) + "\n")
        except Exception as e:
            self._log.error(f"Failed to write structured log for session {log_entry.session_id}: {e}")

    async def _write_metrics_log(self, metrics: QualityMetrics) -> None:
        """Write quality metrics to file."""
        try:
            metrics_file = self.metrics_dir / f"{metrics.session_id}_metrics.jsonl"
            with open(metrics_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(metrics.to_dict(), ensure_ascii=False) + "\n")
        except Exception as e:
            self._log.error(f"Failed to write metrics log for session {metrics.session_id}: {e}")

    async def _write_analytics_log(self, analytics: ConversationAnalytics) -> None:
        """Write analytics data to file."""
        try:
            analytics_file = self.analytics_dir / f"{analytics.session_id}_analytics.json"
            with open(analytics_file, "w", encoding="utf-8") as f:
                json.dump(analytics.to_dict(), f, ensure_ascii=False, indent=2)
        except Exception as e:
            self._log.error(f"Failed to write analytics log for session {analytics.session_id}: {e}")

    async def _write_log(self, session_id: str, log_entry: Dict[str, Any]) -> None:
        """Write a legacy log entry to the appropriate file."""
        try:
            log_file = self.log_dir / f"{session_id}.jsonl"
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        except Exception as e:
            self._log.error(f"Failed to write to conversation log for session {session_id}: {e}")

    async def _periodic_cleanup(self) -> None:
        """Periodic cleanup of old log files."""
        while True:
            try:
                await asyncio.sleep(24 * 3600)  # Run daily

                cutoff_date = datetime.now() - timedelta(days=self.log_retention_days)

                # Clean up old log files
                for log_dir in [self.log_dir, self.metrics_dir, self.analytics_dir]:
                    for log_file in log_dir.glob("*.jsonl"):
                        if log_file.stat().st_mtime < cutoff_date.timestamp():
                            log_file.unlink()
                            self._log.info(f"Deleted old log file: {log_file}")

                self._log.info("Periodic log cleanup completed")

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in periodic cleanup: {e}")

    async def _periodic_analytics_processing(self) -> None:
        """Periodic processing of analytics data."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run hourly
                await self._process_pending_analytics()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in periodic analytics processing: {e}")

    async def _process_pending_analytics(self) -> None:
        """Process any pending analytics data."""
        # This could include aggregating metrics, generating reports, etc.
        # For now, just log the current state
        active_sessions = len(self.session_analytics)
        total_metrics = sum(len(metrics) for metrics in self.session_metrics.values())

        self._log.info(f"Analytics status: {active_sessions} active sessions, {total_metrics} total metrics")
