# Alert rules for AI Voice Customer Service
groups:
  - name: aivoice.system
    rules:
      - alert: SystemDown
        expr: up{job="aivoice-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "AI Voice Customer Service is down"
          description: "AI Voice Customer Service has been down for more than 1 minute."

      - alert: HighCPUUsage
        expr: aivoice_cpu_utilization > 80
        for: 5m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 5 minutes."

      - alert: CriticalCPUUsage
        expr: aivoice_cpu_utilization > 95
        for: 2m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 2 minutes."

      - alert: HighMemoryUsage
        expr: aivoice_memory_utilization > 85
        for: 5m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for more than 5 minutes."

      - alert: CriticalMemoryUsage
        expr: aivoice_memory_utilization > 95
        for: 2m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical memory usage detected"
          description: "Memory usage is {{ $value }}% for more than 2 minutes."

  - name: aivoice.performance
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, aivoice_response_time_seconds_bucket) > 2
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for more than 3 minutes."

      - alert: CriticalResponseTime
        expr: histogram_quantile(0.95, aivoice_response_time_seconds_bucket) > 5
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical response time detected"
          description: "95th percentile response time is {{ $value }}s for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(aivoice_errors_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors/sec for more than 3 minutes."

      - alert: CriticalErrorRate
        expr: rate(aivoice_errors_total[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical error rate detected"
          description: "Error rate is {{ $value }} errors/sec for more than 1 minute."

  - name: aivoice.calls
    rules:
      - alert: LowCallSuccessRate
        expr: rate(aivoice_calls_completed_total[5m]) / rate(aivoice_calls_started_total[5m]) < 0.9
        for: 5m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "Low call success rate"
          description: "Call success rate is {{ $value | humanizePercentage }} for more than 5 minutes."

      - alert: CriticalCallSuccessRate
        expr: rate(aivoice_calls_completed_total[5m]) / rate(aivoice_calls_started_total[5m]) < 0.8
        for: 2m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical call success rate"
          description: "Call success rate is {{ $value | humanizePercentage }} for more than 2 minutes."

      - alert: HighActiveCalls
        expr: aivoice_active_calls > 100
        for: 5m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High number of active calls"
          description: "Number of active calls is {{ $value }} for more than 5 minutes."

      - alert: CriticalActiveCalls
        expr: aivoice_active_calls > 200
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical number of active calls"
          description: "Number of active calls is {{ $value }} for more than 1 minute."

  - name: aivoice.audio
    rules:
      - alert: HighAudioProcessingLatency
        expr: aivoice_audio_processing_latency_ms > 500
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High audio processing latency"
          description: "Audio processing latency is {{ $value }}ms for more than 3 minutes."

      - alert: CriticalAudioProcessingLatency
        expr: aivoice_audio_processing_latency_ms > 1000
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical audio processing latency"
          description: "Audio processing latency is {{ $value }}ms for more than 1 minute."

      - alert: AudioProcessingErrors
        expr: rate(aivoice_audio_processing_errors_total[5m]) > 0.01
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "Audio processing errors detected"
          description: "Audio processing error rate is {{ $value }} errors/sec for more than 3 minutes."

  - name: aivoice.models
    rules:
      - alert: LowASRAccuracy
        expr: aivoice_asr_accuracy < 0.9
        for: 5m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "Low ASR accuracy"
          description: "ASR accuracy is {{ $value | humanizePercentage }} for more than 5 minutes."

      - alert: CriticalASRAccuracy
        expr: aivoice_asr_accuracy < 0.8
        for: 2m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical ASR accuracy"
          description: "ASR accuracy is {{ $value | humanizePercentage }} for more than 2 minutes."

      - alert: LowLLMResponseQuality
        expr: aivoice_llm_response_quality < 0.85
        for: 5m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "Low LLM response quality"
          description: "LLM response quality is {{ $value | humanizePercentage }} for more than 5 minutes."

      - alert: HighModelLatency
        expr: aivoice_model_inference_latency_ms > 1000
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High model inference latency"
          description: "Model inference latency is {{ $value }}ms for more than 3 minutes."

  - name: aivoice.components
    rules:
      - alert: ComponentUnhealthy
        expr: aivoice_component_health == 0
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Component unhealthy"
          description: "Component {{ $labels.component }} is unhealthy for more than 1 minute."

      - alert: ComponentStartupFailure
        expr: increase(aivoice_component_startup_failures_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Component startup failure"
          description: "Component {{ $labels.component }} failed to start."

  - name: aivoice.database
    rules:
      - alert: DatabaseConnectionFailure
        expr: aivoice_database_connections_failed_total > 0
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Database connection failure"
          description: "Database connection failures detected."

      - alert: HighDatabaseLatency
        expr: aivoice_database_query_latency_ms > 1000
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "High database latency"
          description: "Database query latency is {{ $value }}ms for more than 3 minutes."

  - name: aivoice.external
    rules:
      - alert: ExternalAPIFailure
        expr: rate(aivoice_external_api_failures_total[5m]) > 0.01
        for: 3m
        labels:
          severity: warning
          service: aivoice
        annotations:
          summary: "External API failures"
          description: "External API failure rate is {{ $value }} failures/sec for more than 3 minutes."

      - alert: CriticalExternalAPIFailure
        expr: rate(aivoice_external_api_failures_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
          service: aivoice
        annotations:
          summary: "Critical external API failures"
          description: "External API failure rate is {{ $value }} failures/sec for more than 1 minute."
