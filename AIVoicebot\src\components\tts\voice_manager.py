"""
Voice management and optimization system.

This module provides consistent voice characteristics, speech parameter adjustment
based on context, and audio quality optimization for network transmission.
"""

import asyncio
import logging
import io
import json
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
import hashlib

try:
    import pydub
    from pydub import AudioSegment
    from pydub.effects import normalize, compress_dynamic_range
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    # Create placeholder class for type hints
    class AudioSegment:
        pass

from ...core.base_component import BaseComponent
from ...core.interfaces import AudioProcessingError
from .edge_tts_generator import EdgeTTSGenerator, TTSR<PERSON>ult, VoiceGender, AudioFormat


class VoicePersonality(Enum):
    """Voice personality types for different scenarios."""
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"
    WARM = "warm"
    AUTHORITATIVE = "authoritative"
    EMPATHETIC = "empathetic"
    ENERGETIC = "energetic"


class SpeechContext(Enum):
    """Speech context types for parameter adjustment."""
    GREETING = "greeting"
    EXPLANATION = "explanation"
    CONFIRMATION = "confirmation"
    APOLOGY = "apology"
    CLOSING = "closing"
    URGENT = "urgent"
    SENSITIVE = "sensitive"
    PROMOTIONAL = "promotional"


class CompressionLevel(Enum):
    """Audio compression levels for network transmission."""
    NONE = "none"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    MAXIMUM = "maximum"


@dataclass
class VoiceProfile:
    """Voice profile configuration."""
    name: str
    voice_id: str
    personality: VoicePersonality
    gender: VoiceGender
    
    # Base parameters
    base_speed: str = "+0%"
    base_pitch: str = "+0Hz"
    base_volume: str = "+0%"
    
    # Context adjustments
    context_adjustments: Dict[SpeechContext, Dict[str, str]] = field(default_factory=dict)
    
    # Quality settings
    preferred_sample_rate: int = 16000
    preferred_format: AudioFormat = AudioFormat.WAV
    
    # Personality traits (0.0 to 1.0)
    warmth: float = 0.5
    authority: float = 0.5
    energy: float = 0.5
    clarity: float = 0.8
    
    # Usage statistics
    usage_count: int = 0
    last_used: Optional[datetime] = None
    
    def get_parameters_for_context(self, context: SpeechContext) -> Dict[str, str]:
        """Get voice parameters adjusted for specific context."""
        # Start with base parameters
        params = {
            "speed": self.base_speed,
            "pitch": self.base_pitch,
            "volume": self.base_volume
        }
        
        # Apply context adjustments
        if context in self.context_adjustments:
            params.update(self.context_adjustments[context])
        
        return params


@dataclass
class AudioOptimizationSettings:
    """Audio optimization settings."""
    # Compression settings
    compression_level: CompressionLevel = CompressionLevel.MEDIUM
    target_bitrate_kbps: int = 64
    
    # Quality settings
    normalize_volume: bool = True
    dynamic_range_compression: bool = True
    noise_reduction: bool = True
    
    # Network optimization
    optimize_for_streaming: bool = True
    chunk_size_ms: int = 100
    buffer_size_ms: int = 500
    
    # Telephony optimization
    telephony_mode: bool = False
    frequency_range_hz: Tuple[int, int] = (300, 3400)
    
    # Adaptive settings
    adaptive_bitrate: bool = True
    quality_threshold: float = 0.7


@dataclass
class VoiceManagerConfig:
    """Configuration for voice manager."""
    # Default settings
    default_personality: VoicePersonality = VoicePersonality.PROFESSIONAL
    default_gender: VoiceGender = VoiceGender.FEMALE
    
    # Voice profiles
    voice_profiles_file: str = "voice_profiles.json"
    auto_save_profiles: bool = True
    
    # Optimization settings
    default_optimization: AudioOptimizationSettings = field(default_factory=AudioOptimizationSettings)
    enable_adaptive_optimization: bool = True
    
    # Context awareness
    enable_context_adaptation: bool = True
    context_learning: bool = True
    
    # Performance settings
    cache_optimized_audio: bool = True
    cache_size_mb: int = 50
    
    # Quality monitoring
    enable_quality_monitoring: bool = True
    quality_feedback_learning: bool = True


class VoiceManager(BaseComponent):
    """
    Voice management and optimization system.
    
    Provides consistent voice characteristics, context-aware parameter adjustment,
    and audio quality optimization for network transmission.
    """
    
    def __init__(self, config: VoiceManagerConfig, tts_generator: EdgeTTSGenerator, 
                 config_manager, logger=None):
        """
        Initialize voice manager.
        
        Args:
            config: Voice manager configuration
            tts_generator: EdgeTTS generator instance
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("voice_manager", config_manager, logger)
        
        if not PYDUB_AVAILABLE:
            raise AudioProcessingError("Pydub not available. Install with: pip install pydub")
        
        self.config = config
        self.tts_generator = tts_generator
        
        # Voice profiles
        self.voice_profiles: Dict[str, VoiceProfile] = {}
        self.active_profile: Optional[VoiceProfile] = None
        
        # Optimization cache
        self._optimization_cache: Dict[str, bytes] = {}
        
        # Context learning
        self._context_usage: Dict[SpeechContext, int] = {}
        self._context_feedback: Dict[SpeechContext, List[float]] = {}
        
        # Quality monitoring
        self._quality_scores: List[float] = []
        self._optimization_stats: Dict[str, Any] = {}
        
        # Load default voice profiles
        self._create_default_profiles() 
   
    async def _initialize_impl(self) -> None:
        """Initialize voice manager."""
        self._log.info("Initializing voice manager...")
        
        # Load voice profiles from file
        await self._load_voice_profiles()
        
        # Set default active profile
        if not self.active_profile and self.voice_profiles:
            self.active_profile = self._select_default_profile()
        
        # Initialize optimization cache
        if self.config.cache_optimized_audio:
            self._optimization_cache.clear()
        
        self._log.info(f"Voice manager initialized with {len(self.voice_profiles)} profiles")
    
    async def _start_impl(self) -> None:
        """Start voice manager."""
        self._log.info("Starting voice manager...")
        
        # Reset statistics
        self._context_usage.clear()
        self._context_feedback.clear()
        self._quality_scores.clear()
        self._optimization_stats.clear()
        
        self._log.info("Voice manager started")
    
    async def _stop_impl(self) -> None:
        """Stop voice manager."""
        self._log.info("Stopping voice manager...")
        
        # Save voice profiles if auto-save is enabled
        if self.config.auto_save_profiles:
            await self._save_voice_profiles()
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("Voice manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup voice manager resources."""
        self._log.info("Cleaning up voice manager...")
        
        # Clear caches
        self._optimization_cache.clear()
        
        self._log.info("Voice manager cleanup completed")
    
    def _create_default_profiles(self) -> None:
        """Create default voice profiles."""
        # Professional female profile
        professional_female = VoiceProfile(
            name="Professional Female",
            voice_id="zh-CN-XiaoxiaoNeural",
            personality=VoicePersonality.PROFESSIONAL,
            gender=VoiceGender.FEMALE,
            base_speed="-5%",
            base_pitch="+0Hz",
            base_volume="+0%",
            warmth=0.6,
            authority=0.8,
            energy=0.5,
            clarity=0.9
        )
        
        # Add context adjustments for professional female
        professional_female.context_adjustments = {
            SpeechContext.GREETING: {"speed": "+5%", "pitch": "+10Hz", "volume": "+5%"},
            SpeechContext.EXPLANATION: {"speed": "-10%", "pitch": "+0Hz", "volume": "+5%"},
            SpeechContext.APOLOGY: {"speed": "-15%", "pitch": "-10Hz", "volume": "-5%"},
            SpeechContext.CLOSING: {"speed": "-5%", "pitch": "-5Hz", "volume": "+0%"},
            SpeechContext.URGENT: {"speed": "+10%", "pitch": "+20Hz", "volume": "+10%"}
        }
        
        # Friendly female profile
        friendly_female = VoiceProfile(
            name="Friendly Female",
            voice_id="zh-CN-XiaoyiNeural",
            personality=VoicePersonality.FRIENDLY,
            gender=VoiceGender.FEMALE,
            base_speed="+5%",
            base_pitch="+15Hz",
            base_volume="+0%",
            warmth=0.9,
            authority=0.5,
            energy=0.8,
            clarity=0.8
        )
        
        friendly_female.context_adjustments = {
            SpeechContext.GREETING: {"speed": "+10%", "pitch": "+20Hz", "volume": "+5%"},
            SpeechContext.EXPLANATION: {"speed": "+0%", "pitch": "+10Hz", "volume": "+0%"},
            SpeechContext.APOLOGY: {"speed": "-10%", "pitch": "+0Hz", "volume": "-5%"},
            SpeechContext.CLOSING: {"speed": "+5%", "pitch": "+10Hz", "volume": "+0%"},
            SpeechContext.PROMOTIONAL: {"speed": "+15%", "pitch": "+25Hz", "volume": "+10%"}
        }
        
        # Professional male profile
        professional_male = VoiceProfile(
            name="Professional Male",
            voice_id="zh-CN-YunxiNeural",
            personality=VoicePersonality.PROFESSIONAL,
            gender=VoiceGender.MALE,
            base_speed="-5%",
            base_pitch="-10Hz",
            base_volume="+0%",
            warmth=0.5,
            authority=0.9,
            energy=0.6,
            clarity=0.9
        )
        
        professional_male.context_adjustments = {
            SpeechContext.GREETING: {"speed": "+0%", "pitch": "+0Hz", "volume": "+5%"},
            SpeechContext.EXPLANATION: {"speed": "-10%", "pitch": "-5Hz", "volume": "+5%"},
            SpeechContext.APOLOGY: {"speed": "-20%", "pitch": "-15Hz", "volume": "-5%"},
            SpeechContext.CLOSING: {"speed": "-10%", "pitch": "-10Hz", "volume": "+0%"},
            SpeechContext.AUTHORITATIVE: {"speed": "-15%", "pitch": "-20Hz", "volume": "+10%"}
        }
        
        # Warm female profile
        warm_female = VoiceProfile(
            name="Warm Female",
            voice_id="zh-CN-XiaomoNeural",
            personality=VoicePersonality.WARM,
            gender=VoiceGender.FEMALE,
            base_speed="-10%",
            base_pitch="+5Hz",
            base_volume="-5%",
            warmth=0.95,
            authority=0.4,
            energy=0.6,
            clarity=0.8
        )
        
        warm_female.context_adjustments = {
            SpeechContext.GREETING: {"speed": "-5%", "pitch": "+10Hz", "volume": "+0%"},
            SpeechContext.EXPLANATION: {"speed": "-15%", "pitch": "+5Hz", "volume": "+0%"},
            SpeechContext.APOLOGY: {"speed": "-25%", "pitch": "-5Hz", "volume": "-10%"},
            SpeechContext.CLOSING: {"speed": "-15%", "pitch": "+0Hz", "volume": "-5%"},
            SpeechContext.SENSITIVE: {"speed": "-20%", "pitch": "+0Hz", "volume": "-10%"}
        }
        
        # Store profiles
        self.voice_profiles["professional_female"] = professional_female
        self.voice_profiles["friendly_female"] = friendly_female
        self.voice_profiles["professional_male"] = professional_male
        self.voice_profiles["warm_female"] = warm_female
    
    def _select_default_profile(self) -> Optional[VoiceProfile]:
        """Select default voice profile based on configuration."""
        # Try to find profile matching default personality and gender
        for profile in self.voice_profiles.values():
            if (profile.personality == self.config.default_personality and 
                profile.gender == self.config.default_gender):
                return profile
        
        # Fallback to first available profile
        return next(iter(self.voice_profiles.values())) if self.voice_profiles else None
    
    async def generate_speech(
        self,
        text: str,
        context: Optional[SpeechContext] = None,
        profile_name: Optional[str] = None,
        optimization_settings: Optional[AudioOptimizationSettings] = None,
        **kwargs
    ) -> TTSResult:
        """
        Generate optimized speech with voice management.
        
        Args:
            text: Text to convert to speech
            context: Speech context for parameter adjustment
            profile_name: Specific voice profile to use
            optimization_settings: Custom optimization settings
            **kwargs: Additional TTS parameters
            
        Returns:
            Optimized TTS result
        """
        try:
            # Select voice profile
            profile = self._select_voice_profile(profile_name, context)
            
            # Get context-adjusted parameters
            voice_params = profile.get_parameters_for_context(context or SpeechContext.EXPLANATION)
            
            # Merge with provided parameters
            voice_params.update(kwargs)
            
            # Generate speech using TTS generator
            tts_result = await self.tts_generator.generate_speech(
                text,
                voice=profile.voice_id,
                **voice_params
            )
            
            # Apply audio optimization
            if optimization_settings or self.config.default_optimization:
                settings = optimization_settings or self.config.default_optimization
                optimized_audio = await self._optimize_audio(tts_result.audio_data, settings)
                
                # Update result with optimized audio
                tts_result.audio_data = optimized_audio
                tts_result.optimized = True
            
            # Update profile usage statistics
            self._update_profile_usage(profile, context)
            
            # Monitor quality if enabled
            if self.config.enable_quality_monitoring:
                quality_score = self._assess_audio_quality(tts_result)
                self._quality_scores.append(quality_score)
                
                # Learn from quality feedback
                if self.config.quality_feedback_learning and context:
                    self._learn_from_quality_feedback(context, quality_score)
            
            return tts_result
            
        except Exception as e:
            self._log.error(f"Error generating managed speech: {e}")
            raise AudioProcessingError(f"Voice management failed: {e}")
    
    def _select_voice_profile(
        self,
        profile_name: Optional[str],
        context: Optional[SpeechContext]
    ) -> VoiceProfile:
        """Select appropriate voice profile."""
        # Use specified profile if provided
        if profile_name and profile_name in self.voice_profiles:
            return self.voice_profiles[profile_name]
        
        # Use active profile if set
        if self.active_profile:
            return self.active_profile
        
        # Context-based selection
        if context and self.config.enable_context_adaptation:
            best_profile = self._select_profile_for_context(context)
            if best_profile:
                return best_profile
        
        # Fallback to default
        return self._select_default_profile() or next(iter(self.voice_profiles.values()))
    
    def _select_profile_for_context(self, context: SpeechContext) -> Optional[VoiceProfile]:
        """Select best profile for specific context."""
        # Context-personality mapping
        context_personality_map = {
            SpeechContext.GREETING: VoicePersonality.FRIENDLY,
            SpeechContext.EXPLANATION: VoicePersonality.PROFESSIONAL,
            SpeechContext.APOLOGY: VoicePersonality.EMPATHETIC,
            SpeechContext.CLOSING: VoicePersonality.WARM,
            SpeechContext.URGENT: VoicePersonality.AUTHORITATIVE,
            SpeechContext.SENSITIVE: VoicePersonality.EMPATHETIC,
            SpeechContext.PROMOTIONAL: VoicePersonality.ENERGETIC
        }
        
        preferred_personality = context_personality_map.get(context)
        if not preferred_personality:
            return None
        
        # Find profiles matching preferred personality
        matching_profiles = [
            profile for profile in self.voice_profiles.values()
            if profile.personality == preferred_personality
        ]
        
        if not matching_profiles:
            return None
        
        # Select based on usage statistics and quality
        return max(matching_profiles, key=lambda p: (
            p.clarity * 0.4 +
            (1.0 / max(1, p.usage_count)) * 0.3 +  # Prefer less used profiles for variety
            p.warmth * 0.3 if context in [SpeechContext.APOLOGY, SpeechContext.SENSITIVE] else 0
        ))
    
    async def _optimize_audio(
        self,
        audio_data: bytes,
        settings: AudioOptimizationSettings
    ) -> bytes:
        """Optimize audio for network transmission."""
        try:
            # Check cache first
            cache_key = self._generate_optimization_cache_key(audio_data, settings)
            if self.config.cache_optimized_audio and cache_key in self._optimization_cache:
                return self._optimization_cache[cache_key]
            
            # Load audio with pydub
            audio = AudioSegment.from_file(io.BytesIO(audio_data))
            
            # Apply telephony optimization if enabled
            if settings.telephony_mode:
                audio = self._apply_telephony_optimization(audio, settings)
            
            # Volume normalization
            if settings.normalize_volume:
                audio = normalize(audio)
            
            # Dynamic range compression
            if settings.dynamic_range_compression:
                audio = compress_dynamic_range(
                    audio,
                    threshold=-20.0,
                    ratio=4.0,
                    attack=5.0,
                    release=50.0
                )
            
            # Apply compression based on level
            if settings.compression_level != CompressionLevel.NONE:
                audio = self._apply_compression(audio, settings)
            
            # Export optimized audio
            output_buffer = io.BytesIO()
            
            # Determine export format and parameters
            export_format = "wav"
            export_params = {}
            
            if settings.compression_level in [CompressionLevel.HIGH, CompressionLevel.MAXIMUM]:
                export_format = "mp3"
                export_params["bitrate"] = f"{settings.target_bitrate_kbps}k"
            
            audio.export(output_buffer, format=export_format, **export_params)
            optimized_data = output_buffer.getvalue()
            
            # Cache result
            if self.config.cache_optimized_audio:
                self._cache_optimized_audio(cache_key, optimized_data)
            
            # Update optimization statistics
            self._update_optimization_stats(len(audio_data), len(optimized_data), settings)
            
            return optimized_data
            
        except Exception as e:
            self._log.warning(f"Audio optimization failed: {e}")
            return audio_data  # Return original if optimization fails
    
    def _apply_telephony_optimization(
        self,
        audio: AudioSegment,
        settings: AudioOptimizationSettings
    ) -> AudioSegment:
        """Apply telephony-specific optimizations."""
        # Convert to telephony sample rate (8kHz)
        audio = audio.set_frame_rate(8000)
        
        # Convert to mono
        audio = audio.set_channels(1)
        
        # Apply frequency filtering for telephony range
        low_freq, high_freq = settings.frequency_range_hz
        
        # High-pass filter (remove frequencies below low_freq)
        audio = audio.high_pass_filter(low_freq)
        
        # Low-pass filter (remove frequencies above high_freq)
        audio = audio.low_pass_filter(high_freq)
        
        return audio
    
    def _apply_compression(
        self,
        audio: AudioSegment,
        settings: AudioOptimizationSettings
    ) -> AudioSegment:
        """Apply audio compression based on level."""
        compression_params = {
            CompressionLevel.LOW: {"threshold": -15.0, "ratio": 2.0},
            CompressionLevel.MEDIUM: {"threshold": -20.0, "ratio": 4.0},
            CompressionLevel.HIGH: {"threshold": -25.0, "ratio": 6.0},
            CompressionLevel.MAXIMUM: {"threshold": -30.0, "ratio": 8.0}
        }
        
        params = compression_params.get(settings.compression_level, {"threshold": -20.0, "ratio": 4.0})
        
        return compress_dynamic_range(
            audio,
            threshold=params["threshold"],
            ratio=params["ratio"],
            attack=2.0,
            release=20.0
        )
    
    def _generate_optimization_cache_key(
        self,
        audio_data: bytes,
        settings: AudioOptimizationSettings
    ) -> str:
        """Generate cache key for optimization."""
        # Create hash of audio data and settings
        audio_hash = hashlib.md5(audio_data).hexdigest()[:16]
        settings_str = f"{settings.compression_level.value}_{settings.target_bitrate_kbps}_{settings.telephony_mode}"
        return f"{audio_hash}_{hashlib.md5(settings_str.encode()).hexdigest()[:8]}"
    
    def _cache_optimized_audio(self, cache_key: str, audio_data: bytes) -> None:
        """Cache optimized audio data."""
        # Simple size-based cache management
        max_cache_size = self.config.cache_size_mb * 1024 * 1024
        current_size = sum(len(data) for data in self._optimization_cache.values())
        
        # Remove oldest entries if cache is full
        while current_size + len(audio_data) > max_cache_size and self._optimization_cache:
            oldest_key = next(iter(self._optimization_cache))
            removed_data = self._optimization_cache.pop(oldest_key)
            current_size -= len(removed_data)
        
        self._optimization_cache[cache_key] = audio_data
    
    def _assess_audio_quality(self, tts_result: TTSResult) -> float:
        """Assess audio quality score."""
        score = 0.8  # Base score
        
        # Duration factor
        if 500 <= tts_result.duration_ms <= 10000:  # Reasonable duration
            score += 0.1
        
        # File size factor (not too small, not too large)
        size_per_second = tts_result.file_size_bytes / (tts_result.duration_ms / 1000)
        if 8000 <= size_per_second <= 32000:  # Reasonable compression
            score += 0.1
        
        return min(1.0, score)
    
    def _update_profile_usage(self, profile: VoiceProfile, context: Optional[SpeechContext]) -> None:
        """Update profile usage statistics."""
        profile.usage_count += 1
        profile.last_used = datetime.now()
        
        if context:
            self._context_usage[context] = self._context_usage.get(context, 0) + 1
    
    def _learn_from_quality_feedback(self, context: SpeechContext, quality_score: float) -> None:
        """Learn from quality feedback to improve context selection."""
        if context not in self._context_feedback:
            self._context_feedback[context] = []
        
        self._context_feedback[context].append(quality_score)
        
        # Keep only recent feedback (last 100 scores)
        if len(self._context_feedback[context]) > 100:
            self._context_feedback[context] = self._context_feedback[context][-100:]
    
    def _update_optimization_stats(
        self,
        original_size: int,
        optimized_size: int,
        settings: AudioOptimizationSettings
    ) -> None:
        """Update optimization statistics."""
        compression_ratio = optimized_size / original_size
        
        level_key = settings.compression_level.value
        if level_key not in self._optimization_stats:
            self._optimization_stats[level_key] = {
                "count": 0,
                "total_compression_ratio": 0.0,
                "total_original_size": 0,
                "total_optimized_size": 0
            }
        
        stats = self._optimization_stats[level_key]
        stats["count"] += 1
        stats["total_compression_ratio"] += compression_ratio
        stats["total_original_size"] += original_size
        stats["total_optimized_size"] += optimized_size 
   
    def set_active_profile(self, profile_name: str) -> bool:
        """Set active voice profile."""
        if profile_name in self.voice_profiles:
            self.active_profile = self.voice_profiles[profile_name]
            self._log.info(f"Active profile set to: {profile_name}")
            return True
        return False
    
    def get_active_profile(self) -> Optional[VoiceProfile]:
        """Get current active voice profile."""
        return self.active_profile
    
    def add_voice_profile(self, profile: VoiceProfile) -> None:
        """Add custom voice profile."""
        self.voice_profiles[profile.name.lower().replace(" ", "_")] = profile
        self._log.info(f"Added voice profile: {profile.name}")
    
    def remove_voice_profile(self, profile_name: str) -> bool:
        """Remove voice profile."""
        if profile_name in self.voice_profiles:
            # Don't remove if it's the active profile
            if self.active_profile and self.active_profile.name == self.voice_profiles[profile_name].name:
                self.active_profile = None
            
            del self.voice_profiles[profile_name]
            self._log.info(f"Removed voice profile: {profile_name}")
            return True
        return False
    
    def list_voice_profiles(self) -> List[VoiceProfile]:
        """Get list of all voice profiles."""
        return list(self.voice_profiles.values())
    
    def get_profile_by_personality(self, personality: VoicePersonality) -> List[VoiceProfile]:
        """Get profiles matching specific personality."""
        return [
            profile for profile in self.voice_profiles.values()
            if profile.personality == personality
        ]
    
    def get_profile_by_gender(self, gender: VoiceGender) -> List[VoiceProfile]:
        """Get profiles matching specific gender."""
        return [
            profile for profile in self.voice_profiles.values()
            if profile.gender == gender
        ]
    
    def recommend_profile_for_scenario(
        self,
        scenario: str,
        customer_type: str = "general",
        urgency: str = "normal"
    ) -> Optional[VoiceProfile]:
        """
        Recommend voice profile for specific scenario.
        
        Args:
            scenario: Scenario type (greeting, support, sales, etc.)
            customer_type: Customer type (vip, elderly, general, etc.)
            urgency: Urgency level (low, normal, high, critical)
            
        Returns:
            Recommended voice profile
        """
        # Scenario-personality mapping
        scenario_map = {
            "greeting": VoicePersonality.FRIENDLY,
            "support": VoicePersonality.PROFESSIONAL,
            "sales": VoicePersonality.ENERGETIC,
            "complaint": VoicePersonality.EMPATHETIC,
            "technical": VoicePersonality.AUTHORITATIVE,
            "closing": VoicePersonality.WARM
        }
        
        # Customer type preferences
        customer_preferences = {
            "vip": {"authority": 0.8, "clarity": 0.9},
            "elderly": {"warmth": 0.9, "clarity": 0.9},
            "young": {"energy": 0.8, "warmth": 0.7},
            "business": {"authority": 0.9, "clarity": 0.8}
        }
        
        # Urgency adjustments
        urgency_weights = {
            "low": {"warmth": 1.2, "energy": 0.8},
            "normal": {"warmth": 1.0, "energy": 1.0},
            "high": {"energy": 1.3, "authority": 1.2},
            "critical": {"energy": 1.5, "authority": 1.4}
        }
        
        # Get preferred personality for scenario
        preferred_personality = scenario_map.get(scenario, VoicePersonality.PROFESSIONAL)
        
        # Filter profiles by personality
        candidates = self.get_profile_by_personality(preferred_personality)
        if not candidates:
            candidates = list(self.voice_profiles.values())
        
        # Score profiles based on criteria
        def score_profile(profile: VoiceProfile) -> float:
            score = 0.0
            
            # Base personality match
            if profile.personality == preferred_personality:
                score += 1.0
            
            # Customer type preferences
            if customer_type in customer_preferences:
                prefs = customer_preferences[customer_type]
                for trait, weight in prefs.items():
                    trait_value = getattr(profile, trait, 0.5)
                    score += trait_value * weight * 0.5
            
            # Urgency adjustments
            if urgency in urgency_weights:
                weights = urgency_weights[urgency]
                for trait, weight in weights.items():
                    trait_value = getattr(profile, trait, 0.5)
                    score += trait_value * weight * 0.3
            
            # Usage diversity (prefer less used profiles for variety)
            usage_factor = 1.0 / max(1, profile.usage_count / 10)
            score += usage_factor * 0.2
            
            return score
        
        # Select best profile
        if candidates:
            best_profile = max(candidates, key=score_profile)
            return best_profile
        
        return None
    
    async def create_optimization_preset(
        self,
        name: str,
        compression_level: CompressionLevel,
        telephony_mode: bool = False,
        target_bitrate: int = 64
    ) -> AudioOptimizationSettings:
        """Create custom optimization preset."""
        settings = AudioOptimizationSettings(
            compression_level=compression_level,
            target_bitrate_kbps=target_bitrate,
            telephony_mode=telephony_mode,
            normalize_volume=True,
            dynamic_range_compression=True,
            optimize_for_streaming=True
        )
        
        # Store preset (could be saved to file)
        self._log.info(f"Created optimization preset: {name}")
        return settings
    
    def get_context_statistics(self) -> Dict[str, Any]:
        """Get context usage statistics."""
        total_usage = sum(self._context_usage.values())
        
        stats = {
            "total_contexts_used": len(self._context_usage),
            "total_usage_count": total_usage,
            "context_distribution": {},
            "context_quality_scores": {}
        }
        
        # Calculate distribution
        for context, count in self._context_usage.items():
            stats["context_distribution"][context.value] = {
                "count": count,
                "percentage": (count / total_usage * 100) if total_usage > 0 else 0
            }
        
        # Calculate average quality scores
        for context, scores in self._context_feedback.items():
            if scores:
                stats["context_quality_scores"][context.value] = {
                    "average": sum(scores) / len(scores),
                    "count": len(scores),
                    "min": min(scores),
                    "max": max(scores)
                }
        
        return stats
    
    def get_optimization_statistics(self) -> Dict[str, Any]:
        """Get optimization statistics."""
        stats = {
            "cache_size": len(self._optimization_cache),
            "cache_memory_mb": sum(len(data) for data in self._optimization_cache.values()) / (1024 * 1024),
            "compression_levels": {}
        }
        
        # Calculate compression statistics
        for level, level_stats in self._optimization_stats.items():
            if level_stats["count"] > 0:
                avg_compression = level_stats["total_compression_ratio"] / level_stats["count"]
                total_saved = level_stats["total_original_size"] - level_stats["total_optimized_size"]
                
                stats["compression_levels"][level] = {
                    "usage_count": level_stats["count"],
                    "average_compression_ratio": avg_compression,
                    "total_bytes_saved": total_saved,
                    "average_original_size": level_stats["total_original_size"] / level_stats["count"],
                    "average_optimized_size": level_stats["total_optimized_size"] / level_stats["count"]
                }
        
        return stats
    
    def get_voice_manager_stats(self) -> Dict[str, Any]:
        """Get comprehensive voice manager statistics."""
        profile_stats = {}
        for name, profile in self.voice_profiles.items():
            profile_stats[name] = {
                "personality": profile.personality.value,
                "gender": profile.gender.value,
                "usage_count": profile.usage_count,
                "last_used": profile.last_used.isoformat() if profile.last_used else None,
                "warmth": profile.warmth,
                "authority": profile.authority,
                "energy": profile.energy,
                "clarity": profile.clarity
            }
        
        return {
            "active_profile": self.active_profile.name if self.active_profile else None,
            "total_profiles": len(self.voice_profiles),
            "profile_statistics": profile_stats,
            "context_statistics": self.get_context_statistics(),
            "optimization_statistics": self.get_optimization_statistics(),
            "quality_monitoring": {
                "enabled": self.config.enable_quality_monitoring,
                "average_quality": sum(self._quality_scores) / len(self._quality_scores) if self._quality_scores else 0,
                "quality_samples": len(self._quality_scores)
            }
        }
    
    async def _load_voice_profiles(self) -> None:
        """Load voice profiles from file."""
        try:
            profiles_file = Path(self.config.voice_profiles_file)
            if profiles_file.exists():
                with open(profiles_file, 'r', encoding='utf-8') as f:
                    profiles_data = json.load(f)
                
                for profile_data in profiles_data.get("profiles", []):
                    profile = self._profile_from_dict(profile_data)
                    self.voice_profiles[profile.name.lower().replace(" ", "_")] = profile
                
                self._log.info(f"Loaded {len(profiles_data.get('profiles', []))} voice profiles from file")
        
        except Exception as e:
            self._log.warning(f"Failed to load voice profiles: {e}")
    
    async def _save_voice_profiles(self) -> None:
        """Save voice profiles to file."""
        try:
            profiles_data = {
                "profiles": [self._profile_to_dict(profile) for profile in self.voice_profiles.values()],
                "saved_at": datetime.now().isoformat()
            }
            
            with open(self.config.voice_profiles_file, 'w', encoding='utf-8') as f:
                json.dump(profiles_data, f, ensure_ascii=False, indent=2)
            
            self._log.info(f"Saved {len(self.voice_profiles)} voice profiles to file")
        
        except Exception as e:
            self._log.error(f"Failed to save voice profiles: {e}")
    
    def _profile_to_dict(self, profile: VoiceProfile) -> Dict[str, Any]:
        """Convert voice profile to dictionary."""
        return {
            "name": profile.name,
            "voice_id": profile.voice_id,
            "personality": profile.personality.value,
            "gender": profile.gender.value,
            "base_speed": profile.base_speed,
            "base_pitch": profile.base_pitch,
            "base_volume": profile.base_volume,
            "context_adjustments": {
                context.value: params for context, params in profile.context_adjustments.items()
            },
            "preferred_sample_rate": profile.preferred_sample_rate,
            "preferred_format": profile.preferred_format.value,
            "warmth": profile.warmth,
            "authority": profile.authority,
            "energy": profile.energy,
            "clarity": profile.clarity,
            "usage_count": profile.usage_count,
            "last_used": profile.last_used.isoformat() if profile.last_used else None
        }
    
    def _profile_from_dict(self, data: Dict[str, Any]) -> VoiceProfile:
        """Create voice profile from dictionary."""
        # Convert context adjustments
        context_adjustments = {}
        for context_str, params in data.get("context_adjustments", {}).items():
            try:
                context = SpeechContext(context_str)
                context_adjustments[context] = params
            except ValueError:
                continue  # Skip invalid contexts
        
        return VoiceProfile(
            name=data["name"],
            voice_id=data["voice_id"],
            personality=VoicePersonality(data["personality"]),
            gender=VoiceGender(data["gender"]),
            base_speed=data.get("base_speed", "+0%"),
            base_pitch=data.get("base_pitch", "+0Hz"),
            base_volume=data.get("base_volume", "+0%"),
            context_adjustments=context_adjustments,
            preferred_sample_rate=data.get("preferred_sample_rate", 16000),
            preferred_format=AudioFormat(data.get("preferred_format", "wav")),
            warmth=data.get("warmth", 0.5),
            authority=data.get("authority", 0.5),
            energy=data.get("energy", 0.5),
            clarity=data.get("clarity", 0.8),
            usage_count=data.get("usage_count", 0),
            last_used=datetime.fromisoformat(data["last_used"]) if data.get("last_used") else None
        )
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_voice_manager_stats()
        
        self._log.info(f"Voice manager statistics:")
        self._log.info(f"  Active profile: {stats['active_profile']}")
        self._log.info(f"  Total profiles: {stats['total_profiles']}")
        self._log.info(f"  Context usage: {len(stats['context_statistics']['context_distribution'])}")
        self._log.info(f"  Average quality: {stats['quality_monitoring']['average_quality']:.3f}")
        self._log.info(f"  Cache size: {stats['optimization_statistics']['cache_size']} items")
    
    def clear_optimization_cache(self) -> None:
        """Clear optimization cache."""
        self._optimization_cache.clear()
        self._log.info("Optimization cache cleared")
    
    def reset_statistics(self) -> None:
        """Reset all statistics."""
        self._context_usage.clear()
        self._context_feedback.clear()
        self._quality_scores.clear()
        self._optimization_stats.clear()
        
        # Reset profile usage statistics
        for profile in self.voice_profiles.values():
            profile.usage_count = 0
            profile.last_used = None
        
        self._log.info("Voice manager statistics reset")


# Utility functions for voice management

async def create_voice_manager(
    tts_generator: EdgeTTSGenerator,
    default_personality: VoicePersonality = VoicePersonality.PROFESSIONAL,
    default_gender: VoiceGender = VoiceGender.FEMALE,
    enable_optimization: bool = True,
    config_manager=None,
    **kwargs
) -> VoiceManager:
    """
    Create and initialize voice manager.
    
    Args:
        tts_generator: EdgeTTS generator instance
        default_personality: Default voice personality
        default_gender: Default voice gender
        enable_optimization: Enable audio optimization
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized voice manager
    """
    config = VoiceManagerConfig(
        default_personality=default_personality,
        default_gender=default_gender,
        **kwargs
    )
    
    if not enable_optimization:
        config.default_optimization = AudioOptimizationSettings(
            compression_level=CompressionLevel.NONE,
            normalize_volume=False,
            dynamic_range_compression=False
        )
    
    manager = VoiceManager(config, tts_generator, config_manager)
    await manager.initialize()
    await manager.start()
    
    return manager


def create_telephony_optimization() -> AudioOptimizationSettings:
    """Create optimization settings for telephony."""
    return AudioOptimizationSettings(
        compression_level=CompressionLevel.HIGH,
        target_bitrate_kbps=32,
        telephony_mode=True,
        frequency_range_hz=(300, 3400),
        normalize_volume=True,
        dynamic_range_compression=True,
        optimize_for_streaming=True,
        chunk_size_ms=50,
        buffer_size_ms=200
    )


def create_streaming_optimization() -> AudioOptimizationSettings:
    """Create optimization settings for streaming."""
    return AudioOptimizationSettings(
        compression_level=CompressionLevel.MEDIUM,
        target_bitrate_kbps=64,
        telephony_mode=False,
        normalize_volume=True,
        dynamic_range_compression=True,
        optimize_for_streaming=True,
        adaptive_bitrate=True,
        chunk_size_ms=100,
        buffer_size_ms=500
    )


def create_high_quality_optimization() -> AudioOptimizationSettings:
    """Create optimization settings for high quality."""
    return AudioOptimizationSettings(
        compression_level=CompressionLevel.LOW,
        target_bitrate_kbps=128,
        telephony_mode=False,
        normalize_volume=True,
        dynamic_range_compression=False,
        optimize_for_streaming=False
    )


def create_custom_voice_profile(
    name: str,
    voice_id: str,
    personality: VoicePersonality,
    gender: VoiceGender,
    **traits
) -> VoiceProfile:
    """
    Create custom voice profile.
    
    Args:
        name: Profile name
        voice_id: TTS voice ID
        personality: Voice personality
        gender: Voice gender
        **traits: Additional personality traits (warmth, authority, energy, clarity)
        
    Returns:
        Custom voice profile
    """
    return VoiceProfile(
        name=name,
        voice_id=voice_id,
        personality=personality,
        gender=gender,
        warmth=traits.get("warmth", 0.5),
        authority=traits.get("authority", 0.5),
        energy=traits.get("energy", 0.5),
        clarity=traits.get("clarity", 0.8)
    )