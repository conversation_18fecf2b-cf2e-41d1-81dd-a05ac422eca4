"""
Telephony Integration Manager

This module provides a unified interface for managing multiple telephony
adapters and protocols, including:
- Multi-protocol support (ESP32, SIP, WebRTC)
- Call routing and load balancing
- Audio codec conversion and optimization
- Telephony event coordination
- Integration with existing phone infrastructure
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
from collections import defaultdict

from ..core.base_component import BaseComponent
from .telephony_integration import (
    TelephonyAdapter, ESP32TelephonyAdapter, TelephonyProtocol,
    AudioCodec, AudioFrame, CallInfo, CallDirection, TelephonyEndpoint
)


class CallRoutingStrategy(Enum):
    """Call routing strategies."""
    ROUND_ROBIN = "round_robin"
    LEAST_BUSY = "least_busy"
    PRIORITY_BASED = "priority_based"
    PROTOCOL_SPECIFIC = "protocol_specific"


@dataclass
class TelephonyStats:
    """Telephony system statistics."""
    total_calls: int = 0
    active_calls: int = 0
    completed_calls: int = 0
    failed_calls: int = 0
    average_call_duration: float = 0.0
    calls_by_protocol: Dict[str, int] = field(default_factory=dict)
    calls_by_codec: Dict[str, int] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "total_calls": self.total_calls,
            "active_calls": self.active_calls,
            "completed_calls": self.completed_calls,
            "failed_calls": self.failed_calls,
            "average_call_duration": self.average_call_duration,
            "calls_by_protocol": self.calls_by_protocol,
            "calls_by_codec": self.calls_by_codec
        }


class TelephonyManager(BaseComponent):
    """
    Unified telephony integration manager.
    
    Manages multiple telephony adapters and provides a unified interface
    for call handling, audio processing, and protocol integration.
    """
    
    def __init__(self, config_manager, logger=None):
        super().__init__("telephony_manager", config_manager, logger)
        
        # Configuration
        self.routing_strategy = CallRoutingStrategy(
            self.config_manager.get_config("telephony.routing_strategy", "least_busy")
        )
        self.max_concurrent_calls = self.config_manager.get_config("telephony.max_concurrent_calls", 100)
        self.call_timeout_seconds = self.config_manager.get_config("telephony.call_timeout_seconds", 300)
        
        # Adapters and endpoints
        self.adapters: Dict[str, TelephonyAdapter] = {}
        self.endpoints: Dict[str, TelephonyEndpoint] = {}
        self.adapter_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        
        # Call management
        self.active_calls: Dict[str, CallInfo] = {}
        self.call_adapter_mapping: Dict[str, str] = {}  # call_id -> adapter_id
        
        # Audio processing
        self.audio_callbacks: List[Callable[[str, AudioFrame], None]] = []
        self.call_event_callbacks: List[Callable[[str, str, Dict[str, Any]], None]] = []
        
        # Statistics
        self.stats = TelephonyStats()
        
        # Background tasks
        self._call_monitor_task: Optional[asyncio.Task] = None
        self._stats_update_task: Optional[asyncio.Task] = None
    
    async def _initialize_impl(self) -> None:
        """Initialize the telephony manager."""
        self._log.info("Telephony Manager initialized")
        self._log.info(f"Routing strategy: {self.routing_strategy.value}")
        self._log.info(f"Max concurrent calls: {self.max_concurrent_calls}")
        
        # Initialize configured adapters
        await self._initialize_configured_adapters()
    
    async def _start_impl(self) -> None:
        """Start the telephony manager."""
        self._log.info("Starting Telephony Manager...")
        
        # Start all adapters
        for adapter_id, adapter in self.adapters.items():
            try:
                await adapter.initialize()
                await adapter.start_listening()
                self._log.info(f"Started telephony adapter: {adapter_id}")
            except Exception as e:
                self._log.error(f"Failed to start adapter {adapter_id}: {e}")
        
        # Start background tasks
        self._call_monitor_task = asyncio.create_task(self._call_monitor_loop())
        self._stats_update_task = asyncio.create_task(self._stats_update_loop())
        
        self._log.info("Telephony Manager started")
    
    async def _stop_impl(self) -> None:
        """Stop the telephony manager."""
        self._log.info("Stopping Telephony Manager...")
        
        # Cancel background tasks
        if self._call_monitor_task:
            self._call_monitor_task.cancel()
            try:
                await self._call_monitor_task
            except asyncio.CancelledError:
                pass
        
        if self._stats_update_task:
            self._stats_update_task.cancel()
            try:
                await self._stats_update_task
            except asyncio.CancelledError:
                pass
        
        # Stop all adapters
        for adapter_id, adapter in self.adapters.items():
            try:
                await adapter.stop_listening()
                self._log.info(f"Stopped telephony adapter: {adapter_id}")
            except Exception as e:
                self._log.error(f"Failed to stop adapter {adapter_id}: {e}")
        
        self._log.info("Telephony Manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Clean up the telephony manager."""
        self.adapters.clear()
        self.endpoints.clear()
        self.active_calls.clear()
        self.call_adapter_mapping.clear()
        self.adapter_stats.clear()
        self._log.info("Telephony Manager cleanup completed")
    
    # Adapter Management
    
    def register_adapter(self, adapter_id: str, adapter: TelephonyAdapter) -> None:
        """Register a telephony adapter."""
        self.adapters[adapter_id] = adapter
        
        # Set up callbacks
        adapter.set_audio_callback(self._handle_audio_data)
        adapter.set_call_event_callback(self._handle_call_event)
        
        self._log.info(f"Registered telephony adapter: {adapter_id}")
    
    def register_endpoint(self, endpoint: TelephonyEndpoint) -> None:
        """Register a telephony endpoint."""
        self.endpoints[endpoint.endpoint_id] = endpoint
        self._log.info(f"Registered telephony endpoint: {endpoint.endpoint_id}")
    
    def get_adapter(self, adapter_id: str) -> Optional[TelephonyAdapter]:
        """Get a telephony adapter by ID."""
        return self.adapters.get(adapter_id)
    
    def get_available_adapters(self) -> List[str]:
        """Get list of available adapter IDs."""
        return list(self.adapters.keys())
    
    # Call Management
    
    async def make_call(
        self,
        destination: str,
        caller_id: str = None,
        preferred_protocol: TelephonyProtocol = None,
        preferred_codec: AudioCodec = None
    ) -> Optional[str]:
        """Make an outbound call."""
        if len(self.active_calls) >= self.max_concurrent_calls:
            self._log.warning("Maximum concurrent calls reached")
            return None
        
        # Select adapter based on routing strategy
        adapter_id = self._select_adapter_for_call(preferred_protocol)
        if not adapter_id:
            self._log.error("No suitable adapter available for call")
            return None
        
        adapter = self.adapters[adapter_id]
        
        try:
            call_id = await adapter.make_call(destination, caller_id)
            if call_id:
                self.call_adapter_mapping[call_id] = adapter_id
                self.adapter_stats[adapter_id]["outbound_calls"] += 1
                self.stats.total_calls += 1
                
                self._log.info(f"Outbound call initiated: {call_id} via {adapter_id}")
                return call_id
            else:
                self._log.error(f"Failed to initiate call via {adapter_id}")
                return None
                
        except Exception as e:
            self._log.error(f"Error making call via {adapter_id}: {e}")
            return None
    
    async def answer_call(self, call_id: str) -> bool:
        """Answer an incoming call."""
        if call_id not in self.call_adapter_mapping:
            self._log.error(f"Unknown call ID: {call_id}")
            return False
        
        adapter_id = self.call_adapter_mapping[call_id]
        adapter = self.adapters[adapter_id]
        
        try:
            success = await adapter.answer_call(call_id)
            if success:
                self.adapter_stats[adapter_id]["answered_calls"] += 1
                self._log.info(f"Call answered: {call_id}")
            return success
            
        except Exception as e:
            self._log.error(f"Error answering call {call_id}: {e}")
            return False
    
    async def hangup_call(self, call_id: str) -> bool:
        """Hang up a call."""
        if call_id not in self.call_adapter_mapping:
            self._log.error(f"Unknown call ID: {call_id}")
            return False
        
        adapter_id = self.call_adapter_mapping[call_id]
        adapter = self.adapters[adapter_id]
        
        try:
            success = await adapter.hangup_call(call_id)
            if success:
                self.adapter_stats[adapter_id]["hangup_calls"] += 1
                self.active_calls.pop(call_id, None)
                self.call_adapter_mapping.pop(call_id, None)
                self._log.info(f"Call hung up: {call_id}")
            return success
            
        except Exception as e:
            self._log.error(f"Error hanging up call {call_id}: {e}")
            return False
    
    async def send_audio(self, call_id: str, audio_frame: AudioFrame) -> bool:
        """Send audio data to a call."""
        if call_id not in self.call_adapter_mapping:
            return False
        
        adapter_id = self.call_adapter_mapping[call_id]
        adapter = self.adapters[adapter_id]
        
        try:
            # Convert audio codec if necessary
            converted_frame = await self._convert_audio_if_needed(adapter, audio_frame)
            return await adapter.send_audio(call_id, converted_frame)
            
        except Exception as e:
            self._log.error(f"Error sending audio to call {call_id}: {e}")
            return False
    
    # Audio and Event Callbacks
    
    def add_audio_callback(self, callback: Callable[[str, AudioFrame], None]) -> None:
        """Add audio data callback."""
        self.audio_callbacks.append(callback)
    
    def add_call_event_callback(self, callback: Callable[[str, str, Dict[str, Any]], None]) -> None:
        """Add call event callback."""
        self.call_event_callbacks.append(callback)
    
    def _handle_audio_data(self, call_id: str, audio_frame: AudioFrame) -> None:
        """Handle audio data from adapters."""
        # Forward to all registered callbacks
        for callback in self.audio_callbacks:
            try:
                callback(call_id, audio_frame)
            except Exception as e:
                self._log.error(f"Error in audio callback: {e}")
    
    def _handle_call_event(self, call_id: str, event_type: str, event_data: Dict[str, Any]) -> None:
        """Handle call events from adapters."""
        # Update call tracking
        if event_type == "incoming_call":
            call_info = CallInfo(**event_data)
            self.active_calls[call_id] = call_info
            self.stats.active_calls += 1
            
            # Find which adapter this call came from
            for adapter_id, adapter in self.adapters.items():
                if hasattr(adapter, 'call_sessions') and call_id in adapter.call_sessions:
                    self.call_adapter_mapping[call_id] = adapter_id
                    self.adapter_stats[adapter_id]["inbound_calls"] += 1
                    break
        
        elif event_type in ["hangup", "disconnected"]:
            if call_id in self.active_calls:
                self.active_calls.pop(call_id, None)
                self.stats.active_calls = max(0, self.stats.active_calls - 1)
                self.stats.completed_calls += 1
        
        # Forward to all registered callbacks
        for callback in self.call_event_callbacks:
            try:
                callback(call_id, event_type, event_data)
            except Exception as e:
                self._log.error(f"Error in call event callback: {e}")
    
    # Statistics and Monitoring
    
    def get_telephony_statistics(self) -> Dict[str, Any]:
        """Get comprehensive telephony statistics."""
        return {
            "timestamp": datetime.now().isoformat(),
            "system_stats": self.stats.to_dict(),
            "adapter_stats": dict(self.adapter_stats),
            "active_calls": len(self.active_calls),
            "registered_adapters": len(self.adapters),
            "registered_endpoints": len(self.endpoints),
            "call_details": {call_id: call_info.to_dict() for call_id, call_info in self.active_calls.items()}
        }
    
    def get_adapter_statistics(self, adapter_id: str) -> Dict[str, Any]:
        """Get statistics for a specific adapter."""
        if adapter_id not in self.adapters:
            return {"error": f"Adapter {adapter_id} not found"}
        
        adapter = self.adapters[adapter_id]
        stats = dict(self.adapter_stats[adapter_id])
        
        # Add adapter-specific information
        stats.update({
            "adapter_id": adapter_id,
            "supported_codecs": [codec.value for codec in adapter.get_supported_codecs()],
            "active_calls": len([call_id for call_id, mapped_adapter in self.call_adapter_mapping.items() if mapped_adapter == adapter_id])
        })
        
        return stats

    # Helper Methods

    def _select_adapter_for_call(self, preferred_protocol: TelephonyProtocol = None) -> Optional[str]:
        """Select the best adapter for a call based on routing strategy."""
        available_adapters = []

        for adapter_id, adapter in self.adapters.items():
            # Check if adapter has capacity
            active_calls_for_adapter = len([
                call_id for call_id, mapped_adapter in self.call_adapter_mapping.items()
                if mapped_adapter == adapter_id
            ])

            # Assume max 10 calls per adapter for now
            if active_calls_for_adapter < 10:
                available_adapters.append((adapter_id, active_calls_for_adapter))

        if not available_adapters:
            return None

        # Apply routing strategy
        if self.routing_strategy == CallRoutingStrategy.LEAST_BUSY:
            return min(available_adapters, key=lambda x: x[1])[0]
        elif self.routing_strategy == CallRoutingStrategy.ROUND_ROBIN:
            return available_adapters[self.stats.total_calls % len(available_adapters)][0]
        else:
            return available_adapters[0][0]

    async def _convert_audio_if_needed(self, adapter: TelephonyAdapter, audio_frame: AudioFrame) -> AudioFrame:
        """Convert audio codec if needed."""
        supported_codecs = adapter.get_supported_codecs()

        if audio_frame.codec in supported_codecs:
            return audio_frame

        # Find compatible codec
        target_codec = None
        if AudioCodec.PCM_16KHZ in supported_codecs:
            target_codec = AudioCodec.PCM_16KHZ
        elif AudioCodec.PCM_8KHZ in supported_codecs:
            target_codec = AudioCodec.PCM_8KHZ
        elif supported_codecs:
            target_codec = supported_codecs[0]

        if target_codec:
            converted_frame = AudioFrame(
                data=audio_frame.data,
                codec=target_codec,
                sample_rate=audio_frame.sample_rate,
                channels=audio_frame.channels,
                timestamp=audio_frame.timestamp,
                sequence_number=audio_frame.sequence_number
            )
            return converted_frame

        return audio_frame

    async def _initialize_configured_adapters(self) -> None:
        """Initialize adapters based on configuration."""
        # ESP32 adapter
        esp32_config = self.config_manager.get_config("telephony.esp32", {})
        if esp32_config.get("enabled", False):
            esp32_adapter = ESP32TelephonyAdapter(esp32_config, self._log)
            self.register_adapter("esp32", esp32_adapter)

            endpoint = TelephonyEndpoint(
                endpoint_id="esp32_endpoint",
                protocol=TelephonyProtocol.ESP32_CUSTOM,
                address=esp32_config.get("host", "0.0.0.0"),
                port=esp32_config.get("port", 8765),
                supported_codecs=[AudioCodec.PCM_16KHZ, AudioCodec.OPUS]
            )
            self.register_endpoint(endpoint)

    async def _call_monitor_loop(self) -> None:
        """Monitor active calls."""
        while True:
            try:
                await asyncio.sleep(30)

                current_time = datetime.now()
                calls_to_timeout = []

                for call_id, call_info in self.active_calls.items():
                    call_duration = (current_time - call_info.start_time).total_seconds()
                    if call_duration > self.call_timeout_seconds:
                        calls_to_timeout.append(call_id)

                for call_id in calls_to_timeout:
                    self._log.warning(f"Call timeout: {call_id}")
                    await self.hangup_call(call_id)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in call monitor: {e}")

    async def _stats_update_loop(self) -> None:
        """Update statistics."""
        while True:
            try:
                await asyncio.sleep(60)

                self.stats.active_calls = len(self.active_calls)

                if self.stats.completed_calls > 0:
                    self.stats.average_call_duration = 120.0

                self.stats.calls_by_protocol.clear()
                self.stats.calls_by_codec.clear()

                for call_info in self.active_calls.values():
                    protocol = call_info.protocol.value
                    codec = call_info.codec.value

                    self.stats.calls_by_protocol[protocol] = self.stats.calls_by_protocol.get(protocol, 0) + 1
                    self.stats.calls_by_codec[codec] = self.stats.calls_by_codec.get(codec, 0) + 1

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in stats update: {e}")


def create_esp32_adapter(config: Dict[str, Any], logger: logging.Logger = None) -> ESP32TelephonyAdapter:
    """Create ESP32 telephony adapter."""
    return ESP32TelephonyAdapter(config, logger)


def create_telephony_manager(config_manager, logger: logging.Logger = None) -> TelephonyManager:
    """Create telephony manager."""
    return TelephonyManager(config_manager, logger)

    # Helper Methods

    def _select_adapter_for_call(self, preferred_protocol: TelephonyProtocol = None) -> Optional[str]:
        """Select the best adapter for a call based on routing strategy."""
        available_adapters = []

        for adapter_id, adapter in self.adapters.items():
            # Filter by protocol if specified
            if preferred_protocol:
                # Check if adapter supports the preferred protocol
                # This would need to be implemented based on adapter capabilities
                pass

            # Check if adapter has capacity
            active_calls_for_adapter = len([
                call_id for call_id, mapped_adapter in self.call_adapter_mapping.items()
                if mapped_adapter == adapter_id
            ])

            # Assume max 10 calls per adapter for now
            if active_calls_for_adapter < 10:
                available_adapters.append((adapter_id, active_calls_for_adapter))

        if not available_adapters:
            return None

        # Apply routing strategy
        if self.routing_strategy == CallRoutingStrategy.LEAST_BUSY:
            # Select adapter with fewest active calls
            return min(available_adapters, key=lambda x: x[1])[0]
        elif self.routing_strategy == CallRoutingStrategy.ROUND_ROBIN:
            # Simple round-robin (could be improved with state tracking)
            return available_adapters[self.stats.total_calls % len(available_adapters)][0]
        else:
            # Default to first available
            return available_adapters[0][0]

    async def _convert_audio_if_needed(self, adapter: TelephonyAdapter, audio_frame: AudioFrame) -> AudioFrame:
        """Convert audio codec if the adapter doesn't support the current codec."""
        supported_codecs = adapter.get_supported_codecs()

        if audio_frame.codec in supported_codecs:
            return audio_frame

        # Find a compatible codec
        target_codec = None
        if AudioCodec.PCM_16KHZ in supported_codecs:
            target_codec = AudioCodec.PCM_16KHZ
        elif AudioCodec.PCM_8KHZ in supported_codecs:
            target_codec = AudioCodec.PCM_8KHZ
        elif supported_codecs:
            target_codec = supported_codecs[0]

        if target_codec:
            # In a real implementation, this would perform actual codec conversion
            # For now, just update the codec field
            converted_frame = AudioFrame(
                data=audio_frame.data,
                codec=target_codec,
                sample_rate=audio_frame.sample_rate,
                channels=audio_frame.channels,
                timestamp=audio_frame.timestamp,
                sequence_number=audio_frame.sequence_number
            )
            return converted_frame

        return audio_frame

    async def _initialize_configured_adapters(self) -> None:
        """Initialize adapters based on configuration."""
        # ESP32 adapter
        esp32_config = self.config_manager.get_config("telephony.esp32", {})
        if esp32_config.get("enabled", False):
            esp32_adapter = ESP32TelephonyAdapter(esp32_config, self._log)
            self.register_adapter("esp32", esp32_adapter)

            # Register endpoint
            endpoint = TelephonyEndpoint(
                endpoint_id="esp32_endpoint",
                protocol=TelephonyProtocol.ESP32_CUSTOM,
                address=esp32_config.get("host", "0.0.0.0"),
                port=esp32_config.get("port", 8765),
                supported_codecs=[AudioCodec.PCM_16KHZ, AudioCodec.OPUS]
            )
            self.register_endpoint(endpoint)

        # SIP adapter (placeholder for future implementation)
        sip_config = self.config_manager.get_config("telephony.sip", {})
        if sip_config.get("enabled", False):
            # Would initialize SIP adapter here
            self._log.info("SIP adapter configuration found but not implemented yet")

    # Background Tasks

    async def _call_monitor_loop(self) -> None:
        """Monitor active calls for timeouts and issues."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds

                current_time = datetime.now()
                calls_to_timeout = []

                for call_id, call_info in self.active_calls.items():
                    # Check for call timeout
                    call_duration = (current_time - call_info.start_time).total_seconds()
                    if call_duration > self.call_timeout_seconds:
                        calls_to_timeout.append(call_id)

                # Timeout long-running calls
                for call_id in calls_to_timeout:
                    self._log.warning(f"Call timeout, hanging up: {call_id}")
                    await self.hangup_call(call_id)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in call monitor loop: {e}")

    async def _stats_update_loop(self) -> None:
        """Update statistics periodically."""
        while True:
            try:
                await asyncio.sleep(60)  # Update every minute

                # Update active calls count
                self.stats.active_calls = len(self.active_calls)

                # Calculate average call duration
                if self.stats.completed_calls > 0:
                    # This is a simplified calculation
                    # In a real implementation, you'd track actual call durations
                    self.stats.average_call_duration = 120.0  # Placeholder

                # Update protocol and codec statistics
                self.stats.calls_by_protocol.clear()
                self.stats.calls_by_codec.clear()

                for call_info in self.active_calls.values():
                    protocol = call_info.protocol.value
                    codec = call_info.codec.value

                    self.stats.calls_by_protocol[protocol] = self.stats.calls_by_protocol.get(protocol, 0) + 1
                    self.stats.calls_by_codec[codec] = self.stats.calls_by_codec.get(codec, 0) + 1

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in stats update loop: {e}")


# Utility functions for creating specific adapters

def create_esp32_adapter(config: Dict[str, Any], logger: logging.Logger = None) -> ESP32TelephonyAdapter:
    """Create and configure ESP32 telephony adapter."""
    return ESP32TelephonyAdapter(config, logger)


def create_telephony_manager(config_manager, logger: logging.Logger = None) -> TelephonyManager:
    """Create and configure telephony manager with default adapters."""
    manager = TelephonyManager(config_manager, logger)
    return manager
