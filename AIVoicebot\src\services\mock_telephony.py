"""
Mock Telephony Interface for development and testing.

This module provides a mock implementation of the telephony interface that
simulates call operations and audio streaming without a real telephony system.
"""

import asyncio
import logging
from typing import AsyncGenerator, Optional, Dict, List, Any
from datetime import datetime
import numpy as np
import random

from .telephony_interface import (
    ITelephonyInterface, TelephonyError, CallStatus, CallInfo,
    CallQualityMetrics, ConnectionQuality, ConnectionProblem
)
from ..core.interfaces import AudioChunk, AudioFormat
from ..core.base_component import BaseComponent


class MockTelephony(BaseComponent, ITelephonyInterface):
    """
    Mock implementation of the telephony interface.
    
    Simulates making calls and provides a dummy audio stream.
    """
    
    def __init__(self, config_manager, logger=None):
        super().__init__("mock_telephony", config_manager, logger)
        self.active_calls: Dict[str, CallInfo] = {}
        self.call_outcomes: Dict[str, str] = {}
        self.quality_history: Dict[str, List[CallQualityMetrics]] = {}

    def set_next_call_outcome(self, phone_number: str, outcome: str):
        """Set the outcome for the next call to a specific number."""
        self.call_outcomes[phone_number] = outcome

    async def _initialize_impl(self) -> None:
        self._log.info("Mock Telephony initialized.")

    async def _start_impl(self) -> None:
        self._log.info("Mock Telephony started.")

    async def _stop_impl(self) -> None:
        self._log.info("Mock Telephony stopped.")

    async def _cleanup_impl(self) -> None:
        self._log.info("Mock Telephony cleaned up.")

    async def make_call(self, phone_number: str, session_id: str) -> str:
        """Simulate making an outbound call."""
        self._log.info(f"Simulating call to {phone_number} for session {session_id}")
        if session_id in self.active_calls:
            raise TelephonyError(f"Call with session_id {session_id} already exists.")

        # Use pre-configured outcome or a random one
        outcome = self.call_outcomes.pop(phone_number, None) or random.choice(["answered", "answered", "answered", "busy", "failed"])

        # Map outcome to CallStatus
        status_map = {
            "answered": CallStatus.ANSWERED,
            "busy": CallStatus.BUSY,
            "failed": CallStatus.FAILED,
            "no_answer": CallStatus.NO_ANSWER
        }
        status = status_map.get(outcome, CallStatus.ANSWERED)

        # Create initial quality metrics
        quality_metrics = CallQualityMetrics(
            jitter_ms=random.uniform(10, 50),
            packet_loss_percent=random.uniform(0, 5),
            latency_ms=random.uniform(50, 200),
            audio_level_db=random.uniform(-70, -20),
            signal_to_noise_ratio=random.uniform(10, 40)
        )

        # Create call info
        call_info = CallInfo(
            call_id=session_id,
            phone_number=phone_number,
            session_id=session_id,
            status=status,
            quality_metrics=quality_metrics
        )

        self.active_calls[session_id] = call_info
        self.quality_history[session_id] = [quality_metrics]

        if outcome == "failed":
            raise TelephonyError(f"Call to {phone_number} failed to connect.")

        return session_id

    async def end_call(self, call_id: str) -> None:
        """Simulate ending a call."""
        self._log.info(f"Simulating ending call {call_id}")
        if call_id in self.active_calls:
            self.active_calls[call_id].status = CallStatus.ENDED
            self.active_calls[call_id].end_time = datetime.now()
            del self.active_calls[call_id]
            # Keep quality history for a while for analysis
            # In a real implementation, this might be persisted

    async def get_audio_stream(self, call_id: str) -> AsyncGenerator[AudioChunk, None]:
        """Simulate an audio stream from the call."""
        self._log.info(f"Simulating audio stream for call {call_id}")

        if call_id not in self.active_calls or self.active_calls[call_id].status != CallStatus.ANSWERED:
            self._log.warning(f"Cannot get audio stream for call {call_id}, status is {self.active_calls.get(call_id, CallInfo('', '', '', CallStatus.FAILED)).status}")
            return

        # Simulate a short conversation
        for i in range(10):
            if call_id not in self.active_calls or self.active_calls[call_id].status != CallStatus.ANSWERED:
                break

            # Update quality metrics periodically
            if i % 3 == 0:
                await self._update_quality_metrics(call_id)

            # Simulate user speaking a short phrase
            duration_ms = 1000
            sample_rate = 16000
            samples = int(duration_ms * sample_rate / 1000)
            audio_data = np.random.randint(-10000, 10000, samples, dtype=np.int16).tobytes()

            chunk = AudioChunk(
                data=audio_data,
                format=AudioFormat.PCM_16KHZ_MONO,
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                sample_rate=sample_rate
            )
            yield chunk
            await asyncio.sleep(0.05) # Shorter delay for testing

    async def send_audio(self, call_id: str, audio_chunk: AudioChunk) -> None:
        """Simulate sending audio to the call."""
        if call_id not in self.active_calls or self.active_calls[call_id].status != CallStatus.ANSWERED:
            raise TelephonyError(f"Call {call_id} is not active.")

        self._log.info(f"Simulating sending {len(audio_chunk.data)} bytes of audio to call {call_id}")
        # In a real implementation, this would send audio to the telephony system
        await asyncio.sleep(0.01) # Simulate network latency

    async def get_call_info(self, call_id: str) -> Optional[CallInfo]:
        """Get information about an active call."""
        return self.active_calls.get(call_id)

    async def get_call_quality(self, call_id: str) -> Optional[CallQualityMetrics]:
        """Get current quality metrics for a call."""
        call_info = self.active_calls.get(call_id)
        if call_info:
            return call_info.quality_metrics
        return None

    async def get_active_calls(self) -> List[CallInfo]:
        """Get information about all active calls."""
        return list(self.active_calls.values())

    async def detect_connection_problems(self, call_id: str) -> List[ConnectionProblem]:
        """Detect connection problems for a specific call."""
        problems = []
        call_info = self.active_calls.get(call_id)

        if not call_info or not call_info.quality_metrics:
            return problems

        metrics = call_info.quality_metrics

        # Check for high packet loss
        if metrics.packet_loss_percent > 3.0:
            problems.append(ConnectionProblem(
                problem_type="high_packet_loss",
                severity="high" if metrics.packet_loss_percent > 5.0 else "medium",
                description=f"Packet loss: {metrics.packet_loss_percent:.1f}%",
                call_id=call_id,
                metrics=metrics
            ))

        # Check for high jitter
        if metrics.jitter_ms > 100:
            problems.append(ConnectionProblem(
                problem_type="high_jitter",
                severity="high" if metrics.jitter_ms > 150 else "medium",
                description=f"Jitter: {metrics.jitter_ms:.1f}ms",
                call_id=call_id,
                metrics=metrics
            ))

        # Check for high latency
        if metrics.latency_ms > 200:
            problems.append(ConnectionProblem(
                problem_type="high_latency",
                severity="high" if metrics.latency_ms > 300 else "medium",
                description=f"Latency: {metrics.latency_ms:.1f}ms",
                call_id=call_id,
                metrics=metrics
            ))

        # Check for low audio level
        if metrics.audio_level_db < -60:
            problems.append(ConnectionProblem(
                problem_type="low_audio_level",
                severity="medium",
                description=f"Audio level: {metrics.audio_level_db:.1f}dB",
                call_id=call_id,
                metrics=metrics
            ))

        return problems

    async def get_system_status(self) -> Dict[str, Any]:
        """Get overall telephony system status."""
        active_call_count = len(self.active_calls)

        # Calculate average quality metrics
        if self.active_calls:
            avg_jitter = sum(call.quality_metrics.jitter_ms for call in self.active_calls.values() if call.quality_metrics) / len(self.active_calls)
            avg_packet_loss = sum(call.quality_metrics.packet_loss_percent for call in self.active_calls.values() if call.quality_metrics) / len(self.active_calls)
            avg_latency = sum(call.quality_metrics.latency_ms for call in self.active_calls.values() if call.quality_metrics) / len(self.active_calls)
        else:
            avg_jitter = avg_packet_loss = avg_latency = 0.0

        return {
            "status": "operational",
            "active_calls": active_call_count,
            "max_concurrent_calls": 100,  # Mock limit
            "average_quality": {
                "jitter_ms": avg_jitter,
                "packet_loss_percent": avg_packet_loss,
                "latency_ms": avg_latency
            },
            "system_load": random.uniform(0.1, 0.8),
            "uptime_seconds": 3600,  # Mock uptime
            "last_updated": datetime.now().isoformat()
        }

    async def _update_quality_metrics(self, call_id: str) -> None:
        """Update quality metrics for a call."""
        if call_id not in self.active_calls:
            return

        # Simulate changing quality metrics
        call_info = self.active_calls[call_id]
        if call_info.quality_metrics:
            # Add some variation to simulate real network conditions
            new_metrics = CallQualityMetrics(
                jitter_ms=max(0, call_info.quality_metrics.jitter_ms + random.uniform(-10, 10)),
                packet_loss_percent=max(0, call_info.quality_metrics.packet_loss_percent + random.uniform(-0.5, 0.5)),
                latency_ms=max(0, call_info.quality_metrics.latency_ms + random.uniform(-20, 20)),
                audio_level_db=call_info.quality_metrics.audio_level_db + random.uniform(-5, 5),
                signal_to_noise_ratio=max(0, call_info.quality_metrics.signal_to_noise_ratio + random.uniform(-2, 2))
            )

            call_info.quality_metrics = new_metrics

            # Add to history
            if call_id in self.quality_history:
                self.quality_history[call_id].append(new_metrics)
                # Keep only recent history
                if len(self.quality_history[call_id]) > 100:
                    self.quality_history[call_id] = self.quality_history[call_id][-50:]
