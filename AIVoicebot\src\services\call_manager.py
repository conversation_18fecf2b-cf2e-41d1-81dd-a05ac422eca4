"""
Call Management System

This module provides call session management, including call initiation,
termination, and lifecycle orchestration for the AI voice customer service system.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid

from ..core.interfaces import (
    ICallManager, IConversationEngine, ITelephonyInterface,
    CallSession, CallState, CallSummary, ConversationContext,
    AIVoiceServiceError, CallManagementError
)
from ..core.base_component import BaseComponent
from .conversation_logger import ConversationLogger


@dataclass
class CallManagerConfig:
    """Configuration for CallManager."""
    max_concurrent_calls: int = 10
    call_timeout_seconds: int = 1800  # 30 minutes
    default_script_id: str = "default"


class CallManager(BaseComponent, ICallManager):
    """
    Manages outbound calls and the overall conversation lifecycle.
    
    Orchestrates the interaction between the telephony interface and the
    conversation engine for each active call session.
    """
    
    def __init__(
        self,
        config: CallManagerConfig,
        conversation_engine: IConversationEngine,
        telephony_interface: ITelephonyInterface,
        conversation_logger: ConversationLogger,
        config_manager,
        logger=None
    ):
        """
        Initialize CallManager.
        
        Args:
            config: CallManager configuration
            conversation_engine: Conversation engine instance
            telephony_interface: Telephony interface instance
            conversation_logger: Logger for conversation events
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("call_manager", config_manager, logger)
        
        self.config = config
        self.conversation_engine = conversation_engine
        self.telephony_interface = telephony_interface
        self.conversation_logger = conversation_logger
        
        self.active_calls: Dict[str, CallSession] = {}
        self._call_tasks: Dict[str, asyncio.Task] = {}
    
    async def _initialize_impl(self) -> None:
        """Initialize CallManager."""
        self._log.info("Initializing CallManager...")
        # Ensure dependencies are initialized
        if not self.conversation_engine.is_initialized:
            await self.conversation_engine.initialize()
        if not self.telephony_interface.is_initialized:
            await self.telephony_interface.initialize()
        self._log.info("CallManager initialized")

    async def _start_impl(self) -> None:
        """Start CallManager."""
        self._log.info("Starting CallManager...")
        if not self.conversation_engine.is_running:
            await self.conversation_engine.start()
        if not self.telephony_interface.is_running:
            await self.telephony_interface.start()
        self._log.info("CallManager started")

    async def _stop_impl(self) -> None:
        """Stop CallManager and all active calls."""
        self._log.info("Stopping CallManager...")
        
        # End all active calls
        active_session_ids = list(self.active_calls.keys())
        if active_session_ids:
            await asyncio.gather(
                *[self.end_call(session_id, "system_shutdown") for session_id in active_session_ids]
            )
        
        # Stop dependencies
        if self.conversation_engine.is_running:
            await self.conversation_engine.stop()
        if self.telephony_interface.is_running:
            await self.telephony_interface.stop()
            
        self._log.info("CallManager stopped")

    async def _cleanup_impl(self) -> None:
        """Cleanup CallManager resources."""
        self._log.info("Cleaning up CallManager...")
        if self.conversation_engine.is_initialized:
            await self.conversation_engine.cleanup()
        if self.telephony_interface.is_initialized:
            await self.telephony_interface.cleanup()
        self._log.info("CallManager cleanup completed")

    async def initiate_call(self, phone_number: str, script_id: Optional[str] = None) -> CallSession:
        """
        Initiate a new outbound call.
        
        Args:
            phone_number: Phone number to call
            script_id: Script ID to use for the call
            
        Returns:
            The created CallSession object
            
        Raises:
            CallManagementError: If the call cannot be initiated
        """
        if len(self.active_calls) >= self.config.max_concurrent_calls:
            raise CallManagementError("Maximum concurrent calls reached")

        session_id = str(uuid.uuid4())
        script_id = script_id or self.config.default_script_id
        
        # Create conversation context
        conversation_context = ConversationContext(
            session_id=session_id,
            conversation_history=[],
            current_intent="",
            customer_info={"phone_number": phone_number},
            script_position="",
            metadata={}
        )
        
        # Create call session
        session = CallSession(
            session_id=session_id,
            phone_number=phone_number,
            start_time=datetime.now(),
            state=CallState.INITIALIZING,
            conversation_context=conversation_context,
            script_id=script_id
        )
        
        self.active_calls[session_id] = session
        
        # Log the start of the session
        await self.conversation_logger.log_session_start(session)

        # Start call handling in a background task
        self._call_tasks[session_id] = asyncio.create_task(self._handle_call(session))
        
        self._log.info(f"Initiated call to {phone_number} with session ID: {session_id}")
        return session

    async def end_call(self, session_id: str, reason: str = "completed") -> CallSummary:
        """
        End an active call and return a summary.
        
        Args:
            session_id: The ID of the session to end
            reason: The reason for ending the call
            
        Returns:
            A summary of the completed call
            
        Raises:
            CallManagementError: If the session is not found
        """
        if session_id not in self.active_calls:
            raise CallManagementError(f"Session not found: {session_id}")

        session = self.active_calls[session_id]
        session.state = CallState.ENDING
        
        # Cancel the call handling task
        if session_id in self._call_tasks:
            self._call_tasks[session_id].cancel()
            del self._call_tasks[session_id]
            
        # End the telephony call
        try:
            # Assuming the telephony interface uses the session_id as the call_id
            await self.telephony_interface.end_call(session_id)
        except Exception as e:
            self._log.error(f"Error ending telephony call for session {session_id}: {e}")

        session.end_time = datetime.now()
        session.state = CallState.COMPLETED
        
        # Create call summary
        summary = CallSummary(
            session_id=session.session_id,
            phone_number=session.phone_number,
            start_time=session.start_time,
            end_time=session.end_time,
            duration=(session.end_time - session.start_time).total_seconds(),
            total_turns=len(session.conversation_context.conversation_history),
            final_intent=session.conversation_context.current_intent,
            success=reason == "completed",
            transcript=session.conversation_context.conversation_history
        )
        
        # Remove from active calls
        del self.active_calls[session_id]
        
        # Log the end of the session
        await self.conversation_logger.log_session_end(summary)

        self._log.info(f"Call ended for session {session_id}, reason: {reason}")
        return summary

    async def get_active_sessions(self) -> List[CallSession]:
        """Get a list of all active call sessions."""
        return list(self.active_calls.values())

    async def get_session(self, session_id: str) -> Optional[CallSession]:
        """Get a specific call session by its ID."""
        return self.active_calls.get(session_id)

    def get_call_statistics(self) -> Dict[str, Any]:
        """Get call statistics."""
        return {
            "active_calls": len(self.active_calls),
            "total_calls_handled": 0,  # Would be tracked in a real implementation
            "average_call_duration": 0.0,  # Would be calculated from historical data
            "success_rate": 100.0,  # Would be calculated from historical data
            "max_concurrent_calls": self.config.max_concurrent_calls
        }

    async def _handle_call(self, session: CallSession):
        """The main logic for handling a single call."""
        session.state = CallState.CONNECTING
        self._log.info(f"Handling call for session: {session.session_id}")
        
        try:
            # Make the call
            call_id = await self.telephony_interface.make_call(session.phone_number, session.session_id)
            # In a real scenario, you'd map call_id to session_id
            
            session.state = CallState.ACTIVE
            
            # Start the conversation engine for this session
            await self.conversation_engine.start_session(
                session_id=session.session_id,
                customer_info=session.conversation_context.customer_info
            )
            
            # Main conversation loop
            audio_stream = self.telephony_interface.get_audio_stream(call_id)
            
            # This is a simplified loop. A real implementation would involve
            # VAD, ASR, and TTS integration in a more complex manner.
            async for audio_chunk in audio_stream:
                # This is where you would integrate with other components
                # e.g., send audio to VAD, then ASR, then process text
                # with conversation engine, get response, send to TTS,
                # and send audio back via telephony_interface.
                
                # For this skeleton, we'll just log the audio chunk receipt
                self._log.debug(f"Received audio chunk for session {session.session_id}")

        except asyncio.CancelledError:
            self._log.info(f"Call handling for session {session.session_id} was cancelled.")
        except Exception as e:
            self._log.error(f"Error during call handling for session {session.session_id}: {e}")
            session.state = CallState.FAILED
        finally:
            if session.state not in [CallState.ENDING, CallState.COMPLETED, CallState.FAILED]:
                await self.end_call(session.session_id, "error")
            elif session.state == CallState.FAILED:
                # If the call failed, we still need to clean it up from active_calls
                if session.session_id in self.active_calls:
                    del self.active_calls[session.session_id]
