"""
Tests for comprehensive performance monitoring system.
"""

import asyncio
import pytest
from unittest.mock import Mock, patch
import time
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.performance_monitor import (
    PerformanceMonitor, MetricType, AlertSeverity, AlertThreshold,
    PerformanceMetric, PerformanceAlert, ComponentHealth, OptimizationRecommendation
)


class TestPerformanceMonitor:
    """Test comprehensive performance monitoring functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        config_manager = Mock()
        config_manager.get_config.side_effect = lambda key, default: {
            "performance.monitoring_interval_seconds": 1,  # Fast for testing
            "performance.metrics_retention_hours": 1,
            "performance.enable_alerting": True,
            "performance.enable_recommendations": True
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def monitor(self, mock_config_manager):
        """Create PerformanceMonitor instance."""
        return PerformanceMonitor(mock_config_manager)
    
    @pytest.mark.asyncio
    async def test_monitor_lifecycle(self, monitor):
        """Test performance monitor lifecycle."""
        await monitor.initialize()
        await monitor.start()
        
        assert monitor.is_initialized
        assert monitor.is_running
        
        await monitor.stop()
        await monitor.cleanup()
    
    def test_component_registration(self, monitor):
        """Test component registration."""
        component_name = "test_component"
        monitor.register_component(component_name)
        
        assert component_name in monitor.registered_components
        assert component_name in monitor.component_health
        
        health = monitor.get_component_health(component_name)
        assert health is not None
        assert health.component == component_name
        assert health.status == "unknown"
    
    def test_metric_recording(self, monitor):
        """Test metric recording."""
        component = "test_component"
        monitor.register_component(component)
        
        # Record response time
        monitor.record_response_time(component, 1500.0, {"endpoint": "/api/test"})
        
        # Record error
        monitor.record_error(component, "timeout_error", {"details": "Request timeout"})
        
        # Record throughput
        monitor.record_throughput(component, 100.5)
        
        # Record queue size
        monitor.record_queue_size(component, 25)
        
        # Verify metrics were recorded
        assert len(monitor.metrics[MetricType.RESPONSE_TIME.value]) > 0
        assert len(monitor.metrics[MetricType.ERROR_RATE.value]) > 0
        assert len(monitor.metrics[MetricType.THROUGHPUT.value]) > 0
        assert len(monitor.metrics[MetricType.QUEUE_SIZE.value]) > 0
        
        # Verify component-specific metrics
        assert len(monitor.component_metrics[component][MetricType.RESPONSE_TIME.value]) > 0
    
    def test_metric_statistics(self, monitor):
        """Test metric statistics calculation."""
        component = "test_component"
        monitor.register_component(component)
        
        # Record multiple response times
        response_times = [1000, 1500, 2000, 2500, 3000]
        for rt in response_times:
            monitor.record_response_time(component, rt)
        
        # Get statistics
        stats = monitor.get_metric_statistics(MetricType.RESPONSE_TIME, component)
        
        assert stats["count"] == 5
        assert stats["min"] == 1000
        assert stats["max"] == 3000
        assert stats["mean"] == 2000
        assert stats["median"] == 2000
        assert stats["p95"] > stats["median"]
    
    def test_alert_thresholds(self, monitor):
        """Test alert threshold management."""
        component = "test_component"
        monitor.register_component(component)
        
        # Add custom threshold
        threshold = AlertThreshold(
            metric_type=MetricType.RESPONSE_TIME,
            component=component,
            warning_threshold=2000.0,
            critical_threshold=3000.0,
            duration_seconds=30
        )
        
        monitor.add_alert_threshold(threshold)
        
        # Verify threshold was added
        threshold_key = f"{component}_{MetricType.RESPONSE_TIME.value}"
        assert threshold_key in monitor.alert_thresholds
    
    @pytest.mark.asyncio
    async def test_alert_triggering(self, monitor):
        """Test alert triggering."""
        await monitor.initialize()
        
        component = "test_component"
        monitor.register_component(component)
        
        # Add threshold with low values for testing
        threshold = AlertThreshold(
            metric_type=MetricType.RESPONSE_TIME,
            component=component,
            warning_threshold=1000.0,
            critical_threshold=1500.0,
            duration_seconds=1  # Short duration for testing
        )
        monitor.add_alert_threshold(threshold)
        
        # Record high response time to trigger alert
        monitor.record_response_time(component, 2000.0)
        
        # Wait a bit and check alerts
        await asyncio.sleep(0.1)
        await monitor._check_alerts()
        
        # Should have triggered an alert
        active_alerts = monitor.get_active_alerts()
        assert len(active_alerts) > 0
        
        alert = active_alerts[0]
        assert alert.component == component
        assert alert.metric_type == MetricType.RESPONSE_TIME
        assert alert.severity in [AlertSeverity.MEDIUM, AlertSeverity.CRITICAL]
    
    def test_component_health_tracking(self, monitor):
        """Test component health tracking."""
        component = "test_component"
        monitor.register_component(component)
        
        # Record some metrics
        monitor.record_response_time(component, 500.0)  # Good response time
        monitor.record_metric(MetricType.CPU_USAGE, component, 30.0)  # Low CPU
        monitor.record_metric(MetricType.MEMORY_USAGE, component, 40.0)  # Low memory
        
        health = monitor.get_component_health(component)
        assert health is not None
        assert health.response_time_ms == 500.0
        assert health.cpu_usage_percent == 30.0
        assert health.memory_usage_percent == 40.0
        assert health.status == "healthy"
        
        # Record high response time
        monitor.record_response_time(component, 6000.0)  # Very high response time
        
        health = monitor.get_component_health(component)
        assert health.status == "critical"
    
    def test_system_overview(self, monitor):
        """Test system overview generation."""
        # Register components and record metrics
        components = ["asr", "tts", "llm"]
        for component in components:
            monitor.register_component(component)
            monitor.record_response_time(component, 1000.0)
            monitor.record_error(component, "test_error")
        
        overview = monitor.get_system_overview()
        
        assert "timestamp" in overview
        assert "total_components" in overview
        assert "healthy_components" in overview
        assert "system_metrics" in overview
        assert "component_summary" in overview
        
        assert overview["total_components"] == 3
        assert len(overview["component_summary"]) == 3
    
    def test_performance_trends(self, monitor):
        """Test performance trends calculation."""
        component = "test_component"
        monitor.register_component(component)
        
        # Record increasing response times to create a trend
        base_time = datetime.now()
        for i in range(10):
            # Simulate metrics over time with increasing values
            metric = PerformanceMetric(
                metric_type=MetricType.RESPONSE_TIME,
                component=component,
                value=1000.0 + i * 100,  # Increasing trend
                timestamp=base_time + timedelta(minutes=i)
            )
            monitor.metrics[MetricType.RESPONSE_TIME.value].append(metric)
        
        trends = monitor.get_performance_trends(time_window_hours=1)
        
        assert "trends" in trends
        if "response_time" in trends["trends"]:
            trend_data = trends["trends"]["response_time"]
            assert trend_data["direction"] == "increasing"
            assert trend_data["slope"] > 0
    
    @pytest.mark.asyncio
    async def test_recommendations_generation(self, monitor):
        """Test optimization recommendations generation."""
        await monitor.initialize()
        
        # Record high CPU usage to trigger recommendation
        monitor.record_metric(MetricType.CPU_USAGE, "system", 85.0)
        monitor.record_metric(MetricType.CPU_USAGE, "system", 87.0)
        monitor.record_metric(MetricType.CPU_USAGE, "system", 89.0)
        
        # Generate recommendations
        await monitor._generate_recommendations()
        
        recommendations = monitor.get_recommendations()
        assert len(recommendations) > 0
        
        # Should have CPU usage recommendation
        cpu_recommendations = [r for r in recommendations if r.issue_type == "high_cpu_usage"]
        assert len(cpu_recommendations) > 0
        
        cpu_rec = cpu_recommendations[0]
        assert cpu_rec.component == "system"
        assert cpu_rec.severity == AlertSeverity.HIGH
        assert "CPU usage" in cpu_rec.description
    
    def test_alert_callbacks(self, monitor):
        """Test alert callback functionality."""
        callback_called = []
        
        def test_callback(alert: PerformanceAlert):
            callback_called.append(alert)
        
        monitor.add_alert_callback(test_callback)
        
        # Manually create and trigger an alert
        alert = PerformanceAlert(
            alert_id="test_alert",
            metric_type=MetricType.RESPONSE_TIME,
            component="test_component",
            severity=AlertSeverity.HIGH,
            message="Test alert",
            current_value=5000.0,
            threshold_value=3000.0
        )
        
        # Simulate alert triggering
        monitor.active_alerts["test_alert"] = alert
        
        # Trigger callback manually
        for callback in monitor.alert_callbacks:
            callback(alert)
        
        assert len(callback_called) == 1
        assert callback_called[0].alert_id == "test_alert"
    
    @pytest.mark.asyncio
    async def test_system_metrics_collection(self, monitor):
        """Test system metrics collection."""
        await monitor.initialize()

        # Mock the _get_resource_usage method to avoid psutil dependency
        async def mock_get_resource_usage():
            return {
                "cpu_percent": 45.0,
                "memory_percent": 60.0,
                "disk_percent": 50.0,
                "memory_available_gb": 4.0,
                "disk_free_gb": 50.0
            }

        monitor._get_resource_usage = mock_get_resource_usage

        await monitor._collect_system_metrics()

        # Check that system metrics were recorded
        cpu_metrics = monitor.metrics.get(MetricType.CPU_USAGE.value, [])
        memory_metrics = monitor.metrics.get(MetricType.MEMORY_USAGE.value, [])
        disk_metrics = monitor.metrics.get(MetricType.DISK_USAGE.value, [])

        assert len(cpu_metrics) > 0
        assert len(memory_metrics) > 0
        assert len(disk_metrics) > 0
    
    def test_legacy_compatibility(self, monitor):
        """Test backward compatibility with legacy methods."""
        component = "test_component"
        
        # Use legacy methods
        monitor.record_response_time(component, 1500.0)
        monitor.record_error(component)
        
        # Get legacy summary
        summary = monitor.get_summary()
        
        assert "avg_response_time_ms" in summary
        assert "error_rate_percent" in summary
        assert "resource_usage" in summary
        assert "component_avg_response_time" in summary
    
    def test_metric_cleanup(self, monitor):
        """Test metric cleanup functionality."""
        component = "test_component"
        monitor.register_component(component)
        
        # Record old metrics
        old_time = datetime.now() - timedelta(hours=25)  # Older than retention
        old_metric = PerformanceMetric(
            metric_type=MetricType.RESPONSE_TIME,
            component=component,
            value=1000.0,
            timestamp=old_time
        )
        
        monitor.metrics[MetricType.RESPONSE_TIME.value].append(old_metric)
        monitor.component_metrics[component][MetricType.RESPONSE_TIME.value].append(old_metric)
        
        # Record recent metrics
        monitor.record_response_time(component, 1500.0)
        
        # Run cleanup
        monitor._cleanup_old_metrics()
        
        # Old metrics should be removed, recent ones should remain
        response_metrics = monitor.metrics[MetricType.RESPONSE_TIME.value]
        recent_metrics = [m for m in response_metrics if m.timestamp > old_time + timedelta(hours=1)]
        assert len(recent_metrics) > 0


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestPerformanceMonitor().test_monitor_lifecycle(PerformanceMonitor(Mock())))
