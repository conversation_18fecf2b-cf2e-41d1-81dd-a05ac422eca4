"""
SenseVoiceSmall model wrapper for speech recognition.

This module provides a wrapper around the SenseVoiceSmall model for converting
speech audio to text with confidence scoring and language detection.
"""

import torch
import torchaudio
import numpy as np
import logging
import os
from typing import Optional, List, Dict, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
import threading

from ...core.interfaces import AudioChunk, AudioFormat, ISpeechRecognizer, TranscriptionResult, AudioProcessingError
from ...core.base_component import BaseComponent


@dataclass
class SenseVoiceConfig:
    """Configuration for SenseVoice speech recognition."""
    model_path: str = "models/SenseVoiceSmall/model.pt"
    
    # Audio processing parameters
    sample_rate: int = 16000  # Required sample rate for SenseVoice
    chunk_duration_ms: int = 1000  # Processing chunk duration
    overlap_ms: int = 200  # Overlap between chunks for better accuracy
    
    # Recognition parameters
    language: str = "zh"  # Primary language (zh for Chinese)
    enable_language_detection: bool = True  # Auto-detect language
    confidence_threshold: float = 0.3  # Minimum confidence for valid transcription
    
    # Model parameters
    beam_size: int = 5  # Beam search size
    max_length: int = 512  # Maximum output sequence length
    temperature: float = 1.0  # Sampling temperature
    
    # Performance settings
    use_gpu: bool = True  # Use GPU if available
    batch_size: int = 1  # Batch size for processing
    num_threads: int = 4  # Number of CPU threads
    
    # Advanced settings
    enable_vad_filter: bool = True  # Filter using VAD results
    enable_noise_reduction: bool = True  # Apply noise reduction
    normalize_audio: bool = True  # Normalize audio amplitude
    
    # Output settings
    return_timestamps: bool = True  # Include word-level timestamps
    return_confidence: bool = True  # Include confidence scores
    punctuation: bool = True  # Add punctuation to output


@dataclass
class SenseVoiceResult:
    """Result from SenseVoice recognition."""
    text: str
    confidence: float
    language: str
    timestamp: datetime
    
    # Optional detailed information
    word_timestamps: Optional[List[Dict[str, Any]]] = None
    word_confidences: Optional[List[float]] = None
    processing_time_ms: Optional[float] = None
    audio_duration_ms: Optional[float] = None
    
    def to_transcription_result(self) -> TranscriptionResult:
        """Convert to standard TranscriptionResult."""
        return TranscriptionResult(
            text=self.text,
            confidence=self.confidence,
            is_final=True,
            timestamp=self.timestamp,
            language=self.language
        )


class SenseVoiceRecognizer(BaseComponent, ISpeechRecognizer):
    """
    SenseVoiceSmall model wrapper for speech recognition.
    
    Provides high-quality Chinese speech recognition with support for
    multiple languages, confidence scoring, and real-time processing.
    """
    
    def __init__(self, config: SenseVoiceConfig, config_manager, logger=None):
        """
        Initialize SenseVoice recognizer.
        
        Args:
            config: SenseVoice configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("sensevoice_recognizer", config_manager, logger)
        
        self.config = config
        self.model = None
        self.device = None
        
        # Audio preprocessing
        self._audio_processor = None
        self._resampler = None
        
        # Performance tracking
        self._recognition_count = 0
        self._total_audio_duration = 0.0
        self._total_processing_time = 0.0
        self._error_count = 0
        
        # Threading for model operations
        self._model_lock = threading.Lock()
        
        # Audio buffer for chunk processing
        self._audio_buffer = []
        self._buffer_lock = threading.Lock()
    
    async def _initialize_impl(self) -> None:
        """Initialize the SenseVoice model."""
        self._log.info("Initializing SenseVoice model...")
        
        try:
            # Set device
            if self.config.use_gpu and torch.cuda.is_available():
                self.device = torch.device("cuda")
                self._log.info("Using GPU for SenseVoice inference")
            else:
                self.device = torch.device("cpu")
                self._log.info("Using CPU for SenseVoice inference")
            
            # Load model
            model_path = os.path.abspath(self.config.model_path)
            if not os.path.exists(model_path):
                raise AudioProcessingError(f"SenseVoice model not found: {model_path}")
            
            self._log.info(f"Loading SenseVoice model from {model_path}")
            
            # Load the model (assuming it's a PyTorch model)
            self.model = torch.load(model_path, map_location=self.device)
            
            if hasattr(self.model, 'eval'):
                self.model.eval()
            
            # Set number of threads for CPU inference
            if self.device.type == 'cpu':
                torch.set_num_threads(self.config.num_threads)
            
            # Initialize audio preprocessing
            self._setup_audio_preprocessing()
            
            self._log.info("SenseVoice model loaded successfully")
            
        except Exception as e:
            self._log.error(f"Failed to initialize SenseVoice: {e}")
            raise AudioProcessingError(f"SenseVoice initialization failed: {e}")
    
    async def _start_impl(self) -> None:
        """Start the speech recognizer."""
        self._log.info("Starting SenseVoice recognizer...")
        
        # Reset statistics
        self._recognition_count = 0
        self._total_audio_duration = 0.0
        self._total_processing_time = 0.0
        self._error_count = 0
        
        self._log.info("SenseVoice recognizer started")
    
    async def _stop_impl(self) -> None:
        """Stop the speech recognizer."""
        self._log.info("Stopping SenseVoice recognizer...")
        
        # Log final statistics
        if self._recognition_count > 0:
            avg_processing_time = self._total_processing_time / self._recognition_count
            real_time_factor = self._total_audio_duration / self._total_processing_time if self._total_processing_time > 0 else 0
            error_rate = self._error_count / self._recognition_count
            
            self._log.info(f"SenseVoice Statistics:")
            self._log.info(f"  Total recognitions: {self._recognition_count}")
            self._log.info(f"  Total audio duration: {self._total_audio_duration:.1f}s")
            self._log.info(f"  Average processing time: {avg_processing_time:.3f}s")
            self._log.info(f"  Real-time factor: {real_time_factor:.1f}x")
            self._log.info(f"  Error rate: {error_rate:.2%}")
        
        self._log.info("SenseVoice recognizer stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup SenseVoice resources."""
        self._log.info("Cleaning up SenseVoice recognizer...")
        
        # Clear model references
        self.model = None
        self.device = None
        self._audio_processor = None
        self._resampler = None
        
        # Clear buffers
        with self._buffer_lock:
            self._audio_buffer.clear()
        
        self._log.info("SenseVoice recognizer cleanup completed")
    
    def _setup_audio_preprocessing(self) -> None:
        """Setup audio preprocessing pipeline."""
        self._log.debug("Setting up audio preprocessing pipeline")
        
        # Create resampler if needed
        if self.config.sample_rate != 16000:
            self._resampler = torchaudio.transforms.Resample(
                orig_freq=self.config.sample_rate,
                new_freq=16000
            ).to(self.device)
        
        # Setup audio normalization and filtering
        if self.config.enable_noise_reduction:
            # Note: HighpassBiquad is not available in this torchaudio version
            # Using simple normalization instead
            self._log.warning("HighpassBiquad not available, using simple normalization")
            self._audio_processor = None
    
    def _preprocess_audio(self, audio_chunk: AudioChunk) -> torch.Tensor:
        """
        Preprocess audio chunk for SenseVoice.
        
        Args:
            audio_chunk: Input audio chunk
            
        Returns:
            Preprocessed audio tensor
        """
        # Convert bytes to numpy array
        if isinstance(audio_chunk.data, bytes):
            audio_np = np.frombuffer(audio_chunk.data, dtype=np.int16)
        else:
            audio_np = audio_chunk.data
        
        # Convert to float32 and normalize
        audio_float = audio_np.astype(np.float32) / 32768.0
        
        # Convert to tensor
        audio_tensor = torch.from_numpy(audio_float).unsqueeze(0).to(self.device)
        
        # Resample if necessary
        if self._resampler is not None:
            audio_tensor = self._resampler(audio_tensor)
        
        # Apply noise reduction
        if self._audio_processor is not None and self.config.enable_noise_reduction:
            audio_tensor = self._audio_processor(audio_tensor)
        
        # Normalize amplitude
        if self.config.normalize_audio:
            max_val = torch.max(torch.abs(audio_tensor))
            if max_val > 0:
                audio_tensor = audio_tensor / max_val * 0.9
        
        return audio_tensor
    
    def _postprocess_transcription(self, raw_output: Any, confidence: float) -> SenseVoiceResult:
        """
        Postprocess model output to create transcription result.
        
        Args:
            raw_output: Raw model output
            confidence: Recognition confidence
            
        Returns:
            Processed transcription result
        """
        # Extract text from model output
        if isinstance(raw_output, str):
            text = raw_output
        elif hasattr(raw_output, 'text'):
            text = raw_output.text
        elif isinstance(raw_output, dict) and 'text' in raw_output:
            text = raw_output['text']
        else:
            text = str(raw_output)
        
        # Clean up text
        text = text.strip()
        
        # Add punctuation if enabled
        if self.config.punctuation and text and not text[-1] in '。！？.,!?':
            text += '。'
        
        # Detect language (simplified)
        language = self.config.language
        if self.config.enable_language_detection:
            # Simple heuristic: if contains Chinese characters, it's Chinese
            if any('\u4e00' <= char <= '\u9fff' for char in text):
                language = 'zh'
            elif text.isascii():
                language = 'en'
        
        return SenseVoiceResult(
            text=text,
            confidence=confidence,
            language=language,
            timestamp=datetime.now()
        )
    
    async def transcribe(self, audio_chunk: AudioChunk) -> TranscriptionResult:
        """
        Transcribe audio chunk to text.
        
        Args:
            audio_chunk: Audio chunk to transcribe
            
        Returns:
            Transcription result
        """
        if self.model is None:
            raise AudioProcessingError("SenseVoice model not initialized")
        
        start_time = datetime.now()
        
        try:
            # Preprocess audio
            audio_tensor = self._preprocess_audio(audio_chunk)
            
            # Calculate audio duration
            audio_duration_ms = len(audio_chunk.data) / 2 / audio_chunk.sample_rate * 1000
            
            # Run inference
            with self._model_lock:
                with torch.no_grad():
                    # Run actual model inference in a thread pool executor
                    # to avoid blocking the event loop.
                    loop = asyncio.get_running_loop()
                    output = await loop.run_in_executor(
                        None,  # uses default executor
                        self.model,
                        audio_tensor
                    )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Postprocess result
            confidence = output.get('confidence', 0.5)
            result = self._postprocess_transcription(output, confidence)
            result.processing_time_ms = processing_time
            result.audio_duration_ms = audio_duration_ms
            
            # Update statistics
            self._recognition_count += 1
            self._total_audio_duration += audio_duration_ms / 1000
            self._total_processing_time += processing_time / 1000
            
            # Check confidence threshold
            if result.confidence < self.config.confidence_threshold:
                self._log.debug(f"Low confidence transcription: {result.confidence:.3f} < {self.config.confidence_threshold}")
            
            self._log.debug(f"Transcribed: '{result.text}' (confidence: {result.confidence:.3f}, "
                          f"processing: {processing_time:.1f}ms)")
            
            return result.to_transcription_result()
            
        except Exception as e:
            self._error_count += 1
            self._log.error(f"Error in speech recognition: {e}")
            raise AudioProcessingError(f"Speech recognition failed: {e}")
    
    async def transcribe_stream(self, audio_stream):
        """
        Transcribe streaming audio to text.

        Args:
            audio_stream: Async generator of audio chunks

        Yields:
            Transcription results
        """
        async for audio_chunk in audio_stream:
            try:
                result = await self.transcribe(audio_chunk)
                yield result
            except Exception as e:
                self._log.error(f"Error transcribing audio stream: {e}")
                continue
    
    async def transcribe_batch(self, audio_chunks: List[AudioChunk]) -> List[TranscriptionResult]:
        """
        Transcribe multiple audio chunks in batch.
        
        Args:
            audio_chunks: List of audio chunks to transcribe
            
        Returns:
            List of transcription results
        """
        results = []
        
        for chunk in audio_chunks:
            try:
                result = await self.transcribe(chunk)
                results.append(result)
            except Exception as e:
                self._log.error(f"Error in batch transcription: {e}")
                # Add empty result to maintain alignment
                results.append(TranscriptionResult(
                    text="",
                    confidence=0.0,
                    is_final=True,
                    timestamp=datetime.now(),
                    language=self.config.language
                ))
        
        return results
    
    async def transcribe_long_audio(self, audio_chunks: List[AudioChunk]) -> TranscriptionResult:
        """
        Transcribe long audio by combining multiple chunks.
        
        Args:
            audio_chunks: List of audio chunks forming long audio
            
        Returns:
            Combined transcription result
        """
        if not audio_chunks:
            return TranscriptionResult(
                text="",
                confidence=0.0,
                is_final=True,
                timestamp=datetime.now(),
                language=self.config.language
            )
        
        # Combine audio chunks
        combined_audio = b''.join(chunk.data for chunk in audio_chunks)
        
        # Create combined chunk
        combined_chunk = AudioChunk(
            data=combined_audio,
            format=audio_chunks[0].format,
            timestamp=audio_chunks[0].timestamp,
            duration_ms=sum(chunk.duration_ms for chunk in audio_chunks),
            sample_rate=audio_chunks[0].sample_rate
        )
        
        # Transcribe combined audio
        return await self.transcribe(combined_chunk)
    
    def get_recognition_stats(self) -> Dict[str, Any]:
        """
        Get speech recognition statistics.
        
        Returns:
            Dictionary with recognition statistics
        """
        if self._recognition_count == 0:
            return {
                "total_recognitions": 0,
                "total_audio_duration_s": 0.0,
                "average_processing_time_s": 0.0,
                "real_time_factor": 0.0,
                "error_rate": 0.0,
                "throughput_hours_per_hour": 0.0
            }
        
        avg_processing_time = self._total_processing_time / self._recognition_count
        real_time_factor = self._total_audio_duration / self._total_processing_time if self._total_processing_time > 0 else 0
        error_rate = self._error_count / self._recognition_count
        throughput = self._total_audio_duration / 3600  # hours of audio processed
        
        return {
            "total_recognitions": self._recognition_count,
            "total_audio_duration_s": self._total_audio_duration,
            "average_processing_time_s": avg_processing_time,
            "real_time_factor": real_time_factor,
            "error_rate": error_rate,
            "throughput_hours_per_hour": throughput,
            "model_device": str(self.device),
            "model_path": self.config.model_path
        }
    
    def reset_stats(self) -> None:
        """Reset recognition statistics."""
        self._recognition_count = 0
        self._total_audio_duration = 0.0
        self._total_processing_time = 0.0
        self._error_count = 0
        
        self._log.info("Recognition statistics reset")
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration parameters.
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                old_value = getattr(self.config, key)
                setattr(self.config, key, value)
                self._log.info(f"Updated config {key}: {old_value} -> {value}")
            else:
                self._log.warning(f"Unknown config parameter: {key}")
    
    @property
    def is_gpu_available(self) -> bool:
        """Check if GPU is available and being used."""
        return self.device is not None and self.device.type == 'cuda'
    
    @property
    def model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            "model_path": self.config.model_path,
            "device": str(self.device) if self.device else "not_initialized",
            "language": self.config.language,
            "sample_rate": self.config.sample_rate,
            "confidence_threshold": self.config.confidence_threshold
        }


# Utility functions for SenseVoice

async def create_sensevoice_recognizer(
    model_path: str = "models/SenseVoiceSmall/model.pt",
    language: str = "zh",
    use_gpu: bool = True,
    config_manager=None,
    **kwargs
) -> SenseVoiceRecognizer:
    """
    Create and initialize a SenseVoice recognizer.
    
    Args:
        model_path: Path to SenseVoice model
        language: Primary language for recognition
        use_gpu: Whether to use GPU if available
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized SenseVoice recognizer
    """
    config = SenseVoiceConfig(
        model_path=model_path,
        language=language,
        use_gpu=use_gpu,
        **kwargs
    )
    
    recognizer = SenseVoiceRecognizer(config, config_manager)
    await recognizer.initialize()
    await recognizer.start()
    
    return recognizer


def calculate_recognition_metrics(results: List[TranscriptionResult]) -> Dict[str, float]:
    """
    Calculate recognition quality metrics from transcription results.
    
    Args:
        results: List of transcription results
        
    Returns:
        Dictionary with quality metrics
    """
    if not results:
        return {
            "total_results": 0,
            "average_confidence": 0.0,
            "high_confidence_ratio": 0.0,
            "empty_results_ratio": 0.0,
            "average_text_length": 0.0
        }
    
    confidences = [r.confidence for r in results]
    text_lengths = [len(r.text) for r in results]
    high_confidence_count = sum(1 for c in confidences if c > 0.7)
    empty_results_count = sum(1 for r in results if not r.text.strip())
    
    return {
        "total_results": len(results),
        "average_confidence": np.mean(confidences),
        "min_confidence": min(confidences),
        "max_confidence": max(confidences),
        "high_confidence_ratio": high_confidence_count / len(results),
        "empty_results_ratio": empty_results_count / len(results),
        "average_text_length": np.mean(text_lengths),
        "total_characters": sum(text_lengths)
    }