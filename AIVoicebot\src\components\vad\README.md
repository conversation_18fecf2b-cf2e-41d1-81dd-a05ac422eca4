# Voice Activity Detection (VAD) Components

This module provides voice activity detection capabilities using SileroVAD model for the AI Voice Customer Service system, including real-time speech detection, speech segmentation, and adaptive threshold management.

## Components

### SileroVADDetector
- **File**: `silero_vad_detector.py`
- **Purpose**: SileroVAD model wrapper for voice activity detection
- **Features**:
  - Real-time voice activity detection
  - Adaptive threshold adjustment based on noise levels
  - Background noise estimation
  - Performance statistics and monitoring
  - Configurable detection parameters

### SpeechSegmenter
- **File**: `speech_segmenter.py`
- **Purpose**: Speech segmentation using voice activity detection
- **Features**:
  - Speech boundary identification
  - Silence detection and speech endpoint determination
  - Adaptive threshold adjustment based on background noise
  - Speech segment padding and quality control
  - Real-time and batch processing modes

## Key Features

### Voice Activity Detection
- **Model**: SileroVAD (PyTorch/ONNX)
- **Sample Rate**: 16kHz (required)
- **Detection Threshold**: Configurable (default: 0.5)
- **Adaptive Threshold**: Automatic adjustment based on noise levels
- **Performance**: Real-time processing capability

### Speech Segmentation
- **Minimum Speech Duration**: 250ms (configurable)
- **Minimum Silence Duration**: 300ms (configurable)
- **Speech Padding**: Configurable start/end padding
- **Maximum Segment Duration**: 30 seconds (configurable)
- **Quality Control**: Confidence-based filtering

### Adaptive Features
- **Noise Level Adaptation**: Automatic threshold adjustment
- **Background Noise Estimation**: Continuous noise level monitoring
- **Threshold Smoothing**: Gradual threshold changes
- **Performance Optimization**: Real-time processing optimization

## Usage Examples

### Basic Voice Activity Detection

```python
from src.components.vad import SileroVADDetector, VADConfig

# Create VAD configuration
config = VADConfig(
    model_path="models/snakers4_silero-vad",
    threshold=0.5,
    enable_adaptive_threshold=True
)

# Create and initialize detector
detector = SileroVADDetector(config, config_manager)
await detector.initialize()
await detector.start()

# Detect voice activity
confidence = await detector.detect_voice_activity(audio_chunk)
detailed_result = await detector.detect_voice_activity_detailed(audio_chunk)

print(f"Voice confidence: {confidence:.3f}")
print(f"Has speech: {detailed_result.has_speech}")
```

### Speech Segmentation

```python
from src.components.vad import SpeechSegmenter, SegmentationConfig

# Create segmentation configuration
seg_config = SegmentationConfig(
    min_speech_duration_ms=250,
    min_silence_duration_ms=300,
    enable_adaptive_threshold=True
)

# Create segmenter (requires VAD detector)
segmenter = SpeechSegmenter(seg_config, vad_detector, config_manager)
await segmenter.initialize()
await segmenter.start()

# Segment audio stream
async for segment in segmenter.segment_audio_stream(audio_stream):
    print(f"Speech segment: {segment.duration_ms:.1f}ms, "
          f"confidence: {segment.average_confidence:.3f}")

# Or segment batch audio
segments = await segmenter.segment_single_audio(audio_chunks)
```

### Real-time Streaming

```python
# Create audio stream generator
async def audio_stream():
    while True:
        audio_chunk = await get_audio_chunk()  # Your audio source
        yield audio_chunk

# Process stream with segmentation
async for speech_segment in segmenter.segment_audio_stream(audio_stream()):
    # Process detected speech segment
    await process_speech_segment(speech_segment)
```

### Utility Functions

```python
from src.components.vad import create_vad_detector, create_speech_segmenter

# Quick detector creation
detector = await create_vad_detector(
    threshold=0.5,
    config_manager=config_manager,
    enable_adaptive_threshold=True
)

# Quick segmenter creation
segmenter = await create_speech_segmenter(
    vad_detector=detector,
    min_speech_duration_ms=200,
    config_manager=config_manager
)
```

## Configuration

### VAD Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `model_path` | "models/snakers4_silero-vad" | Path to SileroVAD model |
| `threshold` | 0.5 | Voice activity threshold (0.0-1.0) |
| `min_speech_duration_ms` | 250 | Minimum speech duration |
| `min_silence_duration_ms` | 100 | Minimum silence between speech |
| `sample_rate` | 16000 | Required sample rate for SileroVAD |
| `window_size_samples` | 512 | Processing window size |
| `speech_pad_ms` | 30 | Padding around detected speech |
| `use_onnx` | false | Use ONNX version for performance |
| `enable_adaptive_threshold` | true | Enable adaptive threshold |
| `noise_level_adaptation` | true | Adapt to noise levels |
| `threshold_adaptation_rate` | 0.1 | Threshold adaptation speed |

### Segmentation Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `min_speech_duration_ms` | 250 | Minimum speech segment duration |
| `min_silence_duration_ms` | 300 | Minimum silence to end speech |
| `max_speech_duration_ms` | 30000 | Maximum single speech segment |
| `speech_start_pad_ms` | 100 | Padding before speech start |
| `speech_end_pad_ms` | 200 | Padding after speech end |
| `enable_adaptive_threshold` | true | Enable adaptive threshold |
| `noise_adaptation_window` | 50 | Frames for noise estimation |
| `threshold_smoothing_factor` | 0.1 | Threshold change smoothing |
| `min_confidence_threshold` | 0.3 | Minimum confidence for speech |
| `confidence_smoothing_window` | 5 | Frames for confidence smoothing |

## Performance Monitoring

### VAD Statistics

```python
# Get detection statistics
stats = detector.get_detection_stats()

print(f"Total detections: {stats['total_detections']}")
print(f"Speech detected: {stats['speech_detected']}")
print(f"Speech rate: {stats['speech_rate']:.2%}")
print(f"Average processing time: {stats['average_processing_time_ms']:.2f}ms")
print(f"Current threshold: {stats['current_threshold']:.3f}")
print(f"Background noise level: {stats['background_noise_level']:.3f}")
```

### Segmentation Statistics

```python
# Get segmentation statistics
seg_stats = segmenter.get_segmentation_stats()

print(f"Segments detected: {seg_stats['segments_detected']}")
print(f"Total speech duration: {seg_stats['total_speech_duration_ms']:.1f}ms")
print(f"Average segment duration: {seg_stats['average_segment_duration_ms']:.1f}ms")
print(f"Speech active: {seg_stats['speech_active']}")
```

### Speech Analysis

```python
from src.components.vad import analyze_speech_segments, calculate_speech_statistics

# Analyze detected segments
analysis = analyze_speech_segments(segments)
print(f"Total segments: {analysis['total_segments']}")
print(f"Average duration: {analysis['average_duration_ms']:.1f}ms")
print(f"Average confidence: {analysis['average_confidence']:.3f}")

# Analyze VAD results
vad_stats = calculate_speech_statistics(vad_results)
print(f"Speech ratio: {vad_stats['speech_ratio']:.2%}")
print(f"Confidence range: {vad_stats['min_confidence']:.3f} - {vad_stats['max_confidence']:.3f}")
```

## Integration with Audio Streaming

### With Audio Stream Manager

```python
from src.components.audio import AudioStreamManager
from src.components.vad import SileroVADDetector

# Create audio stream for VAD processing
stream_config = StreamConfig(
    stream_id="vad_input",
    sample_rate=16000,
    target_latency_ms=50  # Low latency for VAD
)

stream = await audio_manager.create_stream(stream_config)

# Process audio through VAD
async for audio_chunk in audio_manager.get_stream_generator("vad_input"):
    vad_result = await vad_detector.detect_voice_activity_detailed(audio_chunk)
    
    if vad_result.has_speech:
        # Forward to speech recognition
        await asr_system.process_audio(audio_chunk)
```

### With Speech Recognition Pipeline

```python
# Complete VAD -> ASR pipeline
async def vad_asr_pipeline(audio_stream):
    async for speech_segment in segmenter.segment_audio_stream(audio_stream):
        # Process each speech segment with ASR
        transcription = await asr_system.transcribe_segment(speech_segment)
        
        if transcription.is_final:
            yield transcription
```

## Model Requirements

### SileroVAD Model Setup

1. **Download Model**: The SileroVAD model should be placed in `models/snakers4_silero-vad/`
2. **Dependencies**: Requires PyTorch and torchaudio
3. **Model Files**:
   - `hubconf.py` - Model loading configuration
   - `src/silero_vad/` - Model implementation
   - `src/silero_vad/data/silero_vad.jit` - PyTorch JIT model
   - `src/silero_vad/data/silero_vad.onnx` - ONNX model (optional)

### Installation

```bash
# Install PyTorch dependencies
pip install torch torchaudio

# The SileroVAD model is included in the models directory
# No additional installation required
```

## Error Handling

### Common Issues

1. **Model Loading Errors**
   - Ensure SileroVAD model files are present
   - Check PyTorch installation
   - Verify model path configuration

2. **Audio Format Issues**
   - SileroVAD requires 16kHz sample rate
   - Audio should be mono (single channel)
   - Input should be 16-bit PCM format

3. **Performance Issues**
   - Use ONNX version for better performance
   - Adjust processing window size
   - Enable adaptive threshold for varying conditions

### Error Recovery

```python
try:
    confidence = await detector.detect_voice_activity(audio_chunk)
except AudioProcessingError as e:
    logger.error(f"VAD processing failed: {e}")
    # Fallback to default behavior
    confidence = 0.0

# Check detector health
if not detector.is_running:
    await detector.start()
```

## Performance Optimization

### Real-time Processing

- **Target Latency**: < 50ms for real-time applications
- **Processing Speed**: Typically 10-50x real-time on modern hardware
- **Memory Usage**: ~10-20MB per detector instance
- **CPU Usage**: Low, suitable for concurrent processing

### Optimization Tips

1. **Use ONNX Model**: Better performance than PyTorch JIT
2. **Batch Processing**: Process multiple chunks together when possible
3. **Adaptive Threshold**: Reduces false positives in noisy environments
4. **Window Size**: Smaller windows for lower latency, larger for accuracy
5. **Threading**: Use separate threads for model inference if needed

## Testing

### Unit Tests

```bash
# Run VAD tests
python -m pytest tests/test_vad.py -v

# Run specific test
python -m pytest tests/test_vad.py::TestSileroVADDetector::test_voice_activity_detection -v
```

### Example Usage

```bash
# Run VAD examples
python examples/vad_example.py
```

### Performance Benchmarking

```python
# Benchmark VAD performance
from examples.vad_example import performance_benchmark
await performance_benchmark()
```

## Troubleshooting

### High False Positive Rate

- Increase detection threshold
- Enable adaptive threshold
- Check background noise levels
- Verify audio quality

### Missing Speech Detection

- Decrease detection threshold
- Check audio amplitude levels
- Verify sample rate (must be 16kHz)
- Enable noise level adaptation

### Performance Issues

- Use ONNX model version
- Reduce processing window size
- Check system resources
- Consider hardware acceleration

### Model Loading Issues

- Verify model files are present
- Check PyTorch installation
- Ensure correct Python path
- Check file permissions

## Integration Examples

### With Telephony System

```python
async def telephony_vad_integration(call_audio_stream):
    """Integrate VAD with telephony system."""
    
    async for speech_segment in segmenter.segment_audio_stream(call_audio_stream):
        # Process speech segment
        if speech_segment.duration_ms > 500:  # Only process longer segments
            # Forward to speech recognition
            transcription = await asr_system.transcribe(speech_segment)
            
            # Generate response
            response = await conversation_engine.process(transcription.text)
            
            # Convert to speech and play
            tts_audio = await tts_system.synthesize(response.text)
            await telephony_system.play_audio(tts_audio)
```

### With Conversation Engine

```python
async def conversation_vad_pipeline(audio_stream, conversation_engine):
    """Complete conversation pipeline with VAD."""
    
    async for speech_segment in segmenter.segment_audio_stream(audio_stream):
        try:
            # Transcribe speech
            transcription = await asr_system.transcribe(speech_segment)
            
            # Process with conversation engine
            response = await conversation_engine.process_user_input(
                transcription.text,
                session_id="current_session"
            )
            
            # Generate audio response
            response_audio = await tts_system.synthesize(response.text)
            
            # Play response
            await audio_output.play(response_audio)
            
        except Exception as e:
            logger.error(f"Error in conversation pipeline: {e}")
```

This VAD system provides robust voice activity detection and speech segmentation capabilities essential for real-time voice interaction systems.