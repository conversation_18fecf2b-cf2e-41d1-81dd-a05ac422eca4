"""
Tests for conversation script management components.
"""

import asyncio
import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import Mock, patch
from datetime import datetime
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.interfaces import ScriptResponse
from src.components.scripts import (
    ScriptParser, ScriptParserConfig, ConversationScript,
    ResponseMatcher, ResponseMatcherConfig, MatchResult,
    ScriptManager, ScriptManagerConfig
)


class TestScriptParser:
    """Test script parser functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def parser_config(self):
        """Create test parser configuration."""
        return ScriptParserConfig(
            script_directory="test_scripts",
            query_column="用户问题",
            response_column="回复内容",
            intent_column="意图分类",
            enable_validation=True
        )
    
    @pytest.fixture
    def sample_excel_data(self):
        """Create sample Excel data for testing."""
        return pd.DataFrame({
            "用户问题": [
                "你好",
                "我想了解贷款产品",
                "利率是多少",
                "如何申请",
                ""  # Empty row for testing
            ],
            "回复内容": [
                "您好！欢迎咨询我们的服务。",
                "我们有多种贷款产品可供选择，包括个人贷款和企业贷款。",
                "我们的利率根据您的信用状况而定，一般在3.5%-8%之间。",
                "您可以通过我们的官网或手机APP进行在线申请。",
                "默认回复"
            ],
            "意图分类": [
                "问候",
                "产品咨询",
                "利率咨询",
                "申请流程",
                ""
            ],
            "优先级": [1, 2, 2, 1, 1]
        })
    
    def test_parser_creation(self, parser_config, mock_config_manager):
        """Test script parser creation."""
        parser = ScriptParser(parser_config, mock_config_manager)
        
        assert parser.config == parser_config
        assert len(parser.parsed_scripts) == 0
        assert len(parser.parsing_errors) == 0
    
    @pytest.mark.asyncio
    async def test_parser_lifecycle(self, parser_config, mock_config_manager):
        """Test parser lifecycle."""
        parser = ScriptParser(parser_config, mock_config_manager)
        
        # Mock directory existence
        with patch('os.path.exists', return_value=True):
            await parser.initialize()
            await parser.start()
            
            assert parser.is_initialized
            assert parser.is_running
            
            await parser.stop()
            await parser.cleanup()
    
    def test_excel_parsing(self, parser_config, mock_config_manager, sample_excel_data):
        """Test Excel file parsing."""
        parser = ScriptParser(parser_config, mock_config_manager)
        
        # Create temporary Excel file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            sample_excel_data.to_excel(tmp_file.name, index=False)
            tmp_file_path = tmp_file.name
        
        try:
            # Parse the Excel file
            scripts = parser.parse_excel_file(tmp_file_path)
            
            # Should parse 4 valid scripts (excluding empty row)
            assert len(scripts) == 4
            
            # Check first script
            first_script = scripts[0]
            assert first_script.query == "你好"
            assert first_script.response == "您好！欢迎咨询我们的服务。"
            assert first_script.intent == "问候"
            assert first_script.priority == 1
            
            # Check script IDs are unique
            script_ids = [s.script_id for s in scripts]
            assert len(script_ids) == len(set(script_ids))
            
        finally:
            # Clean up temporary file
            os.unlink(tmp_file_path)
    
    def test_script_validation(self, parser_config, mock_config_manager):
        """Test script validation."""
        parser = ScriptParser(parser_config, mock_config_manager)
        
        # Create test scripts
        valid_script = ConversationScript(
            script_id="test_1",
            query="测试问题",
            response="测试回复",
            intent="测试"
        )
        
        invalid_script = ConversationScript(
            script_id="test_2",
            query="",  # Empty query
            response="回复",
            intent="测试"
        )
        
        parser.parsed_scripts = {
            "test_1": valid_script,
            "test_2": invalid_script
        }
        
        # Validate scripts
        report = parser.validate_all_scripts()
        
        assert report["total_scripts"] == 2
        assert report["valid_scripts"] == 1
        assert report["invalid_scripts"] == 1
        assert len(report["validation_errors"]) == 1


class TestResponseMatcher:
    """Test response matcher functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def matcher_config(self):
        """Create test matcher configuration."""
        return ResponseMatcherConfig(
            fuzzy_threshold=0.6,
            enable_fuzzy_matching=True,
            enable_keyword_matching=True,
            enable_intent_matching=True
        )
    
    @pytest.fixture
    def sample_scripts(self):
        """Create sample scripts for testing."""
        return {
            "script_1": ConversationScript(
                script_id="script_1",
                query="你好",
                response="您好！",
                intent="问候",
                priority=1
            ),
            "script_2": ConversationScript(
                script_id="script_2",
                query="贷款利率是多少",
                response="我们的贷款利率在3.5%-8%之间",
                intent="利率咨询",
                priority=2
            ),
            "script_3": ConversationScript(
                script_id="script_3",
                query="如何申请贷款",
                response="您可以通过官网申请",
                intent="申请流程",
                priority=1
            )
        }
    
    @pytest.mark.asyncio
    async def test_matcher_creation(self, matcher_config, mock_config_manager):
        """Test response matcher creation."""
        matcher = ResponseMatcher(matcher_config, mock_config_manager)
        
        assert matcher.config == matcher_config
        assert len(matcher.scripts) == 0
    
    @pytest.mark.asyncio
    async def test_matcher_lifecycle(self, matcher_config, mock_config_manager):
        """Test matcher lifecycle."""
        matcher = ResponseMatcher(matcher_config, mock_config_manager)
        
        await matcher.initialize()
        await matcher.start()
        
        assert matcher.is_initialized
        assert matcher.is_running
        
        await matcher.stop()
        await matcher.cleanup()
    
    @pytest.mark.asyncio
    async def test_script_loading_and_matching(self, matcher_config, mock_config_manager, sample_scripts):
        """Test script loading and matching."""
        matcher = ResponseMatcher(matcher_config, mock_config_manager)
        
        await matcher.initialize()
        await matcher.start()
        
        # Load scripts
        matcher.load_scripts(sample_scripts)
        
        # Test exact match
        matches = matcher.find_matches("你好", limit=5)
        assert len(matches) > 0
        assert matches[0].script.script_id == "script_1"
        assert matches[0].confidence > 0.8
        
        # Test fuzzy match
        matches = matcher.find_matches("你好吗", limit=5)
        assert len(matches) > 0
        
        # Test keyword match
        matches = matcher.find_matches("贷款的利率", limit=5)
        assert len(matches) > 0
        # Should match script_2 which contains "贷款利率"
        
        await matcher.stop()
        await matcher.cleanup()
    
    @pytest.mark.asyncio
    async def test_intent_matching(self, matcher_config, mock_config_manager, sample_scripts):
        """Test intent-based matching."""
        matcher = ResponseMatcher(matcher_config, mock_config_manager)
        
        await matcher.initialize()
        await matcher.start()
        
        matcher.load_scripts(sample_scripts)
        
        # Test intent matching
        intent_matches = matcher.match_by_intent("问候", limit=5)
        assert len(intent_matches) > 0
        assert intent_matches[0].script.intent == "问候"
        
        await matcher.stop()
        await matcher.cleanup()
    
    @pytest.mark.asyncio
    async def test_matching_statistics(self, matcher_config, mock_config_manager, sample_scripts):
        """Test matching statistics."""
        matcher = ResponseMatcher(matcher_config, mock_config_manager)
        
        await matcher.initialize()
        await matcher.start()
        
        matcher.load_scripts(sample_scripts)
        
        # Perform some matches
        matcher.find_matches("你好", limit=5)
        matcher.find_matches("贷款", limit=5)
        matcher.find_matches("申请", limit=5)
        
        # Get statistics
        stats = matcher.get_matching_statistics()
        
        assert stats["total_queries"] == 3
        assert "average_match_time_ms" in stats
        assert "loaded_scripts" in stats
        
        await matcher.stop()
        await matcher.cleanup()


class TestScriptManager:
    """Test script manager functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def manager_config(self):
        """Create test manager configuration."""
        return ScriptManagerConfig(
            script_directory="test_scripts",
            enable_hot_reload=False,  # Disable for testing
            enable_validation=True,
            preload_scripts=False  # Don't preload for testing
        )
    
    @pytest.fixture
    def sample_excel_file(self, sample_excel_data):
        """Create temporary Excel file for testing."""
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            sample_excel_data.to_excel(tmp_file.name, index=False)
            return tmp_file.name
    
    @pytest.fixture
    def sample_excel_data(self):
        """Create sample Excel data."""
        return pd.DataFrame({
            "用户问题": ["你好", "贷款利率", "如何申请"],
            "回复内容": ["您好！", "利率在3.5%-8%", "请访问官网申请"],
            "意图分类": ["问候", "利率咨询", "申请流程"],
            "优先级": [1, 2, 1]
        })
    
    @pytest.mark.asyncio
    async def test_manager_creation(self, manager_config, mock_config_manager):
        """Test script manager creation."""
        manager = ScriptManager(manager_config, mock_config_manager)
        
        assert manager.config == manager_config
        assert isinstance(manager.parser, ScriptParser)
        assert isinstance(manager.matcher, ResponseMatcher)
    
    @pytest.mark.asyncio
    async def test_manager_lifecycle(self, manager_config, mock_config_manager):
        """Test manager lifecycle."""
        manager = ScriptManager(manager_config, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        assert manager.is_initialized
        assert manager.is_running
        
        await manager.stop()
        await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_script_loading_and_response_finding(self, manager_config, mock_config_manager, 
                                                     sample_excel_file):
        """Test script loading and response finding."""
        # Update config to use temporary directory
        temp_dir = os.path.dirname(sample_excel_file)
        manager_config.script_directory = temp_dir
        manager_config.parser_config = ScriptParserConfig(script_directory=temp_dir)
        
        manager = ScriptManager(manager_config, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        try:
            # Load scripts
            await manager.load_scripts()
            
            assert len(manager.loaded_scripts) > 0
            
            # Test finding response
            response = await manager.find_response("你好")
            assert response is not None
            assert isinstance(response, ScriptResponse)
            
            # Test finding multiple responses
            responses = await manager.find_multiple_responses("贷款", limit=3)
            assert len(responses) >= 0
            
            # Test finding by intent
            intent_responses = await manager.find_by_intent("问候")
            assert len(intent_responses) >= 0
            
        finally:
            # Clean up
            await manager.stop()
            await manager.cleanup()
            os.unlink(sample_excel_file)


# Integration test
@pytest.mark.asyncio
async def test_script_management_integration():
    """Integration test for script management system."""
    # Create temporary Excel file
    test_data = pd.DataFrame({
        "用户问题": [
            "你好",
            "我想了解贷款",
            "利率多少",
            "怎么申请"
        ],
        "回复内容": [
            "您好！很高兴为您服务。",
            "我们提供多种贷款产品，请问您需要哪种类型的贷款？",
            "我们的贷款利率根据您的信用情况，一般在3.5%-8%之间。",
            "您可以通过我们的官网、手机APP或到营业厅申请。"
        ],
        "意图分类": [
            "问候",
            "产品咨询",
            "利率咨询",
            "申请流程"
        ],
        "优先级": [1, 2, 2, 1]
    })
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        test_data.to_excel(tmp_file.name, index=False)
        temp_file_path = tmp_file.name
    
    try:
        # Create script manager
        config_manager = Mock()
        
        manager = await create_script_manager(
            script_directory=os.path.dirname(temp_file_path),
            enable_hot_reload=False,
            fuzzy_threshold=0.5,
            config_manager=config_manager
        )
        
        # Test the complete workflow
        test_queries = [
            "你好",
            "我想贷款",
            "利率是多少",
            "如何申请贷款"
        ]
        
        for query in test_queries:
            # Find response
            response = await manager.find_response(query)
            
            if response:
                print(f"Query: {query}")
                print(f"Response: {response.text}")
                print(f"Confidence: {response.metadata.get('match_confidence', 0):.3f}")
                print("---")
        
        # Get statistics
        stats = manager.get_manager_stats()
        assert "loaded_scripts" in stats
        assert "parser_stats" in stats
        assert "matcher_stats" in stats
        
        # Validate scripts
        validation_report = await manager.validate_scripts()
        assert "valid_scripts" in validation_report
        
        # Cleanup
        await manager.stop()
        await manager.cleanup()
        
    finally:
        # Clean up temporary file
        os.unlink(temp_file_path)


if __name__ == "__main__":
    # Run integration test
    asyncio.run(test_script_management_integration())