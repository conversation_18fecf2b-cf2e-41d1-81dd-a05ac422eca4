"""
ASR Processing Pipeline Demo

This demo shows how to use the ASRProcessor for:
- Managing speech recognition workflow
- Handling transcription results and buffering
- Error handling and retry logic
"""

import sys
import numpy as np
import time
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from components.asr.asr_processor import (
    ASRProcessor,
    TranscriptionBuffer,
    TranscriptionResult,
    ASRProcessorConfig,
    TranscriptionStatus,
    create_asr_processor,
    process_audio_batch
)


def generate_test_audio(duration: float = 2.0, sample_rate: int = 16000, 
                       signal_type: str = "speech") -> np.ndarray:
    """Generate synthetic test audio"""
    num_samples = int(duration * sample_rate)
    t = np.linspace(0, duration, num_samples)
    
    if signal_type == "speech":
        # Simulate speech with multiple frequency components
        audio = (
            0.3 * np.sin(2 * np.pi * 200 * t) +  # Fundamental
            0.2 * np.sin(2 * np.pi * 400 * t) +  # First harmonic
            0.1 * np.sin(2 * np.pi * 600 * t) +  # Second harmonic
            0.05 * np.random.randn(num_samples)   # Noise
        )
        
        # Add amplitude modulation to simulate speech patterns
        modulation = 0.5 + 0.5 * np.sin(2 * np.pi * 5 * t)  # 5 Hz modulation
        audio = audio * modulation
        
    elif signal_type == "noise":
        # White noise
        audio = 0.1 * np.random.randn(num_samples)
        
    elif signal_type == "silence":
        # Very quiet audio
        audio = 0.001 * np.random.randn(num_samples)
        
    else:
        # Default to sine wave
        audio = 0.5 * np.sin(2 * np.pi * 440 * t)
    
    return audio.astype(np.float32)


def demo_basic_asr_processing():
    """Demonstrate basic ASR processing functionality"""
    print("=== Basic ASR Processing Demo ===")
    
    # Create ASR processor
    processor = create_asr_processor(
        sample_rate=16000,
        min_confidence=0.3,
        max_retries=2
    )
    
    print(f"Created ASR processor with config:")
    print(f"  Sample rate: {processor.config.sample_rate}Hz")
    print(f"  Min confidence: {processor.config.min_confidence}")
    print(f"  Max retries: {processor.config.max_retries}")
    
    # Generate test audio
    print(f"\nGenerating test audio...")
    audio_data = generate_test_audio(duration=2.0, signal_type="speech")
    print(f"Generated {len(audio_data)} samples ({len(audio_data)/16000:.1f}s)")
    
    # Process audio
    print(f"\nProcessing audio...")
    start_time = time.time()
    result = processor.process_and_buffer(audio_data)
    processing_time = time.time() - start_time
    
    # Display results
    print(f"Processing completed in {processing_time:.3f}s")
    print(f"Result:")
    print(f"  Text: {result.text}")
    print(f"  Confidence: {result.confidence:.3f}")
    print(f"  Status: {result.status.value}")
    print(f"  Duration: {result.duration:.2f}s")
    
    if result.metadata:
        print(f"  Metadata: {result.metadata}")
    
    # Get processing stats
    stats = processor.get_processing_stats()
    print(f"\nProcessing Statistics:")
    print(f"  Total processed: {stats['total_processed']}")
    print(f"  Success rate: {stats['success_rate']:.1%}")
    print(f"  Average processing time: {stats['average_processing_time']:.3f}s")


def demo_transcription_buffer():
    """Demonstrate transcription buffer functionality"""
    print("\n=== Transcription Buffer Demo ===")
    
    config = ASRProcessorConfig(max_buffer_size=5)
    buffer = TranscriptionBuffer(config)
    
    print(f"Created transcription buffer with max size: {config.max_buffer_size}")
    
    # Add various types of results
    print(f"\nAdding transcription results...")
    
    results_to_add = [
        TranscriptionResult("你好", 0.9, TranscriptionStatus.FINAL, 0.0, 1.0),
        TranscriptionResult("世界", 0.8, TranscriptionStatus.FINAL, 1.0, 1.2),
        TranscriptionResult("正在识别", 0.6, TranscriptionStatus.PARTIAL, 2.0, 0.8),
        TranscriptionResult("", 0.0, TranscriptionStatus.ERROR, 3.0, 0.0),
        TranscriptionResult("完整句子", 0.85, TranscriptionStatus.FINAL, 4.0, 1.5),
    ]
    
    for i, result in enumerate(results_to_add):
        success = buffer.add_result(result)
        print(f"  Added result {i+1}: {result.text or '[' + result.status.value + ']'} "
              f"(confidence: {result.confidence:.2f}) - Success: {success}")
    
    # Show buffer contents
    print(f"\nBuffer contents:")
    all_results = buffer.get_all_results()
    print(f"  Total results in buffer: {len(all_results)}")
    
    for i, result in enumerate(all_results):
        print(f"    {i+1}: {result.text or '[' + result.status.value + ']'} "
              f"(confidence: {result.confidence:.2f}, status: {result.status.value})")
    
    # Show latest result
    latest = buffer.get_latest_result()
    if latest:
        print(f"  Latest result: {latest.text or '[' + latest.status.value + ']'}")
    
    # Show final results only
    final_results = buffer.get_final_results()
    print(f"  Final results: {len(final_results)}")
    for result in final_results:
        print(f"    - {result.text} (confidence: {result.confidence:.2f})")
    
    # Show buffer statistics
    stats = buffer.get_buffer_stats()
    print(f"\nBuffer Statistics:")
    print(f"  Total results: {stats['total_results']}")
    print(f"  Final results: {stats['final_results']}")
    print(f"  Error results: {stats['error_results']}")
    print(f"  Has partial: {stats['has_partial']}")
    print(f"  Average confidence: {stats['average_confidence']:.3f}")
    print(f"  Success rate: {stats['success_rate']:.1%}")


def demo_audio_preprocessing():
    """Demonstrate audio preprocessing functionality"""
    print("\n=== Audio Preprocessing Demo ===")
    
    processor = create_asr_processor()
    
    # Test different types of audio
    test_cases = [
        ("Normal speech", generate_test_audio(2.0, signal_type="speech")),
        ("Short audio", generate_test_audio(0.05, signal_type="speech")),  # Too short
        ("Long audio", generate_test_audio(35.0, signal_type="speech")),   # Too long
        ("Silent audio", generate_test_audio(2.0, signal_type="silence")), # Too quiet
        ("Noisy audio", generate_test_audio(2.0, signal_type="noise")),
    ]
    
    print("Testing audio preprocessing with different inputs:")
    
    for name, audio_data in test_cases:
        print(f"\n{name}:")
        print(f"  Original: {len(audio_data)} samples ({len(audio_data)/16000:.2f}s)")
        
        if len(audio_data) > 0:
            rms = np.sqrt(np.mean(audio_data ** 2))
            peak = np.max(np.abs(audio_data))
            print(f"  RMS: {rms:.6f}, Peak: {peak:.3f}")
        
        processed = processor.preprocess_audio(audio_data)
        
        if processed is not None:
            processed_rms = np.sqrt(np.mean(processed ** 2))
            processed_peak = np.max(np.abs(processed))
            print(f"  Processed: {len(processed)} samples ({len(processed)/16000:.2f}s)")
            print(f"  Processed RMS: {processed_rms:.6f}, Peak: {processed_peak:.3f}")
            print(f"  Status: ✓ Accepted")
        else:
            print(f"  Status: ✗ Rejected")


def demo_error_handling_and_retry():
    """Demonstrate error handling and retry logic"""
    print("\n=== Error Handling and Retry Demo ===")
    
    # Create processor with retry configuration
    processor = create_asr_processor(
        min_confidence=0.9,  # Very high threshold to trigger retries
        max_retries=3
    )
    
    print(f"Created processor with high confidence threshold ({processor.config.min_confidence}) "
          f"and {processor.config.max_retries} max retries")
    
    # Test with different audio qualities
    test_audios = [
        ("Good quality", generate_test_audio(2.0, signal_type="speech")),
        ("Poor quality", generate_test_audio(1.0, signal_type="noise")),
        ("Very short", generate_test_audio(0.05, signal_type="speech")),
    ]
    
    for name, audio_data in test_audios:
        print(f"\nProcessing {name} audio:")
        
        start_time = time.time()
        result = processor.process_audio_with_retry(audio_data)
        processing_time = time.time() - start_time
        
        print(f"  Result: {result.text or '[' + result.status.value + ']'}")
        print(f"  Confidence: {result.confidence:.3f}")
        print(f"  Status: {result.status.value}")
        print(f"  Processing time: {processing_time:.3f}s")
        
        if result.metadata and "error" in result.metadata:
            print(f"  Error: {result.metadata['error']}")


def demo_batch_processing():
    """Demonstrate batch audio processing"""
    print("\n=== Batch Processing Demo ===")
    
    processor = create_asr_processor()
    
    # Generate multiple audio segments
    print("Generating batch of audio segments...")
    audio_segments = []
    segment_info = [
        ("Segment 1", 1.5, "speech"),
        ("Segment 2", 2.0, "speech"),
        ("Segment 3", 0.8, "speech"),
        ("Segment 4", 1.2, "noise"),  # This might fail
        ("Segment 5", 2.5, "speech"),
    ]
    
    for name, duration, signal_type in segment_info:
        audio = generate_test_audio(duration, signal_type=signal_type)
        audio_segments.append(audio)
        print(f"  {name}: {duration}s ({signal_type})")
    
    # Process batch
    print(f"\nProcessing batch of {len(audio_segments)} segments...")
    start_time = time.time()
    results = process_audio_batch(audio_segments, processor)
    total_time = time.time() - start_time
    
    print(f"Batch processing completed in {total_time:.3f}s")
    print(f"Average time per segment: {total_time/len(audio_segments):.3f}s")
    
    # Show results
    print(f"\nBatch Results:")
    successful = 0
    for i, (result, (name, _, _)) in enumerate(zip(results, segment_info)):
        status_symbol = "✓" if result.is_final else "✗"
        print(f"  {status_symbol} {name}: {result.text or '[' + result.status.value + ']'} "
              f"(confidence: {result.confidence:.3f})")
        
        if result.is_final:
            successful += 1
    
    print(f"\nBatch Summary:")
    print(f"  Successful: {successful}/{len(results)} ({successful/len(results):.1%})")
    
    # Show final processor statistics
    final_stats = processor.get_processing_stats()
    print(f"  Total processed: {final_stats['total_processed']}")
    print(f"  Overall success rate: {final_stats['success_rate']:.1%}")


def demo_real_time_simulation():
    """Demonstrate real-time processing simulation"""
    print("\n=== Real-time Processing Simulation ===")
    
    processor = create_asr_processor()
    
    # Add callback to show results as they come
    results_received = []
    def result_callback(result):
        results_received.append(result)
        print(f"    -> Received: {result.text or '[' + result.status.value + ']'} "
              f"(confidence: {result.confidence:.3f})")
    
    processor.add_result_callback(result_callback)
    
    print("Simulating real-time audio processing...")
    print("Processing audio chunks as they arrive:")
    
    # Simulate real-time chunks
    chunk_duration = 1.0  # 1 second chunks
    total_chunks = 8
    
    for i in range(total_chunks):
        print(f"\nChunk {i+1}/{total_chunks} (time: {i*chunk_duration:.1f}s):")
        
        # Generate chunk with varying quality
        if i % 3 == 0:
            audio_chunk = generate_test_audio(chunk_duration, signal_type="speech")
        elif i % 3 == 1:
            audio_chunk = generate_test_audio(chunk_duration, signal_type="noise")
        else:
            audio_chunk = generate_test_audio(chunk_duration * 0.5, signal_type="speech")  # Shorter
        
        # Process chunk
        result = processor.process_and_buffer(audio_chunk)
        
        # Simulate real-time delay
        time.sleep(0.1)
    
    print(f"\nReal-time simulation completed!")
    print(f"Total results received via callback: {len(results_received)}")
    
    # Show buffer contents
    buffer_results = processor.buffer.get_final_results()
    print(f"Final results in buffer: {len(buffer_results)}")
    
    for i, result in enumerate(buffer_results):
        print(f"  {i+1}: {result.text} (confidence: {result.confidence:.3f})")


def main():
    """Run all ASR processor demos"""
    print("AI Voice Customer Service - ASR Processing Pipeline Demo")
    print("=" * 60)
    
    try:
        demo_basic_asr_processing()
        demo_transcription_buffer()
        demo_audio_preprocessing()
        demo_error_handling_and_retry()
        demo_batch_processing()
        demo_real_time_simulation()
        
        print("\n" + "=" * 60)
        print("ASR Processing Pipeline Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()