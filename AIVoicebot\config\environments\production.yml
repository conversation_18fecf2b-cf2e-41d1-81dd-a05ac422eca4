# AI Voice Customer Service System - Production Configuration

# Application settings
app:
  name: "AI Voice Customer Service"
  version: "1.0.0"
  environment: "production"
  debug: false
  secret_key: "${SECRET_KEY}"
  
# Server configuration
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  max_requests: 1000
  timeout: 30
  keep_alive: 2

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: true
  max_file_size: "100MB"
  backup_count: 10
  handlers:
    - type: "file"
      filename: "/app/logs/app/application.log"
    - type: "console"
      stream: "stdout"
  loggers:
    aivoice:
      level: "INFO"
    uvicorn:
      level: "INFO"
    gunicorn:
      level: "INFO"

# Database configuration
database:
  url: "${DATABASE_URL}"
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  echo: false
  
# Redis configuration
redis:
  url: "${REDIS_URL}"
  max_connections: 50
  socket_timeout: 5
  socket_connect_timeout: 5
  retry_on_timeout: true
  health_check_interval: 30

# Audio processing configuration
audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  format: "pcm_16"
  max_audio_length: 300  # seconds
  
# VAD (Voice Activity Detection) configuration
vad:
  model_path: "/app/models/silero_vad.onnx"
  threshold: 0.5
  min_speech_duration: 0.25
  min_silence_duration: 0.5
  window_size: 512
  
# ASR (Automatic Speech Recognition) configuration
asr:
  model_path: "/app/models/SenseVoiceSmall"
  language: "zh"
  use_itn: true
  beam_size: 5
  max_length: 200
  timeout: 30
  
# TTS (Text-to-Speech) configuration
tts:
  voice: "zh-CN-XiaoxiaoNeural"
  rate: "+0%"
  pitch: "+0Hz"
  volume: "+0%"
  output_format: "audio-16khz-32kbitrate-mono-mp3"
  timeout: 30
  
# LLM (Large Language Model) configuration
llm:
  model: "gpt-3.5-turbo"
  api_key: "${OPENAI_API_KEY}"
  max_tokens: 500
  temperature: 0.7
  timeout: 30
  retry_attempts: 3
  
# Conversation management
conversation:
  max_history_length: 50
  context_window_size: 10
  intent_confidence_threshold: 0.7
  auto_save_interval: 30
  session_timeout: 1800
  
# Prompt management
prompts:
  template_dir: "/app/templates"
  default_language: "zh-CN"
  cache_size: 100
  template_reload_interval: 300
  
# Call management
calls:
  max_concurrent: 100
  default_timeout: 300
  cleanup_interval: 60
  recording_enabled: true
  recording_path: "/app/data/recordings"
  
# Telephony configuration
telephony:
  routing_strategy: "least_busy"
  max_concurrent_calls: 100
  call_timeout_seconds: 300
  esp32:
    enabled: true
    host: "0.0.0.0"
    port: 8765
    max_connections: 50
  sip:
    enabled: false
    server: "sip.example.com"
    port: 5060
    username: "${SIP_USERNAME}"
    password: "${SIP_PASSWORD}"
    
# Component coordination
coordination:
  max_queue_size: 1000
  event_timeout_seconds: 30.0
  conflict_resolution_timeout: 10.0
  
# Performance monitoring
performance:
  metrics_interval: 10
  alert_thresholds:
    cpu_percent: 80
    memory_percent: 85
    response_time_ms: 1000
    error_rate_percent: 5
  prometheus:
    enabled: true
    gateway: "${PROMETHEUS_GATEWAY}"
    job_name: "aivoice-app"
    
# Health monitoring
health:
  check_interval_seconds: 30
  recovery_enabled: true
  max_recovery_attempts: 3
  endpoints:
    - name: "database"
      url: "postgresql://check"
      timeout: 5
    - name: "redis"
      url: "redis://check"
      timeout: 5
    - name: "external_api"
      url: "https://api.openai.com/v1/models"
      timeout: 10
      
# Security configuration
security:
  cors:
    enabled: true
    origins:
      - "https://yourdomain.com"
      - "https://admin.yourdomain.com"
    methods: ["GET", "POST", "PUT", "DELETE"]
    headers: ["Content-Type", "Authorization"]
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_size: 10
  authentication:
    jwt_secret: "${JWT_SECRET}"
    token_expiry: 3600
    
# File storage
storage:
  type: "local"  # or "s3", "azure", "gcp"
  local:
    base_path: "/app/data"
    max_file_size: "100MB"
  s3:
    bucket: "${S3_BUCKET}"
    region: "${S3_REGION}"
    access_key: "${S3_ACCESS_KEY}"
    secret_key: "${S3_SECRET_KEY}"
    
# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  destinations:
    - type: "local"
      path: "/app/backups"
    - type: "s3"
      bucket: "${BACKUP_S3_BUCKET}"
      
# Feature flags
features:
  conversation_recording: true
  real_time_transcription: true
  sentiment_analysis: false
  call_analytics: true
  multi_language_support: false
  
# External integrations
integrations:
  webhook:
    enabled: true
    url: "${WEBHOOK_URL}"
    secret: "${WEBHOOK_SECRET}"
    events: ["call_started", "call_ended", "transcription_complete"]
  crm:
    enabled: false
    type: "salesforce"
    api_url: "${CRM_API_URL}"
    api_key: "${CRM_API_KEY}"
