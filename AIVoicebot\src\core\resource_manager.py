"""
Resource Manager for AI Voice Customer Service

This module provides resource pool management, auto-scaling,
and resource optimization for the AI voice system.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Callable, TypeVar, Generic

# Optional psutil import
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod
import threading
from concurrent.futures import ThreadPoolExecutor
import weakref

from .base_component import BaseComponent

T = TypeVar('T')


class ResourceType(Enum):
    """Resource types."""
    AUDIO_PROCESSOR = "audio_processor"
    LLM_CLIENT = "llm_client"
    DATABASE_CONNECTION = "database_connection"
    HTTP_CLIENT = "http_client"
    WEBSOCKET_CONNECTION = "websocket_connection"
    THREAD_POOL = "thread_pool"
    MEMORY_BUFFER = "memory_buffer"
    CUSTOM = "custom"


class ResourceStatus(Enum):
    """Resource status."""
    AVAILABLE = "available"
    IN_USE = "in_use"
    RESERVED = "reserved"
    MAINTENANCE = "maintenance"
    ERROR = "error"


@dataclass
class ResourceMetrics:
    """Resource usage metrics."""
    total_resources: int = 0
    available_resources: int = 0
    in_use_resources: int = 0
    peak_usage: int = 0
    average_usage: float = 0.0
    utilization_rate: float = 0.0
    creation_count: int = 0
    destruction_count: int = 0
    error_count: int = 0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class ResourceConfig:
    """Resource pool configuration."""
    min_size: int = 1
    max_size: int = 10
    initial_size: int = 2
    idle_timeout: int = 300  # seconds
    max_lifetime: int = 3600  # seconds
    health_check_interval: int = 60  # seconds
    auto_scale: bool = True
    scale_up_threshold: float = 0.8  # 80% utilization
    scale_down_threshold: float = 0.3  # 30% utilization
    scale_factor: float = 1.5  # scaling factor


class Resource(Generic[T]):
    """Resource wrapper."""
    
    def __init__(self, resource: T, resource_id: str):
        self.resource = resource
        self.resource_id = resource_id
        self.status = ResourceStatus.AVAILABLE
        self.created_at = datetime.now()
        self.last_used = datetime.now()
        self.use_count = 0
        self.error_count = 0
        self.in_use_by: Optional[str] = None
        
    def mark_in_use(self, user_id: str = None):
        """Mark resource as in use."""
        self.status = ResourceStatus.IN_USE
        self.last_used = datetime.now()
        self.use_count += 1
        self.in_use_by = user_id
        
    def mark_available(self):
        """Mark resource as available."""
        self.status = ResourceStatus.AVAILABLE
        self.in_use_by = None
        
    def mark_error(self):
        """Mark resource as error."""
        self.status = ResourceStatus.ERROR
        self.error_count += 1
        self.in_use_by = None
        
    @property
    def age(self) -> timedelta:
        """Get resource age."""
        return datetime.now() - self.created_at
        
    @property
    def idle_time(self) -> timedelta:
        """Get idle time."""
        return datetime.now() - self.last_used


class ResourceFactory(ABC, Generic[T]):
    """Abstract resource factory."""
    
    @abstractmethod
    async def create_resource(self) -> T:
        """Create a new resource."""
        pass
    
    @abstractmethod
    async def destroy_resource(self, resource: T) -> None:
        """Destroy a resource."""
        pass
    
    @abstractmethod
    async def validate_resource(self, resource: T) -> bool:
        """Validate if resource is healthy."""
        pass


class ResourcePool(Generic[T]):
    """Generic resource pool."""
    
    def __init__(
        self,
        name: str,
        factory: ResourceFactory[T],
        config: ResourceConfig,
        logger=None
    ):
        self.name = name
        self.factory = factory
        self.config = config
        self.logger = logger
        
        # Resource storage
        self.resources: Dict[str, Resource[T]] = {}
        self.available_queue = asyncio.Queue()
        self.metrics = ResourceMetrics()
        
        # Synchronization
        self.lock = asyncio.Lock()
        self.shutdown_event = asyncio.Event()
        
        # Background tasks
        self.maintenance_task: Optional[asyncio.Task] = None
        self.metrics_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> None:
        """Initialize the resource pool."""
        async with self.lock:
            # Create initial resources
            for _ in range(self.config.initial_size):
                await self._create_resource()
            
            # Start background tasks
            self.maintenance_task = asyncio.create_task(self._maintenance_loop())
            self.metrics_task = asyncio.create_task(self._metrics_loop())
            
            if self.logger:
                self.logger.info(f"Resource pool '{self.name}' initialized with {len(self.resources)} resources")
    
    async def shutdown(self) -> None:
        """Shutdown the resource pool."""
        self.shutdown_event.set()
        
        # Cancel background tasks
        if self.maintenance_task:
            self.maintenance_task.cancel()
        if self.metrics_task:
            self.metrics_task.cancel()
        
        # Destroy all resources
        async with self.lock:
            for resource in list(self.resources.values()):
                await self._destroy_resource(resource.resource_id)
        
        if self.logger:
            self.logger.info(f"Resource pool '{self.name}' shutdown completed")
    
    async def acquire(self, timeout: float = 30.0, user_id: str = None) -> Optional[T]:
        """Acquire a resource from the pool."""
        try:
            # Try to get available resource
            resource_id = await asyncio.wait_for(
                self.available_queue.get(),
                timeout=timeout
            )
            
            async with self.lock:
                if resource_id in self.resources:
                    resource = self.resources[resource_id]
                    
                    # Validate resource
                    if await self.factory.validate_resource(resource.resource):
                        resource.mark_in_use(user_id)
                        self.metrics.in_use_resources += 1
                        self.metrics.available_resources -= 1
                        return resource.resource
                    else:
                        # Resource is invalid, destroy it
                        await self._destroy_resource(resource_id)
                        # Try to create a new one
                        await self._create_resource()
                        # Retry acquisition
                        return await self.acquire(timeout, user_id)
                
        except asyncio.TimeoutError:
            # Try to scale up if possible
            if self.config.auto_scale and len(self.resources) < self.config.max_size:
                async with self.lock:
                    await self._create_resource()
                return await self.acquire(timeout, user_id)
            
            if self.logger:
                self.logger.warning(f"Resource acquisition timeout for pool '{self.name}'")
            return None
        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error acquiring resource from pool '{self.name}': {e}")
            return None
    
    async def release(self, resource: T, user_id: str = None) -> None:
        """Release a resource back to the pool."""
        async with self.lock:
            # Find the resource
            resource_wrapper = None
            for res in self.resources.values():
                if res.resource is resource:
                    resource_wrapper = res
                    break
            
            if resource_wrapper:
                resource_wrapper.mark_available()
                self.metrics.in_use_resources -= 1
                self.metrics.available_resources += 1
                
                # Put back in available queue
                await self.available_queue.put(resource_wrapper.resource_id)
            else:
                if self.logger:
                    self.logger.warning(f"Attempted to release unknown resource in pool '{self.name}'")
    
    async def _create_resource(self) -> Optional[str]:
        """Create a new resource."""
        if len(self.resources) >= self.config.max_size:
            return None
        
        try:
            # Create resource using factory
            new_resource = await self.factory.create_resource()
            resource_id = f"{self.name}_{len(self.resources)}_{int(time.time())}"
            
            # Wrap resource
            resource_wrapper = Resource(new_resource, resource_id)
            self.resources[resource_id] = resource_wrapper
            
            # Add to available queue
            await self.available_queue.put(resource_id)
            
            # Update metrics
            self.metrics.total_resources += 1
            self.metrics.available_resources += 1
            self.metrics.creation_count += 1
            
            if self.logger:
                self.logger.debug(f"Created resource {resource_id} in pool '{self.name}'")
            
            return resource_id
            
        except Exception as e:
            self.metrics.error_count += 1
            if self.logger:
                self.logger.error(f"Failed to create resource in pool '{self.name}': {e}")
            return None
    
    async def _destroy_resource(self, resource_id: str) -> None:
        """Destroy a resource."""
        if resource_id not in self.resources:
            return
        
        try:
            resource_wrapper = self.resources[resource_id]
            
            # Destroy resource using factory
            await self.factory.destroy_resource(resource_wrapper.resource)
            
            # Remove from pool
            del self.resources[resource_id]
            
            # Update metrics
            self.metrics.total_resources -= 1
            if resource_wrapper.status == ResourceStatus.AVAILABLE:
                self.metrics.available_resources -= 1
            elif resource_wrapper.status == ResourceStatus.IN_USE:
                self.metrics.in_use_resources -= 1
            
            self.metrics.destruction_count += 1
            
            if self.logger:
                self.logger.debug(f"Destroyed resource {resource_id} in pool '{self.name}'")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to destroy resource {resource_id} in pool '{self.name}': {e}")
    
    async def _maintenance_loop(self) -> None:
        """Background maintenance loop."""
        while not self.shutdown_event.is_set():
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._perform_maintenance()
            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error in maintenance loop for pool '{self.name}': {e}")
    
    async def _perform_maintenance(self) -> None:
        """Perform maintenance tasks."""
        async with self.lock:
            current_time = datetime.now()
            resources_to_remove = []
            
            # Check for idle and old resources
            for resource_id, resource in self.resources.items():
                # Remove resources that exceeded max lifetime
                if resource.age.total_seconds() > self.config.max_lifetime:
                    resources_to_remove.append(resource_id)
                    continue
                
                # Remove idle resources (but keep minimum)
                if (resource.status == ResourceStatus.AVAILABLE and
                    resource.idle_time.total_seconds() > self.config.idle_timeout and
                    len(self.resources) > self.config.min_size):
                    resources_to_remove.append(resource_id)
                    continue
                
                # Check resource health
                if resource.status == ResourceStatus.AVAILABLE:
                    try:
                        if not await self.factory.validate_resource(resource.resource):
                            resources_to_remove.append(resource_id)
                    except Exception:
                        resources_to_remove.append(resource_id)
            
            # Remove unhealthy/old resources
            for resource_id in resources_to_remove:
                await self._destroy_resource(resource_id)
            
            # Auto-scaling
            if self.config.auto_scale:
                await self._auto_scale()
    
    async def _auto_scale(self) -> None:
        """Perform auto-scaling based on utilization."""
        if self.metrics.total_resources == 0:
            return
        
        utilization = self.metrics.in_use_resources / self.metrics.total_resources
        
        # Scale up
        if (utilization > self.config.scale_up_threshold and
            self.metrics.total_resources < self.config.max_size):
            
            target_size = min(
                int(self.metrics.total_resources * self.config.scale_factor),
                self.config.max_size
            )
            
            for _ in range(target_size - self.metrics.total_resources):
                await self._create_resource()
            
            if self.logger:
                self.logger.info(f"Scaled up pool '{self.name}' to {target_size} resources")
        
        # Scale down
        elif (utilization < self.config.scale_down_threshold and
              self.metrics.total_resources > self.config.min_size):
            
            target_size = max(
                int(self.metrics.total_resources / self.config.scale_factor),
                self.config.min_size
            )
            
            # Remove excess available resources
            resources_to_remove = []
            for resource_id, resource in self.resources.items():
                if (resource.status == ResourceStatus.AVAILABLE and
                    len(resources_to_remove) < (self.metrics.total_resources - target_size)):
                    resources_to_remove.append(resource_id)
            
            for resource_id in resources_to_remove:
                await self._destroy_resource(resource_id)
            
            if self.logger:
                self.logger.info(f"Scaled down pool '{self.name}' to {target_size} resources")
    
    async def _metrics_loop(self) -> None:
        """Background metrics collection loop."""
        usage_history = []
        
        while not self.shutdown_event.is_set():
            try:
                await asyncio.sleep(10)  # Update metrics every 10 seconds
                
                # Update utilization rate
                if self.metrics.total_resources > 0:
                    current_utilization = self.metrics.in_use_resources / self.metrics.total_resources
                    self.metrics.utilization_rate = current_utilization
                    
                    # Track usage history for average calculation
                    usage_history.append(current_utilization)
                    if len(usage_history) > 360:  # Keep 1 hour of history (360 * 10s)
                        usage_history.pop(0)
                    
                    self.metrics.average_usage = sum(usage_history) / len(usage_history)
                
                # Update peak usage
                self.metrics.peak_usage = max(self.metrics.peak_usage, self.metrics.in_use_resources)
                
                # Update timestamp
                self.metrics.last_updated = datetime.now()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error in metrics loop for pool '{self.name}': {e}")
    
    def get_metrics(self) -> ResourceMetrics:
        """Get current resource metrics."""
        return self.metrics
    
    def get_status(self) -> Dict[str, Any]:
        """Get pool status."""
        return {
            "name": self.name,
            "total_resources": self.metrics.total_resources,
            "available_resources": self.metrics.available_resources,
            "in_use_resources": self.metrics.in_use_resources,
            "utilization_rate": self.metrics.utilization_rate,
            "average_usage": self.metrics.average_usage,
            "peak_usage": self.metrics.peak_usage,
            "config": {
                "min_size": self.config.min_size,
                "max_size": self.config.max_size,
                "auto_scale": self.config.auto_scale
            }
        }


class ResourceManager(BaseComponent):
    """Central resource manager for the AI voice system."""

    def __init__(self, config_manager, logger=None):
        super().__init__("resource_manager", config_manager, logger)

        # Resource pools
        self.pools: Dict[str, ResourcePool] = {}

        # System resource monitoring
        self.system_metrics = {
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "disk_percent": 0.0,
            "last_updated": datetime.now()
        }

        # Auto-scaling configuration
        self.auto_scaling_enabled = self.config_manager.get_config("resources.auto_scaling.enabled", True)
        self.scaling_thresholds = self.config_manager.get_config("resources.auto_scaling.thresholds", {
            "cpu_high": 80,
            "cpu_low": 30,
            "memory_high": 85,
            "memory_low": 40
        })

        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.scaling_task: Optional[asyncio.Task] = None

    async def _initialize_impl(self) -> None:
        """Initialize the resource manager."""
        self._log.info("Resource Manager initialized")

        # Initialize default resource pools
        await self._initialize_default_pools()

    async def _start_impl(self) -> None:
        """Start the resource manager."""
        self._log.info("Starting Resource Manager...")

        # Start all resource pools
        for pool in self.pools.values():
            await pool.initialize()

        # Start monitoring tasks
        self.monitoring_task = asyncio.create_task(self._system_monitoring_loop())
        if self.auto_scaling_enabled:
            self.scaling_task = asyncio.create_task(self._auto_scaling_loop())

        self._log.info("Resource Manager started")

    async def _stop_impl(self) -> None:
        """Stop the resource manager."""
        self._log.info("Stopping Resource Manager...")

        # Cancel monitoring tasks
        if self.monitoring_task:
            self.monitoring_task.cancel()
        if self.scaling_task:
            self.scaling_task.cancel()

        # Shutdown all resource pools
        for pool in self.pools.values():
            await pool.shutdown()

        self._log.info("Resource Manager stopped")

    async def _cleanup_impl(self) -> None:
        """Clean up the resource manager."""
        self.pools.clear()
        self._log.info("Resource Manager cleanup completed")

    async def _initialize_default_pools(self) -> None:
        """Initialize default resource pools."""
        # HTTP Client Pool
        http_config = ResourceConfig(
            min_size=2,
            max_size=20,
            initial_size=5,
            idle_timeout=300,
            auto_scale=True
        )
        http_factory = HTTPClientFactory()
        await self.create_pool("http_clients", http_factory, http_config)

        # Thread Pool
        thread_config = ResourceConfig(
            min_size=4,
            max_size=32,
            initial_size=8,
            idle_timeout=600,
            auto_scale=True
        )
        thread_factory = ThreadPoolFactory()
        await self.create_pool("thread_pools", thread_factory, thread_config)

        # Memory Buffer Pool
        buffer_config = ResourceConfig(
            min_size=10,
            max_size=100,
            initial_size=20,
            idle_timeout=180,
            auto_scale=True
        )
        buffer_factory = MemoryBufferFactory(buffer_size=1024*1024)  # 1MB buffers
        await self.create_pool("memory_buffers", buffer_factory, buffer_config)

    async def create_pool(
        self,
        name: str,
        factory: ResourceFactory,
        config: ResourceConfig
    ) -> bool:
        """Create a new resource pool."""
        if name in self.pools:
            self._log.warning(f"Resource pool '{name}' already exists")
            return False

        try:
            pool = ResourcePool(name, factory, config, self._log)
            self.pools[name] = pool

            # Initialize if resource manager is running
            if self.is_running:
                await pool.initialize()

            self._log.info(f"Created resource pool: {name}")
            return True

        except Exception as e:
            self._log.error(f"Failed to create resource pool '{name}': {e}")
            return False

    async def get_resource(self, pool_name: str, timeout: float = 30.0, user_id: str = None):
        """Get a resource from a pool."""
        if pool_name not in self.pools:
            self._log.error(f"Resource pool '{pool_name}' not found")
            return None

        return await self.pools[pool_name].acquire(timeout, user_id)

    async def release_resource(self, pool_name: str, resource, user_id: str = None) -> None:
        """Release a resource back to a pool."""
        if pool_name not in self.pools:
            self._log.error(f"Resource pool '{pool_name}' not found")
            return

        await self.pools[pool_name].release(resource, user_id)

    def get_resource_statistics(self) -> Dict[str, Any]:
        """Get comprehensive resource statistics."""
        pool_stats = {}
        total_resources = 0
        total_in_use = 0

        for name, pool in self.pools.items():
            metrics = pool.get_metrics()
            pool_stats[name] = {
                "total": metrics.total_resources,
                "available": metrics.available_resources,
                "in_use": metrics.in_use_resources,
                "utilization": metrics.utilization_rate,
                "peak_usage": metrics.peak_usage,
                "creation_count": metrics.creation_count,
                "destruction_count": metrics.destruction_count,
                "error_count": metrics.error_count
            }
            total_resources += metrics.total_resources
            total_in_use += metrics.in_use_resources

        overall_utilization = total_in_use / total_resources if total_resources > 0 else 0

        return {
            "system_metrics": self.system_metrics,
            "overall_utilization": overall_utilization,
            "total_resources": total_resources,
            "total_in_use": total_in_use,
            "pool_count": len(self.pools),
            "pools": pool_stats,
            "auto_scaling_enabled": self.auto_scaling_enabled,
            "scaling_thresholds": self.scaling_thresholds
        }


# Concrete Resource Factories

class HTTPClientFactory(ResourceFactory):
    """Factory for HTTP client resources."""

    async def create_resource(self):
        """Create HTTP client."""
        import aiohttp
        return aiohttp.ClientSession()

    async def destroy_resource(self, resource) -> None:
        """Destroy HTTP client."""
        if hasattr(resource, 'close'):
            await resource.close()

    async def validate_resource(self, resource) -> bool:
        """Validate HTTP client."""
        return hasattr(resource, 'get') and not resource.closed


class ThreadPoolFactory(ResourceFactory):
    """Factory for thread pool resources."""

    async def create_resource(self):
        """Create thread pool."""
        return ThreadPoolExecutor(max_workers=4)

    async def destroy_resource(self, resource) -> None:
        """Destroy thread pool."""
        resource.shutdown(wait=True)

    async def validate_resource(self, resource) -> bool:
        """Validate thread pool."""
        return not resource._shutdown


class MemoryBufferFactory(ResourceFactory):
    """Factory for memory buffer resources."""

    def __init__(self, buffer_size: int = 1024*1024):
        self.buffer_size = buffer_size

    async def create_resource(self):
        """Create memory buffer."""
        return bytearray(self.buffer_size)

    async def destroy_resource(self, resource) -> None:
        """Destroy memory buffer."""
        # Memory will be garbage collected
        pass

    async def validate_resource(self, resource) -> bool:
        """Validate memory buffer."""
        return isinstance(resource, bytearray) and len(resource) == self.buffer_size


class ResourceManager(BaseComponent):
    """Central resource manager for the AI voice system."""

    def __init__(self, config_manager, logger=None):
        super().__init__("resource_manager", config_manager, logger)

        # Resource pools
        self.pools: Dict[str, ResourcePool] = {}

        # System resource monitoring
        self.system_metrics = {
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "disk_percent": 0.0,
            "network_io": {"bytes_sent": 0, "bytes_recv": 0},
            "last_updated": datetime.now()
        }

        # Auto-scaling configuration
        self.auto_scaling_enabled = self.config_manager.get_config("resources.auto_scaling.enabled", True)
        self.scaling_thresholds = self.config_manager.get_config("resources.auto_scaling.thresholds", {
            "cpu_high": 80,
            "cpu_low": 30,
            "memory_high": 85,
            "memory_low": 40
        })

        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.scaling_task: Optional[asyncio.Task] = None

    async def _initialize_impl(self) -> None:
        """Initialize the resource manager."""
        self._log.info("Resource Manager initialized")

        # Initialize default resource pools
        await self._initialize_default_pools()

    async def _start_impl(self) -> None:
        """Start the resource manager."""
        self._log.info("Starting Resource Manager...")

        # Start all resource pools
        for pool in self.pools.values():
            await pool.initialize()

        # Start monitoring tasks
        self.monitoring_task = asyncio.create_task(self._system_monitoring_loop())
        if self.auto_scaling_enabled:
            self.scaling_task = asyncio.create_task(self._auto_scaling_loop())

        self._log.info("Resource Manager started")

    async def _stop_impl(self) -> None:
        """Stop the resource manager."""
        self._log.info("Stopping Resource Manager...")

        # Cancel monitoring tasks
        if self.monitoring_task:
            self.monitoring_task.cancel()
        if self.scaling_task:
            self.scaling_task.cancel()

        # Shutdown all resource pools
        for pool in self.pools.values():
            await pool.shutdown()

        self._log.info("Resource Manager stopped")

    async def _cleanup_impl(self) -> None:
        """Clean up the resource manager."""
        self.pools.clear()
        self._log.info("Resource Manager cleanup completed")

    async def _initialize_default_pools(self) -> None:
        """Initialize default resource pools."""
        # HTTP Client Pool
        http_config = ResourceConfig(
            min_size=2,
            max_size=20,
            initial_size=5,
            idle_timeout=300,
            auto_scale=True
        )
        http_factory = HTTPClientFactory()
        await self.create_pool("http_clients", http_factory, http_config)

        # Thread Pool
        thread_config = ResourceConfig(
            min_size=4,
            max_size=32,
            initial_size=8,
            idle_timeout=600,
            auto_scale=True
        )
        thread_factory = ThreadPoolFactory()
        await self.create_pool("thread_pools", thread_factory, thread_config)

        # Memory Buffer Pool
        buffer_config = ResourceConfig(
            min_size=10,
            max_size=100,
            initial_size=20,
            idle_timeout=180,
            auto_scale=True
        )
        buffer_factory = MemoryBufferFactory(buffer_size=1024*1024)  # 1MB buffers
        await self.create_pool("memory_buffers", buffer_factory, buffer_config)

    async def create_pool(
        self,
        name: str,
        factory: ResourceFactory,
        config: ResourceConfig
    ) -> bool:
        """Create a new resource pool."""
        if name in self.pools:
            self._log.warning(f"Resource pool '{name}' already exists")
            return False

        try:
            pool = ResourcePool(name, factory, config, self._log)
            self.pools[name] = pool

            # Initialize if resource manager is running
            if self.is_running:
                await pool.initialize()

            self._log.info(f"Created resource pool: {name}")
            return True

        except Exception as e:
            self._log.error(f"Failed to create resource pool '{name}': {e}")
            return False

    async def get_resource(self, pool_name: str, timeout: float = 30.0, user_id: str = None):
        """Get a resource from a pool."""
        if pool_name not in self.pools:
            self._log.error(f"Resource pool '{pool_name}' not found")
            return None

        return await self.pools[pool_name].acquire(timeout, user_id)

    async def release_resource(self, pool_name: str, resource, user_id: str = None) -> None:
        """Release a resource back to a pool."""
        if pool_name not in self.pools:
            self._log.error(f"Resource pool '{pool_name}' not found")
            return

        await self.pools[pool_name].release(resource, user_id)

    async def _system_monitoring_loop(self) -> None:
        """Monitor system resources."""
        while True:
            try:
                await asyncio.sleep(10)  # Update every 10 seconds

                # Get system metrics
                self.system_metrics.update({
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_percent": psutil.disk_usage('/').percent,
                    "network_io": psutil.net_io_counters()._asdict(),
                    "last_updated": datetime.now()
                })

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in system monitoring: {e}")

    async def _auto_scaling_loop(self) -> None:
        """Auto-scaling loop based on system metrics."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds

                cpu_percent = self.system_metrics["cpu_percent"]
                memory_percent = self.system_metrics["memory_percent"]

                # Determine scaling action
                should_scale_up = (
                    cpu_percent > self.scaling_thresholds["cpu_high"] or
                    memory_percent > self.scaling_thresholds["memory_high"]
                )

                should_scale_down = (
                    cpu_percent < self.scaling_thresholds["cpu_low"] and
                    memory_percent < self.scaling_thresholds["memory_low"]
                )

                if should_scale_up:
                    await self._scale_up_pools()
                elif should_scale_down:
                    await self._scale_down_pools()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in auto-scaling: {e}")

    async def _scale_up_pools(self) -> None:
        """Scale up resource pools."""
        for pool_name, pool in self.pools.items():
            if pool.config.auto_scale and pool.metrics.total_resources < pool.config.max_size:
                # Trigger pool's internal scaling
                await pool._auto_scale()
                self._log.info(f"Triggered scale-up for pool: {pool_name}")

    async def _scale_down_pools(self) -> None:
        """Scale down resource pools."""
        for pool_name, pool in self.pools.items():
            if pool.config.auto_scale and pool.metrics.total_resources > pool.config.min_size:
                # Pool will handle scale-down in its maintenance loop
                pass

    def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics."""
        return self.system_metrics.copy()

    def get_pool_metrics(self, pool_name: str = None) -> Dict[str, Any]:
        """Get resource pool metrics."""
        if pool_name:
            if pool_name in self.pools:
                return self.pools[pool_name].get_status()
            else:
                return {}
        else:
            return {
                name: pool.get_status()
                for name, pool in self.pools.items()
            }

    def get_resource_statistics(self) -> Dict[str, Any]:
        """Get comprehensive resource statistics."""
        pool_stats = {}
        total_resources = 0
        total_in_use = 0

        for name, pool in self.pools.items():
            metrics = pool.get_metrics()
            pool_stats[name] = {
                "total": metrics.total_resources,
                "available": metrics.available_resources,
                "in_use": metrics.in_use_resources,
                "utilization": metrics.utilization_rate,
                "peak_usage": metrics.peak_usage,
                "creation_count": metrics.creation_count,
                "destruction_count": metrics.destruction_count,
                "error_count": metrics.error_count
            }
            total_resources += metrics.total_resources
            total_in_use += metrics.in_use_resources

        overall_utilization = total_in_use / total_resources if total_resources > 0 else 0

        return {
            "system_metrics": self.system_metrics,
            "overall_utilization": overall_utilization,
            "total_resources": total_resources,
            "total_in_use": total_in_use,
            "pool_count": len(self.pools),
            "pools": pool_stats,
            "auto_scaling_enabled": self.auto_scaling_enabled,
            "scaling_thresholds": self.scaling_thresholds
        }


# Concrete Resource Factories

class HTTPClientFactory(ResourceFactory):
    """Factory for HTTP client resources."""

    async def create_resource(self):
        """Create HTTP client."""
        import aiohttp
        return aiohttp.ClientSession()

    async def destroy_resource(self, resource) -> None:
        """Destroy HTTP client."""
        if hasattr(resource, 'close'):
            await resource.close()

    async def validate_resource(self, resource) -> bool:
        """Validate HTTP client."""
        return hasattr(resource, 'get') and not resource.closed


class ThreadPoolFactory(ResourceFactory):
    """Factory for thread pool resources."""

    async def create_resource(self):
        """Create thread pool."""
        return ThreadPoolExecutor(max_workers=4)

    async def destroy_resource(self, resource) -> None:
        """Destroy thread pool."""
        resource.shutdown(wait=True)

    async def validate_resource(self, resource) -> bool:
        """Validate thread pool."""
        return not resource._shutdown


class MemoryBufferFactory(ResourceFactory):
    """Factory for memory buffer resources."""

    def __init__(self, buffer_size: int = 1024*1024):
        self.buffer_size = buffer_size

    async def create_resource(self):
        """Create memory buffer."""
        return bytearray(self.buffer_size)

    async def destroy_resource(self, resource) -> None:
        """Destroy memory buffer."""
        # Memory will be garbage collected
        pass

    async def validate_resource(self, resource) -> bool:
        """Validate memory buffer."""
        return isinstance(resource, bytearray) and len(resource) == self.buffer_size
