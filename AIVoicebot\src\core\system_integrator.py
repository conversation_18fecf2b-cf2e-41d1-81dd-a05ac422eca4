"""
System Integrator for AI Voice Customer Service

This module integrates all components into a complete working system,
orchestrating the entire conversation flow from call initiation to completion.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .base_component import BaseComponent
from .extension_manager import ExtensionManager

# Import components with fallback for missing modules
try:
    from ..services.component_coordinator import ComponentCoordinator
except ImportError:
    ComponentCoordinator = None

try:
    from ..services.call_manager import CallManager
except ImportError:
    CallManager = None

try:
    from ..components.conversation.conversation_engine import ConversationEngine as ConversationManager
except ImportError:
    ConversationManager = None

try:
    from ..components.llm.prompt_manager import PromptManager
except ImportError:
    PromptManager = None

try:
    from ..integrations.telephony_manager import TelephonyManager
except ImportError:
    TelephonyManager = None

try:
    from ..components.audio.audio_pipeline import AudioPipeline
except ImportError:
    AudioPipeline = None

try:
    from ..services.performance_monitor import PerformanceMonitor
except ImportError:
    PerformanceMonitor = None

try:
    from ..services.health_monitor import SystemHealthMonitor
except ImportError:
    SystemHealthMonitor = None

try:
    from ..components.voice.voice_processor import VoiceProcessor
except ImportError:
    VoiceProcessor = None

try:
    from ..components.voice_ai_system import VoiceAISystem
except ImportError:
    VoiceAISystem = None

try:
    from ..services.conversation_logger import ConversationLogger
except ImportError:
    ConversationLogger = None


class SystemState(Enum):
    """System state enumeration."""
    INITIALIZING = "initializing"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class SystemStatus:
    """System status information."""
    state: SystemState
    components_initialized: int
    components_running: int
    components_error: int
    active_calls: int
    total_calls_handled: int
    system_uptime: float
    last_updated: datetime


class AIVoiceCustomerServiceSystem(BaseComponent):
    """Complete AI Voice Customer Service System."""
    
    def __init__(self, config_manager, logger=None):
        super().__init__("ai_voice_system", config_manager, logger)
        
        # System state
        self.system_state = SystemState.INITIALIZING
        self.start_time: Optional[datetime] = None
        
        # Core components
        self.extension_manager: Optional[ExtensionManager] = None
        self.component_coordinator: Optional[ComponentCoordinator] = None
        self.call_manager: Optional[CallManager] = None
        self.conversation_manager: Optional[ConversationManager] = None
        self.prompt_manager: Optional[PromptManager] = None
        self.telephony_manager: Optional[TelephonyManager] = None
        self.audio_pipeline: Optional[AudioPipeline] = None
        
        # Monitoring and logging
        self.performance_monitor: Optional[PerformanceMonitor] = None
        self.health_monitor: Optional[SystemHealthMonitor] = None
        self.conversation_logger: Optional[ConversationLogger] = None

        # Voice processing
        self.voice_processor: Optional[VoiceProcessor] = None

        # Voice AI system
        self.voice_ai_system: Optional[VoiceAISystem] = None
        
        # Component registry
        self.components: Dict[str, BaseComponent] = {}
        self.component_dependencies: Dict[str, List[str]] = {}
        
        # System statistics
        self.total_calls_handled = 0
        self.system_errors = 0
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}
        
    async def _initialize_impl(self) -> None:
        """Initialize the complete system."""
        self._log.info("Initializing AI Voice Customer Service System...")
        self.system_state = SystemState.INITIALIZING
        
        try:
            # Initialize core components in dependency order
            await self._initialize_core_components()
            
            # Setup component dependencies
            await self._setup_component_dependencies()
            
            # Register event handlers
            await self._register_event_handlers()
            
            self._log.info("AI Voice Customer Service System initialized successfully")
            
        except Exception as e:
            self.system_state = SystemState.ERROR
            self._log.error(f"Failed to initialize system: {e}")
            raise
    
    async def _start_impl(self) -> None:
        """Start the complete system."""
        self._log.info("Starting AI Voice Customer Service System...")
        self.system_state = SystemState.STARTING
        
        try:
            # Start components in dependency order
            await self._start_components_in_order()
            
            # Start system monitoring
            await self._start_system_monitoring()
            
            # Mark system as running
            self.system_state = SystemState.RUNNING
            self.start_time = datetime.now()
            
            self._log.info("AI Voice Customer Service System started successfully")
            
            # Trigger system started event
            await self._trigger_event("system_started", {"timestamp": self.start_time})
            
        except Exception as e:
            self.system_state = SystemState.ERROR
            self._log.error(f"Failed to start system: {e}")
            raise
    
    async def _stop_impl(self) -> None:
        """Stop the complete system."""
        self._log.info("Stopping AI Voice Customer Service System...")
        self.system_state = SystemState.STOPPING
        
        try:
            # Trigger system stopping event
            await self._trigger_event("system_stopping", {"timestamp": datetime.now()})
            
            # Stop components in reverse dependency order
            await self._stop_components_in_reverse_order()
            
            self.system_state = SystemState.STOPPED
            self._log.info("AI Voice Customer Service System stopped successfully")
            
        except Exception as e:
            self.system_state = SystemState.ERROR
            self._log.error(f"Error stopping system: {e}")
            raise
    
    async def _cleanup_impl(self) -> None:
        """Clean up the complete system."""
        self._log.info("Cleaning up AI Voice Customer Service System...")
        
        try:
            # Cleanup all components
            for component in reversed(list(self.components.values())):
                try:
                    await component.cleanup()
                except Exception as e:
                    self._log.error(f"Error cleaning up component {component.component_name}: {e}")
            
            # Clear registries
            self.components.clear()
            self.component_dependencies.clear()
            self.event_handlers.clear()
            
            self._log.info("AI Voice Customer Service System cleanup completed")
            
        except Exception as e:
            self._log.error(f"Error during system cleanup: {e}")
    
    async def _initialize_core_components(self) -> None:
        """Initialize all core components."""
        self._log.info("Initializing core components...")

        # Initialize extension manager first
        self.extension_manager = ExtensionManager(self.config_manager, self._log)
        await self.extension_manager.initialize()
        self.components["extension_manager"] = self.extension_manager

        # Initialize component coordinator if available
        if ComponentCoordinator:
            self.component_coordinator = ComponentCoordinator(self.config_manager, self._log)
            await self.component_coordinator.initialize()
            self.components["component_coordinator"] = self.component_coordinator
        else:
            self._log.warning("ComponentCoordinator not available, skipping")

        # Initialize conversation logger if available
        if ConversationLogger:
            self.conversation_logger = ConversationLogger(self.config_manager, self._log)
            await self.conversation_logger.initialize()
            self.components["conversation_logger"] = self.conversation_logger
        else:
            self._log.warning("ConversationLogger not available, skipping")

        # Initialize prompt manager if available
        if PromptManager:
            from ..components.llm.prompt_manager import PromptManagerConfig
            prompt_config = PromptManagerConfig(
                template_directory=self.config_manager.get_config("prompts.template_directory", "data/prompts")
            )
            self.prompt_manager = PromptManager(prompt_config, self.config_manager, self._log)
            await self.prompt_manager.initialize()
            self.components["prompt_manager"] = self.prompt_manager
        else:
            self._log.warning("PromptManager not available, skipping")

        # Initialize conversation manager if available
        if ConversationManager:
            self.conversation_manager = ConversationManager(self.config_manager, self._log)
            await self.conversation_manager.initialize()
            self.components["conversation_manager"] = self.conversation_manager
        else:
            self._log.warning("ConversationManager not available, skipping")

        # Initialize audio pipeline if available
        if AudioPipeline:
            from ..components.audio.audio_pipeline import AudioConfig
            audio_config = AudioConfig(
                sample_rate=self.config_manager.get_config("audio.sample_rate", 16000),
                channels=self.config_manager.get_config("audio.channels", 1),
                chunk_size=self.config_manager.get_config("audio.chunk_size", 1024),
                buffer_duration=self.config_manager.get_config("audio.buffer_duration", 5.0)
            )
            self.audio_pipeline = AudioPipeline(audio_config)
            # AudioPipeline doesn't inherit from BaseComponent, so no initialize() call needed
            self.components["audio_pipeline"] = self.audio_pipeline
        else:
            self._log.warning("AudioPipeline not available, skipping")

        # Initialize telephony manager if available
        if TelephonyManager:
            self.telephony_manager = TelephonyManager(self.config_manager, self._log)
            await self.telephony_manager.initialize()
            self.components["telephony_manager"] = self.telephony_manager
        else:
            self._log.warning("TelephonyManager not available, skipping")

        # Initialize call manager if available and dependencies are met
        if CallManager and self.telephony_manager and self.conversation_logger:
            try:
                from ..services.call_manager import CallManagerConfig
                # Create a mock conversation engine for now
                class MockConversationEngine:
                    def __init__(self):
                        self.is_initialized = True
                        self.is_running = True

                    async def process_message(self, message, context):
                        return {"response": "Hello, how can I help you today?"}

                call_config = CallManagerConfig(
                    max_concurrent_calls=self.config_manager.get_config("calls.max_concurrent", 10),
                    call_timeout_seconds=self.config_manager.get_config("calls.timeout_seconds", 1800)
                )

                mock_conversation_engine = MockConversationEngine()

                self.call_manager = CallManager(
                    config=call_config,
                    conversation_engine=mock_conversation_engine,
                    telephony_interface=self.telephony_manager,
                    conversation_logger=self.conversation_logger,
                    config_manager=self.config_manager,
                    logger=self._log
                )
                await self.call_manager.initialize()
                self.components["call_manager"] = self.call_manager
            except Exception as e:
                self._log.warning(f"Failed to initialize CallManager: {e}")
        else:
            self._log.warning("CallManager not available or dependencies missing, skipping")

        # Initialize monitoring components if available
        if PerformanceMonitor:
            self.performance_monitor = PerformanceMonitor(self.config_manager, self._log)
            await self.performance_monitor.initialize()
            self.components["performance_monitor"] = self.performance_monitor
        else:
            self._log.warning("PerformanceMonitor not available, skipping")

        if SystemHealthMonitor:
            self.health_monitor = SystemHealthMonitor(self.config_manager, self._log)
            await self.health_monitor.initialize()
            self.components["health_monitor"] = self.health_monitor
        else:
            self._log.warning("SystemHealthMonitor not available, skipping")

        # Initialize voice processor if available
        if VoiceProcessor:
            try:
                from ..components.voice.voice_processor import VoiceProcessorConfig
                from ..components.voice.asr_service import ASRConfig, ASREngine
                from ..components.voice.tts_service import TTSConfig, TTSEngine

                # Create ASR configuration
                asr_config = ASRConfig(
                    engine=ASREngine.SENSEVOICE,
                    language=self.config_manager.get_config("voice.asr.language", "zh-CN"),
                    timeout=self.config_manager.get_config("voice.asr.timeout", 10.0),
                    energy_threshold=self.config_manager.get_config("voice.asr.energy_threshold", 300)
                )

                # Create TTS configuration - Use Edge-TTS for better performance
                tts_config = TTSConfig(
                    engine=TTSEngine.EDGE_TTS,  # Use Edge-TTS instead of pyttsx3
                    rate=self.config_manager.get_config("voice.tts.rate", 200),
                    volume=self.config_manager.get_config("voice.tts.volume", 0.9),
                    language=self.config_manager.get_config("voice.tts.language", "en-US")
                )

                # Create voice processor configuration
                voice_config = VoiceProcessorConfig(
                    asr_config=asr_config,
                    tts_config=tts_config,
                    enable_vad=self.config_manager.get_config("voice.enable_vad", True),
                    silence_timeout=self.config_manager.get_config("voice.silence_timeout", 2.0)
                )

                self.voice_processor = VoiceProcessor(voice_config, self._log)
                await self.voice_processor.initialize()
                self.components["voice_processor"] = self.voice_processor
                self._log.info("Voice Processor initialized successfully")
            except Exception as e:
                self._log.warning(f"Failed to initialize Voice Processor: {e}")
        else:
            self._log.warning("VoiceProcessor not available, skipping")

        # Initialize Voice AI System if available
        if VoiceAISystem and VoiceProcessor:
            try:
                from ..components.voice_ai_system import VoiceAIConfig
                from ..components.ai.conversation_engine import ConversationConfig, LLMProvider

                # Create conversation configuration - Use Qwen LLM
                conversation_config = ConversationConfig(
                    llm_provider=LLMProvider.QWEN,  # Use Qwen (通义千问)
                    model_name=self.config_manager.get_config("qwen.model", "qwen-turbo"),
                    max_tokens=self.config_manager.get_config("qwen.max_tokens", 2000),
                    temperature=self.config_manager.get_config("qwen.temperature", 0.7),
                    system_prompt=self.config_manager.get_config("ai.system_prompt",
                        "You are a helpful customer service assistant. Please respond in Chinese."),
                    max_history_length=self.config_manager.get_config("ai.max_history_length", 10)
                )

                # Create Voice AI configuration
                voice_ai_config = VoiceAIConfig(
                    voice_config=voice_config,  # Reuse the voice config from above
                    conversation_config=conversation_config,
                    auto_session_timeout=self.config_manager.get_config("voice_ai.session_timeout", 300.0),
                    enable_conversation_logging=self.config_manager.get_config("voice_ai.enable_logging", True)
                )

                self.voice_ai_system = VoiceAISystem(voice_ai_config, self._log)
                await self.voice_ai_system.initialize()
                self.components["voice_ai_system"] = self.voice_ai_system
                self._log.info("Voice AI System initialized successfully")
            except Exception as e:
                self._log.warning(f"Failed to initialize Voice AI System: {e}")
        else:
            self._log.warning("VoiceAISystem not available, skipping")

        self._log.info(f"Initialized {len(self.components)} core components")
    
    async def _setup_component_dependencies(self) -> None:
        """Setup component dependencies."""
        self._log.info("Setting up component dependencies...")
        
        # Define component dependencies
        self.component_dependencies = {
            "extension_manager": [],
            "component_coordinator": ["extension_manager"],
            "conversation_logger": [],
            "prompt_manager": ["conversation_logger"],
            "conversation_manager": ["prompt_manager", "conversation_logger"],
            "audio_pipeline": ["component_coordinator"],
            "telephony_manager": ["audio_pipeline", "component_coordinator"],
            "call_manager": ["conversation_manager", "telephony_manager", "audio_pipeline"],
            "performance_monitor": ["component_coordinator"],
            "health_monitor": ["performance_monitor"]
        }
        
        # Register components with coordinator
        if self.component_coordinator:
            for name, component in self.components.items():
                # Some components may not have component_type attribute
                component_type = getattr(component, 'component_type', 'generic')
                self.component_coordinator.register_component(name, component_type)
        
        self._log.info("Component dependencies configured")
    
    async def _register_event_handlers(self) -> None:
        """Register system event handlers."""
        self._log.info("Registering event handlers...")
        
        # Register call event handlers
        self.register_event_handler("call_started", self._handle_call_started)
        self.register_event_handler("call_ended", self._handle_call_ended)
        self.register_event_handler("call_error", self._handle_call_error)
        
        # Register conversation event handlers
        self.register_event_handler("conversation_started", self._handle_conversation_started)
        self.register_event_handler("conversation_ended", self._handle_conversation_ended)
        
        # Register system event handlers
        self.register_event_handler("component_error", self._handle_component_error)
        self.register_event_handler("system_overload", self._handle_system_overload)
        
        self._log.info("Event handlers registered")
    
    async def _start_components_in_order(self) -> None:
        """Start components in dependency order."""
        self._log.info("Starting components in dependency order...")
        
        # Topological sort of components based on dependencies
        start_order = self._get_component_start_order()
        
        for component_name in start_order:
            component = self.components.get(component_name)
            if component:
                try:
                    self._log.info(f"Starting component: {component_name}")
                    await component.start()
                    self._log.info(f"Component started: {component_name}")
                except Exception as e:
                    self._log.error(f"Failed to start component {component_name}: {e}")
                    raise
        
        self._log.info("All components started successfully")
    
    async def _stop_components_in_reverse_order(self) -> None:
        """Stop components in reverse dependency order."""
        self._log.info("Stopping components in reverse order...")
        
        # Get start order and reverse it
        start_order = self._get_component_start_order()
        stop_order = list(reversed(start_order))
        
        for component_name in stop_order:
            component = self.components.get(component_name)
            if component:
                try:
                    self._log.info(f"Stopping component: {component_name}")
                    await component.stop()
                    self._log.info(f"Component stopped: {component_name}")
                except Exception as e:
                    self._log.error(f"Error stopping component {component_name}: {e}")
        
        self._log.info("All components stopped")
    
    def _get_component_start_order(self) -> List[str]:
        """Get component start order using topological sort."""
        # Simple topological sort implementation
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(component_name: str):
            if component_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving {component_name}")
            if component_name in visited:
                return
            
            temp_visited.add(component_name)
            
            # Visit dependencies first
            for dependency in self.component_dependencies.get(component_name, []):
                visit(dependency)
            
            temp_visited.remove(component_name)
            visited.add(component_name)
            result.append(component_name)
        
        # Visit all components
        for component_name in self.components.keys():
            if component_name not in visited:
                visit(component_name)
        
        return result
    
    async def _start_system_monitoring(self) -> None:
        """Start system-wide monitoring."""
        self._log.info("Starting system monitoring...")
        
        # Performance and health monitoring are already started as components
        # No additional monitoring startup needed
        
        self._log.info("System monitoring started")
    
    # Event handling system
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """Register an event handler."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    async def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Trigger an event and call all registered handlers."""
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event_data)
                    else:
                        handler(event_data)
                except Exception as e:
                    self._log.error(f"Error in event handler for {event_type}: {e}")
    
    # Event handlers
    
    async def _handle_call_started(self, event_data: Dict[str, Any]) -> None:
        """Handle call started event."""
        call_id = event_data.get("call_id")
        self._log.info(f"Call started: {call_id}")
        
        # Log call start
        if self.conversation_logger:
            await self.conversation_logger.log_call_event(call_id, "call_started", event_data)
    
    async def _handle_call_ended(self, event_data: Dict[str, Any]) -> None:
        """Handle call ended event."""
        call_id = event_data.get("call_id")
        self._log.info(f"Call ended: {call_id}")
        
        # Update statistics
        self.total_calls_handled += 1
        
        # Log call end
        if self.conversation_logger:
            await self.conversation_logger.log_call_event(call_id, "call_ended", event_data)
    
    async def _handle_call_error(self, event_data: Dict[str, Any]) -> None:
        """Handle call error event."""
        call_id = event_data.get("call_id")
        error = event_data.get("error")
        self._log.error(f"Call error for {call_id}: {error}")
        
        # Update error statistics
        self.system_errors += 1
        
        # Log error
        if self.conversation_logger:
            await self.conversation_logger.log_call_event(call_id, "call_error", event_data)
    
    async def _handle_conversation_started(self, event_data: Dict[str, Any]) -> None:
        """Handle conversation started event."""
        conversation_id = event_data.get("conversation_id")
        self._log.info(f"Conversation started: {conversation_id}")
    
    async def _handle_conversation_ended(self, event_data: Dict[str, Any]) -> None:
        """Handle conversation ended event."""
        conversation_id = event_data.get("conversation_id")
        self._log.info(f"Conversation ended: {conversation_id}")
    
    async def _handle_component_error(self, event_data: Dict[str, Any]) -> None:
        """Handle component error event."""
        component_name = event_data.get("component")
        error = event_data.get("error")
        self._log.error(f"Component error in {component_name}: {error}")
        
        # Trigger recovery if health monitor is available
        if self.health_monitor:
            await self.health_monitor.handle_component_error(component_name, error)
    
    async def _handle_system_overload(self, event_data: Dict[str, Any]) -> None:
        """Handle system overload event."""
        self._log.warning("System overload detected")
        
        # Trigger auto-scaling if available
        if self.extension_manager and self.extension_manager.auto_scaler:
            # Auto-scaler will handle the overload
            pass
    
    # Public API
    
    def get_system_status(self) -> SystemStatus:
        """Get current system status."""
        components_initialized = sum(1 for c in self.components.values() if getattr(c, 'is_initialized', True))
        components_running = sum(1 for c in self.components.values() if getattr(c, 'is_running', True))
        components_error = sum(1 for c in self.components.values() if getattr(c, 'has_error', False))
        
        active_calls = 0
        if self.call_manager:
            call_stats = self.call_manager.get_call_statistics()
            active_calls = call_stats.get("active_calls", 0)
        
        uptime = 0.0
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        return SystemStatus(
            state=self.system_state,
            components_initialized=components_initialized,
            components_running=components_running,
            components_error=components_error,
            active_calls=active_calls,
            total_calls_handled=self.total_calls_handled,
            system_uptime=uptime,
            last_updated=datetime.now()
        )
    
    def get_component_status(self, component_name: str = None) -> Dict[str, Any]:
        """Get status of specific component or all components."""
        if component_name:
            component = self.components.get(component_name)
            if component:
                return {
                    "name": getattr(component, 'component_name', component_name),
                    "type": getattr(component, 'component_type', 'unknown'),
                    "initialized": getattr(component, 'is_initialized', False),
                    "running": getattr(component, 'is_running', False)
                }
            else:
                return {}
        else:
            return {
                name: {
                    "name": getattr(component, 'component_name', name),
                    "type": getattr(component, 'component_type', 'unknown'),
                    "initialized": getattr(component, 'is_initialized', False),
                    "running": getattr(component, 'is_running', False)
                }
                for name, component in self.components.items()
            }
    
    async def process_incoming_call(self, caller_number: str, callee_number: str) -> Optional[str]:
        """Process an incoming call through the complete system."""
        if self.system_state != SystemState.RUNNING:
            self._log.error("System is not running, cannot process call")
            return None
        
        try:
            # Create call session
            call_session = await self.call_manager.initiate_call(caller_number)
            if not call_session:
                return None
            call_id = call_session.session_id
            
            # Trigger call started event
            await self._trigger_event("call_started", {
                "call_id": call_id,
                "caller_number": caller_number,
                "callee_number": callee_number,
                "timestamp": datetime.now()
            })
            
            return call_id
            
        except Exception as e:
            self._log.error(f"Error processing incoming call: {e}")
            await self._trigger_event("call_error", {
                "caller_number": caller_number,
                "callee_number": callee_number,
                "error": str(e),
                "timestamp": datetime.now()
            })
            return None
