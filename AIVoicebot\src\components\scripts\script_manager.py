"""
Script manager for conversation script management.

This module provides centralized management of conversation scripts,
including parsing, matching, hot-reloading, and validation.
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from ...core.interfaces import IScriptManager, ScriptResponse, ConversationError
from ...core.base_component import BaseComponent
from .script_parser import <PERSON>riptPars<PERSON>, ScriptParserConfig, ConversationScript
from .huashu_parser import HuashuParser, HuashuParserConfig
from .response_matcher import ResponseMatcher, ResponseMatcherConfig, MatchResult
from .file_watcher import FileWatcher, FileWatcherConfig, setup_script_hot_reload


@dataclass
class ScriptManagerConfig:
    """Configuration for script manager."""
    # Parser configuration
    script_directory: str = "docs"
    parser_config: Optional[ScriptParserConfig] = None
    
    # Matcher configuration
    matcher_config: Optional[ResponseMatcherConfig] = None
    
    # File watcher configuration
    file_watcher_config: Optional[FileWatcherConfig] = None
    
    # Hot-reloading
    enable_hot_reload: bool = True
    reload_debounce_seconds: float = 2.0  # Debounce file changes
    
    # Validation
    enable_validation: bool = True
    validation_on_load: bool = True
    
    # Performance
    preload_scripts: bool = True
    cache_responses: bool = True


class ScriptFileWatcher(FileSystemEventHandler):
    """File system watcher for script hot-reloading."""
    
    def __init__(self, script_manager):
        self.script_manager = script_manager
        self.last_reload_time = 0
        
    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return
        
        # Check if it's an Excel file
        if any(event.src_path.endswith(ext) for ext in ['.xlsx', '.xls']):
            current_time = datetime.now().timestamp()
            
            # Debounce rapid file changes
            if current_time - self.last_reload_time > self.script_manager.config.reload_debounce_seconds:
                self.last_reload_time = current_time
                
                # Schedule reload
                asyncio.create_task(self.script_manager._handle_file_change(event.src_path))


class ScriptManager(BaseComponent, IScriptManager):
    """
    Centralized manager for conversation scripts.
    
    Provides script parsing, matching, hot-reloading, and validation
    with a unified interface for the conversation system.
    """
    
    def __init__(self, config: ScriptManagerConfig, config_manager, logger=None):
        """
        Initialize script manager.
        
        Args:
            config: Script manager configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("script_manager", config_manager, logger)
        
        self.config = config
        
        # Create parser and matcher
        parser_config = config.parser_config or ScriptParserConfig(
            script_directory=config.script_directory
        )
        matcher_config = config.matcher_config or ResponseMatcherConfig()
        
        self.parser = ScriptParser(parser_config, config_manager, logger)
        self.matcher = ResponseMatcher(matcher_config, config_manager, logger)

        # 话术解析器 (专门用于处理话术Excel文件)
        huashu_config = HuashuParserConfig()
        self.huashu_parser = HuashuParser(huashu_config, config_manager, logger)
        
        # File watching - new enhanced file watcher
        file_watcher_config = config.file_watcher_config or FileWatcherConfig(
            watch_directory=config.script_directory,
            debounce_seconds=config.reload_debounce_seconds
        )
        self.file_watcher: Optional[FileWatcher] = FileWatcher(file_watcher_config, config_manager, logger)
        
        # Legacy file watching (keeping for compatibility)
        self.file_observer: Optional[Observer] = None
        self.legacy_file_watcher: Optional[ScriptFileWatcher] = None
        
        # Script storage
        self.loaded_scripts: Dict[str, ConversationScript] = {}
        self.last_load_time: Optional[datetime] = None
        
        # Statistics
        self._reload_count = 0
        self._last_validation_report: Optional[Dict[str, Any]] = None
    
    async def _initialize_impl(self) -> None:
        """Initialize script manager."""
        self._log.info("Initializing script manager...")
        
        # Initialize components
        await self.parser.initialize()
        await self.matcher.initialize()
        await self.huashu_parser.initialize()
        
        # Initialize file watcher if hot reload is enabled
        if self.config.enable_hot_reload and self.file_watcher:
            await self.file_watcher.initialize()
            # Setup hot reload integration
            setup_script_hot_reload(self, self.file_watcher)
        
        # Load scripts if enabled
        if self.config.preload_scripts:
            await self.load_scripts()
        
        # Setup legacy file watching if enabled (fallback)
        if self.config.enable_hot_reload and not self.file_watcher:
            self._setup_legacy_file_watching()
        
        self._log.info("Script manager initialized")
    
    async def _start_impl(self) -> None:
        """Start script manager."""
        self._log.info("Starting script manager...")
        
        # Start components
        await self.parser.start()
        await self.matcher.start()
        await self.huashu_parser.start()
        
        # Start file watcher
        if self.file_watcher and self.config.enable_hot_reload:
            await self.file_watcher.start()
        
        # Start legacy file watcher (fallback)
        if self.file_observer:
            self.file_observer.start()
        
        self._log.info("Script manager started")
    
    async def _stop_impl(self) -> None:
        """Stop script manager."""
        self._log.info("Stopping script manager...")
        
        # Stop file watcher
        if self.file_watcher:
            await self.file_watcher.stop()
        
        # Stop legacy file watcher
        if self.file_observer:
            self.file_observer.stop()
            self.file_observer.join()
        
        # Stop components
        await self.matcher.stop()
        await self.parser.stop()
        await self.huashu_parser.stop()
        
        # Log statistics
        self._log.info(f"Script Manager Statistics:")
        self._log.info(f"  Loaded scripts: {len(self.loaded_scripts)}")
        self._log.info(f"  Reload count: {self._reload_count}")
        if self.last_load_time:
            self._log.info(f"  Last load time: {self.last_load_time}")
        
        self._log.info("Script manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup script manager resources."""
        self._log.info("Cleaning up script manager...")
        
        # Cleanup file watcher
        if self.file_watcher:
            await self.file_watcher.cleanup()
        
        # Cleanup components
        await self.matcher.cleanup()
        await self.parser.cleanup()
        await self.huashu_parser.cleanup()
        
        # Clear data
        self.loaded_scripts.clear()
        self._last_validation_report = None
        
        self._log.info("Script manager cleanup completed")
    
    def _setup_legacy_file_watching(self) -> None:
        """Setup legacy file system watching for hot-reloading (fallback)."""
        try:
            self.legacy_file_watcher = ScriptFileWatcher(self)
            self.file_observer = Observer()
            
            # Watch script directory
            self.file_observer.schedule(
                self.legacy_file_watcher,
                self.config.script_directory,
                recursive=False
            )
            
            self._log.info(f"Legacy file watching enabled for: {self.config.script_directory}")
            
        except Exception as e:
            self._log.error(f"Failed to setup legacy file watching: {e}")
            self.file_observer = None
            self.legacy_file_watcher = None
    
    async def load_scripts(self, directory_path: Optional[str] = None) -> None:
        """
        Load conversation scripts from Excel files.
        
        Args:
            directory_path: Directory path (uses config default if None)
        """
        try:
            self._log.info("Loading conversation scripts...")
            
            # Parse scripts using both parsers
            parsed_results = self.parser.parse_directory(directory_path)
            huashu_results = self.huashu_parser.parse_directory(directory_path or self.config.script_directory)

            # Collect all scripts
            all_scripts = {}

            # Add scripts from regular parser
            for file_path, scripts in parsed_results.items():
                for script in scripts:
                    all_scripts[script.script_id] = script

            # Add scripts from huashu parser
            for file_path, scripts in huashu_results.items():
                for script in scripts:
                    all_scripts[script.script_id] = script
            
            # Update loaded scripts
            self.loaded_scripts = all_scripts
            self.last_load_time = datetime.now()
            
            # Load scripts into matcher
            self.matcher.load_scripts(all_scripts)
            
            # Validate if enabled
            if self.config.validation_on_load:
                await self.validate_scripts()
            
            total_files = len(parsed_results) + len(huashu_results)
            self._log.info(f"Loaded {len(all_scripts)} scripts from {total_files} files")
            self._log.info(f"  Regular parser: {len(parsed_results)} files, {sum(len(scripts) for scripts in parsed_results.values())} scripts")
            self._log.info(f"  Huashu parser: {len(huashu_results)} files, {sum(len(scripts) for scripts in huashu_results.values())} scripts")
            
        except Exception as e:
            self._log.error(f"Failed to load scripts: {e}")
            raise ConversationError(f"Script loading failed: {e}")
    
    async def reload_scripts(self) -> None:
        """Reload all scripts from directory."""
        self._log.info("Reloading conversation scripts...")
        
        try:
            # Clear existing scripts
            self.loaded_scripts.clear()
            self.parser.clear_scripts()
            self.matcher.clear_cache()
            
            # Reload scripts
            await self.load_scripts()
            
            self._reload_count += 1
            self._log.info("Scripts reloaded successfully")
            
        except Exception as e:
            self._log.error(f"Failed to reload scripts: {e}")
            raise ConversationError(f"Script reloading failed: {e}")
    
    async def _handle_file_change(self, file_path: str) -> None:
        """Handle file system change event."""
        self._log.info(f"Detected file change: {file_path}")
        
        try:
            # Reload scripts
            await self.reload_scripts()
            self._log.info(f"Successfully reloaded scripts after file change: {file_path}")
            
        except Exception as e:
            self._log.error(f"Failed to reload scripts after file change {file_path}: {e}")
    
    async def find_response(self, query: str, context=None) -> Optional[ScriptResponse]:
        """
        Find appropriate script response for a query.
        
        Args:
            query: User query
            context: Optional conversation context
            
        Returns:
            Script response or None if no match found
        """
        try:
            # Find best match
            best_match = self.matcher.find_best_match(query)
            
            if best_match:
                # Convert to ScriptResponse
                script_response = best_match.script.to_script_response()
                
                # Add matching metadata
                script_response.metadata.update({
                    "match_confidence": best_match.confidence,
                    "match_type": best_match.match_type,
                    "matched_keywords": best_match.matched_keywords,
                    "processing_time_ms": best_match.processing_time_ms
                })
                
                return script_response
            
            return None
            
        except Exception as e:
            self._log.error(f"Error finding response for query '{query}': {e}")
            return None
    
    async def find_multiple_responses(self, query: str, limit: int = 5) -> List[ScriptResponse]:
        """
        Find multiple script responses for a query.
        
        Args:
            query: User query
            limit: Maximum number of responses
            
        Returns:
            List of script responses
        """
        try:
            matches = self.matcher.find_matches(query, limit=limit)
            
            responses = []
            for match in matches:
                script_response = match.script.to_script_response()
                script_response.metadata.update({
                    "match_confidence": match.confidence,
                    "match_type": match.match_type,
                    "matched_keywords": match.matched_keywords
                })
                responses.append(script_response)
            
            return responses
            
        except Exception as e:
            self._log.error(f"Error finding multiple responses for query '{query}': {e}")
            return []
    
    async def find_combined_response(self, query: str) -> Optional[List[ScriptResponse]]:
        """
        Find multiple scripts that can be combined for complex response.
        
        Args:
            query: User query
            
        Returns:
            List of script responses that can be combined
        """
        try:
            combined_matches = self.matcher.find_combined_response(query)
            
            if combined_matches:
                responses = []
                for match in combined_matches:
                    script_response = match.script.to_script_response()
                    script_response.metadata.update({
                        "match_confidence": match.confidence,
                        "match_type": match.match_type,
                        "combination_candidate": True
                    })
                    responses.append(script_response)
                
                return responses
            
            return None
            
        except Exception as e:
            self._log.error(f"Error finding combined response for query '{query}': {e}")
            return None
    
    async def find_by_intent(self, intent: str, limit: int = 5) -> List[ScriptResponse]:
        """
        Find scripts by intent classification.
        
        Args:
            intent: Intent to match
            limit: Maximum number of responses
            
        Returns:
            List of matching script responses
        """
        try:
            matches = self.matcher.match_by_intent(intent, limit=limit)
            
            responses = []
            for match in matches:
                script_response = match.script.to_script_response()
                script_response.metadata.update({
                    "match_confidence": match.confidence,
                    "match_type": "intent",
                    "matched_intent": intent
                })
                responses.append(script_response)
            
            return responses
            
        except Exception as e:
            self._log.error(f"Error finding responses by intent '{intent}': {e}")
            return []
    
    async def validate_scripts(self) -> Dict[str, Any]:
        """
        Validate all loaded scripts.
        
        Returns:
            Validation report
        """
        try:
            self._log.info("Validating loaded scripts...")
            
            # Run parser validation
            validation_report = self.parser.validate_all_scripts()
            
            # Add manager-specific validation
            validation_report["manager_stats"] = {
                "loaded_scripts": len(self.loaded_scripts),
                "last_load_time": self.last_load_time.isoformat() if self.last_load_time else None,
                "reload_count": self._reload_count
            }
            
            # Store report
            self._last_validation_report = validation_report
            
            self._log.info(f"Validation completed: {validation_report['valid_scripts']} valid, "
                          f"{validation_report['invalid_scripts']} invalid")
            
            return validation_report
            
        except Exception as e:
            self._log.error(f"Error validating scripts: {e}")
            return {"error": str(e)}
    
    def get_script_by_id(self, script_id: str) -> Optional[ConversationScript]:
        """Get script by ID."""
        return self.loaded_scripts.get(script_id)
    
    def get_scripts_by_intent(self, intent: str) -> List[ConversationScript]:
        """Get scripts filtered by intent."""
        return [
            script for script in self.loaded_scripts.values()
            if script.intent and script.intent.lower() == intent.lower()
        ]
    
    def get_all_intents(self) -> List[str]:
        """Get list of all unique intents."""
        intents = set()
        for script in self.loaded_scripts.values():
            if script.intent:
                intents.add(script.intent)
        return sorted(list(intents))
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """Get script manager statistics."""
        parser_stats = self.parser.get_parsing_statistics()
        matcher_stats = self.matcher.get_matching_statistics()
        
        # Get file watcher stats if available
        file_watcher_stats = None
        if self.file_watcher:
            file_watcher_stats = self.file_watcher.get_watcher_stats()
        
        return {
            "loaded_scripts": len(self.loaded_scripts),
            "last_load_time": self.last_load_time.isoformat() if self.last_load_time else None,
            "reload_count": self._reload_count,
            "hot_reload_enabled": self.config.enable_hot_reload,
            "validation_enabled": self.config.enable_validation,
            "parser_stats": parser_stats,
            "matcher_stats": matcher_stats,
            "file_watcher_stats": file_watcher_stats,
            "last_validation": self._last_validation_report
        }
    
    def explain_response_selection(self, query: str, response: ScriptResponse) -> Dict[str, Any]:
        """
        Explain why a particular response was selected.
        
        Args:
            query: Original query
            response: Selected response
            
        Returns:
            Explanation dictionary
        """
        # Find the script that generated this response
        script_id = response.response_id
        
        if script_id not in self.loaded_scripts:
            return {"error": "Script not found"}
        
        script = self.loaded_scripts[script_id]
        
        # Create a match result for explanation
        match_result = MatchResult(
            script=script,
            confidence=response.metadata.get("match_confidence", 0.0),
            match_type=response.metadata.get("match_type", "unknown"),
            matched_keywords=response.metadata.get("matched_keywords", [])
        )
        
        return self.matcher.explain_match(query, match_result)
    
    async def force_reload_all_scripts(self) -> None:
        """Force reload all scripts from directory."""
        if self.file_watcher:
            await self.file_watcher.trigger_reload_all()
        else:
            await self.reload_scripts()
    
    def get_watched_files(self) -> List[str]:
        """Get list of files being watched."""
        if self.file_watcher:
            excel_files = self.file_watcher.force_scan()
            return [str(f) for f in excel_files]
        return []
    
    def is_hot_reload_active(self) -> bool:
        """Check if hot reload is currently active."""
        if self.file_watcher:
            stats = self.file_watcher.get_watcher_stats()
            return stats.get("is_watching", False)
        return False


# Utility functions for script management

async def create_script_manager(
    script_directory: str = "docs",
    enable_hot_reload: bool = True,
    fuzzy_threshold: float = 0.6,
    config_manager=None,
    **kwargs
) -> ScriptManager:
    """
    Create and initialize script manager.
    
    Args:
        script_directory: Directory containing script files
        enable_hot_reload: Enable automatic reloading
        fuzzy_threshold: Matching threshold
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized script manager
    """
    # Create configurations
    parser_config = ScriptParserConfig(
        script_directory=script_directory,
        **{k: v for k, v in kwargs.items() if k.startswith('parser_')}
    )
    
    matcher_config = ResponseMatcherConfig(
        fuzzy_threshold=fuzzy_threshold,
        **{k: v for k, v in kwargs.items() if k.startswith('matcher_')}
    )
    
    file_watcher_config = FileWatcherConfig(
        watch_directory=script_directory,
        **{k: v for k, v in kwargs.items() if k.startswith('watcher_')}
    )
    
    manager_config = ScriptManagerConfig(
        script_directory=script_directory,
        enable_hot_reload=enable_hot_reload,
        parser_config=parser_config,
        matcher_config=matcher_config,
        file_watcher_config=file_watcher_config,
        **{k: v for k, v in kwargs.items() if not k.startswith(('parser_', 'matcher_', 'watcher_'))}
    )
    
    # Create and initialize manager
    manager = ScriptManager(manager_config, config_manager)
    await manager.initialize()
    await manager.start()
    
    return manager