"""
Response matcher for query-to-script matching.

This module provides fuzzy matching and intent classification for script selection,
supporting combining multiple script elements for complex responses.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import difflib
from collections import defaultdict

from ...core.interfaces import ConversationError
from ...core.base_component import BaseComponent
from .script_parser import ConversationScript


@dataclass
class ResponseMatcherConfig:
    """Configuration for response matcher."""
    # Matching parameters
    fuzzy_threshold: float = 0.6  # Minimum similarity score for fuzzy matching
    exact_match_bonus: float = 0.2  # Bonus for exact keyword matches
    intent_match_bonus: float = 0.3  # Bonus for intent matches
    
    # Matching methods
    enable_fuzzy_matching: bool = True
    enable_keyword_matching: bool = True
    enable_intent_matching: bool = True
    enable_semantic_matching: bool = False  # For future enhancement
    
    # Response combination
    enable_response_combination: bool = True
    max_combined_responses: int = 3
    combination_threshold: float = 0.4
    
    # Text processing
    normalize_text: bool = True
    remove_punctuation: bool = True
    case_sensitive: bool = False
    
    # Performance settings
    max_candidates: int = 50  # Maximum candidates to consider
    cache_results: bool = True
    cache_size: int = 1000


@dataclass
class MatchResult:
    """Result from script matching."""
    script: ConversationScript
    confidence: float
    match_type: str  # "exact", "fuzzy", "keyword", "intent"
    matched_keywords: List[str] = field(default_factory=list)
    
    # Matching details
    similarity_score: float = 0.0
    keyword_score: float = 0.0
    intent_score: float = 0.0
    
    # Metadata
    processing_time_ms: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


class ResponseMatcher(BaseComponent):
    """
    Response matcher for query-to-script matching.
    
    Provides fuzzy matching, keyword matching, and intent classification
    for selecting appropriate conversation scripts.
    """
    
    def __init__(self, config: ResponseMatcherConfig, config_manager, logger=None):
        """
        Initialize response matcher.
        
        Args:
            config: Matcher configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("response_matcher", config_manager, logger)
        
        self.config = config
        self.scripts: Dict[str, ConversationScript] = {}
        
        # Matching caches
        self._match_cache: Dict[str, List[MatchResult]] = {}
        self._keyword_index: Dict[str, List[str]] = defaultdict(list)  # keyword -> script_ids
        self._intent_index: Dict[str, List[str]] = defaultdict(list)   # intent -> script_ids
        
        # Statistics
        self._total_queries = 0
        self._cache_hits = 0
        self._match_times = []
    
    async def _initialize_impl(self) -> None:
        """Initialize response matcher."""
        self._log.info("Initializing response matcher...")
        
        # Reset statistics
        self._total_queries = 0
        self._cache_hits = 0
        self._match_times.clear()
        
        self._log.info("Response matcher initialized")
    
    async def _start_impl(self) -> None:
        """Start response matcher."""
        self._log.info("Starting response matcher...")
        self._log.info("Response matcher started")
    
    async def _stop_impl(self) -> None:
        """Stop response matcher."""
        self._log.info("Stopping response matcher...")
        
        # Log statistics
        if self._total_queries > 0:
            avg_time = sum(self._match_times) / len(self._match_times) if self._match_times else 0
            cache_hit_rate = self._cache_hits / self._total_queries
            
            self._log.info(f"Matcher Statistics:")
            self._log.info(f"  Total queries: {self._total_queries}")
            self._log.info(f"  Cache hit rate: {cache_hit_rate:.2%}")
            self._log.info(f"  Average match time: {avg_time:.2f}ms")
        
        self._log.info("Response matcher stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup response matcher resources."""
        self._log.info("Cleaning up response matcher...")
        
        # Clear caches and indexes
        self._match_cache.clear()
        self._keyword_index.clear()
        self._intent_index.clear()
        self.scripts.clear()
        
        self._log.info("Response matcher cleanup completed")
    
    def load_scripts(self, scripts: Dict[str, ConversationScript]) -> None:
        """
        Load conversation scripts for matching.
        
        Args:
            scripts: Dictionary of scripts to load
        """
        self.scripts = scripts.copy()
        
        # Rebuild indexes
        self._build_keyword_index()
        self._build_intent_index()
        
        # Clear cache
        self._match_cache.clear()
        
        self._log.info(f"Loaded {len(scripts)} scripts for matching")
    
    def _build_keyword_index(self) -> None:
        """Build keyword index for fast keyword matching."""
        self._keyword_index.clear()
        
        for script_id, script in self.scripts.items():
            # Extract keywords from query
            keywords = self._extract_keywords(script.query)
            
            for keyword in keywords:
                self._keyword_index[keyword].append(script_id)
        
        self._log.debug(f"Built keyword index with {len(self._keyword_index)} keywords")
    
    def _build_intent_index(self) -> None:
        """Build intent index for fast intent matching."""
        self._intent_index.clear()
        
        for script_id, script in self.scripts.items():
            if script.intent:
                intent_key = script.intent.lower() if not self.config.case_sensitive else script.intent
                self._intent_index[intent_key].append(script_id)
        
        self._log.debug(f"Built intent index with {len(self._intent_index)} intents")
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text."""
        if not text:
            return []
        
        # Normalize text
        if self.config.normalize_text:
            text = self._normalize_text(text)
        
        # Simple keyword extraction (can be enhanced with NLP)
        # Remove punctuation and split by whitespace
        if self.config.remove_punctuation:
            text = re.sub(r'[^\w\s]', ' ', text)
        
        words = text.split()
        
        # Filter out short words and common stop words
        stop_words = {'的', '了', '是', '在', '有', '和', '就', '不', '人', '都', '一', '我', '你', '他', '她', '它'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for matching."""
        if not text:
            return text
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Convert case
        if not self.config.case_sensitive:
            text = text.lower()
        
        return text
    
    def find_matches(self, query: str, limit: int = 10) -> List[MatchResult]:
        """
        Find matching scripts for query.
        
        Args:
            query: User query
            limit: Maximum number of matches to return
            
        Returns:
            List of match results sorted by confidence
        """
        start_time = datetime.now()
        
        # Check cache first
        cache_key = f"{query}:{limit}"
        if self.config.cache_results and cache_key in self._match_cache:
            self._cache_hits += 1
            self._total_queries += 1
            return self._match_cache[cache_key]
        
        matches = []
        
        try:
            # Normalize query
            normalized_query = self._normalize_text(query) if self.config.normalize_text else query
            
            # Get candidate scripts
            candidates = self._get_candidate_scripts(normalized_query)
            
            # Score each candidate
            for script_id in candidates:
                if script_id not in self.scripts:
                    continue
                
                script = self.scripts[script_id]
                match_result = self._score_script_match(normalized_query, script)
                
                if match_result.confidence >= self.config.fuzzy_threshold:
                    matches.append(match_result)
            
            # Sort by confidence (descending)
            matches.sort(key=lambda m: m.confidence, reverse=True)
            
            # Limit results
            matches = matches[:limit]
            
            # Cache results
            if self.config.cache_results:
                if len(self._match_cache) >= self.config.cache_size:
                    # Remove oldest entries (simple FIFO)
                    oldest_key = next(iter(self._match_cache))
                    del self._match_cache[oldest_key]
                
                self._match_cache[cache_key] = matches
            
        except Exception as e:
            self._log.error(f"Error finding matches for query '{query}': {e}")
            matches = []
        
        # Update statistics
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        self._match_times.append(processing_time)
        if len(self._match_times) > 1000:
            self._match_times = self._match_times[-1000:]
        
        self._total_queries += 1
        
        return matches
    
    def _get_candidate_scripts(self, query: str) -> List[str]:
        """Get candidate script IDs for detailed scoring."""
        candidates = set()
        
        # Get candidates from keyword matching
        if self.config.enable_keyword_matching:
            query_keywords = self._extract_keywords(query)
            for keyword in query_keywords:
                if keyword in self._keyword_index:
                    candidates.update(self._keyword_index[keyword])
        
        # If no keyword matches, consider all scripts
        if not candidates:
            candidates = set(self.scripts.keys())
        
        # Limit candidates for performance
        candidate_list = list(candidates)[:self.config.max_candidates]
        
        return candidate_list
    
    def _score_script_match(self, query: str, script: ConversationScript) -> MatchResult:
        """Score how well a script matches the query."""
        # Initialize scores
        similarity_score = 0.0
        keyword_score = 0.0
        intent_score = 0.0
        matched_keywords = []
        match_type = "fuzzy"
        
        # Fuzzy matching score
        if self.config.enable_fuzzy_matching:
            similarity_score = self._calculate_similarity(query, script.query)
            
            # Check for exact match
            if query.strip() == script.query.strip():
                similarity_score += self.config.exact_match_bonus
                match_type = "exact"
        
        # Keyword matching score
        if self.config.enable_keyword_matching:
            keyword_score, matched_keywords = self._calculate_keyword_score(query, script.query)
            if matched_keywords:
                match_type = "keyword"
        
        # Intent matching score
        if self.config.enable_intent_matching and script.intent:
            intent_score = self._calculate_intent_score(query, script.intent)
            if intent_score > 0:
                match_type = "intent"
        
        # Combine scores
        confidence = (
            similarity_score * 0.5 +
            keyword_score * 0.3 +
            intent_score * 0.2
        )
        
        # Apply bonuses
        if matched_keywords:
            confidence += self.config.exact_match_bonus * len(matched_keywords) / 10
        
        if intent_score > 0:
            confidence += self.config.intent_match_bonus
        
        # Ensure confidence is in [0, 1] range
        confidence = max(0.0, min(1.0, confidence))
        
        return MatchResult(
            script=script,
            confidence=confidence,
            match_type=match_type,
            matched_keywords=matched_keywords,
            similarity_score=similarity_score,
            keyword_score=keyword_score,
            intent_score=intent_score
        )
    
    def _calculate_similarity(self, query1: str, query2: str) -> float:
        """Calculate text similarity using difflib."""
        return difflib.SequenceMatcher(None, query1, query2).ratio()
    
    def _calculate_keyword_score(self, query: str, script_query: str) -> Tuple[float, List[str]]:
        """Calculate keyword matching score."""
        query_keywords = set(self._extract_keywords(query))
        script_keywords = set(self._extract_keywords(script_query))
        
        if not query_keywords or not script_keywords:
            return 0.0, []
        
        # Find matching keywords
        matched = query_keywords.intersection(script_keywords)
        
        if not matched:
            return 0.0, []
        
        # Calculate score based on overlap
        score = len(matched) / len(query_keywords.union(script_keywords))
        
        return score, list(matched)
    
    def _calculate_intent_score(self, query: str, script_intent: str) -> float:
        """Calculate intent matching score (placeholder for future enhancement)."""
        # Simple keyword-based intent matching
        query_lower = query.lower()
        intent_lower = script_intent.lower()
        
        # Check if intent keywords appear in query
        intent_keywords = intent_lower.split()
        matches = sum(1 for keyword in intent_keywords if keyword in query_lower)
        
        if matches > 0:
            return matches / len(intent_keywords)
        
        return 0.0    

    def find_best_match(self, query: str) -> Optional[MatchResult]:
        """
        Find the best matching script for query.
        
        Args:
            query: User query
            
        Returns:
            Best match result or None if no good match found
        """
        matches = self.find_matches(query, limit=1)
        
        if matches and matches[0].confidence >= self.config.fuzzy_threshold:
            return matches[0]
        
        return None
    
    def find_combined_response(self, query: str) -> Optional[List[MatchResult]]:
        """
        Find multiple scripts that can be combined for a complex response.
        
        Args:
            query: User query
            
        Returns:
            List of match results that can be combined
        """
        if not self.config.enable_response_combination:
            return None
        
        matches = self.find_matches(query, limit=self.config.max_combined_responses * 2)
        
        # Filter matches above combination threshold
        combinable_matches = [
            match for match in matches
            if match.confidence >= self.config.combination_threshold
        ]
        
        if len(combinable_matches) <= 1:
            return None
        
        # Select diverse matches (avoid too similar responses)
        combined_matches = self._select_diverse_matches(combinable_matches)
        
        return combined_matches[:self.config.max_combined_responses]
    
    def _select_diverse_matches(self, matches: List[MatchResult]) -> List[MatchResult]:
        """Select diverse matches to avoid redundant responses."""
        if len(matches) <= 1:
            return matches
        
        selected = [matches[0]]  # Always include the best match
        
        for match in matches[1:]:
            # Check if this match is sufficiently different from selected ones
            is_diverse = True
            
            for selected_match in selected:
                # Simple diversity check based on response similarity
                similarity = self._calculate_similarity(
                    match.script.response,
                    selected_match.script.response
                )
                
                if similarity > 0.8:  # Too similar
                    is_diverse = False
                    break
            
            if is_diverse:
                selected.append(match)
        
        return selected
    
    def match_by_intent(self, intent: str, limit: int = 5) -> List[MatchResult]:
        """
        Find scripts matching specific intent.
        
        Args:
            intent: Intent to match
            limit: Maximum number of results
            
        Returns:
            List of matching scripts
        """
        intent_key = intent.lower() if not self.config.case_sensitive else intent
        
        if intent_key not in self._intent_index:
            return []
        
        matches = []
        script_ids = self._intent_index[intent_key]
        
        for script_id in script_ids[:limit]:
            if script_id in self.scripts:
                script = self.scripts[script_id]
                
                # Create match result with high intent score
                match_result = MatchResult(
                    script=script,
                    confidence=0.9,  # High confidence for exact intent match
                    match_type="intent",
                    intent_score=1.0
                )
                
                matches.append(match_result)
        
        # Sort by priority
        matches.sort(key=lambda m: m.script.priority, reverse=True)
        
        return matches
    
    def get_matching_statistics(self) -> Dict[str, Any]:
        """Get matching statistics."""
        avg_time = sum(self._match_times) / len(self._match_times) if self._match_times else 0
        cache_hit_rate = self._cache_hits / max(1, self._total_queries)
        
        return {
            "total_queries": self._total_queries,
            "cache_hits": self._cache_hits,
            "cache_hit_rate": cache_hit_rate,
            "average_match_time_ms": avg_time,
            "cache_size": len(self._match_cache),
            "keyword_index_size": len(self._keyword_index),
            "intent_index_size": len(self._intent_index),
            "loaded_scripts": len(self.scripts)
        }
    
    def clear_cache(self) -> None:
        """Clear matching cache."""
        self._match_cache.clear()
        self._log.info("Cleared matching cache")
    
    def update_config(self, **kwargs) -> None:
        """
        Update matcher configuration.
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                old_value = getattr(self.config, key)
                setattr(self.config, key, value)
                self._log.info(f"Updated config {key}: {old_value} -> {value}")
            else:
                self._log.warning(f"Unknown config parameter: {key}")
        
        # Clear cache after config update
        self.clear_cache()
    
    def explain_match(self, query: str, match_result: MatchResult) -> Dict[str, Any]:
        """
        Explain why a particular match was selected.
        
        Args:
            query: Original query
            match_result: Match result to explain
            
        Returns:
            Explanation dictionary
        """
        explanation = {
            "query": query,
            "matched_script_id": match_result.script.script_id,
            "confidence": match_result.confidence,
            "match_type": match_result.match_type,
            "scores": {
                "similarity": match_result.similarity_score,
                "keyword": match_result.keyword_score,
                "intent": match_result.intent_score
            },
            "matched_keywords": match_result.matched_keywords,
            "script_details": {
                "query": match_result.script.query,
                "response": match_result.script.response,
                "intent": match_result.script.intent,
                "priority": match_result.script.priority
            }
        }
        
        # Add reasoning
        reasons = []
        
        if match_result.similarity_score > 0.8:
            reasons.append("High text similarity")
        
        if match_result.matched_keywords:
            reasons.append(f"Matched keywords: {', '.join(match_result.matched_keywords)}")
        
        if match_result.intent_score > 0:
            reasons.append(f"Intent match: {match_result.script.intent}")
        
        if match_result.script.priority > 1:
            reasons.append(f"High priority script (priority: {match_result.script.priority})")
        
        explanation["reasons"] = reasons
        
        return explanation


# Utility functions for response matching

def create_response_matcher(
    fuzzy_threshold: float = 0.6,
    enable_fuzzy_matching: bool = True,
    enable_keyword_matching: bool = True,
    config_manager=None,
    **kwargs
) -> ResponseMatcher:
    """
    Create response matcher with configuration.
    
    Args:
        fuzzy_threshold: Minimum similarity threshold
        enable_fuzzy_matching: Enable fuzzy text matching
        enable_keyword_matching: Enable keyword matching
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured response matcher
    """
    config = ResponseMatcherConfig(
        fuzzy_threshold=fuzzy_threshold,
        enable_fuzzy_matching=enable_fuzzy_matching,
        enable_keyword_matching=enable_keyword_matching,
        **kwargs
    )
    
    return ResponseMatcher(config, config_manager)


def evaluate_matching_performance(
    matcher: ResponseMatcher,
    test_queries: List[str],
    expected_results: List[str]
) -> Dict[str, float]:
    """
    Evaluate matching performance with test data.
    
    Args:
        matcher: Response matcher instance
        test_queries: List of test queries
        expected_results: List of expected script IDs
        
    Returns:
        Performance metrics
    """
    if len(test_queries) != len(expected_results):
        raise ValueError("Test queries and expected results must have same length")
    
    correct_matches = 0
    total_queries = len(test_queries)
    match_times = []
    
    for query, expected_script_id in zip(test_queries, expected_results):
        start_time = datetime.now()
        
        # Find best match
        best_match = matcher.find_best_match(query)
        
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        match_times.append(processing_time)
        
        # Check if match is correct
        if best_match and best_match.script.script_id == expected_script_id:
            correct_matches += 1
    
    accuracy = correct_matches / total_queries if total_queries > 0 else 0
    avg_time = sum(match_times) / len(match_times) if match_times else 0
    
    return {
        "accuracy": accuracy,
        "correct_matches": correct_matches,
        "total_queries": total_queries,
        "average_match_time_ms": avg_time,
        "min_match_time_ms": min(match_times) if match_times else 0,
        "max_match_time_ms": max(match_times) if match_times else 0
    }


def analyze_query_patterns(queries: List[str]) -> Dict[str, Any]:
    """
    Analyze patterns in user queries for matcher optimization.
    
    Args:
        queries: List of user queries
        
    Returns:
        Analysis results
    """
    if not queries:
        return {"error": "No queries provided"}
    
    # Basic statistics
    total_queries = len(queries)
    avg_length = sum(len(q) for q in queries) / total_queries
    
    # Word frequency analysis
    word_freq = defaultdict(int)
    for query in queries:
        words = re.findall(r'\w+', query.lower())
        for word in words:
            word_freq[word] += 1
    
    # Most common words
    common_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
    
    # Query length distribution
    lengths = [len(q) for q in queries]
    
    return {
        "total_queries": total_queries,
        "average_length": avg_length,
        "min_length": min(lengths),
        "max_length": max(lengths),
        "common_words": common_words,
        "unique_words": len(word_freq),
        "vocabulary_diversity": len(word_freq) / sum(word_freq.values())
    }