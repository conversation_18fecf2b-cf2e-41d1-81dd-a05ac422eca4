"""
Example configuration for response processor.

This file shows how to configure the response processor for different
environments and use cases.
"""

from src.components.llm.response_processor import (
    ResponseProcessorConfig, ValidationRule, ResponseQuality,
    create_validation_rule, create_banking_safety_rules
)


# Development configuration
DEVELOPMENT_CONFIG = ResponseProcessorConfig(
    # Validation settings
    enable_validation=True,
    enable_safety_checks=True,
    enable_content_filtering=True,
    
    # Quality thresholds (more lenient for development)
    min_acceptable_quality=ResponseQuality.ACCEPTABLE,
    min_confidence_threshold=0.5,
    
    # Length limits
    min_response_length=3,
    max_response_length=1000,
    
    # Fallback settings
    enable_fallback=True,
    fallback_confidence=0.7,
    max_fallback_attempts=2,
    
    # Processing settings
    enable_text_normalization=True,
    enable_profanity_filter=True,
    enable_sensitive_info_filter=True,
    
    # Performance settings
    enable_caching=True,
    cache_size=100
)


# Production configuration
PRODUCTION_CONFIG = ResponseProcessorConfig(
    # Validation settings (strict for production)
    enable_validation=True,
    enable_safety_checks=True,
    enable_content_filtering=True,
    
    # Quality thresholds (strict for production)
    min_acceptable_quality=ResponseQuality.GOOD,
    min_confidence_threshold=0.7,
    
    # Length limits
    min_response_length=5,
    max_response_length=800,
    
    # Fallback settings
    enable_fallback=True,
    fallback_confidence=0.8,
    max_fallback_attempts=3,
    
    # Processing settings
    enable_text_normalization=True,
    enable_profanity_filter=True,
    enable_sensitive_info_filter=True,
    
    # Performance settings
    enable_caching=True,
    cache_size=500
)


# Testing configuration
TESTING_CONFIG = ResponseProcessorConfig(
    # Validation settings (minimal for testing)
    enable_validation=True,
    enable_safety_checks=False,  # Disabled for testing flexibility
    enable_content_filtering=False,
    
    # Quality thresholds (lenient for testing)
    min_acceptable_quality=ResponseQuality.POOR,
    min_confidence_threshold=0.1,
    
    # Length limits (very permissive)
    min_response_length=1,
    max_response_length=2000,
    
    # Fallback settings
    enable_fallback=True,
    fallback_confidence=0.5,
    max_fallback_attempts=1,
    
    # Processing settings (minimal processing)
    enable_text_normalization=False,
    enable_profanity_filter=False,
    enable_sensitive_info_filter=False,
    
    # Performance settings
    enable_caching=False,  # Disabled for testing
    cache_size=10
)


# High-security configuration (for sensitive environments)
HIGH_SECURITY_CONFIG = ResponseProcessorConfig(
    # Validation settings (maximum security)
    enable_validation=True,
    enable_safety_checks=True,
    enable_content_filtering=True,
    
    # Quality thresholds (very strict)
    min_acceptable_quality=ResponseQuality.EXCELLENT,
    min_confidence_threshold=0.8,
    
    # Length limits (conservative)
    min_response_length=10,
    max_response_length=500,
    
    # Fallback settings (always use fallback for safety)
    enable_fallback=True,
    fallback_confidence=0.9,
    max_fallback_attempts=5,
    
    # Processing settings (maximum filtering)
    enable_text_normalization=True,
    enable_profanity_filter=True,
    enable_sensitive_info_filter=True,
    
    # Performance settings
    enable_caching=True,
    cache_size=1000
)


def get_config_by_environment(environment: str = "development") -> ResponseProcessorConfig:
    """
    Get configuration by environment name.
    
    Args:
        environment: Environment name
        
    Returns:
        Appropriate configuration
    """
    configs = {
        "development": DEVELOPMENT_CONFIG,
        "production": PRODUCTION_CONFIG,
        "testing": TESTING_CONFIG,
        "high_security": HIGH_SECURITY_CONFIG
    }
    
    return configs.get(environment, DEVELOPMENT_CONFIG)


def create_banking_validation_rules():
    """Create banking-specific validation rules."""
    return [
        create_validation_rule(
            name="banking_language",
            description="Use professional banking language",
            required_elements=["贷款", "银行", "金融"],
            severity="warning"
        ),
        create_validation_rule(
            name="interest_rate_format",
            description="Interest rates should be properly formatted",
            pattern=r"\d+\.?\d*%",
            severity="warning"
        ),
        create_validation_rule(
            name="contact_information",
            description="Should provide contact information",
            keywords=["网点", "客服", "咨询"],
            severity="info"
        ),
        create_validation_rule(
            name="risk_disclosure",
            description="Include appropriate risk disclosure",
            keywords=["风险", "评估", "条件"],
            severity="warning"
        )
    ]


def create_customer_service_rules():
    """Create customer service specific rules."""
    return [
        create_validation_rule(
            name="polite_language",
            description="Use polite and respectful language",
            required_elements=["您", "请"],
            severity="warning"
        ),
        create_validation_rule(
            name="helpful_tone",
            description="Maintain helpful and supportive tone",
            keywords=["帮助", "为您", "建议", "可以"],
            severity="info"
        ),
        create_validation_rule(
            name="clear_communication",
            description="Communication should be clear and understandable",
            forbidden_elements=["可能", "大概", "应该是", "不太确定"],
            severity="warning"
        ),
        create_validation_rule(
            name="complete_response",
            description="Response should be complete and informative",
            min_length=20,
            pattern=r"[。！？]$",
            severity="warning"
        )
    ]


def create_safety_rules_for_banking():
    """Create comprehensive safety rules for banking environment."""
    # Start with default banking safety rules
    safety_rules = create_banking_safety_rules()
    
    # Add additional banking-specific safety rules
    additional_rules = [
        create_validation_rule(
            name="no_account_details",
            description="Never request or disclose account details",
            forbidden_elements=["账户余额", "交易记录", "密码", "PIN码"],
            severity="error"
        ),
        create_validation_rule(
            name="no_investment_advice",
            description="No specific investment advice without proper qualification",
            forbidden_elements=["建议购买", "推荐投资", "一定要买"],
            severity="error"
        ),
        create_validation_rule(
            name="regulatory_compliance",
            description="Ensure regulatory compliance",
            forbidden_elements=["绕过监管", "避税", "隐瞒收入"],
            severity="error"
        ),
        create_validation_rule(
            name="fraud_prevention",
            description="Prevent fraud-related content",
            forbidden_elements=["虚假材料", "伪造证明", "代办征信"],
            severity="error"
        )
    ]
    
    safety_rules.extend(additional_rules)
    return safety_rules


def create_custom_config(**kwargs) -> ResponseProcessorConfig:
    """
    Create custom configuration with overrides.
    
    Args:
        **kwargs: Configuration parameters to override
        
    Returns:
        Custom configuration
    """
    # Start with development config as base
    base_config = DEVELOPMENT_CONFIG
    
    # Create new config with overrides
    config_dict = {
        "enable_validation": kwargs.get("enable_validation", base_config.enable_validation),
        "enable_safety_checks": kwargs.get("enable_safety_checks", base_config.enable_safety_checks),
        "enable_content_filtering": kwargs.get("enable_content_filtering", base_config.enable_content_filtering),
        "min_acceptable_quality": kwargs.get("min_acceptable_quality", base_config.min_acceptable_quality),
        "min_confidence_threshold": kwargs.get("min_confidence_threshold", base_config.min_confidence_threshold),
        "min_response_length": kwargs.get("min_response_length", base_config.min_response_length),
        "max_response_length": kwargs.get("max_response_length", base_config.max_response_length),
        "enable_fallback": kwargs.get("enable_fallback", base_config.enable_fallback),
        "fallback_confidence": kwargs.get("fallback_confidence", base_config.fallback_confidence),
        "max_fallback_attempts": kwargs.get("max_fallback_attempts", base_config.max_fallback_attempts),
        "enable_text_normalization": kwargs.get("enable_text_normalization", base_config.enable_text_normalization),
        "enable_profanity_filter": kwargs.get("enable_profanity_filter", base_config.enable_profanity_filter),
        "enable_sensitive_info_filter": kwargs.get("enable_sensitive_info_filter", base_config.enable_sensitive_info_filter),
        "enable_caching": kwargs.get("enable_caching", base_config.enable_caching),
        "cache_size": kwargs.get("cache_size", base_config.cache_size)
    }
    
    return ResponseProcessorConfig(**config_dict)


# Example usage scenarios
def get_loan_consultation_config() -> ResponseProcessorConfig:
    """Configuration optimized for loan consultation scenarios."""
    return create_custom_config(
        min_acceptable_quality=ResponseQuality.GOOD,
        min_confidence_threshold=0.75,
        min_response_length=15,
        max_response_length=600,
        enable_safety_checks=True,
        fallback_confidence=0.85
    )


def get_quick_support_config() -> ResponseProcessorConfig:
    """Configuration optimized for quick support scenarios."""
    return create_custom_config(
        min_acceptable_quality=ResponseQuality.ACCEPTABLE,
        min_confidence_threshold=0.6,
        min_response_length=5,
        max_response_length=200,
        enable_content_filtering=True,
        fallback_confidence=0.7
    )


def get_complex_inquiry_config() -> ResponseProcessorConfig:
    """Configuration optimized for complex inquiry scenarios."""
    return create_custom_config(
        min_acceptable_quality=ResponseQuality.EXCELLENT,
        min_confidence_threshold=0.8,
        min_response_length=30,
        max_response_length=1000,
        enable_fallback=True,
        max_fallback_attempts=3,
        fallback_confidence=0.9
    )


# Example usage:
if __name__ == "__main__":
    # Get production config
    prod_config = get_config_by_environment("production")
    print(f"Production min confidence: {prod_config.min_confidence_threshold}")
    
    # Create custom config for loan consultation
    loan_config = get_loan_consultation_config()
    print(f"Loan consultation quality threshold: {loan_config.min_acceptable_quality.value}")
    
    # Create banking validation rules
    banking_rules = create_banking_validation_rules()
    print(f"Banking validation rules: {len(banking_rules)}")
    
    # Create safety rules
    safety_rules = create_safety_rules_for_banking()
    print(f"Banking safety rules: {len(safety_rules)}")