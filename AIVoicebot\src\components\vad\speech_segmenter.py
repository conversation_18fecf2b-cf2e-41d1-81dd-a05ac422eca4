"""
Speech Segmentation System

This module provides speech segmentation functionality including:
- SpeechSegmenter to identify speech boundaries
- Silence detection and speech endpoint determination
- Adaptive threshold adjustment based on background noise
"""

import numpy as np
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum
import logging
import time

logger = logging.getLogger(__name__)


class SegmentType(Enum):
    """Types of audio segments"""
    SILENCE = "silence"
    SPEECH = "speech"
    UNKNOWN = "unknown"


@dataclass
class SpeechSegment:
    """Represents a speech segment with timing and metadata"""
    start_time: float
    end_time: float
    segment_type: SegmentType
    confidence: float = 0.0
    audio_data: Optional[np.ndarray] = None
    
    @property
    def duration(self) -> float:
        """Get segment duration in seconds"""
        return self.end_time - self.start_time
    
    @property
    def start_sample(self) -> int:
        """Get start sample index (assuming 16kHz)"""
        return int(self.start_time * 16000)
    
    @property
    def end_sample(self) -> int:
        """Get end sample index (assuming 16kHz)"""
        return int(self.end_time * 16000)


@dataclass
class SegmentationConfig:
    """Configuration for speech segmentation"""
    sample_rate: int = 16000
    
    # VAD thresholds
    speech_threshold: float = 0.5
    silence_threshold: float = 0.3
    
    # Timing parameters (in seconds)
    min_speech_duration: float = 0.1
    min_silence_duration: float = 0.1
    max_speech_duration: float = 30.0
    
    # Adaptive threshold parameters
    adaptive_threshold: bool = True
    noise_floor_update_rate: float = 0.01
    threshold_margin: float = 0.1
    
    # Smoothing parameters
    smoothing_window: int = 5
    hysteresis_enabled: bool = True


class SpeechSegmenter:
    """
    Speech segmentation system for identifying speech boundaries
    
    Features:
    - Real-time speech/silence detection
    - Adaptive threshold adjustment
    - Segment boundary detection
    - Noise floor estimation
    """
    
    def __init__(self, config: SegmentationConfig):
        self.config = config
        
        # State tracking
        self.current_state = SegmentType.SILENCE
        self.state_start_time = 0.0
        self.last_process_time = 0.0
        
        # Adaptive threshold tracking
        self.noise_floor = 0.0
        self.adaptive_speech_threshold = config.speech_threshold
        self.adaptive_silence_threshold = config.silence_threshold
        
        # Smoothing buffer
        self.vad_history = []
        
        # Segment tracking
        self.current_segments: List[SpeechSegment] = []
        self.completed_segments: List[SpeechSegment] = []
        
        logger.info(f"SpeechSegmenter initialized with config: {config}")
    
    def process_vad_results(self, vad_probabilities: np.ndarray, 
                           timestamps: np.ndarray) -> List[SpeechSegment]:
        """
        Process VAD results to generate speech segments
        
        Args:
            vad_probabilities: Array of VAD probabilities [0-1]
            timestamps: Corresponding timestamps in seconds
            
        Returns:
            List of completed speech segments
        """
        if len(vad_probabilities) == 0:
            return []
        
        # Update adaptive thresholds
        if self.config.adaptive_threshold:
            self._update_adaptive_thresholds(vad_probabilities)
        
        # Apply smoothing
        smoothed_probs = self._apply_smoothing(vad_probabilities)
        
        # Process each frame
        new_segments = []
        for prob, timestamp in zip(smoothed_probs, timestamps):
            segment = self._process_frame(prob, timestamp)
            if segment:
                new_segments.append(segment)
        
        self.last_process_time = timestamps[-1] if len(timestamps) > 0 else self.last_process_time
        
        return new_segments
    
    def _update_adaptive_thresholds(self, vad_probabilities: np.ndarray):
        """Update adaptive thresholds based on recent audio"""
        # Estimate noise floor from low VAD probability frames
        low_prob_frames = vad_probabilities[vad_probabilities < 0.3]
        if len(low_prob_frames) > 0:
            current_noise = np.mean(low_prob_frames)
            
            # Update noise floor with exponential moving average
            alpha = self.config.noise_floor_update_rate
            self.noise_floor = alpha * current_noise + (1 - alpha) * self.noise_floor
            
            # Adjust thresholds based on noise floor
            margin = self.config.threshold_margin
            self.adaptive_speech_threshold = max(
                self.config.speech_threshold,
                self.noise_floor + margin
            )
            self.adaptive_silence_threshold = max(
                self.config.silence_threshold,
                self.noise_floor + margin * 0.5
            )
    
    def _apply_smoothing(self, vad_probabilities: np.ndarray) -> np.ndarray:
        """Apply temporal smoothing to VAD probabilities"""
        if self.config.smoothing_window <= 1:
            return vad_probabilities
        
        # Add to history
        self.vad_history.extend(vad_probabilities.tolist())
        
        # Keep only recent history
        max_history = self.config.smoothing_window * 10
        if len(self.vad_history) > max_history:
            self.vad_history = self.vad_history[-max_history:]
        
        # Apply moving average smoothing
        smoothed = []
        window = self.config.smoothing_window
        
        for i, prob in enumerate(vad_probabilities):
            # Get window around current position
            history_idx = len(self.vad_history) - len(vad_probabilities) + i
            start_idx = max(0, history_idx - window // 2)
            end_idx = min(len(self.vad_history), history_idx + window // 2 + 1)
            
            window_probs = self.vad_history[start_idx:end_idx]
            smoothed_prob = np.mean(window_probs) if window_probs else prob
            smoothed.append(smoothed_prob)
        
        return np.array(smoothed)
    
    def _process_frame(self, vad_probability: float, timestamp: float) -> Optional[SpeechSegment]:
        """Process a single VAD frame and update state"""
        # Determine current frame state
        if self.config.hysteresis_enabled:
            # Use hysteresis to avoid rapid state changes
            if self.current_state == SegmentType.SILENCE:
                frame_state = SegmentType.SPEECH if vad_probability > self.adaptive_speech_threshold else SegmentType.SILENCE
            else:  # current_state == SegmentType.SPEECH
                frame_state = SegmentType.SILENCE if vad_probability < self.adaptive_silence_threshold else SegmentType.SPEECH
        else:
            # Simple thresholding
            frame_state = SegmentType.SPEECH if vad_probability > self.adaptive_speech_threshold else SegmentType.SILENCE
        
        # Check for state change
        if frame_state != self.current_state:
            return self._handle_state_change(frame_state, timestamp, vad_probability)
        
        # Check for maximum speech duration
        if (self.current_state == SegmentType.SPEECH and 
            timestamp - self.state_start_time > self.config.max_speech_duration):
            return self._force_segment_end(timestamp, vad_probability)
        
        return None
    
    def _handle_state_change(self, new_state: SegmentType, 
                           timestamp: float, confidence: float) -> Optional[SpeechSegment]:
        """Handle state transition and create segments"""
        segment_duration = timestamp - self.state_start_time
        
        # Check minimum duration requirements
        if self.current_state == SegmentType.SPEECH:
            if segment_duration >= self.config.min_speech_duration:
                # Create completed speech segment
                segment = SpeechSegment(
                    start_time=self.state_start_time,
                    end_time=timestamp,
                    segment_type=self.current_state,
                    confidence=confidence
                )
                self.completed_segments.append(segment)
                
                # Update state
                self.current_state = new_state
                self.state_start_time = timestamp
                
                return segment
        
        elif self.current_state == SegmentType.SILENCE:
            if segment_duration >= self.config.min_silence_duration:
                # Update state (don't return silence segments typically)
                self.current_state = new_state
                self.state_start_time = timestamp
        
        return None
    
    def _force_segment_end(self, timestamp: float, confidence: float) -> SpeechSegment:
        """Force end of current segment (e.g., max duration reached)"""
        segment = SpeechSegment(
            start_time=self.state_start_time,
            end_time=timestamp,
            segment_type=self.current_state,
            confidence=confidence
        )
        self.completed_segments.append(segment)
        
        # Start new silence segment
        self.current_state = SegmentType.SILENCE
        self.state_start_time = timestamp
        
        return segment
    
    def finalize_current_segment(self, end_time: Optional[float] = None) -> Optional[SpeechSegment]:
        """Finalize the current segment (e.g., at end of stream)"""
        if end_time is None:
            end_time = self.last_process_time
        
        if self.current_state == SegmentType.SPEECH:
            segment_duration = end_time - self.state_start_time
            if segment_duration >= self.config.min_speech_duration:
                segment = SpeechSegment(
                    start_time=self.state_start_time,
                    end_time=end_time,
                    segment_type=self.current_state,
                    confidence=0.5  # Default confidence for finalized segment
                )
                self.completed_segments.append(segment)
                return segment
        
        return None
    
    def get_all_segments(self) -> List[SpeechSegment]:
        """Get all completed segments"""
        return self.completed_segments.copy()
    
    def get_recent_segments(self, count: int = 10) -> List[SpeechSegment]:
        """Get most recent completed segments"""
        return self.completed_segments[-count:] if self.completed_segments else []
    
    def clear_segments(self):
        """Clear all stored segments"""
        self.completed_segments.clear()
    
    def get_segmentation_stats(self) -> Dict[str, Any]:
        """Get segmentation statistics"""
        if not self.completed_segments:
            return {
                "total_segments": 0,
                "total_speech_time": 0.0,
                "average_segment_duration": 0.0,
                "speech_ratio": 0.0
            }
        
        speech_segments = [s for s in self.completed_segments if s.segment_type == SegmentType.SPEECH]
        total_speech_time = sum(s.duration for s in speech_segments)
        total_time = self.last_process_time - (self.completed_segments[0].start_time if self.completed_segments else 0)
        
        return {
            "total_segments": len(self.completed_segments),
            "speech_segments": len(speech_segments),
            "total_speech_time": total_speech_time,
            "total_time": total_time,
            "average_segment_duration": total_speech_time / len(speech_segments) if speech_segments else 0.0,
            "speech_ratio": total_speech_time / total_time if total_time > 0 else 0.0,
            "current_state": self.current_state.value,
            "adaptive_speech_threshold": self.adaptive_speech_threshold,
            "adaptive_silence_threshold": self.adaptive_silence_threshold,
            "noise_floor": self.noise_floor
        }


def create_speech_segmenter(sample_rate: int = 16000,
                          speech_threshold: float = 0.5,
                          min_speech_duration: float = 0.1,
                          adaptive_threshold: bool = True) -> SpeechSegmenter:
    """Create speech segmenter with common configuration"""
    config = SegmentationConfig(
        sample_rate=sample_rate,
        speech_threshold=speech_threshold,
        min_speech_duration=min_speech_duration,
        adaptive_threshold=adaptive_threshold
    )
    return SpeechSegmenter(config)


def segment_audio_with_vad(audio_data: np.ndarray,
                          vad_probabilities: np.ndarray,
                          sample_rate: int = 16000,
                          **kwargs) -> List[SpeechSegment]:
    """
    Convenience function to segment audio with VAD probabilities
    
    Args:
        audio_data: Audio data array
        vad_probabilities: VAD probability array
        sample_rate: Audio sample rate
        **kwargs: Additional segmentation config parameters
        
    Returns:
        List of speech segments
    """
    # Create timestamps
    timestamps = np.arange(len(vad_probabilities)) / sample_rate
    
    # Create segmenter
    segmenter = create_speech_segmenter(sample_rate=sample_rate, **kwargs)
    
    # Process VAD results
    segments = segmenter.process_vad_results(vad_probabilities, timestamps)
    
    # Finalize any remaining segment
    final_segment = segmenter.finalize_current_segment()
    if final_segment:
        segments.append(final_segment)
    
    # Add audio data to segments
    for segment in segments:
        if segment.segment_type == SegmentType.SPEECH:
            start_idx = segment.start_sample
            end_idx = min(segment.end_sample, len(audio_data))
            segment.audio_data = audio_data[start_idx:end_idx]
    
    return segments