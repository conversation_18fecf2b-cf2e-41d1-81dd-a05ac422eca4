"""
Prompt management system for context-aware LLM interactions.

This module provides prompt generation, conversation history integration,
and script context injection for enhanced LLM responses.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import re
from pathlib import Path

from ...core.base_component import BaseComponent
from ...core.interfaces import AudioProcessingError
from ..scripts.script_parser import ConversationScript


class PromptType(Enum):
    """Types of prompts for different scenarios."""
    CUSTOMER_SERVICE = "customer_service"
    SCRIPT_BASED = "script_based"
    FALLBACK = "fallback"
    GREETING = "greeting"
    CLOSING = "closing"


@dataclass
class ConversationTurn:
    """Represents a single conversation turn."""
    user_input: str
    assistant_response: str
    timestamp: datetime = field(default_factory=datetime.now)
    confidence: float = 1.0
    source: str = "unknown"  # "script", "llm", "fallback"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "user": self.user_input,
            "assistant": self.assistant_response,
            "timestamp": self.timestamp.isoformat(),
            "confidence": self.confidence,
            "source": self.source
        }


@dataclass
class PromptContext:
    """Context information for prompt generation."""
    # User information
    user_query: str
    user_intent: Optional[str] = None
    user_emotion: Optional[str] = None
    
    # Conversation context
    conversation_history: List[ConversationTurn] = field(default_factory=list)
    session_id: Optional[str] = None
    
    # Script context
    matched_scripts: List[ConversationScript] = field(default_factory=list)
    script_confidence: float = 0.0
    
    # Business context
    customer_info: Dict[str, Any] = field(default_factory=dict)
    product_context: Dict[str, Any] = field(default_factory=dict)
    
    # System context
    current_time: datetime = field(default_factory=datetime.now)
    system_state: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PromptTemplate:
    """Template for generating prompts."""
    name: str
    prompt_type: PromptType
    template: str
    
    # Template metadata
    description: str = ""
    version: str = "1.0"
    author: str = ""
    
    # Template parameters
    required_variables: List[str] = field(default_factory=list)
    optional_variables: List[str] = field(default_factory=list)
    
    # Template settings
    max_history_turns: int = 5
    include_timestamps: bool = False
    include_confidence: bool = False
    
    def validate_variables(self, variables: Dict[str, Any]) -> bool:
        """Validate that all required variables are present."""
        missing = [var for var in self.required_variables if var not in variables]
        if missing:
            raise ValueError(f"Missing required variables: {missing}")
        return True


@dataclass
class PromptManagerConfig:
    """Configuration for prompt manager."""
    # Template settings
    template_directory: str = "prompts"
    default_template_type: PromptType = PromptType.CUSTOMER_SERVICE
    
    # History management
    max_conversation_history: int = 10
    history_compression_threshold: int = 20
    
    # Context settings
    include_script_context: bool = True
    include_customer_context: bool = True
    include_system_context: bool = True
    
    # Prompt optimization
    max_prompt_length: int = 6000
    enable_prompt_compression: bool = True
    
    # Template caching
    enable_template_caching: bool = True
    cache_size: int = 100


class PromptManager(BaseComponent):
    """
    Prompt management system for context-aware LLM interactions.
    
    Generates context-aware prompts by combining conversation history,
    script context, and business information for enhanced LLM responses.
    """
    
    def __init__(self, config: PromptManagerConfig, config_manager, logger=None):
        """
        Initialize prompt manager.
        
        Args:
            config: Prompt manager configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("prompt_manager", config_manager, logger)
        
        self.config = config
        
        # Template storage
        self.templates: Dict[str, PromptTemplate] = {}
        self.default_templates: Dict[PromptType, PromptTemplate] = {}
        
        # Prompt cache
        self._prompt_cache: Dict[str, str] = {}
        
        # Statistics
        self._total_prompts_generated = 0
        self._cache_hits = 0
        self._template_usage: Dict[str, int] = {}
        
        # Load default templates
        self._load_default_templates()

    async def _initialize_impl(self) -> None:
        """Initialize prompt manager."""
        self._log.info("Initializing prompt manager...")
        
        # Load templates from directory if it exists
        template_path = Path(self.config.template_directory)
        if template_path.exists():
            await self._load_templates_from_directory(template_path)
        
        self._log.info(f"Prompt manager initialized with {len(self.templates)} templates")
    
    async def _start_impl(self) -> None:
        """Start prompt manager."""
        self._log.info("Starting prompt manager...")
        
        # Reset statistics
        self._total_prompts_generated = 0
        self._cache_hits = 0
        self._template_usage.clear()
        
        self._log.info("Prompt manager started")
    
    async def _stop_impl(self) -> None:
        """Stop prompt manager."""
        self._log.info("Stopping prompt manager...")
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("Prompt manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup prompt manager resources."""
        self._log.info("Cleaning up prompt manager...")
        
        # Clear caches and templates
        self._prompt_cache.clear()
        self.templates.clear()
        self.default_templates.clear()
        
        self._log.info("Prompt manager cleanup completed")
    
    def _load_default_templates(self) -> None:
        """Load default prompt templates."""
        # Customer service template
        customer_service_template = PromptTemplate(
            name="default_customer_service",
            prompt_type=PromptType.CUSTOMER_SERVICE,
            template="""你是一个专业的银行客服助手，负责为客户提供贷款咨询服务。

## 角色定位
- 专业、友好、耐心的客服代表
- 熟悉各种贷款产品和政策
- 能够准确理解客户需求并提供合适的建议

## 服务原则
1. 始终保持礼貌和专业态度
2. 准确理解客户问题，提供精确答案
3. 如果不确定，诚实告知并寻求帮助
4. 保护客户隐私和信息安全

{script_context}

{conversation_history}

## 当前客户咨询
客户问题：{user_query}

请根据以上信息，为客户提供专业、准确的回复。回复应该：
- 直接回答客户问题
- 语言简洁明了
- 态度友好专业
- 如有需要，主动提供相关建议""",
            description="Default customer service prompt template",
            required_variables=["user_query"],
            optional_variables=["script_context", "conversation_history", "customer_info"],
            max_history_turns=5
        )
        
        # Script-based template
        script_based_template = PromptTemplate(
            name="script_based_response",
            prompt_type=PromptType.SCRIPT_BASED,
            template="""你是一个银行客服助手。请基于以下脚本内容回答客户问题。

## 匹配的脚本内容
{matched_scripts}

{customer_info}

## 对话历史
{conversation_history}

## 客户问题
{user_query}

请基于匹配的脚本内容，结合对话历史，为客户提供准确的回复。
- 优先使用脚本中的标准回复
- 可以适当调整语言使其更自然
- 确保信息准确性
- 保持专业友好的语调""",
            description="Script-based response template",
            required_variables=["user_query", "matched_scripts"],
            optional_variables=["conversation_history", "customer_info"],
            max_history_turns=3
        )
        
        # Fallback template
        fallback_template = PromptTemplate(
            name="fallback_response",
            prompt_type=PromptType.FALLBACK,
            template="""你是一个银行客服助手。客户的问题可能超出了标准脚本范围，请提供合适的回复。

{conversation_history}

客户问题：{user_query}

请提供专业的回复：
- 如果问题在你的知识范围内，请直接回答
- 如果不确定，请礼貌地表示需要进一步确认
- 可以建议客户联系专门的业务人员
- 始终保持友好和专业的态度""",
            description="Fallback response when no scripts match",
            required_variables=["user_query"],
            optional_variables=["conversation_history"],
            max_history_turns=3
        )
        
        # Store default templates
        self.default_templates[PromptType.CUSTOMER_SERVICE] = customer_service_template
        self.default_templates[PromptType.SCRIPT_BASED] = script_based_template
        self.default_templates[PromptType.FALLBACK] = fallback_template
        
        # Add to main template storage
        for template in self.default_templates.values():
            self.templates[template.name] = template
    
    async def _load_templates_from_directory(self, template_path: Path) -> None:
        """Load templates from directory."""
        try:
            for template_file in template_path.glob("*.json"):
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                    template = self._create_template_from_dict(template_data)
                    self.templates[template.name] = template
                    
                    self._log.debug(f"Loaded template: {template.name}")
        
        except Exception as e:
            self._log.error(f"Error loading templates from {template_path}: {e}")
    
    def _create_template_from_dict(self, data: Dict[str, Any]) -> PromptTemplate:
        """Create template from dictionary data."""
        return PromptTemplate(
            name=data["name"],
            prompt_type=PromptType(data["prompt_type"]),
            template=data["template"],
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            author=data.get("author", ""),
            required_variables=data.get("required_variables", []),
            optional_variables=data.get("optional_variables", []),
            max_history_turns=data.get("max_history_turns", 5),
            include_timestamps=data.get("include_timestamps", False),
            include_confidence=data.get("include_confidence", False)
        )
    
    def generate_prompt(
        self,
        context: PromptContext,
        template_name: Optional[str] = None,
        prompt_type: Optional[PromptType] = None
    ) -> str:
        """
        Generate context-aware prompt.
        
        Args:
            context: Prompt context information
            template_name: Specific template name to use
            prompt_type: Type of prompt to generate
            
        Returns:
            Generated prompt string
        """
        try:
            # Select template
            template = self._select_template(template_name, prompt_type, context)
            
            # Generate cache key
            cache_key = self._generate_cache_key(context, template.name)
            
            # Check cache
            if self.config.enable_template_caching and cache_key in self._prompt_cache:
                self._cache_hits += 1
                self._total_prompts_generated += 1
                return self._prompt_cache[cache_key]
            
            # Generate prompt
            prompt = self._generate_prompt_from_template(template, context)
            
            # Cache result
            if self.config.enable_template_caching:
                if len(self._prompt_cache) >= self.config.cache_size:
                    # Remove oldest entry
                    oldest_key = next(iter(self._prompt_cache))
                    del self._prompt_cache[oldest_key]
                
                self._prompt_cache[cache_key] = prompt
            
            # Update statistics
            self._total_prompts_generated += 1
            self._template_usage[template.name] = self._template_usage.get(template.name, 0) + 1
            
            return prompt
            
        except Exception as e:
            self._log.error(f"Error generating prompt: {e}")
            return self._generate_fallback_prompt(context)
    
    def _select_template(
        self,
        template_name: Optional[str],
        prompt_type: Optional[PromptType],
        context: PromptContext
    ) -> PromptTemplate:
        """Select appropriate template based on parameters and context."""
        # Use specific template if provided
        if template_name and template_name in self.templates:
            return self.templates[template_name]
        
        # Use template by type
        if prompt_type and prompt_type in self.default_templates:
            return self.default_templates[prompt_type]
        
        # Auto-select based on context
        if context.matched_scripts and context.script_confidence > 0.7:
            return self.default_templates[PromptType.SCRIPT_BASED]
        
        # Default to customer service template
        return self.default_templates.get(
            self.config.default_template_type,
            self.default_templates[PromptType.CUSTOMER_SERVICE]
        )
    
    def _generate_prompt_from_template(
        self,
        template: PromptTemplate,
        context: PromptContext
    ) -> str:
        """Generate prompt from template and context."""
        # Prepare template variables
        variables = self._prepare_template_variables(template, context)
        
        # Validate required variables
        template.validate_variables(variables)
        
        # Format template
        try:
            prompt = template.template.format(**variables)
            
            # Apply length limits
            if len(prompt) > self.config.max_prompt_length:
                prompt = self._compress_prompt(prompt, template, context)
            
            return prompt
            
        except KeyError as e:
            raise ValueError(f"Missing template variable: {e}")
    
    def _prepare_template_variables(
        self,
        template: PromptTemplate,
        context: PromptContext
    ) -> Dict[str, str]:
        """Prepare variables for template formatting."""
        variables = {
            "user_query": context.user_query,
            "current_time": context.current_time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Add conversation history
        if context.conversation_history:
            history_text = self._format_conversation_history(
                context.conversation_history,
                template.max_history_turns,
                template.include_timestamps,
                template.include_confidence
            )
            variables["conversation_history"] = history_text
        else:
            variables["conversation_history"] = "## 对话历史\n暂无历史对话记录。"
        
        # Add script context
        if context.matched_scripts and self.config.include_script_context:
            script_text = self._format_script_context(context.matched_scripts)
            variables["matched_scripts"] = script_text
            variables["script_context"] = f"## 相关脚本内容\n{script_text}"
        else:
            variables["matched_scripts"] = "暂无匹配的脚本内容。"
            variables["script_context"] = ""
        
        # Add customer context
        if context.customer_info and self.config.include_customer_context:
            customer_text = self._format_customer_context(context.customer_info)
            variables["customer_info"] = customer_text
        else:
            variables["customer_info"] = ""
        
        # Add user intent and emotion
        variables["user_intent"] = context.user_intent or "未识别"
        variables["user_emotion"] = context.user_emotion or "中性"
        
        # Add system context
        if context.system_state and self.config.include_system_context:
            system_text = self._format_system_context(context.system_state)
            variables["system_context"] = system_text
        else:
            variables["system_context"] = ""
        
        return variables
    
    def _format_conversation_history(
        self,
        history: List[ConversationTurn],
        max_turns: int,
        include_timestamps: bool,
        include_confidence: bool
    ) -> str:
        """Format conversation history for prompt."""
        if not history:
            return "## 对话历史\n暂无历史对话记录。"
        
        # Limit history length
        recent_history = history[-max_turns:] if max_turns > 0 else history
        
        formatted_turns = []
        for i, turn in enumerate(recent_history, 1):
            turn_text = f"**第{i}轮对话：**\n"
            turn_text += f"客户：{turn.user_input}\n"
            turn_text += f"助手：{turn.assistant_response}"
            
            if include_timestamps:
                turn_text += f"\n时间：{turn.timestamp.strftime('%H:%M:%S')}"
            
            if include_confidence and turn.confidence < 1.0:
                turn_text += f"\n置信度：{turn.confidence:.2f}"
            
            formatted_turns.append(turn_text)
        
        return "## 对话历史\n" + "\n\n".join(formatted_turns)
    
    def _format_script_context(self, scripts: List[ConversationScript]) -> str:
        """Format script context for prompt."""
        if not scripts:
            return "暂无匹配的脚本内容。"
        
        formatted_scripts = []
        for i, script in enumerate(scripts[:3], 1):  # Limit to top 3 scripts
            script_text = f"**脚本{i}：**\n"
            script_text += f"问题：{script.query}\n"
            script_text += f"回复：{script.response}"

            if script.intent:
                script_text += f"\n分类：{script.intent}"
            
            if script.priority > 1:
                script_text += f"\n优先级：{script.priority}"
            
            formatted_scripts.append(script_text)
        
        return "\n\n".join(formatted_scripts)
    
    def _format_customer_context(self, customer_info: Dict[str, Any]) -> str:
        """Format customer context for prompt."""
        if not customer_info:
            return ""
        
        context_parts = []
        
        # Customer basic info
        if "name" in customer_info:
            context_parts.append(f"客户姓名：{customer_info['name']}")
        
        if "customer_level" in customer_info:
            context_parts.append(f"客户等级：{customer_info['customer_level']}")
        
        if "previous_products" in customer_info:
            products = ", ".join(customer_info['previous_products'])
            context_parts.append(f"已有产品：{products}")
        
        # Customer preferences
        if "preferences" in customer_info:
            prefs = ", ".join(customer_info['preferences'])
            context_parts.append(f"客户偏好：{prefs}")
        
        if context_parts:
            return "## 客户信息\n" + "\n".join(context_parts)
        
        return ""
    
    def _format_system_context(self, system_state: Dict[str, Any]) -> str:
        """Format system context for prompt."""
        if not system_state:
            return ""
        
        context_parts = []
        
        if "current_promotion" in system_state:
            context_parts.append(f"当前活动：{system_state['current_promotion']}")
        
        if "system_status" in system_state:
            context_parts.append(f"系统状态：{system_state['system_status']}")
        
        if "available_services" in system_state:
            services = ", ".join(system_state['available_services'])
            context_parts.append(f"可用服务：{services}")
        
        if context_parts:
            return "## 系统信息\n" + "\n".join(context_parts)
        
        return ""
    
    def _compress_prompt(
        self,
        prompt: str,
        template: PromptTemplate,
        context: PromptContext
    ) -> str:
        """Compress prompt to fit length limits."""
        if not self.config.enable_prompt_compression:
            return prompt[:self.config.max_prompt_length]
        
        # Try to compress conversation history first
        if len(context.conversation_history) > 2:
            # Reduce history and regenerate
            compressed_context = PromptContext(
                user_query=context.user_query,
                user_intent=context.user_intent,
                conversation_history=context.conversation_history[-2:],  # Keep only last 2 turns
                matched_scripts=context.matched_scripts,
                script_confidence=context.script_confidence,
                customer_info=context.customer_info,
                current_time=context.current_time
            )
            
            compressed_prompt = self._generate_prompt_from_template(template, compressed_context)
            
            if len(compressed_prompt) <= self.config.max_prompt_length:
                return compressed_prompt
        
        # If still too long, truncate
        return prompt[:self.config.max_prompt_length]
    
    def _generate_cache_key(self, context: PromptContext, template_name: str) -> str:
        """Generate cache key for prompt."""
        # Create a simple hash-like key based on context
        key_parts = [
            template_name,
            context.user_query,
            str(len(context.conversation_history)),
            str(len(context.matched_scripts)),
            str(context.script_confidence)
        ]
        
        return "|".join(key_parts)
    
    def _generate_fallback_prompt(self, context: PromptContext) -> str:
        """Generate fallback prompt when template generation fails."""
        return f"""你是一个专业的银行客服助手。

客户问题：{context.user_query}

请为客户提供专业、友好的回复。如果不确定答案，请礼貌地表示需要进一步确认。""" 
   
    def add_template(self, template: PromptTemplate) -> None:
        """Add custom template to manager."""
        self.templates[template.name] = template
        self._log.info(f"Added template: {template.name}")
    
    def remove_template(self, template_name: str) -> bool:
        """Remove template from manager."""
        if template_name in self.templates:
            del self.templates[template_name]
            self._log.info(f"Removed template: {template_name}")
            return True
        return False
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get template by name."""
        return self.templates.get(template_name)
    
    def list_templates(self) -> List[str]:
        """List all available template names."""
        return list(self.templates.keys())
    
    def get_templates_by_type(self, prompt_type: PromptType) -> List[PromptTemplate]:
        """Get all templates of specific type."""
        return [
            template for template in self.templates.values()
            if template.prompt_type == prompt_type
        ]
    
    def create_conversation_context(
        self,
        user_query: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        matched_scripts: Optional[List[ConversationScript]] = None,
        **kwargs
    ) -> PromptContext:
        """
        Create prompt context from basic parameters.
        
        Args:
            user_query: Current user query
            conversation_history: Previous conversation turns
            matched_scripts: Matched conversation scripts
            **kwargs: Additional context parameters
            
        Returns:
            Prompt context object
        """
        # Convert history format
        turns = []
        if conversation_history:
            for turn in conversation_history:
                if "user" in turn and "assistant" in turn:
                    turns.append(ConversationTurn(
                        user_input=turn["user"],
                        assistant_response=turn["assistant"],
                        confidence=turn.get("confidence", 1.0),
                        source=turn.get("source", "unknown")
                    ))
        
        return PromptContext(
            user_query=user_query,
            user_intent=kwargs.get("user_intent"),
            user_emotion=kwargs.get("user_emotion"),
            conversation_history=turns,
            session_id=kwargs.get("session_id"),
            matched_scripts=matched_scripts or [],
            script_confidence=kwargs.get("script_confidence", 0.0),
            customer_info=kwargs.get("customer_info", {}),
            product_context=kwargs.get("product_context", {}),
            system_state=kwargs.get("system_state", {})
        )
    
    def generate_system_prompt(
        self,
        role_description: str = "专业的银行客服助手",
        service_principles: Optional[List[str]] = None,
        additional_context: Optional[str] = None
    ) -> str:
        """
        Generate system prompt for LLM initialization.
        
        Args:
            role_description: Description of the assistant's role
            service_principles: List of service principles
            additional_context: Additional context information
            
        Returns:
            System prompt string
        """
        prompt_parts = [f"你是一个{role_description}。"]
        
        if service_principles:
            prompt_parts.append("\n## 服务原则")
            for i, principle in enumerate(service_principles, 1):
                prompt_parts.append(f"{i}. {principle}")
        
        if additional_context:
            prompt_parts.append(f"\n## 附加信息\n{additional_context}")
        
        prompt_parts.append("\n请始终保持专业、友好的态度，为客户提供准确的帮助。")
        
        return "\n".join(prompt_parts)
    
    def validate_prompt_length(self, prompt: str) -> bool:
        """Validate prompt length against limits."""
        return len(prompt) <= self.config.max_prompt_length
    
    def get_prompt_statistics(self) -> Dict[str, Any]:
        """Get prompt generation statistics."""
        cache_hit_rate = self._cache_hits / max(1, self._total_prompts_generated)
        
        return {
            "total_prompts_generated": self._total_prompts_generated,
            "cache_hits": self._cache_hits,
            "cache_hit_rate": cache_hit_rate,
            "cache_size": len(self._prompt_cache),
            "available_templates": len(self.templates),
            "template_usage": dict(self._template_usage),
            "most_used_template": max(self._template_usage.items(), key=lambda x: x[1])[0] if self._template_usage else None
        }
    
    def clear_cache(self) -> None:
        """Clear prompt cache."""
        self._prompt_cache.clear()
        self._log.info("Prompt cache cleared")
    
    def reset_statistics(self) -> None:
        """Reset prompt statistics."""
        self._total_prompts_generated = 0
        self._cache_hits = 0
        self._template_usage.clear()
        self._log.info("Prompt statistics reset")
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_prompt_statistics()
        
        self._log.info(f"Prompt manager statistics:")
        self._log.info(f"  Total prompts generated: {stats['total_prompts_generated']}")
        self._log.info(f"  Cache hit rate: {stats['cache_hit_rate']:.2%}")
        self._log.info(f"  Available templates: {stats['available_templates']}")
        if stats['most_used_template']:
            self._log.info(f"  Most used template: {stats['most_used_template']}")
    
    def export_template(self, template_name: str, file_path: str) -> bool:
        """Export template to JSON file."""
        if template_name not in self.templates:
            return False
        
        template = self.templates[template_name]
        template_data = {
            "name": template.name,
            "prompt_type": template.prompt_type.value,
            "template": template.template,
            "description": template.description,
            "version": template.version,
            "author": template.author,
            "required_variables": template.required_variables,
            "optional_variables": template.optional_variables,
            "max_history_turns": template.max_history_turns,
            "include_timestamps": template.include_timestamps,
            "include_confidence": template.include_confidence
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
            
            self._log.info(f"Exported template {template_name} to {file_path}")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to export template {template_name}: {e}")
            return False
    
    def import_template(self, file_path: str) -> bool:
        """Import template from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            template = self._create_template_from_dict(template_data)
            self.add_template(template)
            
            self._log.info(f"Imported template {template.name} from {file_path}")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to import template from {file_path}: {e}")
            return False


# Utility functions for prompt management

async def create_prompt_manager(
    template_directory: str = "prompts",
    max_conversation_history: int = 10,
    max_prompt_length: int = 6000,
    config_manager=None,
    **kwargs
) -> PromptManager:
    """
    Create and initialize prompt manager.
    
    Args:
        template_directory: Directory containing prompt templates
        max_conversation_history: Maximum conversation turns to keep
        max_prompt_length: Maximum prompt length in characters
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized prompt manager
    """
    config = PromptManagerConfig(
        template_directory=template_directory,
        max_conversation_history=max_conversation_history,
        max_prompt_length=max_prompt_length,
        **kwargs
    )
    
    manager = PromptManager(config, config_manager)
    await manager.initialize()
    await manager.start()
    
    return manager


def create_customer_service_context(
    user_query: str,
    customer_name: Optional[str] = None,
    customer_level: Optional[str] = None,
    conversation_history: Optional[List[Dict[str, str]]] = None,
    matched_scripts: Optional[List[ConversationScript]] = None,
    current_promotion: Optional[str] = None
) -> PromptContext:
    """
    Create context specifically for customer service scenarios.
    
    Args:
        user_query: Customer query
        customer_name: Customer name
        customer_level: Customer service level
        conversation_history: Previous conversation
        matched_scripts: Matched scripts
        current_promotion: Current promotional information
        
    Returns:
        Customer service prompt context
    """
    customer_info = {}
    if customer_name:
        customer_info["name"] = customer_name
    if customer_level:
        customer_info["customer_level"] = customer_level
    
    system_state = {}
    if current_promotion:
        system_state["current_promotion"] = current_promotion
    
    # Convert history
    turns = []
    if conversation_history:
        for turn in conversation_history:
            if "user" in turn and "assistant" in turn:
                turns.append(ConversationTurn(
                    user_input=turn["user"],
                    assistant_response=turn["assistant"],
                    source=turn.get("source", "script")
                ))
    
    return PromptContext(
        user_query=user_query,
        conversation_history=turns,
        matched_scripts=matched_scripts or [],
        customer_info=customer_info,
        system_state=system_state
    )


def create_simple_template(
    name: str,
    template_text: str,
    prompt_type: PromptType = PromptType.CUSTOMER_SERVICE,
    required_vars: Optional[List[str]] = None
) -> PromptTemplate:
    """
    Create simple prompt template.
    
    Args:
        name: Template name
        template_text: Template content
        prompt_type: Type of prompt
        required_vars: Required variables
        
    Returns:
        Prompt template
    """
    return PromptTemplate(
        name=name,
        prompt_type=prompt_type,
        template=template_text,
        required_variables=required_vars or ["user_query"],
        description=f"Simple {prompt_type.value} template"
    )