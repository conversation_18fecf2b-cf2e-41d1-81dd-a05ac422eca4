#!/usr/bin/env python3
"""
Simple test for conversation context.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from src.components.conversation.conversation_context import (
        ConversationContext, ConversationContextConfig, ContextScope, ContextPriority
    )
    print("✓ Import successful")
    
    # Test basic creation
    from unittest.mock import Mock
    config = ConversationContextConfig()
    mock_config_manager = Mock()
    
    context = ConversationContext("test_session", config, mock_config_manager)
    print("✓ Context creation successful")
    
    # Test basic operations
    context.add_context_item("test_key", "test_value", ContextScope.SESSION, ContextPriority.HIGH)
    value = context.get_context_item("test_key")
    assert value == "test_value"
    print("✓ Context operations successful")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
