"""
Model Configuration Demo

This demo shows how to use the ModelConfigManager for:
- Managing AI model configurations
- Checking model availability
- Updating model parameters
- Getting model information and status
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from config.model_config_simple import (
    ModelConfigManager,
    initialize_model_config,
    get_model_config
)


def demo_basic_model_config():
    """Demonstrate basic model configuration usage"""
    print("=== Basic Model Configuration Usage ===")
    
    # Initialize model configuration manager
    manager = initialize_model_config(
        models_dir="models",
        cache_dir="cache"
    )
    
    print(f"Models directory: {manager.models_dir}")
    print(f"Cache directory: {manager.cache_dir}")
    
    # Get available models
    available_models = manager.get_available_models()
    print(f"\nAvailable models: {available_models}")
    
    # Check individual model availability
    models_to_check = ["sensevoice", "silero_vad", "edge_tts", "qwen"]
    print("\nModel availability:")
    for model_name in models_to_check:
        is_available = manager.is_model_available(model_name)
        status = "✓ Available" if is_available else "✗ Not Available"
        print(f"  {model_name}: {status}")


def demo_model_configurations():
    """Demonstrate accessing model configurations"""
    print("\n=== Model Configurations ===")
    
    manager = get_model_config()
    
    # SenseVoice configuration
    print("SenseVoice ASR Configuration:")
    sense_voice = manager.get_model_config("sensevoice")
    if sense_voice:
        print(f"  Model Path: {sense_voice.model_path.model_file}")
        print(f"  Sample Rate: {sense_voice.sample_rate}")
        print(f"  Beam Size: {sense_voice.beam_size}")
        print(f"  Use GPU: {sense_voice.use_gpu}")
        print(f"  Beam Size: {sense_voice.beam_size}")
    
    # SileroVAD configuration
    print("\nSileroVAD Configuration:")
    silero_vad = manager.get_model_config("silero_vad")
    if silero_vad:
        print(f"  Model Path: {silero_vad.model_path.model_file}")
        print(f"  Threshold: {silero_vad.threshold}")
        print(f"  Threshold: {silero_vad.threshold}")
        print(f"  Use GPU: {silero_vad.use_gpu}")
    
    # EdgeTTS configuration
    print("\nEdgeTTS Configuration:")
    edge_tts = manager.get_model_config("edge_tts")
    if edge_tts:
        print(f"  Voice: {edge_tts.voice}")
        print(f"  Rate: {edge_tts.rate}")

        print(f"  Cache Enabled: {edge_tts.cache_enabled}")
    
    # Qwen configuration
    print("\nQwen LLM Configuration:")
    qwen = manager.get_model_config("qwen")
    if qwen:
        print(f"  Model Name: {qwen.model_name}")
        print(f"  Max Tokens: {qwen.max_tokens}")
        print(f"  Temperature: {qwen.temperature}")
        print(f"  API Key Set: {'Yes' if qwen.api_key else 'No'}")


def demo_model_information():
    """Demonstrate getting detailed model information"""
    print("\n=== Detailed Model Information ===")
    
    manager = get_model_config()
    
    models = ["sensevoice", "silero_vad", "edge_tts", "qwen"]
    
    for model_name in models:
        print(f"\n{model_name.upper()}:")
        available = manager.is_model_available(model_name)
        print(f"  Available: {available}")
        
        config = manager.get_model_config(model_name)
        if hasattr(config, 'model_path'):
            print(f"  Model Path: {config.model_path.model_file}")
            if config.model_path.exists():
                print(f"  Size: {config.model_path.get_size_mb():.1f}MB")
        else:
            print(f"  Type: API-based")


def demo_model_validation():
    """Demonstrate model file validation"""
    print("\n=== Model File Validation ===")
    
    manager = get_model_config()
    
    models = ["sensevoice", "silero_vad", "edge_tts", "qwen"]
    
    print("Model validation results:")
    for model_name in models:
        available = manager.is_model_available(model_name)
        status = "✓ Available" if available else "✗ Not Available"
        print(f"  {model_name}: {status}")


def demo_configuration_updates():
    """Demonstrate updating model configurations"""
    print("\n=== Configuration Updates ===")
    
    manager = get_model_config()
    
    # Update SenseVoice configuration
    print("Updating SenseVoice configuration:")
    original_beam_size = manager.sense_voice.beam_size
    print(f"  Original beam size: {original_beam_size}")
    
    # Simple attribute update (no update_model_config method in simple version)
    manager.sense_voice.beam_size = 3
    print(f"  New beam size: {manager.sense_voice.beam_size}")
    
    # Reset to original
    manager.sense_voice.beam_size = original_beam_size
    print(f"  Reset beam size: {manager.sense_voice.beam_size}")
    
    # Update VAD threshold
    print("\nUpdating VAD threshold:")
    original_threshold = manager.silero_vad.threshold
    print(f"  Original threshold: {original_threshold}")
    
    manager.silero_vad.threshold = 0.7
    print(f"  New threshold: {manager.silero_vad.threshold}")
    
    # Reset to original
    manager.silero_vad.threshold = original_threshold
    print(f"  Reset threshold: {manager.silero_vad.threshold}")


def demo_environment_variables():
    """Demonstrate environment variable overrides"""
    print("\n=== Environment Variable Overrides ===")
    
    # Set environment variables
    os.environ["SENSEVOICE_SAMPLE_RATE"] = "22050"
    os.environ["VAD_THRESHOLD"] = "0.8"
    os.environ["TTS_VOICE"] = "zh-CN-YunxiNeural"
    os.environ["QWEN_TEMPERATURE"] = "0.9"
    
    print("Environment variables set:")
    print(f"  SENSEVOICE_SAMPLE_RATE: {os.environ.get('SENSEVOICE_SAMPLE_RATE')}")
    print(f"  VAD_THRESHOLD: {os.environ.get('VAD_THRESHOLD')}")
    print(f"  TTS_VOICE: {os.environ.get('TTS_VOICE')}")
    print(f"  QWEN_TEMPERATURE: {os.environ.get('QWEN_TEMPERATURE')}")
    
    # Create new manager to pick up env vars
    manager = ModelConfigManager(models_dir="models", cache_dir="cache")
    
    print("\nConfiguration values (with env overrides):")
    print(f"  SenseVoice Sample Rate: {manager.sense_voice.sample_rate}")
    print(f"  VAD Threshold: {manager.silero_vad.threshold}")
    print(f"  TTS Voice: {manager.edge_tts.voice}")
    print(f"  Qwen Temperature: {manager.qwen.temperature}")
    
    # Clean up environment variables
    for key in ["SENSEVOICE_SAMPLE_RATE", "VAD_THRESHOLD", "TTS_VOICE", "QWEN_TEMPERATURE"]:
        os.environ.pop(key, None)


def demo_configuration_export():
    """Demonstrate configuration export"""
    print("\n=== Configuration Export ===")
    
    manager = get_model_config()
    
    print("Model configurations:")
    models = ["sensevoice", "silero_vad", "edge_tts", "qwen"]
    for model_name in models:
        config = manager.get_model_config(model_name)
        print(f"\n{model_name.upper()}:")
        if hasattr(config, '__dict__'):
            for key, value in config.__dict__.items():
                if not key.startswith('_'):
                    print(f"  {key}: {value}")


def demo_status_report():
    """Demonstrate comprehensive status report"""
    print("\n=== Model Status Report ===")
    
    manager = get_model_config()
    
    report = manager.get_model_status_report()
    print(report)


def demo_model_environment_setup():
    """Demonstrate model environment setup"""
    print("\n=== Model Environment Setup ===")
    
    manager = get_model_config()
    
    # Check created directories
    print("Created directories:")
    directories = [
        manager.models_dir / "sensevoice",
        manager.models_dir / "silero_vad",
        manager.cache_dir
    ]
    
    for directory in directories:
        exists = "✓" if directory.exists() else "✗"
        print(f"  {exists} {directory}")


def main():
    """Run all model configuration demos"""
    print("AI Voice Customer Service - Model Configuration Demo")
    print("=" * 60)
    
    try:
        # Run demos
        demo_basic_model_config()
        demo_model_configurations()
        demo_model_information()
        demo_model_validation()
        demo_configuration_updates()
        demo_environment_variables()
        demo_configuration_export()
        demo_status_report()
        demo_model_environment_setup()
        
        print("\n" + "=" * 60)
        print("Model Configuration Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()