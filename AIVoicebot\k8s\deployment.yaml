# AI Voice Customer Service System - Kubernetes Deployment
apiVersion: v1
kind: Namespace
metadata:
  name: aivoice
  labels:
    name: aivoice
---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: aivoice-config
  namespace: aivoice
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  REDIS_URL: "redis://aivoice-redis:6379"
  DATABASE_URL: "***********************************************************/aivoice"
  PROMETHEUS_GATEWAY: "http://aivoice-prometheus:9090"
---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: aivoice-secrets
  namespace: aivoice
type: Opaque
data:
  SECRET_KEY: "your-base64-encoded-secret-key"
  JWT_SECRET: "your-base64-encoded-jwt-secret"
  OPENAI_API_KEY: "your-base64-encoded-openai-api-key"
  DATABASE_PASSWORD: "your-base64-encoded-db-password"
---
# Persistent Volume for data storage
apiVersion: v1
kind: PersistentVolume
metadata:
  name: aivoice-data-pv
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: standard
  hostPath:
    path: /data/aivoice
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aivoice-data-pvc
  namespace: aivoice
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
# AI Voice Application Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aivoice-app
  namespace: aivoice
  labels:
    app: aivoice-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: aivoice-app
  template:
    metadata:
      labels:
        app: aivoice-app
    spec:
      containers:
      - name: aivoice-app
        image: aivoice/aivoice-app:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8765
          name: websocket
        - containerPort: 5060
          name: sip
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: aivoice-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: aivoice-config
              key: LOG_LEVEL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: aivoice-config
              key: REDIS_URL
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: aivoice-config
              key: DATABASE_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: aivoice-secrets
              key: SECRET_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: aivoice-secrets
              key: OPENAI_API_KEY
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: aivoice-data-pvc
      - name: logs-volume
        emptyDir: {}
      - name: config-volume
        configMap:
          name: aivoice-config
---
# Service for AI Voice Application
apiVersion: v1
kind: Service
metadata:
  name: aivoice-app-service
  namespace: aivoice
  labels:
    app: aivoice-app
spec:
  selector:
    app: aivoice-app
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: websocket
    port: 8765
    targetPort: 8765
    protocol: TCP
  - name: sip
    port: 5060
    targetPort: 5060
    protocol: TCP
  type: ClusterIP
---
# Redis Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aivoice-redis
  namespace: aivoice
  labels:
    app: aivoice-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aivoice-redis
  template:
    metadata:
      labels:
        app: aivoice-redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}
---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: aivoice-redis
  namespace: aivoice
  labels:
    app: aivoice-redis
spec:
  selector:
    app: aivoice-redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
---
# PostgreSQL Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aivoice-postgres
  namespace: aivoice
  labels:
    app: aivoice-postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aivoice-postgres
  template:
    metadata:
      labels:
        app: aivoice-postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "aivoice"
        - name: POSTGRES_USER
          value: "aivoice"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: aivoice-secrets
              key: DATABASE_PASSWORD
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-data-pvc
---
# PostgreSQL PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-data-pvc
  namespace: aivoice
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
# PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: aivoice-postgres
  namespace: aivoice
  labels:
    app: aivoice-postgres
spec:
  selector:
    app: aivoice-postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aivoice-ingress
  namespace: aivoice
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  tls:
  - hosts:
    - aivoice.yourdomain.com
    secretName: aivoice-tls
  rules:
  - host: aivoice.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: aivoice-app-service
            port:
              number: 8000
---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: aivoice-app-hpa
  namespace: aivoice
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: aivoice-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
