"""
Comprehensive tests for PromptManager.
"""

import pytest
import asyncio
import tempfile
import os
import json
from unittest.mock import Mock, patch
from datetime import datetime
import sys

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.prompt_manager import PromptManager, PromptTemplate, PromptContext
from tests.test_utils import <PERSON><PERSON><PERSON>onfigManager, <PERSON>ckLog<PERSON>, TextTestData, create_test_conversation_context


class TestPromptManagerComprehensive:
    """Comprehensive tests for PromptManager."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        config_manager = MockConfigManager({
            "prompts.template_dir": tempfile.mkdtemp(),
            "prompts.default_language": "zh-CN",
            "prompts.cache_size": 100,
            "prompts.template_reload_interval": 300
        })
        return config_manager
    
    @pytest.fixture
    def mock_logger(self):
        """Create mock logger."""
        return MockLogger()
    
    @pytest.fixture
    def prompt_manager(self, mock_config_manager, mock_logger):
        """Create PromptManager instance."""
        return PromptManager(mock_config_manager, mock_logger)
    
    @pytest.fixture
    def sample_templates(self, mock_config_manager):
        """Create sample template files."""
        template_dir = mock_config_manager.get_config("prompts.template_dir")
        
        # Create greeting template
        greeting_template = {
            "name": "greeting",
            "language": "zh-CN",
            "template": "您好，{user_name}！欢迎使用AI客服系统。我是您的专属助手，有什么可以帮助您的吗？",
            "variables": ["user_name"],
            "metadata": {
                "category": "greeting",
                "priority": "high"
            }
        }
        
        with open(os.path.join(template_dir, "greeting.json"), 'w', encoding='utf-8') as f:
            json.dump(greeting_template, f, ensure_ascii=False, indent=2)
        
        # Create account inquiry template
        account_template = {
            "name": "account_inquiry",
            "language": "zh-CN",
            "template": "我来帮您查询账户信息。根据您的{account_type}账户，我需要验证您的身份。请提供您的{required_info}。",
            "variables": ["account_type", "required_info"],
            "metadata": {
                "category": "account",
                "priority": "medium"
            }
        }
        
        with open(os.path.join(template_dir, "account_inquiry.json"), 'w', encoding='utf-8') as f:
            json.dump(account_template, f, ensure_ascii=False, indent=2)
        
        # Create error handling template
        error_template = {
            "name": "error_handling",
            "language": "zh-CN",
            "template": "抱歉，在处理您的请求时遇到了{error_type}。请稍后重试，或者我可以为您转接人工客服。",
            "variables": ["error_type"],
            "metadata": {
                "category": "error",
                "priority": "high"
            }
        }
        
        with open(os.path.join(template_dir, "error_handling.json"), 'w', encoding='utf-8') as f:
            json.dump(error_template, f, ensure_ascii=False, indent=2)
        
        return template_dir
    
    @pytest.mark.asyncio
    async def test_prompt_manager_lifecycle(self, prompt_manager):
        """Test PromptManager lifecycle."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        assert prompt_manager.is_initialized
        assert prompt_manager.is_running
        
        await prompt_manager.stop()
        await prompt_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_template_loading(self, prompt_manager, sample_templates):
        """Test template loading from files."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Check that templates were loaded
        templates = prompt_manager.get_available_templates()
        assert len(templates) >= 3
        
        template_names = [t.name for t in templates]
        assert "greeting" in template_names
        assert "account_inquiry" in template_names
        assert "error_handling" in template_names
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_prompt_generation_basic(self, prompt_manager, sample_templates):
        """Test basic prompt generation."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Test greeting prompt
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={"user_name": "张三"}
        )
        
        prompt = await prompt_manager.generate_prompt("greeting", context)
        assert prompt is not None
        assert "张三" in prompt
        assert "欢迎使用AI客服系统" in prompt
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_prompt_generation_with_conversation_history(self, prompt_manager, sample_templates):
        """Test prompt generation with conversation history."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Create context with conversation history
        conversation_data = create_test_conversation_context()
        context = PromptContext(
            conversation_id=conversation_data["conversation_id"],
            user_id=conversation_data["user_id"],
            variables={"user_name": "张三", "account_type": "储蓄", "required_info": "身份证号码"},
            conversation_history=conversation_data["message_history"]
        )
        
        prompt = await prompt_manager.generate_prompt("account_inquiry", context)
        assert prompt is not None
        assert "储蓄" in prompt
        assert "身份证号码" in prompt
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_context_aware_prompt_generation(self, prompt_manager, sample_templates):
        """Test context-aware prompt generation."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Test with different contexts
        contexts = [
            PromptContext(
                conversation_id="conv1",
                user_id="user1",
                variables={"error_type": "网络连接问题"},
                intent="error_handling"
            ),
            PromptContext(
                conversation_id="conv2",
                user_id="user2",
                variables={"error_type": "系统维护"},
                intent="error_handling"
            )
        ]
        
        for context in contexts:
            prompt = await prompt_manager.generate_prompt("error_handling", context)
            assert prompt is not None
            assert context.variables["error_type"] in prompt
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_template_caching(self, prompt_manager, sample_templates):
        """Test template caching mechanism."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Generate same prompt multiple times
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={"user_name": "李四"}
        )
        
        # First generation
        prompt1 = await prompt_manager.generate_prompt("greeting", context)
        
        # Second generation (should use cache)
        prompt2 = await prompt_manager.generate_prompt("greeting", context)
        
        assert prompt1 == prompt2
        assert "李四" in prompt1
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_template_validation(self, prompt_manager, sample_templates):
        """Test template validation."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Test with missing variables
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={}  # Missing user_name
        )
        
        # Should handle missing variables gracefully
        prompt = await prompt_manager.generate_prompt("greeting", context)
        assert prompt is not None
        # Should contain placeholder or default value
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_script_context_injection(self, prompt_manager, sample_templates):
        """Test script context injection."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Create context with script information
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={"user_name": "王五"},
            script_context={
                "current_step": "identity_verification",
                "next_actions": ["verify_id", "check_account"],
                "business_rules": ["require_id_verification"]
            }
        )
        
        prompt = await prompt_manager.generate_prompt("greeting", context)
        assert prompt is not None
        
        # Check that script context influenced the prompt
        # (This would depend on the actual implementation)
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_multi_language_support(self, prompt_manager, mock_config_manager):
        """Test multi-language template support."""
        # Create English template
        template_dir = mock_config_manager.get_config("prompts.template_dir")
        
        english_template = {
            "name": "greeting",
            "language": "en-US",
            "template": "Hello, {user_name}! Welcome to our AI customer service. How can I help you today?",
            "variables": ["user_name"],
            "metadata": {
                "category": "greeting",
                "priority": "high"
            }
        }
        
        with open(os.path.join(template_dir, "greeting_en.json"), 'w', encoding='utf-8') as f:
            json.dump(english_template, f, ensure_ascii=False, indent=2)
        
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Test English prompt
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={"user_name": "John"},
            language="en-US"
        )
        
        prompt = await prompt_manager.generate_prompt("greeting", context)
        assert prompt is not None
        assert "Hello, John" in prompt
        assert "Welcome to our AI customer service" in prompt
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_template_reload(self, prompt_manager, sample_templates, mock_config_manager):
        """Test template reloading."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Get initial template count
        initial_templates = prompt_manager.get_available_templates()
        initial_count = len(initial_templates)
        
        # Add a new template file
        template_dir = mock_config_manager.get_config("prompts.template_dir")
        new_template = {
            "name": "farewell",
            "language": "zh-CN",
            "template": "感谢您使用我们的服务，{user_name}。祝您生活愉快！",
            "variables": ["user_name"],
            "metadata": {
                "category": "farewell",
                "priority": "medium"
            }
        }
        
        with open(os.path.join(template_dir, "farewell.json"), 'w', encoding='utf-8') as f:
            json.dump(new_template, f, ensure_ascii=False, indent=2)
        
        # Trigger template reload
        await prompt_manager.reload_templates()
        
        # Check that new template was loaded
        updated_templates = prompt_manager.get_available_templates()
        assert len(updated_templates) == initial_count + 1
        
        template_names = [t.name for t in updated_templates]
        assert "farewell" in template_names
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, prompt_manager, sample_templates):
        """Test error handling in prompt generation."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Test with non-existent template
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={"user_name": "测试用户"}
        )
        
        prompt = await prompt_manager.generate_prompt("non_existent_template", context)
        # Should return None or default prompt
        
        await prompt_manager.stop()
    
    @pytest.mark.asyncio
    async def test_prompt_statistics(self, prompt_manager, sample_templates):
        """Test prompt generation statistics."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Generate several prompts
        context = PromptContext(
            conversation_id="test_conv",
            user_id="test_user",
            variables={"user_name": "统计测试"}
        )
        
        for _ in range(5):
            await prompt_manager.generate_prompt("greeting", context)
        
        for _ in range(3):
            await prompt_manager.generate_prompt("error_handling", context)
        
        # Get statistics
        stats = prompt_manager.get_prompt_statistics()
        assert stats is not None
        assert "total_prompts_generated" in stats
        assert stats["total_prompts_generated"] >= 8
        
        await prompt_manager.stop()
    
    def test_template_validation_schema(self, prompt_manager):
        """Test template validation against schema."""
        # Test valid template
        valid_template = PromptTemplate(
            name="test_template",
            language="zh-CN",
            template="测试模板：{variable}",
            variables=["variable"],
            metadata={"category": "test"}
        )
        
        assert prompt_manager._validate_template(valid_template)
        
        # Test invalid template (missing required fields)
        invalid_template = PromptTemplate(
            name="",  # Empty name
            language="zh-CN",
            template="测试模板",
            variables=[],
            metadata={}
        )
        
        assert not prompt_manager._validate_template(invalid_template)
    
    @pytest.mark.asyncio
    async def test_concurrent_prompt_generation(self, prompt_manager, sample_templates):
        """Test concurrent prompt generation."""
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        # Create multiple contexts
        contexts = [
            PromptContext(
                conversation_id=f"conv_{i}",
                user_id=f"user_{i}",
                variables={"user_name": f"用户{i}"}
            )
            for i in range(10)
        ]
        
        # Generate prompts concurrently
        tasks = [
            prompt_manager.generate_prompt("greeting", context)
            for context in contexts
        ]
        
        prompts = await asyncio.gather(*tasks)
        
        # Check all prompts were generated
        assert len(prompts) == 10
        for i, prompt in enumerate(prompts):
            assert prompt is not None
            assert f"用户{i}" in prompt
        
        await prompt_manager.stop()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestPromptManagerComprehensive().test_prompt_manager_lifecycle(
        PromptManager(MockConfigManager(), MockLogger())
    ))
