"""
Demo script for response processing and validation system.

This script demonstrates how to use the response processor for validating
and processing LLM outputs with fallback mechanisms.
"""

import asyncio
import logging
import sys
import os
from typing import List

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.llm.response_processor import (
    ResponseProcessor, ResponseProcessorConfig, ValidationRule,
    ProcessedResponse, ResponseQuality, ResponseSource,
    create_response_processor, create_validation_rule,
    create_banking_safety_rules, assess_response_appropriateness
)
from src.components.llm.qwen_client import QwenResponse
from src.components.scripts.script_parser import ConversationScript
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_basic_processing():
    """Demonstrate basic response processing."""
    logger.info("=== Basic Response Processing Demo ===")
    
    # Create response processor
    processor = await create_response_processor(
        enable_validation=True,
        enable_safety_checks=True,
        min_confidence_threshold=0.6,
        config_manager=Mock()
    )
    
    try:
        # Create sample LLM response
        llm_response = QwenResponse(
            text="您好！根据您的信用状况，我们的个人贷款利率一般在年化3.5%-8%之间。建议您携带身份证、收入证明等材料到我行网点详细咨询。",
            finish_reason="stop",
            total_tokens=45,
            response_id="demo_response_1"
        )
        
        # Process the response
        processed = await processor.process_llm_response(llm_response)
        
        logger.info("Processing Results:")
        logger.info(f"  Original: {llm_response.text}")
        logger.info(f"  Processed: {processed.text}")
        logger.info(f"  Source: {processed.source.value}")
        logger.info(f"  Quality: {processed.quality.value}")
        logger.info(f"  Confidence: {processed.confidence:.3f}")
        logger.info(f"  Valid: {processed.is_valid}")
        logger.info(f"  Applied filters: {processed.applied_filters}")
        
        if processed.validation_warnings:
            logger.info(f"  Warnings: {processed.validation_warnings}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def demo_validation_rules():
    """Demonstrate validation rules."""
    logger.info("=== Validation Rules Demo ===")
    
    processor = await create_response_processor(config_manager=Mock())
    
    try:
        # Test different types of responses
        test_responses = [
            ("您好！我们的贷款利率在3.5%-8%之间。", "Good response"),
            ("不知道", "Too short response"),
            ("您这个傻子，什么都不懂！" * 20, "Inappropriate and too long"),
            ("请提供您的身份证号码和银行卡密码。", "Contains sensitive info request"),
            ("我们保证您100%盈利，零风险投资！", "Contains financial guarantees")
        ]
        
        for response_text, description in test_responses:
            logger.info(f"\nTesting: {description}")
            logger.info(f"Text: {response_text[:50]}...")
            
            # Validate the response
            is_valid, errors, warnings = await processor.validate_text(response_text)
            
            logger.info(f"Valid: {is_valid}")
            if errors:
                logger.info(f"Errors: {errors}")
            if warnings:
                logger.info(f"Warnings: {warnings}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def demo_fallback_mechanism():
    """Demonstrate fallback mechanism."""
    logger.info("=== Fallback Mechanism Demo ===")
    
    processor = await create_response_processor(
        enable_fallback=True,
        config_manager=Mock()
    )
    
    try:
        # Create fallback scripts
        fallback_scripts = [
            ConversationScript(
                script_id="fallback_1",
                user_input="利率咨询",
                response="我们的个人贷款利率根据您的信用状况而定，一般在年化3.5%-8%之间。具体利率需要经过信用评估确定。",
                category="利率咨询",
                priority=2
            ),
            ConversationScript(
                script_id="fallback_2",
                user_input="通用回复",
                response="感谢您的咨询。为了给您提供更准确的信息，建议您到我行网点或致电客服热线详细了解。",
                category="通用",
                priority=1
            )
        ]
        
        # Test scenarios that trigger fallback
        test_scenarios = [
            # Failed LLM response
            QwenResponse(
                text="",
                error_code="500",
                error_message="Internal server error"
            ),
            # Poor quality LLM response
            QwenResponse(
                text="不知道",
                finish_reason="stop",
                total_tokens=2
            ),
            # Invalid LLM response
            QwenResponse(
                text="你这个傻子，什么都不懂！",
                finish_reason="stop",
                total_tokens=8
            )
        ]
        
        for i, llm_response in enumerate(test_scenarios, 1):
            logger.info(f"\nScenario {i}: {llm_response.error_message or 'Poor/Invalid response'}")
            logger.info(f"LLM Response: '{llm_response.text}'")
            
            # Process with fallback
            processed = await processor.process_llm_response(
                llm_response, fallback_scripts
            )
            
            logger.info(f"Final Response: {processed.text}")
            logger.info(f"Source: {processed.source.value}")
            logger.info(f"Fallback Used: {processed.fallback_used}")
            if processed.fallback_used:
                logger.info(f"Fallback Reason: {processed.fallback_reason}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def demo_content_filtering():
    """Demonstrate content filtering."""
    logger.info("=== Content Filtering Demo ===")
    
    processor = await create_response_processor(
        enable_content_filtering=True,
        enable_profanity_filter=True,
        enable_sensitive_info_filter=True,
        config_manager=Mock()
    )
    
    try:
        # Test different types of content that need filtering
        test_content = [
            ("我不太确定利率是多少，可能是3.5%左右吧。", "Uncertain language"),
            ("你这个傻子，贷款利率当然是固定的！", "Profanity"),
            ("您的手机号是13812345678，请记住。", "Sensitive information"),
            ("我们保证您100%盈利，绝对零风险！", "Financial guarantees"),
            ("请提供您的身份证号码123456789012345678进行验证。", "Personal ID information")
        ]
        
        for content, description in test_content:
            logger.info(f"\nTesting: {description}")
            logger.info(f"Original: {content}")
            
            # Process the content
            processed = await processor._process_response_text(
                content, ResponseSource.LLM, f"test_{description}"
            )
            
            logger.info(f"Filtered: {processed.text}")
            logger.info(f"Applied filters: {processed.applied_filters}")
            logger.info(f"Valid: {processed.is_valid}")
            
            if processed.validation_errors:
                logger.info(f"Errors: {processed.validation_errors}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def demo_hybrid_responses():
    """Demonstrate hybrid response creation."""
    logger.info("=== Hybrid Responses Demo ===")
    
    processor = await create_response_processor(config_manager=Mock())
    
    try:
        # Create script responses
        script_responses = [
            ConversationScript(
                script_id="script_1",
                user_input="利率咨询",
                response="我们的个人贷款利率在3.5%-8%之间。",
                category="利率咨询",
                priority=2
            )
        ]
        
        # Test different LLM response qualities
        test_cases = [
            # Good LLM response - should use LLM
            QwenResponse(
                text="根据您的信用状况，我们可以为您提供年化3.5%-8%的个人贷款利率。具体利率需要通过信用评估确定，建议您到网点详细咨询。",
                finish_reason="stop",
                total_tokens=50,
                response_id="good_llm"
            ),
            # Poor LLM response - should create hybrid
            QwenResponse(
                text="利率不确定，可能3.5%吧。",
                finish_reason="stop",
                total_tokens=12,
                response_id="poor_llm"
            )
        ]
        
        for i, llm_response in enumerate(test_cases, 1):
            logger.info(f"\nTest Case {i}:")
            logger.info(f"LLM Response: {llm_response.text}")
            
            # Create hybrid response
            processed = await processor.create_hybrid_response(
                llm_response, script_responses
            )
            
            logger.info(f"Final Response: {processed.text}")
            logger.info(f"Source: {processed.source.value}")
            logger.info(f"Quality: {processed.quality.value}")
            logger.info(f"Confidence: {processed.confidence:.3f}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def demo_custom_rules():
    """Demonstrate custom validation rules."""
    logger.info("=== Custom Validation Rules Demo ===")
    
    processor = await create_response_processor(config_manager=Mock())
    
    try:
        # Add custom banking-specific rules
        banking_rules = create_banking_safety_rules()
        for rule in banking_rules:
            processor.add_safety_rule(rule)
        
        # Add custom validation rule
        custom_rule = create_validation_rule(
            name="banking_terminology",
            description="Must use proper banking terminology",
            required_elements=["贷款", "利率"],
            severity="warning"
        )
        processor.add_validation_rule(custom_rule)
        
        logger.info(f"Total validation rules: {len(processor.get_validation_rules())}")
        logger.info(f"Total safety rules: {len(processor.get_safety_rules())}")
        
        # Test responses against custom rules
        test_responses = [
            "我们的贷款利率在3.5%-8%之间，具体需要评估。",  # Should pass
            "我们保证您100%盈利，零风险投资！",  # Should fail safety
            "价格很便宜，你可以买。",  # Should fail terminology
        ]
        
        for response in test_responses:
            logger.info(f"\nTesting: {response}")
            
            is_valid, errors, warnings = await processor.validate_text(response)
            
            logger.info(f"Valid: {is_valid}")
            if errors:
                logger.info(f"Errors: {errors}")
            if warnings:
                logger.info(f"Warnings: {warnings}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def demo_response_appropriateness():
    """Demonstrate response appropriateness assessment."""
    logger.info("=== Response Appropriateness Demo ===")
    
    # Test different query-response pairs
    test_pairs = [
        (
            "贷款利率是多少？",
            "我们的个人贷款利率根据您的信用状况，一般在年化3.5%-8%之间。"
        ),
        (
            "贷款利率是多少？",
            "谢谢您的咨询。"
        ),
        (
            "如何申请贷款？",
            "申请贷款需要准备身份证、收入证明等材料，您可以到我行网点办理。"
        ),
        (
            "如何申请贷款？",
            "我们的利率很优惠。"
        )
    ]
    
    for query, response in test_pairs:
        logger.info(f"\nQuery: {query}")
        logger.info(f"Response: {response}")
        
        is_appropriate, relevance_score, issues = assess_response_appropriateness(
            response, query
        )
        
        logger.info(f"Appropriate: {is_appropriate}")
        logger.info(f"Relevance Score: {relevance_score:.3f}")
        if issues:
            logger.info(f"Issues: {issues}")


async def demo_statistics_and_monitoring():
    """Demonstrate statistics and monitoring."""
    logger.info("=== Statistics and Monitoring Demo ===")
    
    processor = await create_response_processor(config_manager=Mock())
    
    try:
        # Process several responses to generate statistics
        test_responses = [
            QwenResponse(text="优秀的回复内容，专业且详细。", finish_reason="stop", total_tokens=20),
            QwenResponse(text="一般的回复。", finish_reason="stop", total_tokens=8),
            QwenResponse(text="", error_code="500", error_message="Error"),
            QwenResponse(text="不太好的回复，包含不当内容。", finish_reason="stop", total_tokens=15)
        ]
        
        fallback_scripts = [
            ConversationScript("fb1", "问题", "标准回复内容", "通用", 1)
        ]
        
        for i, response in enumerate(test_responses):
            logger.info(f"Processing response {i+1}...")
            processed = await processor.process_llm_response(response, fallback_scripts)
        
        # Get and display statistics
        stats = processor.get_processor_stats()
        
        logger.info("\nProcessor Statistics:")
        logger.info(f"  Total processed: {stats['total_processed']}")
        logger.info(f"  Validation failure rate: {stats['validation_failure_rate']:.2%}")
        logger.info(f"  Fallback rate: {stats['fallback_rate']:.2%}")
        logger.info(f"  Quality distribution: {stats['quality_distribution']}")
        logger.info(f"  Cache size: {stats['cache_size']}")
        
    finally:
        await processor.stop()
        await processor.cleanup()


async def main():
    """Run all demos."""
    logger.info("Starting Response Processor Demo")
    
    try:
        await demo_basic_processing()
        await demo_validation_rules()
        await demo_fallback_mechanism()
        await demo_content_filtering()
        await demo_hybrid_responses()
        await demo_custom_rules()
        await demo_response_appropriateness()
        await demo_statistics_and_monitoring()
        
        logger.info("All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())