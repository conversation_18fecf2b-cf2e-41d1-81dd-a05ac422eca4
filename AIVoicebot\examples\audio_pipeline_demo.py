"""
Audio Pipeline Demo

This demo shows how to use the AudioPipeline for:
- Real-time audio buffering
- Audio format conversion and preprocessing
- Pipeline coordination and processing
"""

import sys
import time
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from components.audio.audio_simple import (
    AudioPipeline,
    AudioBuffer,
    AudioProcessor,
    AudioConfig,
    AudioFormat,
    create_audio_pipeline,
    create_simple_processor
)


def demo_audio_buffer():
    """Demonstrate AudioBuffer functionality"""
    print("=== Audio Buffer Demo ===")
    
    # Create audio configuration
    config = AudioConfig(
        sample_rate=16000,
        channels=1,
        buffer_duration=2.0  # 2 second buffer
    )
    
    # Create audio buffer
    buffer = AudioBuffer(config)
    
    print(f"Buffer size: {buffer.buffer_size} samples")
    print(f"Buffer duration: {config.buffer_duration} seconds")
    print(f"Initial available data: {buffer.get_available_data()} samples")
    
    # Write some test data
    test_data1 = np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, 1600))  # 440Hz tone, 0.1s
    test_data2 = np.sin(2 * np.pi * 880 * np.linspace(0, 0.1, 1600))  # 880Hz tone, 0.1s
    
    print(f"\nWriting {len(test_data1)} samples...")
    success1 = buffer.write(test_data1.astype(np.float32))
    print(f"Write success: {success1}")
    print(f"Available data: {buffer.get_available_data()} samples")
    
    print(f"\nWriting {len(test_data2)} samples...")
    success2 = buffer.write(test_data2.astype(np.float32))
    print(f"Write success: {success2}")
    print(f"Available data: {buffer.get_available_data()} samples")
    
    # Read data back
    print(f"\nReading 1600 samples...")
    read_data1 = buffer.read(1600)
    if read_data1 is not None:
        print(f"Read {len(read_data1)} samples")
        print(f"Remaining data: {buffer.get_available_data()} samples")
    
    print(f"\nReading remaining data...")
    read_data2 = buffer.read(1600)
    if read_data2 is not None:
        print(f"Read {len(read_data2)} samples")
        print(f"Remaining data: {buffer.get_available_data()} samples")


def demo_audio_processor():
    """Demonstrate AudioProcessor functionality"""
    print("\n=== Audio Processor Demo ===")
    
    config = AudioConfig()
    processor = AudioProcessor(config)
    
    # Create test audio data
    print("Creating test audio data...")
    duration = 0.1  # 0.1 seconds
    sample_rate = 16000
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Generate test data
    float_data = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440Hz sine wave
    
    print(f"Original data range: [{np.min(float_data):.3f}, {np.max(float_data):.3f}]")
    
    # Test normalization
    print("\nTesting normalization:")
    loud_data = float_data * 2.5  # Make it louder
    print(f"Loud data range: [{np.min(loud_data):.3f}, {np.max(loud_data):.3f}]")
    
    normalized = processor.normalize_audio(loud_data)
    print(f"Normalized range: [{np.min(normalized):.3f}, {np.max(normalized):.3f}]")
    
    # Test with zero data
    zero_data = np.zeros(100)
    normalized_zero = processor.normalize_audio(zero_data)
    print(f"Zero data normalized: all zeros = {np.all(normalized_zero == 0)}")


def demo_audio_pipeline():
    """Demonstrate AudioPipeline functionality"""
    print("\n=== Audio Pipeline Demo ===")
    
    # Create pipeline
    pipeline = create_audio_pipeline(
        sample_rate=16000,
        channels=1,
        chunk_size=1024,
        buffer_duration=3.0
    )
    
    print("Created audio pipeline")
    print(f"Sample rate: {pipeline.config.sample_rate}Hz")
    print(f"Buffer duration: {pipeline.config.buffer_duration}s")
    
    # Add processors to pipeline
    print("\nAdding processors to pipeline:")
    
    # 1. Normalization processor
    normalize_processor = create_simple_processor(normalize=True)
    pipeline.add_processor(normalize_processor)
    print("Added normalization processor")
    
    # 2. Gain processor
    def gain_processor(data):
        return data * 0.8  # Reduce volume slightly
    
    pipeline.add_processor(gain_processor)
    print("Added gain processor")
    
    # Start pipeline
    print("\nStarting pipeline...")
    pipeline.start()
    
    # Generate and process test audio
    print("Processing test audio...")
    
    # Create test signals
    duration = 0.5  # 0.5 seconds
    sample_rate = 16000
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Generate different test signals
    signals = [
        ("440Hz Sine", 0.7 * np.sin(2 * np.pi * 440 * t)),
        ("880Hz Sine", 0.5 * np.sin(2 * np.pi * 880 * t)),
        ("White Noise", 0.3 * np.random.randn(len(t))),
        ("Chirp", 0.6 * np.sin(2 * np.pi * t * (200 + 400 * t)))
    ]
    
    for signal_name, signal_data in signals:
        print(f"\nProcessing {signal_name}...")
        
        # Write signal to pipeline
        success = pipeline.write_audio(signal_data.astype(np.float32))
        print(f"Write success: {success}")
        
        # Wait for processing
        time.sleep(0.1)
        
        # Read processed signal
        processed = pipeline.read_audio(timeout=1.0)
        if processed is not None:
            print(f"Processed {len(processed)} samples")
            print(f"Input range: [{np.min(signal_data):.3f}, {np.max(signal_data):.3f}]")
            print(f"Output range: [{np.min(processed):.3f}, {np.max(processed):.3f}]")
        else:
            print("No processed data received")
        
        # Check buffer status
        status = pipeline.get_buffer_status()
        available_samples = status['available_samples']
        buffer_size = status['buffer_size']
        usage_percent = (available_samples / buffer_size) * 100 if buffer_size > 0 else 0
        print(f"Buffer usage: {usage_percent:.1f}%")
    
    # Stop pipeline
    print("\nStopping pipeline...")
    pipeline.stop()
    print("Pipeline stopped")


def demo_real_time_simulation():
    """Demonstrate real-time audio processing simulation"""
    print("\n=== Real-time Processing Simulation ===")
    
    # Create pipeline for real-time processing
    pipeline = create_audio_pipeline(
        sample_rate=16000,
        chunk_size=512,  # Smaller chunks for real-time feel
        buffer_duration=1.0
    )
    
    # Add real-time processors
    def real_time_processor(data):
        # Simulate some processing (e.g., noise gate)
        threshold = 0.1
        processed = np.where(np.abs(data) > threshold, data, data * 0.1)
        return processed
    
    pipeline.add_processor(real_time_processor)
    
    print("Starting real-time simulation...")
    pipeline.start()
    
    # Simulate real-time audio chunks
    chunk_duration = 0.032  # 32ms chunks (typical for real-time)
    chunk_samples = int(16000 * chunk_duration)
    
    print(f"Processing {chunk_samples} samples every {chunk_duration*1000:.1f}ms")
    
    for i in range(20):  # Process 20 chunks
        # Generate chunk of audio (simulate microphone input)
        t = np.linspace(i * chunk_duration, (i + 1) * chunk_duration, chunk_samples)
        
        # Mix of signal and noise
        signal = 0.3 * np.sin(2 * np.pi * 440 * t)  # Weak signal
        noise = 0.05 * np.random.randn(len(t))  # Background noise
        chunk = signal + noise
        
        # Process chunk
        pipeline.write_audio(chunk.astype(np.float32))
        
        # Read processed chunk
        processed = pipeline.read_audio(timeout=0.1)
        if processed is not None:
            signal_power = np.mean(processed ** 2)
            print(f"Chunk {i+1:2d}: Signal power = {signal_power:.6f}")
        
        # Simulate real-time timing
        time.sleep(chunk_duration * 0.5)  # Process faster than real-time
    
    pipeline.stop()
    print("Real-time simulation completed")


def demo_buffer_performance():
    """Demonstrate buffer performance characteristics"""
    print("\n=== Buffer Performance Demo ===")
    
    # Test different buffer sizes
    buffer_sizes = [0.5, 1.0, 2.0, 5.0]  # seconds
    
    for buffer_duration in buffer_sizes:
        print(f"\nTesting {buffer_duration}s buffer:")
        
        config = AudioConfig(
            sample_rate=16000,
            buffer_duration=buffer_duration
        )
        buffer = AudioBuffer(config)
        
        print(f"Buffer size: {buffer.buffer_size} samples")
        
        # Test write performance
        chunk_size = 1024
        num_chunks = int(buffer.buffer_size // chunk_size)
        
        start_time = time.time()
        
        for i in range(num_chunks):
            test_chunk = np.random.randn(chunk_size).astype(np.float32)
            success = buffer.write(test_chunk)
            if not success:
                print(f"Buffer full after {i} chunks")
                break
        
        write_time = time.time() - start_time
        
        print(f"Wrote {i+1} chunks in {write_time:.3f}s")
        if write_time > 0:
            print(f"Write rate: {(i+1) * chunk_size / write_time:.0f} samples/s")
        else:
            print("Write rate: Very fast (< 1ms)")
        
        # Test read performance
        start_time = time.time()
        
        total_read = 0
        while buffer.get_available_data() >= chunk_size:
            read_data = buffer.read(chunk_size)
            if read_data is not None:
                total_read += len(read_data)
        
        read_time = time.time() - start_time
        
        print(f"Read {total_read} samples in {read_time:.3f}s")
        if read_time > 0:
            print(f"Read rate: {total_read / read_time:.0f} samples/s")
        else:
            print("Read rate: Very fast (< 1ms)")


def main():
    """Run all audio pipeline demos"""
    print("AI Voice Customer Service - Audio Pipeline Demo")
    print("=" * 60)
    
    try:
        demo_audio_buffer()
        demo_audio_processor()
        demo_audio_pipeline()
        demo_real_time_simulation()
        demo_buffer_performance()
        
        print("\n" + "=" * 60)
        print("Audio Pipeline Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()