"""
Demo script for prompt management system.

This script demonstrates how to use the prompt manager for generating
context-aware prompts for LLM interactions.
"""

import asyncio
import logging
import sys
import os
from typing import List, Dict

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.llm.prompt_manager import (
    PromptManager, PromptManagerConfig, PromptTemplate, PromptContext,
    ConversationTurn, PromptType, create_prompt_manager,
    create_customer_service_context, create_simple_template
)
from src.components.scripts.script_parser import ConversationScript
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_basic_prompt_generation():
    """Demonstrate basic prompt generation."""
    logger.info("=== Basic Prompt Generation Demo ===")
    
    # Create prompt manager
    manager = await create_prompt_manager(
        max_conversation_history=5,
        max_prompt_length=3000,
        config_manager=Mock()
    )
    
    try:
        # Create simple context
        context = PromptContext(
            user_query="你好，我想了解一下贷款产品。",
            user_intent="产品咨询"
        )
        
        # Generate prompt
        prompt = manager.generate_prompt(context)
        
        logger.info("Generated prompt:")
        logger.info("=" * 50)
        logger.info(prompt)
        logger.info("=" * 50)
        
        # Show statistics
        stats = manager.get_prompt_statistics()
        logger.info(f"Prompt statistics: {stats}")
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def demo_conversation_history():
    """Demonstrate prompt generation with conversation history."""
    logger.info("=== Conversation History Demo ===")
    
    manager = await create_prompt_manager(config_manager=Mock())
    
    try:
        # Create conversation history
        conversation_history = [
            {"user": "你好", "assistant": "您好！欢迎咨询我们的银行服务。"},
            {"user": "我想了解贷款", "assistant": "我们有多种贷款产品，包括个人贷款、房贷、车贷等。"},
            {"user": "个人贷款需要什么条件？", "assistant": "个人贷款需要稳定收入、良好信用记录等基本条件。"}
        ]
        
        # Create context with history
        context = create_customer_service_context(
            user_query="利率大概是多少？",
            customer_name="王先生",
            customer_level="VIP",
            conversation_history=conversation_history,
            current_promotion="春季贷款优惠活动"
        )
        
        # Generate prompt
        prompt = manager.generate_prompt(context)
        
        logger.info("Prompt with conversation history:")
        logger.info("=" * 50)
        logger.info(prompt)
        logger.info("=" * 50)
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def demo_script_based_prompts():
    """Demonstrate script-based prompt generation."""
    logger.info("=== Script-Based Prompts Demo ===")
    
    manager = await create_prompt_manager(config_manager=Mock())
    
    try:
        # Create matched scripts
        matched_scripts = [
            ConversationScript(
                script_id="script_1",
                user_input="利率咨询",
                response="我们的个人贷款利率根据您的信用状况，一般在年化3.5%-8%之间。",
                category="利率咨询",
                priority=2
            ),
            ConversationScript(
                script_id="script_2", 
                user_input="贷款条件",
                response="申请个人贷款需要：1.年满18周岁 2.有稳定收入 3.信用记录良好",
                category="申请条件",
                priority=1
            )
        ]
        
        # Create context with scripts
        context = PromptContext(
            user_query="我想知道贷款利率和申请条件",
            matched_scripts=matched_scripts,
            script_confidence=0.85,
            customer_info={"name": "李女士", "customer_level": "普通客户"}
        )
        
        # Generate script-based prompt
        prompt = manager.generate_prompt(
            context,
            prompt_type=PromptType.SCRIPT_BASED
        )
        
        logger.info("Script-based prompt:")
        logger.info("=" * 50)
        logger.info(prompt)
        logger.info("=" * 50)
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def demo_custom_templates():
    """Demonstrate custom template creation and usage."""
    logger.info("=== Custom Templates Demo ===")
    
    manager = await create_prompt_manager(config_manager=Mock())
    
    try:
        # Create custom template
        custom_template = create_simple_template(
            name="loan_specialist",
            template_text="""你是一个专业的贷款顾问，专门为客户提供贷款咨询服务。

## 专业背景
- 5年以上贷款行业经验
- 熟悉各类贷款产品和政策
- 擅长为客户量身定制贷款方案

## 当前客户信息
{customer_info}

## 对话历史
{conversation_history}

## 客户问题
{user_query}

请基于你的专业知识，为客户提供详细、准确的贷款建议。重点关注：
1. 客户的具体需求
2. 适合的贷款产品
3. 申请流程和注意事项
4. 优惠政策和活动信息""",
            prompt_type=PromptType.CUSTOMER_SERVICE,
            required_vars=["user_query"]
        )
        
        # Add custom template
        manager.add_template(custom_template)
        
        # Create context
        context = create_customer_service_context(
            user_query="我需要50万的贷款用于装修，有什么好的产品推荐？",
            customer_name="张先生",
            customer_level="VIP",
            conversation_history=[
                {"user": "你好", "assistant": "您好！我是您的专属贷款顾问。"}
            ]
        )
        
        # Generate prompt using custom template
        prompt = manager.generate_prompt(
            context,
            template_name="loan_specialist"
        )
        
        logger.info("Custom template prompt:")
        logger.info("=" * 50)
        logger.info(prompt)
        logger.info("=" * 50)
        
        # Show template usage statistics
        stats = manager.get_prompt_statistics()
        logger.info(f"Template usage: {stats['template_usage']}")
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def demo_prompt_optimization():
    """Demonstrate prompt optimization features."""
    logger.info("=== Prompt Optimization Demo ===")
    
    # Create manager with optimization settings
    config = PromptManagerConfig(
        max_prompt_length=500,  # Short limit for demo
        enable_prompt_compression=True,
        enable_template_caching=True
    )
    
    manager = PromptManager(config, Mock())
    await manager.initialize()
    await manager.start()
    
    try:
        # Create context with long history (will trigger compression)
        long_history = [
            ConversationTurn(
                user_input=f"这是第{i}个问题，内容比较长，用来测试提示压缩功能。",
                assistant_response=f"这是第{i}个回答，同样比较长，包含了详细的解释和说明。",
                source="script"
            )
            for i in range(1, 8)
        ]
        
        context = PromptContext(
            user_query="请问现在有什么优惠活动吗？",
            conversation_history=long_history,
            customer_info={"name": "赵先生", "customer_level": "VIP"}
        )
        
        # Generate prompt (should be compressed)
        prompt = manager.generate_prompt(context)
        
        logger.info(f"Compressed prompt length: {len(prompt)} characters")
        logger.info("Compressed prompt:")
        logger.info("=" * 50)
        logger.info(prompt)
        logger.info("=" * 50)
        
        # Test caching by generating same prompt again
        prompt2 = manager.generate_prompt(context)
        assert prompt == prompt2
        
        stats = manager.get_prompt_statistics()
        logger.info(f"Cache hit rate: {stats['cache_hit_rate']:.2%}")
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def demo_system_prompt_generation():
    """Demonstrate system prompt generation."""
    logger.info("=== System Prompt Generation Demo ===")
    
    manager = await create_prompt_manager(config_manager=Mock())
    
    try:
        # Generate system prompt
        system_prompt = manager.generate_system_prompt(
            role_description="专业的银行贷款顾问",
            service_principles=[
                "始终以客户利益为先",
                "提供准确、及时的信息",
                "保护客户隐私和数据安全",
                "主动了解客户需求，提供个性化建议"
            ],
            additional_context="当前正在进行春季贷款优惠活动，利率最低可享受8.5折优惠。"
        )
        
        logger.info("Generated system prompt:")
        logger.info("=" * 50)
        logger.info(system_prompt)
        logger.info("=" * 50)
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def demo_template_management():
    """Demonstrate template management operations."""
    logger.info("=== Template Management Demo ===")
    
    manager = await create_prompt_manager(config_manager=Mock())
    
    try:
        # List available templates
        templates = manager.list_templates()
        logger.info(f"Available templates: {templates}")
        
        # Get templates by type
        cs_templates = manager.get_templates_by_type(PromptType.CUSTOMER_SERVICE)
        logger.info(f"Customer service templates: {[t.name for t in cs_templates]}")
        
        # Create and add new template
        new_template = PromptTemplate(
            name="quick_response",
            prompt_type=PromptType.CUSTOMER_SERVICE,
            template="快速回复：{user_query}\n\n请提供简洁的回答。",
            required_variables=["user_query"],
            description="Quick response template for simple queries"
        )
        
        manager.add_template(new_template)
        logger.info(f"Added template: {new_template.name}")
        
        # Test the new template
        context = PromptContext(user_query="营业时间是什么时候？")
        prompt = manager.generate_prompt(context, template_name="quick_response")
        
        logger.info("Quick response prompt:")
        logger.info(prompt)
        
        # Show final statistics
        stats = manager.get_prompt_statistics()
        logger.info(f"Final statistics: {stats}")
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def main():
    """Run all demos."""
    logger.info("Starting Prompt Manager Demo")
    
    try:
        await demo_basic_prompt_generation()
        await demo_conversation_history()
        await demo_script_based_prompts()
        await demo_custom_templates()
        await demo_prompt_optimization()
        await demo_system_prompt_generation()
        await demo_template_management()
        
        logger.info("All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())