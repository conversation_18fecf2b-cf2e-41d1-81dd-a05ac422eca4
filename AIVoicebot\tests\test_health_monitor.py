"""
Tests for health monitoring and recovery system.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock
import time
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.health_monitor import (
    SystemHealthMonitor, HealthCheckProvider, ComponentHealthChecker,
    HealthStatus, RecoveryAction, HealthCheckResult, RecoveryPlan,
    RecoveryAttempt, create_http_health_endpoint
)
from src.core.base_component import BaseComponent


class MockComponent(BaseComponent):
    """Mock component for testing."""
    
    def __init__(self, name: str, config_manager=None):
        super().__init__(name, config_manager or Mock(), Mock())
        self.should_fail = False
        self.fail_count = 0
        self.recovery_count = 0
    
    async def _initialize_impl(self) -> None:
        pass
    
    async def _start_impl(self) -> None:
        pass
    
    async def _stop_impl(self) -> None:
        pass
    
    async def _cleanup_impl(self) -> None:
        pass
    
    async def get_metrics(self):
        """Mock metrics method."""
        if self.should_fail:
            self.fail_count += 1
            raise Exception(f"Mock component failure #{self.fail_count}")
        return {"status": "ok", "requests": 100}
    
    async def reconnect(self):
        """Mock reconnect method."""
        self.recovery_count += 1
        self.should_fail = False
        return True
    
    async def clear_cache(self):
        """Mock cache clear method."""
        self.recovery_count += 1
        return True


class MockHealthProvider(HealthCheckProvider):
    """Mock health check provider."""
    
    def __init__(self, component_name: str):
        self.component_name = component_name
        self.should_fail = False
        self.response_time = 100.0
        self.recovery_success = True
    
    async def check_health(self) -> HealthCheckResult:
        """Mock health check."""
        await asyncio.sleep(self.response_time / 1000)  # Simulate response time
        
        if self.should_fail:
            return HealthCheckResult(
                component_name=self.component_name,
                status=HealthStatus.FAILED,
                response_time_ms=self.response_time,
                error_message="Mock failure",
                recovery_suggestions=["Restart component"]
            )
        else:
            return HealthCheckResult(
                component_name=self.component_name,
                status=HealthStatus.HEALTHY,
                response_time_ms=self.response_time,
                details={"mock": "data"}
            )
    
    def get_component_name(self) -> str:
        """Get component name."""
        return self.component_name
    
    async def recover(self, action: RecoveryAction) -> bool:
        """Mock recovery."""
        if action == RecoveryAction.RESTART:
            self.should_fail = False
            return self.recovery_success
        return False


class TestHealthMonitor:
    """Test health monitoring system."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        config_manager = Mock()
        config_manager.get_config.side_effect = lambda key, default: {
            "health.check_interval_seconds": 1,  # Fast for testing
            "health.recovery_enabled": True,
            "health.max_recovery_attempts": 3,
            "health.escalation_threshold": 5
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def health_monitor(self, mock_config_manager):
        """Create health monitor instance."""
        return SystemHealthMonitor(mock_config_manager)
    
    @pytest.fixture
    def mock_component(self, mock_config_manager):
        """Create mock component."""
        return MockComponent("test_component", mock_config_manager)
    
    @pytest.mark.asyncio
    async def test_health_monitor_lifecycle(self, health_monitor):
        """Test health monitor lifecycle."""
        await health_monitor.initialize()
        await health_monitor.start()
        
        assert health_monitor.is_initialized
        assert health_monitor.is_running
        
        await health_monitor.stop()
        await health_monitor.cleanup()
    
    @pytest.mark.asyncio
    async def test_component_registration(self, health_monitor, mock_component):
        """Test component registration."""
        await health_monitor.initialize()
        
        # Register component
        health_monitor.register_component(mock_component)
        
        assert "test_component" in health_monitor.health_providers
        assert "test_component" in health_monitor.recovery_plans
        
        # Check that health provider was created
        provider = health_monitor.health_providers["test_component"]
        assert isinstance(provider, ComponentHealthChecker)
        assert provider.get_component_name() == "test_component"
    
    @pytest.mark.asyncio
    async def test_health_check_provider_registration(self, health_monitor):
        """Test custom health provider registration."""
        await health_monitor.initialize()
        
        provider = MockHealthProvider("mock_service")
        health_monitor.register_health_provider(provider)
        
        assert "mock_service" in health_monitor.health_providers
        assert health_monitor.health_providers["mock_service"] == provider
    
    @pytest.mark.asyncio
    async def test_component_health_check(self, health_monitor, mock_component):
        """Test component health checking."""
        await health_monitor.initialize()
        await mock_component.initialize()
        await mock_component.start()
        
        health_monitor.register_component(mock_component)
        
        # Perform health check
        result = await health_monitor.check_component_health("test_component")
        
        assert result is not None
        assert result.component_name == "test_component"
        assert result.status == HealthStatus.HEALTHY
        assert result.response_time_ms >= 0
        
        # Check that result was stored
        assert "test_component" in health_monitor.current_health
        assert len(health_monitor.health_history["test_component"]) == 1
    
    @pytest.mark.asyncio
    async def test_failed_component_health_check(self, health_monitor, mock_component):
        """Test health check of failed component."""
        await health_monitor.initialize()
        await mock_component.initialize()
        await mock_component.start()
        
        # Make component fail
        mock_component.should_fail = True
        
        health_monitor.register_component(mock_component)
        
        # Perform health check
        result = await health_monitor.check_component_health("test_component")
        
        assert result is not None
        assert result.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert result.error_message is not None
        assert len(result.recovery_suggestions) > 0
    
    @pytest.mark.asyncio
    async def test_all_components_health_check(self, health_monitor):
        """Test checking all components."""
        await health_monitor.initialize()
        
        # Register multiple providers
        provider1 = MockHealthProvider("service1")
        provider2 = MockHealthProvider("service2")
        provider2.should_fail = True  # Make one fail
        
        health_monitor.register_health_provider(provider1)
        health_monitor.register_health_provider(provider2)
        
        # Check all components
        results = await health_monitor.check_all_components()
        
        assert len(results) == 2
        assert "service1" in results
        assert "service2" in results
        
        assert results["service1"].status == HealthStatus.HEALTHY
        assert results["service2"].status == HealthStatus.FAILED
    
    def test_system_health_summary(self, health_monitor):
        """Test system health summary."""
        # Add some mock health results
        health_monitor.current_health["service1"] = HealthCheckResult(
            component_name="service1",
            status=HealthStatus.HEALTHY
        )
        health_monitor.current_health["service2"] = HealthCheckResult(
            component_name="service2",
            status=HealthStatus.FAILED
        )
        
        summary = health_monitor.get_system_health_summary()
        
        assert "overall_status" in summary
        assert "component_count" in summary
        assert "healthy_components" in summary
        assert "failed_components" in summary
        assert "components" in summary
        
        assert summary["component_count"] == 2
        assert summary["healthy_components"] == 1
        assert summary["failed_components"] == 1
        assert summary["overall_status"] == HealthStatus.FAILED.value
    
    @pytest.mark.asyncio
    async def test_component_recovery(self, health_monitor):
        """Test component recovery."""
        await health_monitor.initialize()
        
        provider = MockHealthProvider("test_service")
        provider.should_fail = True
        
        health_monitor.register_health_provider(provider)
        
        # Attempt recovery
        success = await health_monitor.recover_component("test_service")
        
        assert success
        assert not provider.should_fail  # Should be fixed after recovery
        
        # Check recovery was recorded
        assert "test_service" in health_monitor.recovery_attempts
        assert len(health_monitor.recovery_attempts["test_service"]) > 0
        
        attempt = health_monitor.recovery_attempts["test_service"][0]
        assert attempt.component_name == "test_service"
        assert attempt.success
    
    @pytest.mark.asyncio
    async def test_failed_recovery(self, health_monitor):
        """Test failed recovery attempt."""
        await health_monitor.initialize()
        
        provider = MockHealthProvider("test_service")
        provider.should_fail = True
        provider.recovery_success = False  # Make recovery fail
        
        health_monitor.register_health_provider(provider)
        
        # Attempt recovery
        success = await health_monitor.recover_component("test_service")
        
        assert not success
        
        # Check recovery attempt was recorded
        attempts = health_monitor.recovery_attempts["test_service"]
        assert len(attempts) > 0
        assert not attempts[0].success
    
    def test_dependency_management(self, health_monitor):
        """Test dependency management."""
        # Add dependencies
        health_monitor.add_dependency("service_a", "service_b")
        health_monitor.add_dependency("service_a", "service_c")
        
        assert "service_b" in health_monitor.dependencies["service_a"]
        assert "service_c" in health_monitor.dependencies["service_a"]
        
        # Test dependency order
        health_monitor.health_providers["service_a"] = MockHealthProvider("service_a")
        health_monitor.health_providers["service_b"] = MockHealthProvider("service_b")
        health_monitor.health_providers["service_c"] = MockHealthProvider("service_c")
        
        order = health_monitor._get_dependency_order()
        
        # Dependencies should come before dependents
        assert order.index("service_b") < order.index("service_a")
        assert order.index("service_c") < order.index("service_a")
    
    def test_component_diagnosis(self, health_monitor):
        """Test component diagnosis."""
        # Set up component with health data
        component_name = "test_service"

        # Register health provider first
        provider = MockHealthProvider(component_name)
        health_monitor.register_health_provider(provider)

        # Add current health
        health_monitor.current_health[component_name] = HealthCheckResult(
            component_name=component_name,
            status=HealthStatus.CRITICAL,
            error_message="Service unavailable",
            recovery_suggestions=["Restart service"]
        )
        
        # Add health history
        for i in range(5):
            health_monitor.health_history[component_name].append(
                HealthCheckResult(
                    component_name=component_name,
                    status=HealthStatus.FAILED if i < 3 else HealthStatus.HEALTHY
                )
            )
        
        # Add recovery attempts
        health_monitor.recovery_attempts[component_name] = [
            RecoveryAttempt(
                component_name=component_name,
                action=RecoveryAction.RESTART,
                attempt_number=1,
                success=False
            )
        ]
        
        diagnosis = health_monitor.diagnose_component(component_name)
        
        assert diagnosis["component_name"] == component_name
        assert diagnosis["current_status"] == HealthStatus.CRITICAL.value
        assert diagnosis["failure_rate_last_hour"] > 0
        assert len(diagnosis["diagnosis"]) > 0
        assert "critical state" in " ".join(diagnosis["diagnosis"]).lower()
    
    def test_system_diagnostics(self, health_monitor):
        """Test system diagnostics."""
        # Add some test data
        health_monitor.current_health["service1"] = HealthCheckResult(
            component_name="service1",
            status=HealthStatus.HEALTHY
        )
        health_monitor.health_providers["service1"] = MockHealthProvider("service1")
        
        diagnostics = health_monitor.get_system_diagnostics()
        
        assert "system_health" in diagnostics
        assert "component_diagnostics" in diagnostics
        assert "dependency_graph" in diagnostics
        assert "recovery_statistics" in diagnostics
        assert "recommendations" in diagnostics
        
        assert "service1" in diagnostics["component_diagnostics"]
    
    @pytest.mark.asyncio
    async def test_http_health_endpoint(self, health_monitor):
        """Test HTTP health endpoint creation."""
        await health_monitor.initialize()
        
        # Add a healthy component
        health_monitor.current_health["service1"] = HealthCheckResult(
            component_name="service1",
            status=HealthStatus.HEALTHY
        )
        
        endpoint = create_http_health_endpoint(health_monitor)
        response = await endpoint()
        
        assert response["status_code"] == 200
        assert "overall_status" in response["body"]
        assert response["headers"]["Content-Type"] == "application/json"


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestHealthMonitor().test_health_monitor_lifecycle(SystemHealthMonitor(Mock())))
