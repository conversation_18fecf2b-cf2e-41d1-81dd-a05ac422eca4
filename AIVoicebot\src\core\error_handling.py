"""
Comprehensive Error Handling Framework

This module provides a comprehensive error handling framework including:
- Error classification and categorization
- Circuit breaker pattern for external API calls
- Graceful degradation strategies for component failures
- Error recovery mechanisms
- Centralized error reporting and monitoring
"""

import asyncio
import logging
import time
import traceback
from typing import Dict, Any, Optional, List, Callable, Union, Type
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod
import uuid
from collections import defaultdict, deque

from .interfaces import AIVoiceServiceError


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    TIMEOUT = "timeout"
    VALIDATION = "validation"
    RESOURCE_EXHAUSTION = "resource_exhaustion"
    EXTERNAL_SERVICE = "external_service"
    INTERNAL_LOGIC = "internal_logic"
    CONFIGURATION = "configuration"
    HARDWARE = "hardware"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAK = "circuit_break"
    GRACEFUL_DEGRADE = "graceful_degrade"
    ESCALATE = "escalate"
    IGNORE = "ignore"


@dataclass
class ErrorContext:
    """Context information for an error."""
    error_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    component: str = ""
    operation: str = ""
    user_session_id: Optional[str] = None
    correlation_id: Optional[str] = None
    request_data: Dict[str, Any] = field(default_factory=dict)
    system_state: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "error_id": self.error_id,
            "timestamp": self.timestamp.isoformat(),
            "component": self.component,
            "operation": self.operation,
            "user_session_id": self.user_session_id,
            "correlation_id": self.correlation_id,
            "request_data": self.request_data,
            "system_state": self.system_state
        }


@dataclass
class ErrorInfo:
    """Comprehensive error information."""
    error_type: str
    error_message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    stack_trace: Optional[str] = None
    recovery_strategy: Optional[RecoveryStrategy] = None
    retry_count: int = 0
    max_retries: int = 3
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "error_type": self.error_type,
            "error_message": self.error_message,
            "severity": self.severity.value,
            "category": self.category.value,
            "context": self.context.to_dict(),
            "stack_trace": self.stack_trace,
            "recovery_strategy": self.recovery_strategy.value if self.recovery_strategy else None,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "resolved": self.resolved,
            "resolution_time": self.resolution_time.isoformat() if self.resolution_time else None
        }


# Specific error types for better classification
class NetworkError(AIVoiceServiceError):
    """Network-related errors."""
    pass


class TimeoutError(AIVoiceServiceError):
    """Timeout errors."""
    pass


class RateLimitError(AIVoiceServiceError):
    """Rate limiting errors."""
    pass


class AuthenticationError(AIVoiceServiceError):
    """Authentication errors."""
    pass


class ValidationError(AIVoiceServiceError):
    """Input validation errors."""
    pass


class ResourceExhaustionError(AIVoiceServiceError):
    """Resource exhaustion errors."""
    pass


class ExternalServiceError(AIVoiceServiceError):
    """External service errors."""
    pass


class InternalLogicError(AIVoiceServiceError):
    """Internal logic errors."""
    pass


class ConfigurationError(AIVoiceServiceError):
    """Configuration errors."""
    pass


class HardwareError(AIVoiceServiceError):
    """Hardware-related errors."""
    pass


class CircuitBreakerOpenError(AIVoiceServiceError):
    """Raised when circuit breaker is open."""
    pass


class RetryableError(AIVoiceServiceError):
    """Base class for errors that can be retried."""
    pass


class NonRetryableError(AIVoiceServiceError):
    """Base class for errors that should not be retried."""
    pass


class ErrorClassifier:
    """Classifies errors and determines appropriate recovery strategies."""
    
    def __init__(self):
        self.classification_rules = {
            # Network-related errors
            "ConnectionError": (ErrorCategory.NETWORK, ErrorSeverity.HIGH, RecoveryStrategy.RETRY),
            "TimeoutError": (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY),
            "HTTPError": (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY),
            "NetworkError": (ErrorCategory.NETWORK, ErrorSeverity.HIGH, RecoveryStrategy.RETRY),
            
            # Authentication errors
            "AuthenticationError": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, RecoveryStrategy.ESCALATE),
            "PermissionError": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, RecoveryStrategy.ESCALATE),
            
            # Rate limiting
            "RateLimitError": (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY),
            "TooManyRequestsError": (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY),
            
            # Resource exhaustion
            "MemoryError": (ErrorCategory.RESOURCE_EXHAUSTION, ErrorSeverity.CRITICAL, RecoveryStrategy.GRACEFUL_DEGRADE),
            "DiskSpaceError": (ErrorCategory.RESOURCE_EXHAUSTION, ErrorSeverity.HIGH, RecoveryStrategy.GRACEFUL_DEGRADE),
            
            # Validation errors
            "ValidationError": (ErrorCategory.VALIDATION, ErrorSeverity.LOW, RecoveryStrategy.ESCALATE),
            "ValueError": (ErrorCategory.VALIDATION, ErrorSeverity.LOW, RecoveryStrategy.ESCALATE),
            
            # Configuration errors
            "ConfigurationError": (ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH, RecoveryStrategy.ESCALATE),
            "KeyError": (ErrorCategory.CONFIGURATION, ErrorSeverity.MEDIUM, RecoveryStrategy.FALLBACK),
            
            # External service errors
            "ExternalServiceError": (ErrorCategory.EXTERNAL_SERVICE, ErrorSeverity.MEDIUM, RecoveryStrategy.CIRCUIT_BREAK),
            "ServiceUnavailableError": (ErrorCategory.EXTERNAL_SERVICE, ErrorSeverity.HIGH, RecoveryStrategy.CIRCUIT_BREAK),
        }
    
    def classify_error(self, error: Exception, context: Optional[ErrorContext] = None) -> ErrorInfo:
        """Classify an error and determine recovery strategy."""
        error_type = type(error).__name__
        error_message = str(error)
        
        # Get classification from rules
        if error_type in self.classification_rules:
            category, severity, recovery_strategy = self.classification_rules[error_type]
        else:
            # Default classification for unknown errors
            category = ErrorCategory.INTERNAL_LOGIC
            severity = ErrorSeverity.MEDIUM
            recovery_strategy = RecoveryStrategy.ESCALATE
        
        # Create error context if not provided
        if context is None:
            context = ErrorContext()
        
        # Get stack trace
        stack_trace = traceback.format_exc()
        
        return ErrorInfo(
            error_type=error_type,
            error_message=error_message,
            severity=severity,
            category=category,
            context=context,
            stack_trace=stack_trace,
            recovery_strategy=recovery_strategy
        )
    
    def add_classification_rule(
        self,
        error_type: str,
        category: ErrorCategory,
        severity: ErrorSeverity,
        recovery_strategy: RecoveryStrategy
    ):
        """Add a custom classification rule."""
        self.classification_rules[error_type] = (category, severity, recovery_strategy)


# Simple ErrorHandler for testing
class ErrorHandler:
    """Basic error handler for testing."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.classifier = ErrorClassifier()
        self.error_history: deque = deque(maxlen=1000)
        self.component_health: Dict[str, Dict[str, Any]] = defaultdict(dict)
    
    async def handle_error(
        self,
        error: Exception,
        component: str,
        operation: str,
        context: Optional[ErrorContext] = None,
        user_session_id: Optional[str] = None
    ) -> Optional[Any]:
        """Handle an error with basic recovery."""
        if context is None:
            context = ErrorContext(
                component=component,
                operation=operation,
                user_session_id=user_session_id
            )
        
        error_info = self.classifier.classify_error(error, context)
        self.error_history.append(error_info)
        
        # Update component health
        if component not in self.component_health:
            self.component_health[component] = {
                "status": "healthy",
                "error_count": 0,
                "last_error_time": None
            }
        
        self.component_health[component]["error_count"] += 1
        self.component_health[component]["last_error_time"] = error_info.context.timestamp
        
        # Determine component health status
        error_count = self.component_health[component]["error_count"]
        if error_count >= 10:
            self.component_health[component]["status"] = "critical"
        elif error_count >= 5:
            self.component_health[component]["status"] = "degraded"
        elif error_count >= 2:
            self.component_health[component]["status"] = "warning"
        
        self.logger.error(f"Error in {component}.{operation}: {error_info.error_message}")
        return None
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get basic error statistics."""
        recent_errors = [
            error for error in self.error_history
            if (datetime.now() - error.context.timestamp).total_seconds() < 3600
        ]
        
        component_errors = defaultdict(int)
        for error in recent_errors:
            component_errors[error.context.component] += 1
        
        return {
            "total_errors_last_hour": len(recent_errors),
            "component_error_counts": dict(component_errors),
            "component_health": dict(self.component_health),
            "total_error_history": len(self.error_history)
        }
    
    def reset_component_health(self, component: str):
        """Reset health status for a component."""
        if component in self.component_health:
            self.component_health[component] = {
                "status": "healthy",
                "error_count": 0,
                "last_error_time": None
            }
