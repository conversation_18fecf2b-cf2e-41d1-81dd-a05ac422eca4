"""
Voice Processor
===============

This module coordinates ASR and TTS services to provide complete voice processing
capabilities for the AI Voice Customer Service System.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass

from ...core.base_component import BaseComponent
from .asr_service import ASRService, ASRConfig, ASRResult
from .tts_service import TTSService, TTSConfig, TTSResult

@dataclass
class VoiceProcessorConfig:
    """Configuration for voice processor."""
    asr_config: ASRConfig
    tts_config: TTSConfig
    enable_vad: bool = True  # Voice Activity Detection
    silence_timeout: float = 2.0
    max_audio_length: float = 30.0

class VoiceProcessor(BaseComponent):
    """Voice processing coordinator that manages ASR and TTS services."""
    
    def __init__(self, config: VoiceProcessorConfig, logger: Optional[logging.Logger] = None):
        super().__init__("voice_processor", logger)
        self.config = config
        
        # Initialize ASR and TTS services
        self.asr_service = ASRService(config.asr_config, logger)
        self.tts_service = TTSService(config.tts_config, logger)
        
        # Conversation callback
        self.conversation_callback: Optional[Callable] = None
        
        self._log.info("Voice Processor initialized")
    
    # Implement abstract methods from BaseComponent
    async def _initialize_impl(self) -> bool:
        """Implementation of initialization logic."""
        try:
            # Initialize ASR service
            await self.asr_service.initialize()
            if not self.asr_service.is_initialized:
                self._log.error("Failed to initialize ASR service")
                return False

            # Initialize TTS service
            await self.tts_service.initialize()
            if not self.tts_service.is_initialized:
                self._log.error("Failed to initialize TTS service")
                return False

            self._log.info("Voice Processor initialized successfully")
            return True

        except Exception as e:
            self._log.error(f"Failed to initialize Voice Processor: {e}")
            return False

    async def _start_impl(self) -> bool:
        """Implementation of start logic."""
        # Start services
        await self.asr_service.start()
        await self.tts_service.start()

        self._log.info("Voice Processor started")
        return True

    async def _stop_impl(self) -> bool:
        """Implementation of stop logic."""
        # Stop services
        await self.asr_service.stop()
        await self.tts_service.stop()

        self._log.info("Voice Processor stopped")
        return True
    
    async def process_audio_input(self, audio_data: bytes, sample_rate: int = 16000) -> ASRResult:
        """
        Process audio input through ASR.
        
        Args:
            audio_data: Raw audio bytes
            sample_rate: Sample rate of the audio
            
        Returns:
            ASRResult with recognition results
        """
        if not self.is_running:
            raise RuntimeError("Voice Processor is not running")
        
        try:
            result = await self.asr_service.recognize_speech_from_audio_data(audio_data, sample_rate)
            self._log.info(f"ASR result: '{result.text}' (confidence: {result.confidence:.2f})")
            return result
            
        except Exception as e:
            self._log.error(f"Audio input processing failed: {e}")
            raise
    
    async def generate_speech_output(self, text: str) -> TTSResult:
        """
        Generate speech output from text.
        
        Args:
            text: Text to convert to speech
            
        Returns:
            TTSResult with audio data and metadata
        """
        if not self.is_running:
            raise RuntimeError("Voice Processor is not running")
        
        try:
            result = await self.tts_service.synthesize_speech(text)
            self._log.info(f"TTS result: {len(result.audio_data)} bytes, {result.duration:.2f}s duration")
            return result
            
        except Exception as e:
            self._log.error(f"Speech output generation failed: {e}")
            raise
    
    async def process_conversation_turn(self, audio_input: bytes, sample_rate: int = 16000) -> TTSResult:
        """
        Process a complete conversation turn: ASR -> Conversation -> TTS.
        
        Args:
            audio_input: Raw audio bytes from user
            sample_rate: Sample rate of the audio
            
        Returns:
            TTSResult with system response audio
        """
        try:
            # Step 1: Convert speech to text
            asr_result = await self.process_audio_input(audio_input, sample_rate)
            
            if not asr_result.text.strip():
                # No speech detected, return silence or prompt
                return await self.generate_speech_output("I'm sorry, I didn't catch that. Could you please repeat?")
            
            # Step 2: Process conversation (would integrate with conversation engine)
            if self.conversation_callback:
                response_text = await self.conversation_callback(asr_result.text)
            else:
                # Default response for testing
                response_text = f"I heard you say: {asr_result.text}. How can I help you with that?"
            
            # Step 3: Convert response to speech
            tts_result = await self.generate_speech_output(response_text)
            
            self._log.info(f"Conversation turn completed: '{asr_result.text}' -> '{response_text}'")
            return tts_result
            
        except Exception as e:
            self._log.error(f"Conversation turn processing failed: {e}")
            # Return error message as speech
            return await self.generate_speech_output("I'm sorry, I encountered an error. Please try again.")
    
    async def listen_and_respond(self, duration: Optional[float] = None) -> TTSResult:
        """
        Listen from microphone and generate response.
        
        Args:
            duration: Maximum listening duration
            
        Returns:
            TTSResult with system response
        """
        try:
            # Listen from microphone
            asr_result = await self.asr_service.recognize_from_microphone(duration)
            
            if not asr_result.text.strip():
                return await self.generate_speech_output("I'm listening. Please speak.")
            
            # Process conversation
            if self.conversation_callback:
                response_text = await self.conversation_callback(asr_result.text)
            else:
                response_text = f"You said: {asr_result.text}. Thank you for your input."
            
            # Generate response
            return await self.generate_speech_output(response_text)
            
        except Exception as e:
            self._log.error(f"Listen and respond failed: {e}")
            return await self.generate_speech_output("I'm sorry, I had trouble hearing you. Please try again.")
    
    def set_conversation_callback(self, callback: Callable[[str], str]):
        """
        Set callback function for processing conversation.
        
        Args:
            callback: Async function that takes user text and returns system response
        """
        self.conversation_callback = callback
        self._log.info("Conversation callback set")
    
    async def test_voice_loop(self, iterations: int = 3) -> List[Dict[str, Any]]:
        """
        Test the complete voice processing loop.
        
        Args:
            iterations: Number of test iterations
            
        Returns:
            List of test results
        """
        results = []
        
        test_phrases = [
            "Hello, I need help with my account",
            "Can you check my balance please?",
            "Thank you for your assistance"
        ]
        
        for i in range(min(iterations, len(test_phrases))):
            phrase = test_phrases[i]
            
            try:
                # Simulate audio input (in real implementation, this would be actual audio)
                simulated_audio = phrase.encode('utf-8')  # Placeholder
                
                # Process the conversation turn
                start_time = asyncio.get_event_loop().time()
                
                # For testing, we'll just simulate the ASR result
                asr_result = ASRResult(
                    text=phrase,
                    confidence=0.95,
                    language=self.config.asr_config.language,
                    processing_time=0.1,
                    engine_used=self.config.asr_config.engine.value
                )
                
                # Generate TTS response
                response_text = f"I understand you said: {phrase}. How can I help you further?"
                tts_result = await self.generate_speech_output(response_text)
                
                end_time = asyncio.get_event_loop().time()
                total_time = end_time - start_time
                
                result = {
                    "iteration": i + 1,
                    "input_text": phrase,
                    "asr_confidence": asr_result.confidence,
                    "asr_time": asr_result.processing_time,
                    "response_text": response_text,
                    "tts_time": tts_result.processing_time,
                    "total_time": total_time,
                    "success": True
                }
                
                results.append(result)
                self._log.info(f"Voice loop test {i+1} completed successfully")
                
            except Exception as e:
                self._log.error(f"Voice loop test {i+1} failed: {e}")
                results.append({
                    "iteration": i + 1,
                    "error": str(e),
                    "success": False
                })
        
        return results
    
    async def _cleanup_impl(self) -> bool:
        """Implementation of cleanup logic."""
        await self.asr_service.cleanup()
        await self.tts_service.cleanup()
        self._log.info("Voice Processor cleanup completed")
        return True
    
    def get_processor_info(self) -> Dict[str, Any]:
        """Get information about the voice processor."""
        return {
            "asr_engine": self.asr_service.get_engine_info(),
            "tts_engine": self.tts_service.get_engine_info(),
            "config": {
                "enable_vad": self.config.enable_vad,
                "silence_timeout": self.config.silence_timeout,
                "max_audio_length": self.config.max_audio_length
            },
            "status": {
                "is_initialized": self.is_initialized,
                "is_running": self.is_running,
                "asr_running": self.asr_service.is_running,
                "tts_running": self.tts_service.is_running
            }
        }
