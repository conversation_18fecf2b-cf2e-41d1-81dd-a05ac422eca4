{"dashboard": {"id": null, "title": "AI Voice Customer Service - Production Dashboard", "tags": ["ai-voice", "customer-service", "production"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "aivoice_system_status", "legendFormat": "System Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "Active Calls", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "aivoice_active_calls", "legendFormat": "Active Calls"}], "yAxes": [{"label": "Count", "min": 0}]}, {"id": 3, "title": "Call Success Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(aivoice_calls_completed_total[5m]) / rate(aivoice_calls_started_total[5m]) * 100", "legendFormat": "Success Rate %"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}]}, {"id": 4, "title": "Response Time", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "histogram_quantile(0.50, aivoice_response_time_seconds_bucket)", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, aivoice_response_time_seconds_bucket)", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, aivoice_response_time_seconds_bucket)", "legendFormat": "99th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}]}, {"id": 5, "title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "rate(aivoice_errors_total[5m])", "legendFormat": "Error Rate"}], "yAxes": [{"label": "Errors/sec", "min": 0}]}, {"id": 6, "title": "CPU Usage", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}, "targets": [{"expr": "aivoice_cpu_utilization", "legendFormat": "CPU %"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}]}, {"id": 7, "title": "Memory Usage", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}, "targets": [{"expr": "aivoice_memory_utilization", "legendFormat": "Memory %"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}]}, {"id": 8, "title": "Audio Processing Latency", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}, "targets": [{"expr": "aivoice_audio_processing_latency_ms", "legendFormat": "Audio Latency"}], "yAxes": [{"label": "Milliseconds", "min": 0}]}, {"id": 9, "title": "Component Health", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "aivoice_component_health", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"component": "Component", "Value": "Health Status"}}}]}, {"id": 10, "title": "Active Alerts", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "aivoice_active_alerts", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"alert_name": "<PERSON><PERSON>", "severity": "Severity", "Value": "Status"}}}]}, {"id": 11, "title": "Model Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "targets": [{"expr": "aivoice_asr_accuracy", "legendFormat": "ASR Accuracy"}, {"expr": "aivoice_llm_response_quality", "legendFormat": "LLM Quality"}, {"expr": "aivoice_tts_naturalness", "legendFormat": "TTS Naturalness"}], "yAxes": [{"label": "Score", "min": 0, "max": 1}]}, {"id": 12, "title": "Throughput", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "targets": [{"expr": "rate(aivoice_requests_total[5m])", "legendFormat": "Requests/sec"}, {"expr": "rate(aivoice_audio_chunks_processed_total[5m])", "legendFormat": "Audio Chunks/sec"}], "yAxes": [{"label": "Rate", "min": 0}]}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(aivoice_system_status, instance)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "expr": "aivoice_deployment_event", "titleFormat": "Deployment", "textFormat": "Version: {{version}}"}]}}}