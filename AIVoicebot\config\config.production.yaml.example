# Production environment configuration example
# Copy this file to config.production.yaml and customize for your production environment
# DO NOT commit the actual production config file to version control

# Production logging - structured JSON for log aggregation
logging:
  level: INFO
  enable_console: false  # Disable console in production
  enable_file: true
  enable_json: true     # Use JSON format for log aggregation
  max_file_size: 52428800  # 50MB
  backup_count: 10

# Production audio settings - optimized for performance
audio:
  chunk_size: 2048      # Larger chunks for better performance
  buffer_size: 8192
  format: pcm_16khz_mono

# Production model settings
models:
  device: cuda          # Use GPU in production if available
  batch_size: 4         # Larger batch size for better throughput

# Production Qwen settings
qwen:
  api_key: ""           # Set via AIVOICE_QWEN_API_KEY environment variable
  timeout: 20
  max_retries: 5
  temperature: 0.6      # More conservative for production

# Production EdgeTTS settings
edge_tts:
  voice: zh-CN-XiaoxiaoNeural
  rate: "+0%"
  pitch: "+0Hz"
  volume: "+0%"
  timeout: 20

# Production telephony settings
telephony:
  provider: production_provider  # Configure your actual telephony provider
  config_path: config/telephony.production.yaml
  max_concurrent_calls: 50      # Scale based on your infrastructure
  call_timeout: 600             # 10 minutes

# Production conversation settings
conversation:
  max_conversation_turns: 100
  response_timeout: 20
  
  # Less frequent script reloading in production
  auto_reload_scripts: true
  script_check_interval: 300    # Check every 5 minutes

# Production performance settings - optimized for scale
performance:
  # Larger buffers for production load
  max_audio_buffer_size: 2097152  # 2MB
  audio_processing_threads: 4
  
  # Optimized VAD settings
  vad_threshold: 0.6
  vad_window_size: 1024
  
  # Production speech settings
  speech_timeout: 2.0
  silence_timeout: 0.8
  min_speech_duration: 0.2
  
  # Production response timeouts
  llm_timeout: 12.0
  tts_timeout: 8.0
  
  # Production memory management
  max_session_history: 200
  cleanup_interval: 600         # 10 minutes

# Production monitoring - comprehensive metrics
monitoring:
  enable_metrics: true
  metrics_port: 8080
  health_check_interval: 60
  
  # Strict production thresholds
  max_response_time: 3.0
  max_error_rate: 0.01          # 1% error rate threshold
  
  # Additional production monitoring
  enable_alerting: true
  alert_webhook_url: ""         # Set your alerting webhook
  
# Production settings
development:
  debug_mode: false
  mock_telephony: false
  mock_models: false
  save_audio_files: false       # Don't save audio in production

# Production security settings
security:
  # Production rate limiting
  rate_limit_calls_per_minute: 30
  rate_limit_calls_per_hour: 500
  
  # Production data retention
  conversation_retention_days: 90
  audio_retention_days: 30
  log_retention_days: 365
  
  # Additional security settings
  enable_encryption: true
  api_key_rotation_days: 90
  
# Production infrastructure settings
infrastructure:
  # Database settings (if using external database)
  database:
    host: ""                    # Set via environment variable
    port: 5432
    name: aivoice_production
    username: ""                # Set via environment variable
    password: ""                # Set via environment variable
    pool_size: 20
    
  # Redis settings (for session management)
  redis:
    host: ""                    # Set via environment variable
    port: 6379
    password: ""                # Set via environment variable
    db: 0
    
  # Load balancing
  load_balancer:
    enable: true
    algorithm: round_robin
    health_check_path: /health
    
# Backup and disaster recovery
backup:
  enable: true
  schedule: "0 2 * * *"         # Daily at 2 AM
  retention_days: 30
  storage_path: "/backup/aivoice"