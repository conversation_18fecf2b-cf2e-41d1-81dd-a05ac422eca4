"""
Tests for EdgeTTS generator.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
import tempfile
import os
import sys

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.tts.edge_tts_generator import (
    EdgeTTSGenerator, EdgeTTSConfig, TTSResult, TTSVoice,
    VoiceGender, AudioFormat, create_edge_tts_generator,
    get_telephony_optimized_config, get_high_quality_config,
    create_customer_service_voice_config, select_best_chinese_voice,
    recommend_voice_for_banking, get_voice_parameters_for_context,
    EDGE_TTS_AVAILABLE
)


@pytest.mark.skipif(not EDGE_TTS_AVAILABLE, reason="EdgeTTS dependencies not available")
class TestEdgeTTSGenerator:
    """Test EdgeTTS generator functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def generator_config(self):
        """Create test generator configuration."""
        return EdgeTTSConfig(
            default_voice="zh-CN-XiaoxiaoNeural",
            sample_rate=16000,
            channels=1,
            enable_caching=False,  # Disable caching for tests
            enable_audio_optimization=False  # Disable optimization for tests
        )
    
    @pytest.fixture
    def sample_voices(self):
        """Create sample voice configurations."""
        return [
            TTSVoice(
                name="Test Female Voice",
                short_name="zh-CN-TestFemale",
                gender=VoiceGender.FEMALE,
                locale="zh-CN",
                language="Chinese",
                naturalness=5,
                clarity=5,
                expressiveness=4
            ),
            TTSVoice(
                name="Test Male Voice",
                short_name="zh-CN-TestMale",
                gender=VoiceGender.MALE,
                locale="zh-CN",
                language="Chinese",
                naturalness=4,
                clarity=5,
                expressiveness=3
            )
        ]
    
    def test_generator_creation(self, generator_config, mock_config_manager):
        """Test EdgeTTS generator creation."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        assert generator.config == generator_config
        assert len(generator.chinese_voices) > 0
        assert len(generator.available_voices) > 0
    
    @pytest.mark.asyncio
    async def test_generator_lifecycle(self, generator_config, mock_config_manager):
        """Test generator lifecycle."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Mock EdgeTTS voice loading to avoid network calls
        with patch.object(generator, '_load_available_voices', new_callable=AsyncMock):
            await generator.initialize()
            await generator.start()
            
            assert generator.is_initialized
            assert generator.is_running
            
            await generator.stop()
            await generator.cleanup()
    
    def test_default_chinese_voices(self, generator_config, mock_config_manager):
        """Test default Chinese voice loading."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Should have loaded default Chinese voices
        assert len(generator.chinese_voices) > 0
        assert "zh-CN-XiaoxiaoNeural" in generator.chinese_voices
        assert "zh-CN-YunxiNeural" in generator.chinese_voices
        
        # Check voice properties
        xiaoxiao = generator.chinese_voices["zh-CN-XiaoxiaoNeural"]
        assert xiaoxiao.gender == VoiceGender.FEMALE
        assert xiaoxiao.locale == "zh-CN"
        assert xiaoxiao.naturalness >= 4
    
    @pytest.mark.asyncio
    async def test_mock_speech_generation(self, generator_config, mock_config_manager):
        """Test speech generation with mocked EdgeTTS."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Mock audio data
        mock_audio_data = b"fake_audio_data_for_testing"
        
        # Mock EdgeTTS communicate
        with patch('src.components.tts.edge_tts_generator.edge_tts.Communicate') as mock_communicate:
            # Setup mock
            mock_instance = Mock()
            mock_communicate.return_value = mock_instance
            
            # Mock stream method
            async def mock_stream():
                yield {"type": "audio", "data": mock_audio_data}
            
            mock_instance.stream = mock_stream
            
            # Initialize generator
            with patch.object(generator, '_load_available_voices', new_callable=AsyncMock):
                await generator.initialize()
                await generator.start()
                
                try:
                    # Generate speech
                    result = await generator.generate_speech(
                        "测试语音生成",
                        "zh-CN-XiaoxiaoNeural"
                    )
                    
                    assert isinstance(result, TTSResult)
                    assert result.audio_data == mock_audio_data
                    assert result.text == "测试语音生成"
                    assert result.voice_used == "zh-CN-XiaoxiaoNeural"
                    assert result.generation_time_ms > 0
                    
                finally:
                    await generator.stop()
                    await generator.cleanup()
    
    def test_voice_parameter_validation(self, generator_config, mock_config_manager):
        """Test voice parameter validation."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Test parameter validation
        params = generator.validate_voice_parameters(
            speed=10,
            pitch=-20,
            volume=5
        )
        
        assert params["speed"] == "+10%"
        assert params["pitch"] == "-20Hz"
        assert params["volume"] == "+5%"
        
        # Test string parameters
        params = generator.validate_voice_parameters(
            speed="+15%",
            pitch="-10Hz",
            volume="+0%"
        )
        
        assert params["speed"] == "+15%"
        assert params["pitch"] == "-10Hz"
        assert params["volume"] == "+0%"
    
    def test_cache_key_generation(self, generator_config, mock_config_manager):
        """Test cache key generation."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Test cache key generation
        key1 = generator._generate_cache_key("测试文本", "zh-CN-XiaoxiaoNeural", {})
        key2 = generator._generate_cache_key("测试文本", "zh-CN-XiaoxiaoNeural", {})
        key3 = generator._generate_cache_key("不同文本", "zh-CN-XiaoxiaoNeural", {})
        
        assert key1 == key2  # Same parameters should generate same key
        assert key1 != key3  # Different text should generate different key
    
    def test_voice_recommendations(self, generator_config, mock_config_manager, sample_voices):
        """Test voice recommendation functionality."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Add sample voices
        for voice in sample_voices:
            generator.chinese_voices[voice.short_name] = voice
            generator.available_voices[voice.short_name] = voice
        
        # Test gender-based recommendation
        female_voice = generator.get_recommended_voice(VoiceGender.FEMALE)
        assert female_voice == "zh-CN-TestFemale"
        
        male_voice = generator.get_recommended_voice(VoiceGender.MALE)
        assert male_voice == "zh-CN-TestMale"
        
        # Test general recommendation
        any_voice = generator.get_recommended_voice()
        assert any_voice in ["zh-CN-TestFemale", "zh-CN-TestMale"]
    
    def test_voice_filtering(self, generator_config, mock_config_manager, sample_voices):
        """Test voice filtering functionality."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Add sample voices
        for voice in sample_voices:
            generator.chinese_voices[voice.short_name] = voice
            generator.available_voices[voice.short_name] = voice
        
        # Test getting Chinese voices
        chinese_voices = generator.get_chinese_voices()
        assert len(chinese_voices) >= 2
        
        # Test gender filtering
        female_voices = generator.get_chinese_voices(VoiceGender.FEMALE)
        assert all(v.gender == VoiceGender.FEMALE for v in female_voices)
        
        male_voices = generator.get_chinese_voices(VoiceGender.MALE)
        assert all(v.gender == VoiceGender.MALE for v in male_voices)
    
    def test_statistics_tracking(self, generator_config, mock_config_manager):
        """Test statistics tracking."""
        generator = EdgeTTSGenerator(generator_config, mock_config_manager)
        
        # Initial stats
        stats = generator.get_generator_stats()
        assert stats["total_generations"] == 0
        assert stats["cache_hits"] == 0
        
        # Simulate generation
        result = TTSResult(
            audio_data=b"test",
            format=AudioFormat.WAV,
            sample_rate=16000,
            channels=1,
            duration_ms=1000,
            text="test",
            voice_used="test_voice",
            generation_time_ms=100,
            file_size_bytes=4
        )
        
        generator._update_statistics(result)
        
        # Check updated stats
        stats = generator.get_generator_stats()
        assert stats["total_generations"] == 1
        assert stats["total_audio_duration_ms"] == 1000


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_telephony_config(self):
        """Test telephony optimized configuration."""
        config = get_telephony_optimized_config()
        
        assert config.sample_rate == 8000
        assert config.channels == 1
        assert config.telephony_optimization == True
        assert config.compress_for_telephony == True
        assert config.target_bitrate_kbps == 32
    
    def test_high_quality_config(self):
        """Test high quality configuration."""
        config = get_high_quality_config()
        
        assert config.sample_rate == 24000
        assert config.telephony_optimization == False
        assert config.compress_for_telephony == False
    
    def test_customer_service_voice_config(self):
        """Test customer service voice configuration."""
        # Test professional tone
        config = create_customer_service_voice_config(
            gender=VoiceGender.FEMALE,
            tone="professional"
        )
        
        assert "speed" in config
        assert "pitch" in config
        assert "volume" in config
        
        # Test friendly tone
        friendly_config = create_customer_service_voice_config(tone="friendly")
        assert friendly_config["speed"] == "+5%"
        
        # Test warm tone
        warm_config = create_customer_service_voice_config(tone="warm")
        assert warm_config["speed"] == "-10%"
    
    def test_voice_selection(self):
        """Test voice selection utility."""
        # Create sample voices
        voices = [
            TTSVoice(
                name="High Quality Female",
                short_name="zh-CN-HighFemale",
                gender=VoiceGender.FEMALE,
                locale="zh-CN",
                language="Chinese",
                naturalness=5,
                clarity=5,
                expressiveness=4
            ),
            TTSVoice(
                name="Medium Quality Male",
                short_name="zh-CN-MediumMale",
                gender=VoiceGender.MALE,
                locale="zh-CN",
                language="Chinese",
                naturalness=3,
                clarity=4,
                expressiveness=3
            ),
            TTSVoice(
                name="English Voice",
                short_name="en-US-Test",
                gender=VoiceGender.FEMALE,
                locale="en-US",
                language="English",
                naturalness=4,
                clarity=4,
                expressiveness=4
            )
        ]
        
        # Test best Chinese voice selection
        best_voice = select_best_chinese_voice(voices)
        assert best_voice is not None
        assert best_voice.locale.startswith("zh-")
        assert best_voice.short_name == "zh-CN-HighFemale"  # Should select highest quality
        
        # Test gender filtering
        best_male = select_best_chinese_voice(voices, gender=VoiceGender.MALE)
        assert best_male.gender == VoiceGender.MALE
        assert best_male.short_name == "zh-CN-MediumMale"
    
    def test_banking_voice_recommendation(self):
        """Test banking voice recommendation."""
        voices = [
            TTSVoice(
                name="Professional Female",
                short_name="zh-CN-Professional",
                gender=VoiceGender.FEMALE,
                locale="zh-CN",
                language="Chinese",
                naturalness=5,
                clarity=5,
                expressiveness=3
            ),
            TTSVoice(
                name="Casual Male",
                short_name="zh-CN-Casual",
                gender=VoiceGender.MALE,
                locale="zh-CN",
                language="Chinese",
                naturalness=3,
                clarity=3,
                expressiveness=5
            )
        ]
        
        # Test VIP customer recommendation
        vip_voice = recommend_voice_for_banking(voices, "vip")
        assert vip_voice == "zh-CN-Professional"  # Should prefer high clarity/naturalness
        
        # Test general recommendation
        general_voice = recommend_voice_for_banking(voices, "general")
        assert general_voice in ["zh-CN-Professional", "zh-CN-Casual"]
    
    def test_context_voice_parameters(self):
        """Test context-based voice parameters."""
        # Test greeting context
        greeting_params = get_voice_parameters_for_context("greeting")
        assert greeting_params["speed"] == "+5%"
        assert greeting_params["pitch"] == "+10Hz"
        
        # Test explanation context
        explanation_params = get_voice_parameters_for_context("explanation")
        assert explanation_params["speed"] == "-5%"
        assert explanation_params["volume"] == "+5%"
        
        # Test urgent context
        urgent_params = get_voice_parameters_for_context("urgent")
        assert urgent_params["speed"] == "+10%"
        assert urgent_params["volume"] == "+10%"
        
        # Test apologetic context
        apologetic_params = get_voice_parameters_for_context("apologetic")
        assert apologetic_params["speed"] == "-15%"
        assert apologetic_params["pitch"] == "-10Hz"


@pytest.mark.skipif(not EDGE_TTS_AVAILABLE, reason="EdgeTTS dependencies not available")
@pytest.mark.asyncio
async def test_create_edge_tts_generator():
    """Test EdgeTTS generator factory function."""
    with patch('src.components.tts.edge_tts_generator.EdgeTTSGenerator._load_available_voices', new_callable=AsyncMock):
        generator = await create_edge_tts_generator(
            default_voice="zh-CN-XiaoxiaoNeural",
            sample_rate=16000,
            config_manager=Mock()
        )
        
        assert generator.is_initialized
        assert generator.is_running
        assert generator.config.default_voice == "zh-CN-XiaoxiaoNeural"
        assert generator.config.sample_rate == 16000
        
        await generator.stop()
        await generator.cleanup()


if __name__ == "__main__":
    # Run a simple test
    if EDGE_TTS_AVAILABLE:
        asyncio.run(test_create_edge_tts_generator())
    else:
        print("EdgeTTS dependencies not available, skipping tests")