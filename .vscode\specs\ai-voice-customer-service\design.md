# Design Document

## Overview

The AI Voice Customer Service system is a real-time conversational AI platform that handles outbound customer calls. The system integrates multiple AI components in a pipeline architecture to provide natural voice interactions. The core workflow involves: call initiation → voice activity detection → speech recognition → language model processing → text-to-speech generation → audio output.

The system is designed as a modular, event-driven architecture that can handle multiple concurrent calls while maintaining low latency and high reliability.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Call Manager] --> B[Audio Pipeline]
    B --> C[Voice Activity Detection]
    C --> D[Speech Recognition]
    D --> E[Conversation Engine]
    E --> F[Language Model]
    E --> G[Script Manager]
    F --> H[Response Generator]
    G --> H
    H --> I[Text-to-Speech]
    I --> J[Audio Output]
    J --> A
    
    K[Configuration Manager] --> A
    K --> E
    K --> F
    L[Logging & Monitoring] --> A
    L --> E
    L --> F
```

### Component Architecture

The system follows a microservices-inspired modular design with the following layers:

1. **Presentation Layer**: Call management and audio I/O interfaces
2. **Processing Layer**: Core AI components (ASR, TTS, LLM)
3. **Business Logic Layer**: Conversation flow and script management
4. **Data Layer**: Configuration, logging, and conversation scripts
5. **Infrastructure Layer**: Audio processing, telephony integration

## Components and Interfaces

### 1. Call Manager Component

**Purpose**: Manages outbound calls and overall conversation lifecycle

**Key Classes**:
- `CallManager`: Main orchestrator for call sessions
- `CallSession`: Represents individual call state and context
- `TelephonyInterface`: Abstraction for telephony system integration

**Interfaces**:
```python
class CallManager:
    def initiate_call(self, phone_number: str, script_id: str) -> CallSession
    def end_call(self, session_id: str) -> CallSummary
    def get_active_sessions(self) -> List[CallSession]

class CallSession:
    def get_session_id(self) -> str
    def get_call_state(self) -> CallState
    def update_context(self, context: Dict) -> None
```

### 2. Audio Pipeline Component

**Purpose**: Manages real-time audio streaming and processing coordination

**Key Classes**:
- `AudioPipeline`: Coordinates audio flow between components
- `AudioBuffer`: Manages audio data buffering and streaming
- `AudioProcessor`: Handles audio format conversion and preprocessing

**Interfaces**:
```python
class AudioPipeline:
    def start_stream(self, session_id: str) -> None
    def stop_stream(self, session_id: str) -> None
    def process_audio_chunk(self, audio_data: bytes) -> None
```

### 3. Voice Activity Detection Component

**Purpose**: Detects speech activity using SileroVAD model

**Key Classes**:
- `SileroVADDetector`: Wrapper for SileroVAD model
- `VADProcessor`: Processes audio chunks for voice activity
- `SpeechSegmenter`: Segments continuous speech from audio stream

**Model Integration**:
- Location: `models/snakers4_silero-vad`
- Input: 16kHz audio chunks
- Output: Voice activity probabilities and speech segments

### 4. Speech Recognition Component

**Purpose**: Converts speech to text using SenseVoiceSmall model

**Key Classes**:
- `SenseVoiceRecognizer`: Wrapper for SenseVoiceSmall model
- `ASRProcessor`: Manages speech recognition pipeline
- `TranscriptionBuffer`: Handles partial and final transcriptions

**Model Integration**:
- Location: `models/SenseVoiceSmall/model.pt`
- Input: Audio segments from VAD
- Output: Transcribed text with confidence scores

### 5. Conversation Engine Component

**Purpose**: Manages conversation flow and context

**Key Classes**:
- `ConversationEngine`: Main conversation orchestrator
- `ConversationContext`: Maintains conversation state and history
- `IntentClassifier`: Classifies customer intents from transcriptions

**Interfaces**:
```python
class ConversationEngine:
    def process_user_input(self, text: str, session_id: str) -> ConversationResponse
    def get_conversation_context(self, session_id: str) -> ConversationContext
    def reset_conversation(self, session_id: str) -> None
```

### 6. Script Manager Component

**Purpose**: Manages conversation scripts and knowledge base from Excel documents

**Key Classes**:
- `ScriptManager`: Loads and manages conversation scripts
- `ScriptParser`: Parses Excel documents into structured data
- `ResponseMatcher`: Matches user queries to appropriate script responses

**Data Sources**:
- Excel documents: `AIVoicebot/docs/言犀复贷话术.xlsx`, `AIVoicebot/docs/零犀复贷AI话术调优240914.xlsx`
- Parsed script data stored in memory for fast access

### 7. Language Model Component

**Purpose**: Generates intelligent responses using Qwen-turbo API

**Key Classes**:
- `QwenLLMClient`: Client for Qwen-turbo API
- `PromptManager`: Manages prompts and context for LLM calls
- `ResponseProcessor`: Processes and validates LLM responses

**API Integration**:
- Model: qwen-turbo
- Authentication: API Key based
- Fallback: Local script responses when API unavailable

### 8. Text-to-Speech Component

**Purpose**: Converts text responses to natural speech using EdgeTTS

**Key Classes**:
- `EdgeTTSGenerator`: Wrapper for EdgeTTS functionality
- `VoiceManager`: Manages voice selection and parameters
- `AudioSynthesizer`: Handles audio synthesis and optimization

**Voice Configuration**:
- Primary language: Chinese
- Voice characteristics: Professional, clear, appropriate pace
- Audio format: Compatible with telephony systems

### 9. Configuration Manager Component

**Purpose**: Manages system configuration and environment settings

**Key Classes**:
- `ConfigManager`: Central configuration management
- `EnvironmentConfig`: Environment-specific settings
- `ModelConfig`: AI model configuration and paths

### 10. Logging and Monitoring Component

**Purpose**: Provides comprehensive logging, monitoring, and analytics

**Key Classes**:
- `ConversationLogger`: Logs conversation transcripts and metadata
- `PerformanceMonitor`: Tracks system performance metrics
- `AnalyticsEngine`: Provides conversation analytics and insights

## Data Models

### Core Data Structures

```python
@dataclass
class CallSession:
    session_id: str
    phone_number: str
    start_time: datetime
    state: CallState
    conversation_context: ConversationContext
    script_id: str

@dataclass
class ConversationContext:
    session_id: str
    conversation_history: List[ConversationTurn]
    current_intent: str
    customer_info: Dict
    script_position: str

@dataclass
class ConversationTurn:
    timestamp: datetime
    speaker: str  # 'customer' or 'ai'
    text: str
    audio_duration: float
    confidence_score: float

@dataclass
class ScriptResponse:
    response_id: str
    text: str
    conditions: List[str]
    follow_up_actions: List[str]
    priority: int
```

## Error Handling

### Error Categories and Strategies

1. **Audio Processing Errors**:
   - Network audio issues: Retry with exponential backoff
   - Model loading failures: Graceful degradation to backup models
   - Real-time processing delays: Buffer management and timeout handling

2. **API Integration Errors**:
   - Qwen API failures: Fallback to script-based responses
   - Rate limiting: Queue management and request throttling
   - Authentication errors: Automatic token refresh and error reporting

3. **Telephony Errors**:
   - Call connection failures: Retry logic with different routing
   - Audio quality issues: Adaptive audio processing parameters
   - Call drops: Automatic reconnection attempts

4. **Model Processing Errors**:
   - Speech recognition failures: Request customer to repeat
   - TTS generation errors: Fallback to pre-recorded messages
   - VAD false positives/negatives: Adaptive threshold adjustment

### Error Recovery Mechanisms

- **Circuit Breaker Pattern**: For external API calls
- **Graceful Degradation**: Fallback to simpler processing when advanced features fail
- **Automatic Retry**: With exponential backoff for transient failures
- **Health Checks**: Continuous monitoring of component health

## Testing Strategy

### Unit Testing
- Individual component testing with mocked dependencies
- Model wrapper testing with sample audio/text data
- Configuration and utility function testing

### Integration Testing
- End-to-end audio pipeline testing
- API integration testing with Qwen service
- Script parsing and response matching testing

### Performance Testing
- Load testing with multiple concurrent calls
- Latency testing for real-time audio processing
- Memory and CPU usage profiling under load

### User Acceptance Testing
- Conversation quality assessment with real scenarios
- Script accuracy validation against business requirements
- Customer experience testing with sample calls

### Testing Tools and Frameworks
- **Unit Tests**: pytest with audio/text fixtures
- **Integration Tests**: Docker containers for isolated testing
- **Performance Tests**: locust for load testing
- **Audio Testing**: Synthetic audio generation for consistent testing

## Deployment Considerations

### Infrastructure Requirements
- **CPU**: Multi-core processors for concurrent audio processing
- **Memory**: Sufficient RAM for model loading and audio buffering
- **Storage**: Fast SSD for model files and audio caching
- **Network**: Stable internet connection for API calls and telephony

### Scalability Design
- **Horizontal Scaling**: Multiple instances behind load balancer
- **Resource Management**: Dynamic allocation based on call volume
- **Model Optimization**: Efficient model loading and memory management
- **Caching Strategy**: Response caching for common queries

### Security Considerations
- **API Key Management**: Secure storage and rotation of credentials
- **Audio Data Protection**: Encryption of audio streams and storage
- **Access Control**: Authentication and authorization for system access
- **Compliance**: Data retention and privacy regulation compliance

### Monitoring and Observability
- **Metrics Collection**: Response times, accuracy rates, error rates
- **Logging Strategy**: Structured logging with correlation IDs
- **Alerting**: Real-time alerts for system failures and performance issues
- **Dashboard**: Real-time monitoring dashboard for operations team