"""
ASR Processing Pipeline

This module provides ASR processing functionality including:
- ASRProcessor for managing speech recognition workflow
- TranscriptionBuffer for handling partial and final results
- Error handling for unclear audio and recognition failures
"""

import asyncio
import numpy as np
from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
import time
import threading
import queue

logger = logging.getLogger(__name__)


class TranscriptionStatus(Enum):
    """Status of transcription results"""
    PARTIAL = "partial"
    FINAL = "final"
    ERROR = "error"
    TIMEOUT = "timeout"


class ASRError(Exception):
    """ASR processing error"""
    pass


@dataclass
class TranscriptionResult:
    """Represents a transcription result"""
    text: str
    confidence: float
    status: TranscriptionStatus
    timestamp: float
    duration: float = 0.0
    language: str = "zh"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_final(self) -> bool:
        """Check if result is final"""
        return self.status == TranscriptionStatus.FINAL
    
    @property
    def is_partial(self) -> bool:
        """Check if result is partial"""
        return self.status == TranscriptionStatus.PARTIAL
    
    @property
    def has_error(self) -> bool:
        """Check if result has error"""
        return self.status == TranscriptionStatus.ERROR


@dataclass
class ASRProcessorConfig:
    """Configuration for ASR processor"""
    # Audio parameters
    sample_rate: int = 16000
    chunk_size: int = 1024
    
    # Processing parameters
    min_audio_length: float = 0.1  # Minimum audio length in seconds
    max_audio_length: float = 30.0  # Maximum audio length in seconds
    
    # Confidence thresholds
    min_confidence: float = 0.3
    partial_confidence_threshold: float = 0.5
    
    # Timeout settings
    processing_timeout: float = 10.0
    silence_timeout: float = 2.0
    
    # Buffer settings
    max_buffer_size: int = 100
    enable_partial_results: bool = True
    
    # Error handling
    max_retries: int = 3
    retry_delay: float = 0.5


class TranscriptionBuffer:
    """
    Buffer for handling partial and final transcription results
    
    Features:
    - Manages partial and final results
    - Handles result merging and updating
    - Provides result history and statistics
    """
    
    def __init__(self, config: ASRProcessorConfig):
        self.config = config
        self.results: List[TranscriptionResult] = []
        self.current_partial: Optional[TranscriptionResult] = None
        self.lock = threading.Lock()
        
        logger.info("TranscriptionBuffer initialized")
    
    def add_result(self, result: TranscriptionResult) -> bool:
        """
        Add transcription result to buffer
        
        Args:
            result: TranscriptionResult to add
            
        Returns:
            True if result was added successfully
        """
        with self.lock:
            try:
                if result.is_partial:
                    # Update current partial result
                    self.current_partial = result
                    logger.debug(f"Updated partial result: {result.text[:50]}...")
                
                elif result.is_final:
                    # Add final result and clear partial
                    self.results.append(result)
                    self.current_partial = None
                    logger.info(f"Added final result: {result.text}")
                    
                    # Limit buffer size
                    if len(self.results) > self.config.max_buffer_size:
                        self.results = self.results[-self.config.max_buffer_size:]
                
                else:
                    # Error or timeout result
                    self.results.append(result)
                    self.current_partial = None
                    logger.warning(f"Added error result: {result.status}")
                
                return True
                
            except Exception as e:
                logger.error(f"Error adding result to buffer: {e}")
                return False
    
    def get_latest_result(self) -> Optional[TranscriptionResult]:
        """Get the latest result (partial or final)"""
        with self.lock:
            if self.current_partial:
                return self.current_partial
            elif self.results:
                return self.results[-1]
            return None
    
    def get_final_results(self, count: int = 10) -> List[TranscriptionResult]:
        """Get recent final results"""
        with self.lock:
            final_results = [r for r in self.results if r.is_final]
            return final_results[-count:] if final_results else []
    
    def get_all_results(self) -> List[TranscriptionResult]:
        """Get all results in buffer"""
        with self.lock:
            return self.results.copy()
    
    def clear_buffer(self):
        """Clear all results from buffer"""
        with self.lock:
            self.results.clear()
            self.current_partial = None
            logger.info("Transcription buffer cleared")
    
    def get_buffer_stats(self) -> Dict[str, Any]:
        """Get buffer statistics"""
        with self.lock:
            final_results = [r for r in self.results if r.is_final]
            error_results = [r for r in self.results if r.has_error]
            
            total_confidence = sum(r.confidence for r in final_results)
            avg_confidence = total_confidence / len(final_results) if final_results else 0.0
            
            return {
                "total_results": len(self.results),
                "final_results": len(final_results),
                "error_results": len(error_results),
                "has_partial": self.current_partial is not None,
                "average_confidence": avg_confidence,
                "success_rate": len(final_results) / len(self.results) if self.results else 0.0
            }


class ASRProcessor:
    """
    ASR processing pipeline for managing speech recognition workflow
    
    Features:
    - Audio preprocessing and validation
    - Asynchronous ASR processing
    - Result buffering and management
    - Error handling and retry logic
    """
    
    def __init__(self, config: ASRProcessorConfig):
        self.config = config
        self.buffer = TranscriptionBuffer(config)
        
        # Processing state
        self.is_processing = False
        self.processing_queue = queue.Queue()
        self.result_callbacks: List[Callable[[TranscriptionResult], None]] = []
        
        # Statistics
        self.total_processed = 0
        self.total_errors = 0
        self.processing_times = []
        
        logger.info(f"ASRProcessor initialized with config: {config}")
    
    def add_result_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Add callback for transcription results"""
        self.result_callbacks.append(callback)
        logger.debug("Added result callback")
    
    def preprocess_audio(self, audio_data: np.ndarray) -> Optional[np.ndarray]:
        """
        Preprocess audio data for ASR
        
        Args:
            audio_data: Raw audio data
            
        Returns:
            Preprocessed audio data or None if invalid
        """
        try:
            # Validate audio length
            duration = len(audio_data) / self.config.sample_rate
            if duration < self.config.min_audio_length:
                logger.debug(f"Audio too short: {duration:.2f}s < {self.config.min_audio_length}s")
                return None
            
            if duration > self.config.max_audio_length:
                logger.warning(f"Audio too long: {duration:.2f}s > {self.config.max_audio_length}s")
                # Truncate to max length
                max_samples = int(self.config.max_audio_length * self.config.sample_rate)
                audio_data = audio_data[:max_samples]
            
            # Ensure float32 format
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Normalize audio
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                audio_data = audio_data / max_val
            
            # Check for silence
            rms = np.sqrt(np.mean(audio_data ** 2))
            if rms < 0.001:  # Very quiet audio
                logger.debug(f"Audio appears to be silent: RMS={rms:.6f}")
                return None
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Error preprocessing audio: {e}")
            return None
    
    async def process_audio_async(self, audio_data: np.ndarray, 
                                 asr_model=None) -> TranscriptionResult:
        """
        Process audio data asynchronously
        
        Args:
            audio_data: Audio data to process
            asr_model: ASR model instance (placeholder)
            
        Returns:
            TranscriptionResult
        """
        start_time = time.time()
        
        try:
            # Preprocess audio
            processed_audio = self.preprocess_audio(audio_data)
            if processed_audio is None:
                return TranscriptionResult(
                    text="",
                    confidence=0.0,
                    status=TranscriptionStatus.ERROR,
                    timestamp=start_time,
                    metadata={"error": "Audio preprocessing failed"}
                )
            
            # Simulate ASR processing (replace with actual model inference)
            await asyncio.sleep(0.1)  # Simulate processing time
            
            # Mock transcription result
            duration = len(processed_audio) / self.config.sample_rate
            mock_text = f"模拟转录结果 {duration:.1f}秒"
            mock_confidence = 0.8 + 0.15 * np.random.random()
            
            result = TranscriptionResult(
                text=mock_text,
                confidence=mock_confidence,
                status=TranscriptionStatus.FINAL,
                timestamp=start_time,
                duration=duration,
                metadata={
                    "processing_time": time.time() - start_time,
                    "audio_length": duration,
                    "audio_rms": float(np.sqrt(np.mean(processed_audio ** 2)))
                }
            )
            
            # Update statistics
            self.total_processed += 1
            self.processing_times.append(time.time() - start_time)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing audio: {e}")
            self.total_errors += 1
            
            return TranscriptionResult(
                text="",
                confidence=0.0,
                status=TranscriptionStatus.ERROR,
                timestamp=start_time,
                metadata={"error": str(e)}
            )
    
    def process_audio(self, audio_data: np.ndarray, 
                     asr_model=None) -> TranscriptionResult:
        """
        Process audio data synchronously
        
        Args:
            audio_data: Audio data to process
            asr_model: ASR model instance (placeholder)
            
        Returns:
            TranscriptionResult
        """
        # Run async processing in sync context
        loop = None
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(
            self.process_audio_async(audio_data, asr_model)
        )
    
    def process_audio_with_retry(self, audio_data: np.ndarray, 
                               asr_model=None) -> TranscriptionResult:
        """
        Process audio with retry logic
        
        Args:
            audio_data: Audio data to process
            asr_model: ASR model instance
            
        Returns:
            TranscriptionResult
        """
        last_error = None
        
        for attempt in range(self.config.max_retries):
            try:
                result = self.process_audio(audio_data, asr_model)
                
                # Check if result is acceptable
                if (result.is_final and 
                    result.confidence >= self.config.min_confidence):
                    return result
                
                # If low confidence, try again
                if attempt < self.config.max_retries - 1:
                    logger.warning(f"Low confidence result ({result.confidence:.2f}), "
                                 f"retrying... (attempt {attempt + 1})")
                    time.sleep(self.config.retry_delay)
                    continue
                
                # Return result even if low confidence on final attempt
                return result
                
            except Exception as e:
                last_error = e
                logger.error(f"ASR processing attempt {attempt + 1} failed: {e}")
                
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay)
        
        # All attempts failed
        return TranscriptionResult(
            text="",
            confidence=0.0,
            status=TranscriptionStatus.ERROR,
            timestamp=time.time(),
            metadata={"error": f"All {self.config.max_retries} attempts failed: {last_error}"}
        )
    
    def process_and_buffer(self, audio_data: np.ndarray, 
                          asr_model=None) -> TranscriptionResult:
        """
        Process audio and add result to buffer
        
        Args:
            audio_data: Audio data to process
            asr_model: ASR model instance
            
        Returns:
            TranscriptionResult
        """
        result = self.process_audio_with_retry(audio_data, asr_model)
        
        # Add to buffer
        self.buffer.add_result(result)
        
        # Notify callbacks
        for callback in self.result_callbacks:
            try:
                callback(result)
            except Exception as e:
                logger.error(f"Error in result callback: {e}")
        
        return result
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        avg_processing_time = (
            np.mean(self.processing_times) if self.processing_times else 0.0
        )
        
        buffer_stats = self.buffer.get_buffer_stats()
        
        return {
            "total_processed": self.total_processed,
            "total_errors": self.total_errors,
            "success_rate": (self.total_processed - self.total_errors) / max(1, self.total_processed),
            "average_processing_time": avg_processing_time,
            "is_processing": self.is_processing,
            **buffer_stats
        }
    
    def clear_stats(self):
        """Clear processing statistics"""
        self.total_processed = 0
        self.total_errors = 0
        self.processing_times.clear()
        self.buffer.clear_buffer()
        logger.info("Processing statistics cleared")


def create_asr_processor(sample_rate: int = 16000,
                        min_confidence: float = 0.3,
                        enable_partial_results: bool = True,
                        max_retries: int = 3) -> ASRProcessor:
    """Create ASR processor with common configuration"""
    config = ASRProcessorConfig(
        sample_rate=sample_rate,
        min_confidence=min_confidence,
        enable_partial_results=enable_partial_results,
        max_retries=max_retries
    )
    return ASRProcessor(config)


def process_audio_batch(audio_segments: List[np.ndarray],
                       asr_processor: ASRProcessor,
                       asr_model=None) -> List[TranscriptionResult]:
    """
    Process multiple audio segments in batch
    
    Args:
        audio_segments: List of audio data arrays
        asr_processor: ASRProcessor instance
        asr_model: ASR model instance
        
    Returns:
        List of transcription results
    """
    results = []
    
    for i, audio_data in enumerate(audio_segments):
        logger.info(f"Processing audio segment {i+1}/{len(audio_segments)}")
        
        result = asr_processor.process_and_buffer(audio_data, asr_model)
        results.append(result)
        
        # Log progress
        if result.is_final:
            logger.info(f"Segment {i+1} transcribed: {result.text[:50]}...")
        else:
            logger.warning(f"Segment {i+1} failed: {result.status}")
    
    return results