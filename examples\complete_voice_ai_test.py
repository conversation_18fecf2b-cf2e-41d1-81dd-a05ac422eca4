#!/usr/bin/env python3
"""
AI Voice Customer Service System - Complete Voice AI Test
========================================================

This file tests the complete Voice AI system including ASR, TTS, and AI conversation.

Prerequisites:
- System running on http://localhost:8000 with Voice AI components
- Python with requests library installed
"""

import requests
import json
import time
from typing import Dict, Any

# Base URL for the API
BASE_URL = "http://localhost:8000"

class CompleteVoiceAITester:
    """Client for testing complete Voice AI functionality."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status including Voice AI components."""
        response = self.session.get(f"{self.base_url}/status")
        response.raise_for_status()
        return response.json()
    
    def get_voice_info(self) -> Dict[str, Any]:
        """Get Voice AI system information."""
        response = self.session.get(f"{self.base_url}/voice/info")
        response.raise_for_status()
        return response.json()
    
    def test_voice_conversation(self) -> Dict[str, Any]:
        """Test Voice AI conversation flow."""
        response = self.session.post(f"{self.base_url}/voice/test-conversation")
        response.raise_for_status()
        return response.json()

def main():
    """Test complete Voice AI functionality."""
    print("🤖 AI Voice System - Complete Voice AI Test")
    print("=" * 55)
    
    tester = CompleteVoiceAITester()
    
    try:
        # 1. Check Voice AI system status
        print("\n1. 🔍 Voice AI System Status Check:")
        status = tester.get_system_status()
        sys_status = status['system_status']
        components = status.get('components', {})
        
        print(f"   System state: {sys_status['state']}")
        print(f"   Total components: {len(components)}")
        print(f"   Components running: {sys_status['components_running']}")
        print(f"   Voice AI system loaded: {'✅' if 'voice_ai_system' in components else '❌'}")
        print(f"   Voice processor loaded: {'✅' if 'voice_processor' in components else '❌'}")
        
        # 2. Get Voice AI detailed information
        print("\n2. 🎙️ Voice AI System Information:")
        try:
            voice_info = tester.get_voice_info()
            
            # Voice processor info
            voice_proc_info = voice_info.get('voice_processor', {})
            voice_status = voice_proc_info.get('status', {})
            print(f"   Voice Processor:")
            print(f"      Initialized: {'✅' if voice_status.get('is_initialized') else '❌'}")
            print(f"      Running: {'✅' if voice_status.get('is_running') else '❌'}")
            print(f"      ASR Running: {'✅' if voice_status.get('asr_running') else '❌'}")
            print(f"      TTS Running: {'✅' if voice_status.get('tts_running') else '❌'}")
            
            # Conversation engine info
            conv_info = voice_info.get('conversation_engine', {})
            print(f"   Conversation Engine:")
            print(f"      LLM Provider: {conv_info.get('llm_provider', 'N/A')}")
            print(f"      Model: {conv_info.get('model_name', 'N/A')}")
            print(f"      Active Sessions: {conv_info.get('active_sessions', 0)}")
            print(f"      Intent Detection: {'✅' if conv_info.get('enable_intent_detection') else '❌'}")
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                print("   ❌ Voice AI system not available")
            else:
                print(f"   ❌ Error getting voice info: {e}")
        
        # 3. Test Voice AI conversation flow
        print("\n3. 🗣️ Voice AI Conversation Flow Test:")
        try:
            conversation_result = tester.test_voice_conversation()
            
            if conversation_result.get('success'):
                print(f"   ✅ Test successful!")
                print(f"   📞 Test Call ID: {conversation_result.get('test_call_id')}")
                print(f"   🆔 Session ID: {conversation_result.get('session_id', 'N/A')[:8]}...")
                print(f"   💬 Total Exchanges: {conversation_result.get('total_exchanges', 0)}")
                
                # Show conversation details
                results = conversation_result.get('results', [])
                for i, result in enumerate(results, 1):
                    print(f"   Exchange {i}:")
                    print(f"      👤 User: '{result.get('user_input', '')}'")
                    print(f"      🤖 AI: '{result.get('ai_response', '')}'")
                    print(f"      🎯 Intent: {result.get('intent', 'N/A')}")
                    print(f"      ⚡ Processing: {result.get('processing_time', 0):.3f}s")
                    print(f"      🎵 TTS Duration: {result.get('tts_duration', 0):.2f}s")
            else:
                print(f"   ❌ Test failed: {conversation_result.get('error', 'Unknown error')}")
                
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                print("   ❌ Voice AI conversation test not available")
            else:
                print(f"   ❌ Error testing conversation: {e}")
        
        # 4. System health with Voice AI
        print("\n4. 🏥 System Health with Voice AI:")
        health_response = tester.session.get(f"{tester.base_url}/health")
        health_response.raise_for_status()
        health = health_response.json()
        
        print(f"   Overall status: {health['status']}")
        print(f"   Active calls: {health['active_calls']}")
        print(f"   System uptime: {sys_status['uptime']:.2f} seconds")
        print(f"   Components with errors: {sys_status['components_error']}")
        
        # 5. Voice AI readiness assessment
        print("\n5. 🚀 Voice AI Readiness Assessment:")
        
        readiness_score = 0
        max_score = 6
        
        # Check system state
        if sys_status['state'] == 'running':
            print("   ✅ System is running")
            readiness_score += 1
        else:
            print("   ❌ System is not running")
        
        # Check component count (should be 11 with Voice AI)
        if len(components) >= 11:
            print("   ✅ All components loaded (including Voice AI)")
            readiness_score += 1
        else:
            print(f"   ⚠️  Only {len(components)} components loaded")
        
        # Check running components
        if sys_status['components_running'] >= 11:
            print("   ✅ All components running")
            readiness_score += 1
        else:
            print(f"   ⚠️  Only {sys_status['components_running']} components running")
        
        # Check Voice AI availability
        try:
            voice_info = tester.get_voice_info()
            print("   ✅ Voice AI system accessible")
            readiness_score += 1
        except:
            print("   ❌ Voice AI system not accessible")
        
        # Check conversation test
        try:
            conversation_result = tester.test_voice_conversation()
            if conversation_result.get('success'):
                print("   ✅ Voice AI conversation test passed")
                readiness_score += 1
            else:
                print("   ❌ Voice AI conversation test failed")
        except:
            print("   ❌ Voice AI conversation test not available")
        
        # Check health
        if health['status'] == 'healthy':
            print("   ✅ System health is good")
            readiness_score += 1
        else:
            print("   ❌ System health issues detected")
        
        # Overall readiness assessment
        print(f"\n   📊 Voice AI Readiness Score: {readiness_score}/{max_score}")
        
        if readiness_score == max_score:
            print("   🟢 EXCELLENT: Voice AI system is fully operational!")
            print("   🎯 Ready for production use")
        elif readiness_score >= 5:
            print("   🟡 VERY GOOD: Voice AI system is mostly ready")
            print("   🔧 Minor optimizations possible")
        elif readiness_score >= 3:
            print("   🟡 GOOD: Voice AI system is functional")
            print("   🛠️  Some improvements needed")
        else:
            print("   🔴 NEEDS WORK: Voice AI system needs attention")
            print("   🚨 Significant issues need to be resolved")
        
        # 6. Next steps and capabilities
        print("\n6. 🎯 Voice AI Capabilities & Next Steps:")
        print("   🎉 CONGRATULATIONS! Voice AI System is Complete!")
        print()
        print("   ✅ Current Capabilities:")
        print("      • 🎤 Real-time speech recognition (ASR)")
        print("      • 🔊 Text-to-speech synthesis (TTS)")
        print("      • 🤖 AI-powered conversation engine")
        print("      • 📞 Complete call management")
        print("      • 📊 Real-time monitoring and health checks")
        print("      • 🔄 Audio processing pipeline")
        print("      • 📈 Performance monitoring")
        print("      • 🗂️  Conversation logging")
        print()
        print("   🚀 Ready for:")
        print("      • Production deployment")
        print("      • Real customer interactions")
        print("      • Integration with external LLMs")
        print("      • Advanced voice features")
        print("      • Custom conversation flows")
        
        print("\n✅ Complete Voice AI testing finished!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Cannot connect to the AI Voice System.")
        print("   Make sure the system is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
