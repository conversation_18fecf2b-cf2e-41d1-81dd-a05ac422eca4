"""
Tests for audio streaming and buffering system.
"""

import asyncio
import pytest
import numpy as np
from unittest.mock import Mock, AsyncMock

from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.interfaces import AudioChunk, AudioFormat
from src.components.audio import (
    CircularAudioBuffer, BufferConfig,
    RealTimeAudioStream, StreamConfig, StreamState,
    AudioStreamManager, StreamManagerConfig
)


class TestCircularAudioBuffer:
    """Test circular audio buffer functionality."""
    
    def test_buffer_creation(self):
        """Test buffer creation with configuration."""
        config = BufferConfig(
            max_size=1024,
            chunk_size=256,
            sample_rate=16000,
            channels=1
        )
        
        buffer = CircularAudioBuffer(config)
        
        assert buffer.config == config
        assert buffer.size() == 0
        assert buffer.is_empty()
        assert not buffer.is_full()
    
    def test_buffer_write_read(self):
        """Test basic write and read operations."""
        config = BufferConfig(max_size=1024, chunk_size=256)
        buffer = CircularAudioBuffer(config)
        
        # Create test audio chunk
        audio_data = np.random.randint(-32768, 32767, 256, dtype=np.int16).tobytes()
        chunk = AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=16,  # 256 samples at 16kHz = 16ms
            sample_rate=16000
        )
        
        # Write chunk
        success = buffer.write(chunk)
        assert success
        assert buffer.size() == 1
        assert not buffer.is_empty()
        
        # Read chunk
        read_chunk = buffer.read()
        assert read_chunk is not None
        assert read_chunk.data == chunk.data
        assert buffer.size() == 0
        assert buffer.is_empty()
    
    def test_buffer_overflow(self):
        """Test buffer overflow handling."""
        config = BufferConfig(max_size=2, chunk_size=256)
        buffer = CircularAudioBuffer(config)
        
        # Create test chunks
        chunks = []
        for i in range(3):
            audio_data = np.random.randint(-32768, 32767, 256, dtype=np.int16).tobytes()
            chunk = AudioChunk(
                data=audio_data,
                format=AudioFormat.PCM_16KHZ_MONO,
                timestamp=datetime.now(),
                duration_ms=16,  # 256 samples at 16kHz = 16ms
                sample_rate=16000
            )
            chunks.append(chunk)
        
        # Fill buffer to capacity
        assert buffer.write(chunks[0])
        assert buffer.write(chunks[1])
        assert buffer.is_full()
        
        # Try to write one more (should fail or overwrite)
        result = buffer.write(chunks[2])
        # Depending on implementation, this might succeed (overwrite) or fail
        
        # Buffer should still have data
        assert not buffer.is_empty()


class TestRealTimeAudioStream:
    """Test real-time audio stream functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def stream_config(self):
        """Create test stream configuration."""
        return StreamConfig(
            stream_id="test_stream",
            sample_rate=16000,
            channels=1,
            buffer_size=1024,
            chunk_size=256,
            target_latency_ms=100
        )
    
    @pytest.mark.asyncio
    async def test_stream_creation(self, stream_config, mock_config_manager):
        """Test stream creation and initialization."""
        stream = RealTimeAudioStream(stream_config, mock_config_manager)
        
        assert stream.stream_id == "test_stream"
        assert stream.state == StreamState.IDLE
        assert not stream.is_active
        assert not stream.is_paused
    
    @pytest.mark.asyncio
    async def test_stream_lifecycle(self, stream_config, mock_config_manager):
        """Test stream lifecycle (initialize, start, stop, cleanup)."""
        stream = RealTimeAudioStream(stream_config, mock_config_manager)
        
        # Initialize
        await stream.initialize()
        assert stream.state == StreamState.IDLE
        
        # Start
        await stream.start()
        assert stream.state == StreamState.ACTIVE
        assert stream.is_active
        
        # Pause
        await stream.pause()
        assert stream.state == StreamState.PAUSED
        assert stream.is_paused
        
        # Resume
        await stream.resume()
        assert stream.state == StreamState.ACTIVE
        assert stream.is_active
        
        # Stop
        await stream.stop()
        assert stream.state == StreamState.IDLE
        assert not stream.is_active
        
        # Cleanup
        await stream.cleanup()
    
    @pytest.mark.asyncio
    async def test_stream_audio_processing(self, stream_config, mock_config_manager):
        """Test audio processing through stream."""
        stream = RealTimeAudioStream(stream_config, mock_config_manager)
        
        await stream.initialize()
        await stream.start()
        
        try:
            # Create test audio chunk
            audio_data = np.random.randint(-32768, 32767, 256, dtype=np.int16).tobytes()
            chunk = AudioChunk(
                data=audio_data,
                format=AudioFormat.PCM_16KHZ_MONO,
                timestamp=datetime.now(),
                duration_ms=16,  # 256 samples at 16kHz = 16ms
                sample_rate=16000
            )
            
            # Write audio to stream
            success = await stream.write_audio(chunk)
            assert success
            
            # Give some time for processing
            await asyncio.sleep(0.1)
            
            # Read processed audio
            processed_chunk = await stream.read_audio()
            # Note: In a real implementation, this might be None if processing takes time
            
        finally:
            await stream.stop()
            await stream.cleanup()


class TestAudioStreamManager:
    """Test audio stream manager functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def manager_config(self):
        """Create test manager configuration."""
        return StreamManagerConfig(
            max_concurrent_streams=5,
            stream_timeout_seconds=60,
            enable_load_balancing=True,
            enable_resource_monitoring=False  # Disable for testing
        )
    
    @pytest.mark.asyncio
    async def test_manager_creation(self, manager_config, mock_config_manager):
        """Test stream manager creation."""
        manager = AudioStreamManager(manager_config, mock_config_manager)
        
        assert manager.config == manager_config
        assert manager.get_stream_count() == 0
        assert len(manager.list_streams()) == 0
    
    @pytest.mark.asyncio
    async def test_manager_lifecycle(self, manager_config, mock_config_manager):
        """Test manager lifecycle."""
        manager = AudioStreamManager(manager_config, mock_config_manager)
        
        # Initialize
        await manager.initialize()
        
        # Start
        await manager.start()
        
        # Stop
        await manager.stop()
        
        # Cleanup
        await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_stream_management(self, manager_config, mock_config_manager):
        """Test stream creation and management."""
        manager = AudioStreamManager(manager_config, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        try:
            # Create stream configuration
            stream_config = StreamConfig(
                stream_id="test_stream_1",
                sample_rate=16000,
                channels=1
            )
            
            # Create stream
            stream = await manager.create_stream(stream_config)
            assert stream is not None
            assert stream.stream_id == "test_stream_1"
            assert manager.get_stream_count() == 1
            assert "test_stream_1" in manager.list_streams()
            
            # Get stream
            retrieved_stream = await manager.get_stream("test_stream_1")
            assert retrieved_stream is stream
            
            # Destroy stream
            success = await manager.destroy_stream("test_stream_1")
            assert success
            assert manager.get_stream_count() == 0
            assert "test_stream_1" not in manager.list_streams()
            
        finally:
            await manager.stop()
            await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_concurrent_stream_limit(self, manager_config, mock_config_manager):
        """Test concurrent stream limit enforcement."""
        manager_config.max_concurrent_streams = 2
        manager = AudioStreamManager(manager_config, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        try:
            # Create streams up to limit
            stream1_config = StreamConfig(stream_id="stream_1")
            stream2_config = StreamConfig(stream_id="stream_2")
            stream3_config = StreamConfig(stream_id="stream_3")
            
            stream1 = await manager.create_stream(stream1_config)
            stream2 = await manager.create_stream(stream2_config)
            
            assert manager.get_stream_count() == 2
            
            # Try to create one more (should fail)
            with pytest.raises(Exception):  # Should raise AudioProcessingError
                await manager.create_stream(stream3_config)
            
        finally:
            await manager.stop()
            await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_stream_statistics(self, manager_config, mock_config_manager):
        """Test stream statistics collection."""
        manager = AudioStreamManager(manager_config, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        try:
            # Create a stream
            stream_config = StreamConfig(stream_id="stats_test")
            stream = await manager.create_stream(stream_config)
            
            # Get manager statistics
            stats = manager.get_manager_stats()
            assert stats.active_streams == 1
            assert stats.total_streams_created == 1
            assert stats.total_streams_destroyed == 0
            
            # Get individual stream stats
            all_stats = manager.get_all_stream_stats()
            assert "stats_test" in all_stats
            
            # Get system health
            health = manager.get_system_health()
            assert "status" in health
            assert "stats" in health
            
        finally:
            await manager.stop()
            await manager.cleanup()


# Integration test
@pytest.mark.asyncio
async def test_audio_streaming_integration():
    """Integration test for complete audio streaming system."""
    # Mock configuration manager
    config_manager = Mock()
    
    # Create manager
    manager_config = StreamManagerConfig(
        max_concurrent_streams=3,
        enable_resource_monitoring=False
    )
    manager = AudioStreamManager(manager_config, config_manager)
    
    await manager.initialize()
    await manager.start()
    
    try:
        # Create multiple streams
        stream_configs = [
            StreamConfig(stream_id=f"integration_stream_{i}", sample_rate=16000)
            for i in range(2)
        ]
        
        streams = []
        for config in stream_configs:
            stream = await manager.create_stream(config)
            streams.append(stream)
        
        assert manager.get_stream_count() == 2
        
        # Test audio processing through manager
        audio_data = np.random.randint(-32768, 32767, 256, dtype=np.int16).tobytes()
        chunk = AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=16,  # 256 samples at 16kHz = 16ms
            sample_rate=16000
        )
        
        # Write to first stream through manager
        success = await manager.write_to_stream("integration_stream_0", chunk)
        assert success
        
        # Give some time for processing
        await asyncio.sleep(0.1)
        
        # Try to read from stream
        processed_chunk = await manager.read_from_stream("integration_stream_0")
        # Note: Might be None depending on processing timing
        
        # Test stream pause/resume through manager
        success = await manager.pause_stream("integration_stream_1")
        assert success
        
        success = await manager.resume_stream("integration_stream_1")
        assert success
        
        # Check system health
        health = manager.get_system_health()
        assert health["status"] in ["healthy", "warning", "critical"]
        
    finally:
        await manager.stop()
        await manager.cleanup()


if __name__ == "__main__":
    # Run basic tests
    asyncio.run(test_audio_streaming_integration())