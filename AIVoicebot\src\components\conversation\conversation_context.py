"""
Conversation context management system for maintaining conversation state and history.

This module provides context management for AI voice customer service conversations,
including conversation history tracking, intent classification, and conversation flow control.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
import json
from collections import deque

from ...core.base_component import BaseComponent
from ...core.interfaces import AudioProcessingError


# Define our own enums and classes to avoid circular imports
class ConversationState(Enum):
    """Conversation state enumeration."""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    SPEAKING = "speaking"
    WAITING_FOR_INPUT = "waiting_for_input"
    TRANSFERRING = "transferring"
    ENDING = "ending"
    ERROR = "error"


class ConversationFlow(Enum):
    """Conversation flow types."""
    GREETING = "greeting"
    INFORMATION_GATHERING = "information_gathering"
    SERVICE_DELIVERY = "service_delivery"
    PROBLEM_SOLVING = "problem_solving"
    CLOSING = "closing"
    ESCALATION = "escalation"


class IntentType(Enum):
    """Intent classification types."""
    GREETING = "greeting"
    INQUIRY = "inquiry"
    REQUEST = "request"
    COMPLAINT = "complaint"
    CONFIRMATION = "confirmation"
    GOODBYE = "goodbye"
    UNKNOWN = "unknown"


@dataclass
class ConversationTurn:
    """Represents a single conversation turn."""
    turn_id: str
    user_input: str
    user_intent: Optional[IntentType] = None
    final_response: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    confidence_score: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "turn_id": self.turn_id,
            "user_input": self.user_input,
            "user_intent": self.user_intent.value if self.user_intent else None,
            "final_response": self.final_response,
            "timestamp": self.timestamp.isoformat(),
            "confidence_score": self.confidence_score
        }


class ContextScope(Enum):
    """Context scope levels."""
    SESSION = "session"          # Entire conversation session
    TOPIC = "topic"             # Current topic/subject
    TURN = "turn"               # Single conversation turn
    IMMEDIATE = "immediate"      # Current interaction


class ContextPriority(Enum):
    """Context information priority levels."""
    CRITICAL = "critical"       # Must be preserved
    HIGH = "high"              # Important to preserve
    MEDIUM = "medium"          # Useful to preserve
    LOW = "low"                # Can be discarded if needed


@dataclass
class ContextItem:
    """Represents a single context item."""
    key: str
    value: Any
    scope: ContextScope
    priority: ContextPriority
    timestamp: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    source: str = "unknown"
    
    @property
    def is_expired(self) -> bool:
        """Check if context item has expired."""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "key": self.key,
            "value": self.value,
            "scope": self.scope.value,
            "priority": self.priority.value,
            "timestamp": self.timestamp.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "source": self.source
        }


@dataclass
class ConversationIntent:
    """Represents conversation intent information."""
    intent_type: IntentType
    confidence: float
    entities: Dict[str, Any] = field(default_factory=dict)
    sub_intents: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "intent_type": self.intent_type.value,
            "confidence": self.confidence,
            "entities": self.entities,
            "sub_intents": self.sub_intents,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class ConversationTopic:
    """Represents a conversation topic."""
    topic_id: str
    name: str
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    turns: List[str] = field(default_factory=list)  # Turn IDs
    entities: Dict[str, Any] = field(default_factory=dict)
    resolution_status: Optional[str] = None
    
    @property
    def is_active(self) -> bool:
        """Check if topic is currently active."""
        return self.end_time is None
    
    @property
    def duration_seconds(self) -> float:
        """Get topic duration in seconds."""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()


@dataclass
class ConversationContextConfig:
    """Configuration for conversation context manager."""
    # History management
    max_conversation_history: int = 20
    max_context_items: int = 100
    
    # Context retention
    default_context_ttl_minutes: int = 30
    critical_context_ttl_minutes: int = 120
    
    # Topic management
    max_active_topics: int = 3
    topic_inactivity_threshold_minutes: int = 5
    
    # Intent tracking
    intent_confidence_threshold: float = 0.7
    max_intent_history: int = 10
    
    # Performance
    context_cleanup_interval_minutes: int = 5
    enable_context_compression: bool = True
    
    # Persistence
    enable_context_persistence: bool = True
    context_save_interval_minutes: int = 2


class ConversationContext(BaseComponent):
    """
    Conversation context management system.
    
    Maintains conversation state, history, intent tracking, and context preservation
    for enhanced AI voice customer service interactions.
    """
    
    def __init__(self, session_id: str, config: ConversationContextConfig, config_manager, logger=None):
        """
        Initialize conversation context manager.
        
        Args:
            session_id: Unique session identifier
            config: Context manager configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__(f"conversation_context_{session_id}", config_manager, logger)
        
        self.session_id = session_id
        self.config = config
        
        # Context storage
        self.context_items: Dict[str, ContextItem] = {}
        self.conversation_history: deque = deque(maxlen=config.max_conversation_history)
        
        # Intent tracking
        self.intent_history: deque = deque(maxlen=config.max_intent_history)
        self.current_intent: Optional[ConversationIntent] = None
        
        # Topic management
        self.topics: Dict[str, ConversationTopic] = {}
        self.active_topics: List[str] = []
        self.current_topic_id: Optional[str] = None
        
        # State tracking
        self.conversation_state: ConversationState = ConversationState.IDLE
        self.conversation_flow: ConversationFlow = ConversationFlow.GREETING
        
        # Session information
        self.session_start_time = datetime.now()
        self.last_activity_time = datetime.now()
        self.customer_info: Dict[str, Any] = {}
        
        # Performance tracking
        self._context_access_count = 0
        self._context_updates = 0
        self._cleanup_runs = 0
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._save_task: Optional[asyncio.Task] = None
    
    async def _initialize_impl(self) -> None:
        """Initialize conversation context manager."""
        self._log.info(f"Initializing conversation context for session {self.session_id}")
        
        # Load persisted context if available
        if self.config.enable_context_persistence:
            await self._load_persisted_context()
        
        self._log.info("Conversation context manager initialized")
    
    async def _start_impl(self) -> None:
        """Start conversation context manager."""
        self._log.info("Starting conversation context manager...")
        
        # Start background tasks
        if self.config.context_cleanup_interval_minutes > 0:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
        
        if self.config.enable_context_persistence and self.config.context_save_interval_minutes > 0:
            self._save_task = asyncio.create_task(self._periodic_save())
        
        self._log.info("Conversation context manager started")
    
    async def _stop_impl(self) -> None:
        """Stop conversation context manager."""
        self._log.info("Stopping conversation context manager...")
        
        # Cancel background tasks
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self._save_task:
            self._save_task.cancel()
            try:
                await self._save_task
            except asyncio.CancelledError:
                pass
        
        # Final save
        if self.config.enable_context_persistence:
            await self._save_context()
        
        self._log.info("Conversation context manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup conversation context resources."""
        self._log.info("Cleaning up conversation context...")
        
        # Clear all context data
        self.context_items.clear()
        self.conversation_history.clear()
        self.intent_history.clear()
        self.topics.clear()
        self.active_topics.clear()
        
        self._log.info("Conversation context cleanup completed")

    # Context Management Methods

    def add_context_item(
        self,
        key: str,
        value: Any,
        scope: ContextScope = ContextScope.SESSION,
        priority: ContextPriority = ContextPriority.MEDIUM,
        ttl_minutes: Optional[int] = None,
        source: str = "user"
    ) -> None:
        """
        Add context item to conversation context.

        Args:
            key: Context item key
            value: Context item value
            scope: Context scope level
            priority: Context priority level
            ttl_minutes: Time to live in minutes
            source: Source of context item
        """
        # Calculate expiration time
        expires_at = None
        if ttl_minutes is not None:
            expires_at = datetime.now() + timedelta(minutes=ttl_minutes)
        elif priority == ContextPriority.CRITICAL:
            expires_at = datetime.now() + timedelta(minutes=self.config.critical_context_ttl_minutes)
        else:
            expires_at = datetime.now() + timedelta(minutes=self.config.default_context_ttl_minutes)

        # Create context item
        context_item = ContextItem(
            key=key,
            value=value,
            scope=scope,
            priority=priority,
            expires_at=expires_at,
            source=source
        )

        # Store context item
        self.context_items[key] = context_item
        self._context_updates += 1

        # Cleanup if needed
        if len(self.context_items) > self.config.max_context_items:
            self._cleanup_context_items()

        self._log.debug(f"Added context item: {key} = {value} (scope: {scope.value}, priority: {priority.value})")

    def get_context_item(self, key: str, default: Any = None) -> Any:
        """
        Get context item value.

        Args:
            key: Context item key
            default: Default value if not found

        Returns:
            Context item value or default
        """
        self._context_access_count += 1

        if key not in self.context_items:
            return default

        item = self.context_items[key]

        # Check if expired
        if item.is_expired:
            del self.context_items[key]
            return default

        return item.value

    def remove_context_item(self, key: str) -> bool:
        """
        Remove context item.

        Args:
            key: Context item key

        Returns:
            True if item was removed, False if not found
        """
        if key in self.context_items:
            del self.context_items[key]
            self._log.debug(f"Removed context item: {key}")
            return True
        return False

    def get_context_by_scope(self, scope: ContextScope) -> Dict[str, Any]:
        """
        Get all context items for a specific scope.

        Args:
            scope: Context scope to filter by

        Returns:
            Dictionary of context items for the scope
        """
        result = {}
        for key, item in self.context_items.items():
            if item.scope == scope and not item.is_expired:
                result[key] = item.value
        return result

    def get_context_by_priority(self, min_priority: ContextPriority) -> Dict[str, Any]:
        """
        Get context items with minimum priority level.

        Args:
            min_priority: Minimum priority level

        Returns:
            Dictionary of context items meeting priority threshold
        """
        priority_order = {
            ContextPriority.CRITICAL: 4,
            ContextPriority.HIGH: 3,
            ContextPriority.MEDIUM: 2,
            ContextPriority.LOW: 1
        }

        min_level = priority_order[min_priority]
        result = {}

        for key, item in self.context_items.items():
            if priority_order[item.priority] >= min_level and not item.is_expired:
                result[key] = item.value

        return result

    # Conversation History Management

    def add_conversation_turn(self, turn: ConversationTurn) -> None:
        """
        Add conversation turn to history.

        Args:
            turn: Conversation turn to add
        """
        self.conversation_history.append(turn)
        self.last_activity_time = datetime.now()

        # Update current topic if applicable
        if self.current_topic_id and self.current_topic_id in self.topics:
            self.topics[self.current_topic_id].turns.append(turn.turn_id)

        self._log.debug(f"Added conversation turn: {turn.turn_id}")

    def get_conversation_history(self, count: Optional[int] = None) -> List[ConversationTurn]:
        """
        Get conversation history.

        Args:
            count: Number of recent turns to return (None for all)

        Returns:
            List of conversation turns
        """
        if count is None:
            return list(self.conversation_history)
        return list(self.conversation_history)[-count:]

    def get_conversation_summary(self) -> Dict[str, Any]:
        """
        Get conversation summary.

        Returns:
            Dictionary containing conversation summary
        """
        total_turns = len(self.conversation_history)
        duration = (self.last_activity_time - self.session_start_time).total_seconds()

        # Calculate intent distribution
        intent_counts = {}
        for turn in self.conversation_history:
            if turn.user_intent:
                intent_type = turn.user_intent.value
                intent_counts[intent_type] = intent_counts.get(intent_type, 0) + 1

        return {
            "session_id": self.session_id,
            "total_turns": total_turns,
            "duration_seconds": duration,
            "current_state": self.conversation_state.value,
            "current_flow": self.conversation_flow.value,
            "intent_distribution": intent_counts,
            "active_topics": len(self.active_topics),
            "context_items": len(self.context_items)
        }

    # Intent Management

    def update_intent(
        self,
        intent_type: IntentType,
        confidence: float,
        entities: Optional[Dict[str, Any]] = None,
        sub_intents: Optional[List[str]] = None
    ) -> None:
        """
        Update current conversation intent.

        Args:
            intent_type: Type of intent
            confidence: Intent confidence score
            entities: Extracted entities
            sub_intents: Sub-intent classifications
        """
        if confidence < self.config.intent_confidence_threshold:
            self._log.debug(f"Intent confidence {confidence} below threshold {self.config.intent_confidence_threshold}")
            return

        # Create new intent
        new_intent = ConversationIntent(
            intent_type=intent_type,
            confidence=confidence,
            entities=entities or {},
            sub_intents=sub_intents or []
        )

        # Update current intent
        self.current_intent = new_intent
        self.intent_history.append(new_intent)

        # Add intent to context
        self.add_context_item(
            key="current_intent",
            value=intent_type.value,
            scope=ContextScope.TURN,
            priority=ContextPriority.HIGH,
            source="intent_classifier"
        )

        self._log.info(f"Updated intent: {intent_type.value} (confidence: {confidence:.2f})")

    def get_intent_history(self, count: Optional[int] = None) -> List[ConversationIntent]:
        """
        Get intent history.

        Args:
            count: Number of recent intents to return

        Returns:
            List of conversation intents
        """
        if count is None:
            return list(self.intent_history)
        return list(self.intent_history)[-count:]

    def get_dominant_intent(self, window_size: int = 5) -> Optional[IntentType]:
        """
        Get dominant intent from recent history.

        Args:
            window_size: Number of recent intents to consider

        Returns:
            Most frequent intent type or None
        """
        recent_intents = self.get_intent_history(window_size)
        if not recent_intents:
            return None

        # Count intent frequencies
        intent_counts = {}
        for intent in recent_intents:
            intent_type = intent.intent_type
            intent_counts[intent_type] = intent_counts.get(intent_type, 0) + 1

        # Return most frequent
        return max(intent_counts.items(), key=lambda x: x[1])[0]

    # Topic Management

    def start_topic(self, name: str, entities: Optional[Dict[str, Any]] = None) -> str:
        """
        Start a new conversation topic.

        Args:
            name: Topic name
            entities: Topic-related entities

        Returns:
            Topic ID
        """
        topic_id = str(uuid.uuid4())

        topic = ConversationTopic(
            topic_id=topic_id,
            name=name,
            entities=entities or {}
        )

        self.topics[topic_id] = topic
        self.active_topics.append(topic_id)
        self.current_topic_id = topic_id

        # Limit active topics
        if len(self.active_topics) > self.config.max_active_topics:
            oldest_topic_id = self.active_topics.pop(0)
            self.end_topic(oldest_topic_id)

        # Add to context
        self.add_context_item(
            key="current_topic",
            value=name,
            scope=ContextScope.TOPIC,
            priority=ContextPriority.HIGH,
            source="topic_manager"
        )

        self._log.info(f"Started topic: {name} (ID: {topic_id})")
        return topic_id

    def end_topic(self, topic_id: str, resolution_status: Optional[str] = None) -> bool:
        """
        End a conversation topic.

        Args:
            topic_id: Topic ID to end
            resolution_status: Resolution status

        Returns:
            True if topic was ended, False if not found
        """
        if topic_id not in self.topics:
            return False

        topic = self.topics[topic_id]
        topic.end_time = datetime.now()
        topic.resolution_status = resolution_status

        # Remove from active topics
        if topic_id in self.active_topics:
            self.active_topics.remove(topic_id)

        # Update current topic
        if self.current_topic_id == topic_id:
            self.current_topic_id = self.active_topics[-1] if self.active_topics else None

        self._log.info(f"Ended topic: {topic.name} (ID: {topic_id}, status: {resolution_status})")
        return True

    def get_active_topics(self) -> List[ConversationTopic]:
        """
        Get all active topics.

        Returns:
            List of active conversation topics
        """
        return [self.topics[topic_id] for topic_id in self.active_topics if topic_id in self.topics]

    def get_current_topic(self) -> Optional[ConversationTopic]:
        """
        Get current active topic.

        Returns:
            Current topic or None
        """
        if self.current_topic_id and self.current_topic_id in self.topics:
            return self.topics[self.current_topic_id]
        return None

    # State Management

    def update_conversation_state(self, new_state: ConversationState) -> None:
        """
        Update conversation state.

        Args:
            new_state: New conversation state
        """
        old_state = self.conversation_state
        self.conversation_state = new_state
        self.last_activity_time = datetime.now()

        # Add to context
        self.add_context_item(
            key="conversation_state",
            value=new_state.value,
            scope=ContextScope.TURN,
            priority=ContextPriority.HIGH,
            source="state_manager"
        )

        self._log.info(f"Conversation state changed: {old_state.value} -> {new_state.value}")

    def update_conversation_flow(self, new_flow: ConversationFlow) -> None:
        """
        Update conversation flow.

        Args:
            new_flow: New conversation flow
        """
        old_flow = self.conversation_flow
        self.conversation_flow = new_flow
        self.last_activity_time = datetime.now()

        # Add to context
        self.add_context_item(
            key="conversation_flow",
            value=new_flow.value,
            scope=ContextScope.SESSION,
            priority=ContextPriority.HIGH,
            source="flow_manager"
        )

        self._log.info(f"Conversation flow changed: {old_flow.value} -> {new_flow.value}")

    def update_customer_info(self, info: Dict[str, Any]) -> None:
        """
        Update customer information.

        Args:
            info: Customer information dictionary
        """
        self.customer_info.update(info)

        # Add important customer info to context
        for key, value in info.items():
            self.add_context_item(
                key=f"customer_{key}",
                value=value,
                scope=ContextScope.SESSION,
                priority=ContextPriority.CRITICAL,
                source="customer_data"
            )

        self._log.debug(f"Updated customer info: {list(info.keys())}")

    # Utility Methods

    def _cleanup_context_items(self) -> None:
        """Clean up expired and low-priority context items."""
        current_time = datetime.now()
        items_to_remove = []

        # Find expired items
        for key, item in self.context_items.items():
            if item.is_expired:
                items_to_remove.append(key)

        # Remove expired items
        for key in items_to_remove:
            del self.context_items[key]

        # If still over limit, remove low-priority items
        if len(self.context_items) > self.config.max_context_items:
            # Sort by priority and age
            items_by_priority = sorted(
                self.context_items.items(),
                key=lambda x: (
                    {"critical": 4, "high": 3, "medium": 2, "low": 1}[x[1].priority.value],
                    x[1].timestamp
                ),
                reverse=True
            )

            # Keep only the most important items
            items_to_keep = items_by_priority[:self.config.max_context_items]
            self.context_items = dict(items_to_keep)

        if items_to_remove:
            self._log.debug(f"Cleaned up {len(items_to_remove)} context items")

    def _cleanup_inactive_topics(self) -> None:
        """Clean up inactive topics."""
        current_time = datetime.now()
        threshold = timedelta(minutes=self.config.topic_inactivity_threshold_minutes)

        topics_to_end = []
        for topic_id in self.active_topics:
            if topic_id in self.topics:
                topic = self.topics[topic_id]
                if current_time - topic.start_time > threshold:
                    topics_to_end.append(topic_id)

        for topic_id in topics_to_end:
            self.end_topic(topic_id, "inactive")

    async def _periodic_cleanup(self) -> None:
        """Periodic cleanup task."""
        while True:
            try:
                await asyncio.sleep(self.config.context_cleanup_interval_minutes * 60)

                self._cleanup_context_items()
                self._cleanup_inactive_topics()
                self._cleanup_runs += 1

                self._log.debug("Periodic cleanup completed")

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in periodic cleanup: {e}")

    async def _periodic_save(self) -> None:
        """Periodic save task."""
        while True:
            try:
                await asyncio.sleep(self.config.context_save_interval_minutes * 60)
                await self._save_context()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in periodic save: {e}")

    async def _load_persisted_context(self) -> None:
        """Load persisted context from storage."""
        # This would typically load from a database or file
        # For now, just log that it would happen
        self._log.debug("Loading persisted context (not implemented)")

    async def _save_context(self) -> None:
        """Save context to persistent storage."""
        # This would typically save to a database or file
        # For now, just log that it would happen
        self._log.debug("Saving context to persistent storage (not implemented)")

    # Export/Import Methods

    def export_context(self) -> Dict[str, Any]:
        """
        Export conversation context to dictionary.

        Returns:
            Dictionary containing all context data
        """
        return {
            "session_id": self.session_id,
            "session_start_time": self.session_start_time.isoformat(),
            "last_activity_time": self.last_activity_time.isoformat(),
            "conversation_state": self.conversation_state.value,
            "conversation_flow": self.conversation_flow.value,
            "customer_info": self.customer_info,
            "context_items": {
                key: item.to_dict() for key, item in self.context_items.items()
            },
            "conversation_history": [
                turn.to_dict() for turn in self.conversation_history
            ],
            "intent_history": [
                intent.to_dict() for intent in self.intent_history
            ],
            "topics": {
                topic_id: {
                    "topic_id": topic.topic_id,
                    "name": topic.name,
                    "start_time": topic.start_time.isoformat(),
                    "end_time": topic.end_time.isoformat() if topic.end_time else None,
                    "turns": topic.turns,
                    "entities": topic.entities,
                    "resolution_status": topic.resolution_status
                }
                for topic_id, topic in self.topics.items()
            },
            "active_topics": self.active_topics,
            "current_topic_id": self.current_topic_id
        }

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get context manager statistics.

        Returns:
            Dictionary containing statistics
        """
        return {
            "session_id": self.session_id,
            "context_access_count": self._context_access_count,
            "context_updates": self._context_updates,
            "cleanup_runs": self._cleanup_runs,
            "total_context_items": len(self.context_items),
            "conversation_turns": len(self.conversation_history),
            "intent_history_length": len(self.intent_history),
            "active_topics_count": len(self.active_topics),
            "total_topics": len(self.topics),
            "session_duration_seconds": (self.last_activity_time - self.session_start_time).total_seconds()
        }


# Utility Functions

async def create_conversation_context(
    session_id: str,
    config_manager,
    max_conversation_history: int = 20,
    max_context_items: int = 100,
    **kwargs
) -> ConversationContext:
    """
    Create and initialize conversation context manager.

    Args:
        session_id: Unique session identifier
        config_manager: Configuration manager instance
        max_conversation_history: Maximum conversation turns to keep
        max_context_items: Maximum context items to store
        **kwargs: Additional configuration parameters

    Returns:
        Initialized conversation context manager
    """
    config = ConversationContextConfig(
        max_conversation_history=max_conversation_history,
        max_context_items=max_context_items,
        **kwargs
    )

    context = ConversationContext(session_id, config, config_manager)
    await context.initialize()
    await context.start()

    return context


# Utility Functions

async def create_conversation_context(
    session_id: str,
    config_manager,
    max_conversation_history: int = 20,
    max_context_items: int = 100,
    **kwargs
) -> ConversationContext:
    """
    Create and initialize conversation context manager.

    Args:
        session_id: Unique session identifier
        config_manager: Configuration manager instance
        max_conversation_history: Maximum conversation turns to keep
        max_context_items: Maximum context items to store
        **kwargs: Additional configuration parameters

    Returns:
        Initialized conversation context manager
    """
    config = ConversationContextConfig(
        max_conversation_history=max_conversation_history,
        max_context_items=max_context_items,
        **kwargs
    )

    context = ConversationContext(session_id, config, config_manager)
    await context.initialize()
    await context.start()

    return context


def create_context_item(
    key: str,
    value: Any,
    scope: ContextScope = ContextScope.SESSION,
    priority: ContextPriority = ContextPriority.MEDIUM,
    ttl_minutes: Optional[int] = None
) -> ContextItem:
    """
    Create context item with specified parameters.

    Args:
        key: Context item key
        value: Context item value
        scope: Context scope level
        priority: Context priority level
        ttl_minutes: Time to live in minutes

    Returns:
        Context item instance
    """
    expires_at = None
    if ttl_minutes is not None:
        expires_at = datetime.now() + timedelta(minutes=ttl_minutes)

    return ContextItem(
        key=key,
        value=value,
        scope=scope,
        priority=priority,
        expires_at=expires_at
    )


def create_conversation_intent(
    intent_type: IntentType,
    confidence: float,
    entities: Optional[Dict[str, Any]] = None
) -> ConversationIntent:
    """
    Create conversation intent with specified parameters.

    Args:
        intent_type: Type of intent
        confidence: Intent confidence score
        entities: Extracted entities

    Returns:
        Conversation intent instance
    """
    return ConversationIntent(
        intent_type=intent_type,
        confidence=confidence,
        entities=entities or {}
    )


# Utility Functions

async def create_conversation_context(
    session_id: str,
    config_manager,
    max_conversation_history: int = 20,
    max_context_items: int = 100,
    **kwargs
) -> ConversationContext:
    """
    Create and initialize conversation context manager.

    Args:
        session_id: Unique session identifier
        config_manager: Configuration manager instance
        max_conversation_history: Maximum conversation turns to keep
        max_context_items: Maximum context items to store
        **kwargs: Additional configuration parameters

    Returns:
        Initialized conversation context manager
    """
    config = ConversationContextConfig(
        max_conversation_history=max_conversation_history,
        max_context_items=max_context_items,
        **kwargs
    )

    context = ConversationContext(session_id, config, config_manager)
    await context.initialize()
    await context.start()

    return context


def create_context_item(
    key: str,
    value: Any,
    scope: ContextScope = ContextScope.SESSION,
    priority: ContextPriority = ContextPriority.MEDIUM,
    ttl_minutes: Optional[int] = None
) -> ContextItem:
    """
    Create context item with specified parameters.

    Args:
        key: Context item key
        value: Context item value
        scope: Context scope level
        priority: Context priority level
        ttl_minutes: Time to live in minutes

    Returns:
        Context item instance
    """
    expires_at = None
    if ttl_minutes is not None:
        expires_at = datetime.now() + timedelta(minutes=ttl_minutes)

    return ContextItem(
        key=key,
        value=value,
        scope=scope,
        priority=priority,
        expires_at=expires_at
    )


def create_conversation_intent(
    intent_type: IntentType,
    confidence: float,
    entities: Optional[Dict[str, Any]] = None
) -> ConversationIntent:
    """
    Create conversation intent with specified parameters.

    Args:
        intent_type: Type of intent
        confidence: Intent confidence score
        entities: Extracted entities

    Returns:
        Conversation intent instance
    """
    return ConversationIntent(
        intent_type=intent_type,
        confidence=confidence,
        entities=entities or {}
    )
