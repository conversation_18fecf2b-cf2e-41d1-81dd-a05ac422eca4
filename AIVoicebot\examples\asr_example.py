"""
Example usage of Automatic Speech Recognition (ASR) components.

This example demonstrates how to use SenseVoiceSmall for speech recognition
and ASR processing pipeline in the AI voice customer service system.
"""

import asyncio
import numpy as np
import logging
from typing import Optional
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import ASR components
from src.core.interfaces import AudioChunk, AudioFormat, TranscriptionResult
from src.components.asr import (
    SenseVoiceRecognizer, SenseVoiceConfig,
    ASRProcessor, ASRProcessorConfig, ASRResult,
    TranscriptionBuffer, BufferConfig,
    create_sensevoice_recognizer, create_asr_processor,
    calculate_recognition_metrics, calculate_asr_quality_metrics
)


class MockConfigManager:
    """Mock configuration manager for example."""
    
    def get(self, key: str, default=None):
        return default


def create_test_audio_chunk(duration_ms: int = 1000, sample_rate: int = 16000, 
                          speech_content: bool = True) -> AudioChunk:
    """
    Create test audio chunk with optional speech simulation.
    
    Args:
        duration_ms: Duration in milliseconds
        sample_rate: Sample rate in Hz
        speech_content: Whether to simulate speech content
        
    Returns:
        Test audio chunk
    """
    samples = int(duration_ms * sample_rate / 1000)
    
    if speech_content:
        # Simulate speech with multiple frequency components
        t = np.linspace(0, duration_ms / 1000, samples)
        
        # Create speech-like signal with formants
        signal = (np.sin(2 * np.pi * 150 * t) * 0.3 +  # F0 (fundamental)
                 np.sin(2 * np.pi * 800 * t) * 0.2 +   # F1 (first formant)
                 np.sin(2 * np.pi * 1200 * t) * 0.15 + # F2 (second formant)
                 np.random.normal(0, 0.1, samples))    # Noise
        
        # Add amplitude modulation to simulate speech rhythm
        envelope = 0.5 + 0.5 * np.sin(2 * np.pi * 5 * t)  # 5Hz modulation
        signal = signal * envelope
    else:
        # Just noise
        signal = np.random.normal(0, 0.1, samples)
    
    # Convert to int16 and clip
    audio_int16 = np.clip(signal * 32767, -32768, 32767).astype(np.int16)
    
    return AudioChunk(
        data=audio_int16.tobytes(),
        format=AudioFormat.PCM_16KHZ_MONO,
        timestamp=datetime.now(),
        duration_ms=duration_ms,
        sample_rate=sample_rate
    )
asyn
c def basic_speech_recognition_example():
    """Demonstrate basic speech recognition functionality."""
    logger.info("=== Basic Speech Recognition Example ===")
    
    # Create mock config manager
    config_manager = MockConfigManager()
    
    # Create SenseVoice configuration
    config = SenseVoiceConfig(
        model_path="models/SenseVoiceSmall/model.pt",
        language="zh",
        confidence_threshold=0.3,
        use_gpu=False,  # Use CPU for example
        enable_language_detection=True,
        return_timestamps=True
    )
    
    try:
        # Create recognizer using utility function
        recognizer = await create_sensevoice_recognizer(
            model_path=config.model_path,
            language=config.language,
            use_gpu=config.use_gpu,
            config_manager=config_manager,
            confidence_threshold=config.confidence_threshold
        )
        
        logger.info(f"SenseVoice recognizer created")
        logger.info(f"Model info: {recognizer.model_info}")
        
        # Test with different audio samples
        test_scenarios = [
            ("Chinese speech", True, 2000),
            ("Short utterance", True, 800),
            ("Long speech", True, 3000),
            ("Background noise", False, 1000),
            ("Quiet speech", True, 1500)
        ]
        
        results = []
        
        for scenario_name, has_speech, duration_ms in test_scenarios:
            logger.info(f"\nTesting: {scenario_name}")
            
            # Create test audio
            audio_chunk = create_test_audio_chunk(
                duration_ms=duration_ms,
                speech_content=has_speech
            )
            
            # Perform recognition
            start_time = datetime.now()
            result = await recognizer.transcribe(audio_chunk)
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            results.append(result)
            
            logger.info(f"  Result: '{result.text}'")
            logger.info(f"  Confidence: {result.confidence:.3f}")
            logger.info(f"  Language: {result.language}")
            logger.info(f"  Processing time: {processing_time:.1f}ms")
            logger.info(f"  Audio duration: {duration_ms}ms")
            logger.info(f"  Real-time factor: {duration_ms / processing_time:.1f}x")
        
        # Show recognition statistics
        stats = recognizer.get_recognition_stats()
        logger.info(f"\nRecognition Statistics:")
        logger.info(f"  Total recognitions: {stats['total_recognitions']}")
        logger.info(f"  Average processing time: {stats['average_processing_time_s']:.3f}s")
        logger.info(f"  Real-time factor: {stats['real_time_factor']:.1f}x")
        logger.info(f"  Error rate: {stats['error_rate']:.2%}")
        
        # Analyze recognition quality
        quality_metrics = calculate_recognition_metrics(results)
        logger.info(f"\nQuality Metrics:")
        logger.info(f"  Average confidence: {quality_metrics['average_confidence']:.3f}")
        logger.info(f"  High confidence ratio: {quality_metrics['high_confidence_ratio']:.2%}")
        logger.info(f"  Empty results ratio: {quality_metrics['empty_results_ratio']:.2%}")
        logger.info(f"  Average text length: {quality_metrics['average_text_length']:.1f}")
        
        # Cleanup
        await recognizer.stop()
        await recognizer.cleanup()
        
    except Exception as e:
        logger.error(f"Speech recognition example failed: {e}")
        logger.info("Note: This example requires the SenseVoiceSmall model to be properly installed")


async def asr_processing_pipeline_example():
    """Demonstrate ASR processing pipeline functionality."""
    logger.info("\n=== ASR Processing Pipeline Example ===")
    
    config_manager = MockConfigManager()
    
    try:
        # Create recognizer
        recognizer = await create_sensevoice_recognizer(
            config_manager=config_manager,
            use_gpu=False
        )
        
        # Create ASR processor
        processor_config = ASRProcessorConfig(
            chunk_duration_ms=1000,
            min_confidence_threshold=0.3,
            enable_confidence_filtering=True,
            enable_duplicate_filtering=True,
            max_retry_attempts=2
        )
        
        processor = ASRProcessor(processor_config, recognizer, config_manager)
        await processor.initialize()
        await processor.start()
        
        logger.info("ASR processing pipeline created")
        
        # Add result handler
        processed_results = []
        async def result_handler(result: ASRResult):
            processed_results.append(result)
            logger.info(f"Processed: '{result.transcription.text}' "
                       f"(confidence: {result.confidence_score:.3f}, "
                       f"processing: {result.processing_time_ms:.1f}ms)")
        
        processor.add_result_handler(result_handler)
        
        # Process multiple audio chunks
        logger.info("\nProcessing audio chunks...")
        
        for i in range(5):
            # Create varied audio content
            has_speech = i % 2 == 0  # Alternate speech and non-speech
            duration = 800 + i * 200  # Varying duration
            
            audio_chunk = create_test_audio_chunk(
                duration_ms=duration,
                speech_content=has_speech
            )
            
            logger.info(f"Processing chunk {i+1}: {duration}ms, speech={has_speech}")
            await processor.process_audio_chunk(audio_chunk)
            
            # Small delay between chunks
            await asyncio.sleep(0.1)
        
        # Wait for processing to complete
        await asyncio.sleep(1.0)
        
        # Get processing statistics
        stats = processor.get_processing_stats()
        logger.info(f"\nProcessing Statistics:")
        logger.info(f"  Total processed: {stats['total_processed']}")
        logger.info(f"  Successful: {stats['successful_recognitions']}")
        logger.info(f"  Failed: {stats['failed_recognitions']}")
        logger.info(f"  Success rate: {stats['success_rate']:.2%}")
        logger.info(f"  Queue size: {stats['queue_size']}")
        
        # Analyze processed results
        if processed_results:
            asr_metrics = calculate_asr_quality_metrics(processed_results)
            logger.info(f"\nASR Quality Metrics:")
            logger.info(f"  Total results: {asr_metrics['total_results']}")
            logger.info(f"  Average confidence: {asr_metrics['average_confidence']:.3f}")
            logger.info(f"  Average processing time: {asr_metrics['average_processing_time_ms']:.1f}ms")
            logger.info(f"  High confidence ratio: {asr_metrics['high_confidence_ratio']:.2%}")
            logger.info(f"  Retry rate: {asr_metrics['retry_rate']:.2%}")
        
        # Cleanup
        await processor.stop()
        await processor.cleanup()
        await recognizer.stop()
        await recognizer.cleanup()
        
    except Exception as e:
        logger.error(f"ASR processing pipeline example failed: {e}")


async def transcription_buffer_example():
    """Demonstrate transcription buffer functionality."""
    logger.info("\n=== Transcription Buffer Example ===")
    
    # Create buffer configuration
    buffer_config = BufferConfig(
        max_size=20,
        timeout_ms=2000,
        enable_result_merging=True,
        merge_threshold_ms=500
    )
    
    # Create transcription buffer
    buffer = TranscriptionBuffer(buffer_config)
    await buffer.initialize()
    await buffer.start()
    
    logger.info("Transcription buffer created")
    
    # Add result handler
    async def result_handler(result: TranscriptionResult):
        logger.info(f"Buffer received: '{result.text}' "
                   f"(final: {result.is_final}, confidence: {result.confidence:.3f})")
    
    buffer.add_result_handler(result_handler)
    
    # Simulate adding partial and final results
    logger.info("\nAdding transcription results...")
    
    # Add partial results
    partial_texts = ["你好", "你好世", "你好世界"]
    for i, text in enumerate(partial_texts):
        result = TranscriptionResult(
            text=text,
            confidence=0.6 + i * 0.1,
            is_final=False,
            timestamp=datetime.now(),
            language="zh"
        )
        
        await buffer.add_result(result)
        await asyncio.sleep(0.2)  # Simulate timing
    
    # Add final result
    final_result = TranscriptionResult(
        text="你好世界，这是最终结果",
        confidence=0.9,
        is_final=True,
        timestamp=datetime.now(),
        language="zh"
    )
    
    await buffer.add_result(final_result)
    
    # Retrieve results
    logger.info("\nRetrieving results...")
    
    # Get final result
    final = await buffer.get_final_result(timeout_ms=1000)
    if final:
        logger.info(f"Final result: '{final.text}' (confidence: {final.confidence:.3f})")
    
    # Get partial results
    partials = await buffer.get_partial_results()
    logger.info(f"Partial results count: {len(partials)}")
    for partial in partials:
        logger.info(f"  Partial: '{partial.text}' (confidence: {partial.confidence:.3f})")
    
    # Get buffer statistics
    stats = buffer.get_buffer_stats()
    logger.info(f"\nBuffer Statistics:")
    logger.info(f"  Total added: {stats['total_added']}")
    logger.info(f"  Total merged: {stats['total_merged']}")
    logger.info(f"  Total finalized: {stats['total_finalized']}")
    logger.info(f"  Merge rate: {stats['merge_rate']:.2%}")
    logger.info(f"  Finalization rate: {stats['finalization_rate']:.2%}")
    
    # Cleanup
    await buffer.stop()
    await buffer.cleanup()


async def batch_processing_example():
    """Demonstrate batch processing capabilities."""
    logger.info("\n=== Batch Processing Example ===")
    
    config_manager = MockConfigManager()
    
    try:
        # Create recognizer
        recognizer = await create_sensevoice_recognizer(
            config_manager=config_manager,
            use_gpu=False
        )
        
        # Create multiple audio chunks for batch processing
        audio_chunks = []
        for i in range(5):
            chunk = create_test_audio_chunk(
                duration_ms=1000 + i * 200,
                speech_content=True
            )
            audio_chunks.append(chunk)
        
        logger.info(f"Created {len(audio_chunks)} audio chunks for batch processing")
        
        # Process batch
        start_time = datetime.now()
        results = await recognizer.transcribe_batch(audio_chunks)
        processing_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"Batch processing completed in {processing_time:.2f}s")
        
        # Analyze results
        total_audio_duration = sum(chunk.duration_ms for chunk in audio_chunks) / 1000
        real_time_factor = total_audio_duration / processing_time
        
        logger.info(f"Total audio duration: {total_audio_duration:.2f}s")
        logger.info(f"Real-time factor: {real_time_factor:.1f}x")
        
        # Show individual results
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}: '{result.text}' (confidence: {result.confidence:.3f})")
        
        # Calculate quality metrics
        quality_metrics = calculate_recognition_metrics(results)
        logger.info(f"\nBatch Quality Metrics:")
        logger.info(f"  Average confidence: {quality_metrics['average_confidence']:.3f}")
        logger.info(f"  Confidence range: {quality_metrics['min_confidence']:.3f} - {quality_metrics['max_confidence']:.3f}")
        logger.info(f"  Total characters: {quality_metrics['total_characters']}")
        
        # Cleanup
        await recognizer.stop()
        await recognizer.cleanup()
        
    except Exception as e:
        logger.error(f"Batch processing example failed: {e}")


async def main():
    """Run all ASR examples."""
    logger.info("Automatic Speech Recognition (ASR) Examples")
    logger.info("=" * 50)
    
    try:
        await basic_speech_recognition_example()
        await asr_processing_pipeline_example()
        await transcription_buffer_example()
        await batch_processing_example()
        
        logger.info("\n" + "=" * 50)
        logger.info("All ASR examples completed!")
        logger.info("\nNote: These examples use simulated audio data.")
        logger.info("For real speech recognition, ensure SenseVoiceSmall model is properly installed.")
        
    except Exception as e:
        logger.error(f"Examples failed: {e}")
        raise


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())