"""
Test utilities and fixtures for comprehensive unit testing.

This module provides common test utilities, mock objects, and fixtures
for testing all major components of the AI voice customer service system.
"""

import asyncio
import json
import os
import tempfile
import wave
import struct
from typing import Dict, Any, List, Optional, Callable
from unittest.mock import Mock, AsyncMock, MagicMock
from datetime import datetime, timedelta
import uuid
import numpy as np

# Test data directory
TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), "test_data")
os.makedirs(TEST_DATA_DIR, exist_ok=True)


class MockConfigManager:
    """Mock configuration manager for testing."""
    
    def __init__(self, config_data: Dict[str, Any] = None):
        self.config_data = config_data or {}
        self.default_config = {
            # Logging configuration
            "logging.level": "INFO",
            "logging.format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            
            # Performance monitoring
            "performance.metrics_interval": 10,
            "performance.alert_thresholds": {
                "cpu_percent": 80,
                "memory_percent": 85,
                "response_time_ms": 1000
            },
            
            # Health monitoring
            "health.check_interval_seconds": 30,
            "health.recovery_enabled": True,
            "health.max_recovery_attempts": 3,
            
            # Component coordination
            "coordination.max_queue_size": 100,
            "coordination.event_timeout_seconds": 5.0,
            
            # Telephony
            "telephony.routing_strategy": "least_busy",
            "telephony.max_concurrent_calls": 10,
            "telephony.esp32": {"enabled": False},
            "telephony.sip": {"enabled": False},
            
            # Conversation
            "conversation.max_history_length": 50,
            "conversation.context_window_size": 10,
            
            # Prompts
            "prompts.template_dir": "templates",
            "prompts.default_language": "zh-CN",
            
            # Call management
            "calls.max_concurrent": 100,
            "calls.default_timeout": 300,
            "calls.cleanup_interval": 60
        }
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        # Check custom config first
        if key in self.config_data:
            return self.config_data[key]
        
        # Check default config
        if key in self.default_config:
            return self.default_config[key]
        
        return default
    
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value."""
        self.config_data[key] = value
    
    def update_config(self, config_dict: Dict[str, Any]) -> None:
        """Update multiple configuration values."""
        self.config_data.update(config_dict)


class MockLogger:
    """Mock logger for testing."""
    
    def __init__(self):
        self.logs = []
    
    def debug(self, message, *args, **kwargs):
        self.logs.append(("DEBUG", message, args, kwargs))
    
    def info(self, message, *args, **kwargs):
        self.logs.append(("INFO", message, args, kwargs))
    
    def warning(self, message, *args, **kwargs):
        self.logs.append(("WARNING", message, args, kwargs))
    
    def error(self, message, *args, **kwargs):
        self.logs.append(("ERROR", message, args, kwargs))
    
    def critical(self, message, *args, **kwargs):
        self.logs.append(("CRITICAL", message, args, kwargs))
    
    def get_logs(self, level: str = None) -> List[tuple]:
        """Get logged messages, optionally filtered by level."""
        if level:
            return [log for log in self.logs if log[0] == level]
        return self.logs
    
    def clear_logs(self):
        """Clear all logged messages."""
        self.logs.clear()


class AudioTestData:
    """Audio test data generator and utilities."""
    
    @staticmethod
    def generate_sine_wave(
        frequency: float = 440.0,
        duration: float = 1.0,
        sample_rate: int = 16000,
        amplitude: float = 0.5
    ) -> bytes:
        """Generate sine wave audio data."""
        num_samples = int(duration * sample_rate)
        samples = []
        
        for i in range(num_samples):
            t = i / sample_rate
            sample = amplitude * np.sin(2 * np.pi * frequency * t)
            # Convert to 16-bit PCM
            sample_int = int(sample * 32767)
            samples.append(sample_int)
        
        # Pack as 16-bit little-endian
        return struct.pack('<' + 'h' * len(samples), *samples)
    
    @staticmethod
    def generate_white_noise(
        duration: float = 1.0,
        sample_rate: int = 16000,
        amplitude: float = 0.1
    ) -> bytes:
        """Generate white noise audio data."""
        num_samples = int(duration * sample_rate)
        samples = np.random.normal(0, amplitude, num_samples)
        
        # Convert to 16-bit PCM
        samples_int = (samples * 32767).astype(np.int16)
        return samples_int.tobytes()
    
    @staticmethod
    def create_wav_file(
        audio_data: bytes,
        filename: str,
        sample_rate: int = 16000,
        channels: int = 1
    ) -> str:
        """Create a WAV file from audio data."""
        filepath = os.path.join(TEST_DATA_DIR, filename)
        
        with wave.open(filepath, 'wb') as wav_file:
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data)
        
        return filepath
    
    @staticmethod
    def load_test_audio(filename: str) -> bytes:
        """Load test audio data from file."""
        filepath = os.path.join(TEST_DATA_DIR, filename)
        if not os.path.exists(filepath):
            # Generate default test audio if file doesn't exist
            audio_data = AudioTestData.generate_sine_wave()
            AudioTestData.create_wav_file(audio_data, filename)
            return audio_data
        
        with wave.open(filepath, 'rb') as wav_file:
            return wav_file.readframes(wav_file.getnframes())


class TextTestData:
    """Text test data and conversation fixtures."""
    
    SAMPLE_CONVERSATIONS = [
        {
            "conversation_id": "conv_001",
            "messages": [
                {"role": "user", "content": "你好，我想查询我的账户余额"},
                {"role": "assistant", "content": "您好！我可以帮您查询账户余额。请提供您的账户号码。"},
                {"role": "user", "content": "我的账户号码是*********"},
                {"role": "assistant", "content": "好的，正在为您查询账户*********的余额信息..."}
            ]
        },
        {
            "conversation_id": "conv_002",
            "messages": [
                {"role": "user", "content": "我要投诉你们的服务"},
                {"role": "assistant", "content": "非常抱歉给您带来不便。我会认真记录您的投诉并帮助解决问题。请详细说明遇到的问题。"},
                {"role": "user", "content": "昨天的转账没有到账"},
                {"role": "assistant", "content": "我理解您的担心。转账未到账确实令人焦虑。让我帮您查询转账状态。"}
            ]
        }
    ]
    
    SAMPLE_INTENTS = [
        {"intent": "account_inquiry", "confidence": 0.95, "entities": {"account_type": "savings"}},
        {"intent": "balance_check", "confidence": 0.88, "entities": {"account_number": "*********"}},
        {"intent": "complaint", "confidence": 0.92, "entities": {"issue_type": "transfer"}},
        {"intent": "transfer_inquiry", "confidence": 0.85, "entities": {"transaction_type": "transfer"}}
    ]
    
    SAMPLE_TRANSCRIPTIONS = [
        {"text": "你好我想查询账户余额", "confidence": 0.95, "language": "zh-CN"},
        {"text": "我要投诉服务质量", "confidence": 0.88, "language": "zh-CN"},
        {"text": "转账什么时候能到账", "confidence": 0.92, "language": "zh-CN"},
        {"text": "我忘记了密码怎么办", "confidence": 0.87, "language": "zh-CN"}
    ]
    
    @staticmethod
    def get_sample_conversation(conversation_id: str = None) -> Dict[str, Any]:
        """Get a sample conversation."""
        if conversation_id:
            for conv in TextTestData.SAMPLE_CONVERSATIONS:
                if conv["conversation_id"] == conversation_id:
                    return conv
        
        return TextTestData.SAMPLE_CONVERSATIONS[0]
    
    @staticmethod
    def get_sample_intent() -> Dict[str, Any]:
        """Get a sample intent classification result."""
        return TextTestData.SAMPLE_INTENTS[0]
    
    @staticmethod
    def get_sample_transcription() -> Dict[str, Any]:
        """Get a sample transcription result."""
        return TextTestData.SAMPLE_TRANSCRIPTIONS[0]


class MockExternalServices:
    """Mock external services for testing."""
    
    class MockASRService:
        """Mock ASR service."""
        
        def __init__(self, should_fail: bool = False):
            self.should_fail = should_fail
            self.transcription_results = TextTestData.SAMPLE_TRANSCRIPTIONS
            self.call_count = 0
        
        async def transcribe(self, audio_data: bytes) -> Dict[str, Any]:
            """Mock transcription."""
            self.call_count += 1
            
            if self.should_fail:
                raise Exception("ASR service failed")
            
            result_index = (self.call_count - 1) % len(self.transcription_results)
            return self.transcription_results[result_index]
    
    class MockTTSService:
        """Mock TTS service."""
        
        def __init__(self, should_fail: bool = False):
            self.should_fail = should_fail
            self.call_count = 0
        
        async def synthesize(self, text: str) -> bytes:
            """Mock speech synthesis."""
            self.call_count += 1
            
            if self.should_fail:
                raise Exception("TTS service failed")
            
            # Return generated audio data
            return AudioTestData.generate_sine_wave(duration=len(text) * 0.1)
    
    class MockLLMService:
        """Mock LLM service."""
        
        def __init__(self, should_fail: bool = False):
            self.should_fail = should_fail
            self.responses = [
                "我理解您的问题，让我为您查询相关信息。",
                "好的，我会帮助您解决这个问题。",
                "感谢您的耐心，我正在处理您的请求。",
                "根据您提供的信息，我建议您..."
            ]
            self.call_count = 0
        
        async def generate_response(self, messages: List[Dict[str, str]]) -> str:
            """Mock response generation."""
            self.call_count += 1
            
            if self.should_fail:
                raise Exception("LLM service failed")
            
            response_index = (self.call_count - 1) % len(self.responses)
            return self.responses[response_index]
    
    class MockVADService:
        """Mock VAD service."""
        
        def __init__(self, should_fail: bool = False):
            self.should_fail = should_fail
            self.speech_detected = True
            self.call_count = 0
        
        async def detect_speech(self, audio_data: bytes) -> Dict[str, Any]:
            """Mock voice activity detection."""
            self.call_count += 1
            
            if self.should_fail:
                raise Exception("VAD service failed")
            
            return {
                "speech_detected": self.speech_detected,
                "confidence": 0.9,
                "start_time": 0.0,
                "end_time": 1.0
            }


def create_test_call_session() -> Dict[str, Any]:
    """Create a test call session data."""
    return {
        "call_id": str(uuid.uuid4()),
        "session_id": str(uuid.uuid4()),
        "caller_number": "+*********0",
        "callee_number": "+0987654321",
        "start_time": datetime.now(),
        "status": "active",
        "protocol": "esp32_custom",
        "codec": "pcm_16khz"
    }


def create_test_conversation_context() -> Dict[str, Any]:
    """Create a test conversation context."""
    return {
        "conversation_id": str(uuid.uuid4()),
        "user_id": "test_user_001",
        "session_id": str(uuid.uuid4()),
        "language": "zh-CN",
        "start_time": datetime.now(),
        "message_history": TextTestData.get_sample_conversation()["messages"],
        "context_variables": {
            "user_name": "张三",
            "account_type": "premium",
            "last_login": "2024-01-15"
        }
    }


def create_test_audio_frame() -> Dict[str, Any]:
    """Create a test audio frame."""
    return {
        "data": AudioTestData.generate_sine_wave(duration=0.1),
        "codec": "pcm_16khz",
        "sample_rate": 16000,
        "channels": 1,
        "timestamp": datetime.now(),
        "sequence_number": 1
    }


# Initialize test data on module import
def setup_test_data():
    """Set up test data files."""
    # Create sample audio files
    sine_wave = AudioTestData.generate_sine_wave()
    AudioTestData.create_wav_file(sine_wave, "test_sine_wave.wav")
    
    white_noise = AudioTestData.generate_white_noise()
    AudioTestData.create_wav_file(white_noise, "test_white_noise.wav")
    
    # Create sample conversation data file
    conversations_file = os.path.join(TEST_DATA_DIR, "sample_conversations.json")
    with open(conversations_file, 'w', encoding='utf-8') as f:
        json.dump(TextTestData.SAMPLE_CONVERSATIONS, f, ensure_ascii=False, indent=2)


# Set up test data when module is imported
setup_test_data()
