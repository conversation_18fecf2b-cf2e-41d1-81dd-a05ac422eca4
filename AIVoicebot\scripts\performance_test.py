#!/usr/bin/env python3
"""
Performance Testing Script for AI Voice Customer Service

This script runs comprehensive performance tests to evaluate system performance
and identify optimization opportunities.
"""

import asyncio
import time
import logging
import statistics
import json
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.system_integrator import AIVoiceCustomerServiceSystem
from src.core.config_manager import ConfigManager
from src.optimization.performance_optimizer import PerformanceOptimizer, OptimizationType
from src.optimization.model_tuner import ModelTuner, OptimizationTechnique
from tests.test_utils import MockConfigManager


class PerformanceTester:
    """Performance testing suite."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.results = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests."""
        self.logger.info("Starting comprehensive performance tests...")
        
        start_time = datetime.now()
        
        # System integration tests
        self.results["system_integration"] = await self._test_system_integration()
        
        # Performance optimization tests
        self.results["performance_optimization"] = await self._test_performance_optimization()
        
        # Model tuning tests
        self.results["model_tuning"] = await self._test_model_tuning()
        
        # Load testing
        self.results["load_testing"] = await self._test_load_performance()
        
        # Memory testing
        self.results["memory_testing"] = await self._test_memory_performance()
        
        # Latency testing
        self.results["latency_testing"] = await self._test_latency_performance()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.results["test_summary"] = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "total_duration_seconds": duration,
            "tests_passed": sum(1 for test in self.results.values() 
                               if isinstance(test, dict) and test.get("success", False)),
            "total_tests": len([k for k in self.results.keys() if k != "test_summary"])
        }
        
        self.logger.info(f"Performance tests completed in {duration:.2f} seconds")
        return self.results
    
    async def _test_system_integration(self) -> Dict[str, Any]:
        """Test system integration performance."""
        self.logger.info("Testing system integration performance...")
        
        try:
            config_manager = MockConfigManager()
            system = AIVoiceCustomerServiceSystem(config_manager)
            
            # Test initialization time
            start_time = time.time()
            await system.initialize()
            init_time = time.time() - start_time
            
            # Test startup time
            start_time = time.time()
            await system.start()
            startup_time = time.time() - start_time
            
            # Test status retrieval time
            start_time = time.time()
            status = system.get_system_status()
            status_time = time.time() - start_time
            
            # Test shutdown time
            start_time = time.time()
            await system.stop()
            await system.cleanup()
            shutdown_time = time.time() - start_time
            
            return {
                "success": True,
                "metrics": {
                    "initialization_time_ms": init_time * 1000,
                    "startup_time_ms": startup_time * 1000,
                    "status_retrieval_time_ms": status_time * 1000,
                    "shutdown_time_ms": shutdown_time * 1000,
                    "components_initialized": len(system.components),
                    "system_state": status.state.value
                }
            }
            
        except Exception as e:
            self.logger.error(f"System integration test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_performance_optimization(self) -> Dict[str, Any]:
        """Test performance optimization capabilities."""
        self.logger.info("Testing performance optimization...")
        
        try:
            config_manager = MockConfigManager()
            optimizer = PerformanceOptimizer(config_manager)
            
            await optimizer.initialize()
            await optimizer.start()
            
            # Test metric collection
            start_time = time.time()
            for i in range(100):
                optimizer.add_metric("test_metric", i * 0.1, "test_unit", "test_component")
            metric_time = time.time() - start_time
            
            # Test profiling
            optimizer.start_profiling("test_profile")
            await asyncio.sleep(0.1)  # Simulate work
            profile_duration = optimizer.end_profiling("test_profile")
            
            # Test optimization
            start_time = time.time()
            result = await optimizer.optimize(OptimizationType.CPU)
            optimization_time = time.time() - start_time
            
            # Test benchmark
            start_time = time.time()
            benchmark_results = await optimizer.run_performance_benchmark()
            benchmark_time = time.time() - start_time
            
            await optimizer.stop()
            await optimizer.cleanup()
            
            return {
                "success": True,
                "metrics": {
                    "metric_collection_time_ms": metric_time * 1000,
                    "profile_duration_ms": profile_duration * 1000,
                    "optimization_time_ms": optimization_time * 1000,
                    "benchmark_time_ms": benchmark_time * 1000,
                    "optimization_success": result.success,
                    "benchmark_results": benchmark_results
                }
            }
            
        except Exception as e:
            self.logger.error(f"Performance optimization test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_model_tuning(self) -> Dict[str, Any]:
        """Test model tuning capabilities."""
        self.logger.info("Testing model tuning...")
        
        try:
            config_manager = MockConfigManager()
            tuner = ModelTuner(config_manager)
            
            await tuner.initialize()
            await tuner.start()
            
            # Test model tuning
            start_time = time.time()
            result = await tuner.tune_model("asr", OptimizationTechnique.QUANTIZATION)
            tuning_time = time.time() - start_time
            
            # Test comprehensive tuning
            start_time = time.time()
            comprehensive_results = await tuner.run_comprehensive_tuning()
            comprehensive_time = time.time() - start_time
            
            await tuner.stop()
            await tuner.cleanup()
            
            return {
                "success": True,
                "metrics": {
                    "single_tuning_time_ms": tuning_time * 1000,
                    "comprehensive_tuning_time_ms": comprehensive_time * 1000,
                    "tuning_success": result.success,
                    "models_tuned": len(comprehensive_results.get("tuning_results", {}))
                }
            }
            
        except Exception as e:
            self.logger.error(f"Model tuning test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_load_performance(self) -> Dict[str, Any]:
        """Test system performance under load."""
        self.logger.info("Testing load performance...")
        
        try:
            config_manager = MockConfigManager()
            system = AIVoiceCustomerServiceSystem(config_manager)
            
            await system.initialize()
            await system.start()
            
            # Simulate concurrent operations
            concurrent_tasks = 50
            task_duration = []
            
            async def simulate_call():
                start_time = time.time()
                # Simulate call processing
                call_id = await system.process_incoming_call("+1234567890", "+0987654321")
                duration = time.time() - start_time
                return duration, call_id is not None
            
            # Run concurrent tasks
            start_time = time.time()
            tasks = [simulate_call() for _ in range(concurrent_tasks)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # Analyze results
            successful_tasks = [r for r in results if not isinstance(r, Exception) and r[1]]
            task_durations = [r[0] for r in successful_tasks]
            
            await system.stop()
            await system.cleanup()
            
            return {
                "success": True,
                "metrics": {
                    "concurrent_tasks": concurrent_tasks,
                    "successful_tasks": len(successful_tasks),
                    "success_rate": len(successful_tasks) / concurrent_tasks,
                    "total_time_seconds": total_time,
                    "average_task_duration_ms": statistics.mean(task_durations) * 1000 if task_durations else 0,
                    "max_task_duration_ms": max(task_durations) * 1000 if task_durations else 0,
                    "min_task_duration_ms": min(task_durations) * 1000 if task_durations else 0,
                    "throughput_tasks_per_second": len(successful_tasks) / total_time if total_time > 0 else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Load performance test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_memory_performance(self) -> Dict[str, Any]:
        """Test memory performance."""
        self.logger.info("Testing memory performance...")
        
        try:
            import tracemalloc
            
            tracemalloc.start()
            
            config_manager = MockConfigManager()
            system = AIVoiceCustomerServiceSystem(config_manager)
            
            # Measure memory during initialization
            await system.initialize()
            init_memory = tracemalloc.get_traced_memory()
            
            # Measure memory during startup
            await system.start()
            startup_memory = tracemalloc.get_traced_memory()
            
            # Simulate memory-intensive operations
            for i in range(100):
                await system.process_incoming_call(f"+123456789{i}", "+0987654321")
            
            peak_memory = tracemalloc.get_traced_memory()
            
            await system.stop()
            await system.cleanup()
            
            final_memory = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            return {
                "success": True,
                "metrics": {
                    "init_memory_mb": init_memory[0] / (1024 * 1024),
                    "startup_memory_mb": startup_memory[0] / (1024 * 1024),
                    "peak_memory_mb": peak_memory[1] / (1024 * 1024),
                    "final_memory_mb": final_memory[0] / (1024 * 1024),
                    "memory_growth_mb": (peak_memory[1] - init_memory[0]) / (1024 * 1024)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Memory performance test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_latency_performance(self) -> Dict[str, Any]:
        """Test latency performance."""
        self.logger.info("Testing latency performance...")
        
        try:
            config_manager = MockConfigManager()
            system = AIVoiceCustomerServiceSystem(config_manager)
            
            await system.initialize()
            await system.start()
            
            # Test various operations latency
            latencies = {}
            
            # Status retrieval latency
            times = []
            for _ in range(100):
                start_time = time.time()
                system.get_system_status()
                times.append((time.time() - start_time) * 1000)
            latencies["status_retrieval_ms"] = {
                "mean": statistics.mean(times),
                "median": statistics.median(times),
                "p95": sorted(times)[int(len(times) * 0.95)],
                "p99": sorted(times)[int(len(times) * 0.99)]
            }
            
            # Component status latency
            times = []
            for _ in range(100):
                start_time = time.time()
                system.get_component_status()
                times.append((time.time() - start_time) * 1000)
            latencies["component_status_ms"] = {
                "mean": statistics.mean(times),
                "median": statistics.median(times),
                "p95": sorted(times)[int(len(times) * 0.95)],
                "p99": sorted(times)[int(len(times) * 0.99)]
            }
            
            await system.stop()
            await system.cleanup()
            
            return {
                "success": True,
                "metrics": latencies
            }
            
        except Exception as e:
            self.logger.error(f"Latency performance test failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def save_results(self, filename: str = None) -> str:
        """Save test results to file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_test_results_{timestamp}.json"
        
        filepath = os.path.join("logs", "performance", filename)
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Performance test results saved to: {filepath}")
        return filepath
    
    def print_summary(self):
        """Print test results summary."""
        if not self.results:
            print("No test results available")
            return
        
        print("\n" + "="*60)
        print("PERFORMANCE TEST RESULTS SUMMARY")
        print("="*60)
        
        summary = self.results.get("test_summary", {})
        print(f"Total Duration: {summary.get('total_duration_seconds', 0):.2f} seconds")
        print(f"Tests Passed: {summary.get('tests_passed', 0)}/{summary.get('total_tests', 0)}")
        
        for test_name, test_result in self.results.items():
            if test_name == "test_summary":
                continue
                
            print(f"\n{test_name.upper()}:")
            if isinstance(test_result, dict):
                if test_result.get("success"):
                    print("  ✅ PASSED")
                    if "metrics" in test_result:
                        for metric, value in test_result["metrics"].items():
                            if isinstance(value, (int, float)):
                                print(f"    {metric}: {value:.2f}")
                            else:
                                print(f"    {metric}: {value}")
                else:
                    print("  ❌ FAILED")
                    if "error" in test_result:
                        print(f"    Error: {test_result['error']}")
        
        print("\n" + "="*60)


async def main():
    """Main function."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create performance tester
    tester = PerformanceTester()
    
    try:
        # Run all tests
        results = await tester.run_all_tests()
        
        # Print summary
        tester.print_summary()
        
        # Save results
        filepath = tester.save_results()
        print(f"\nDetailed results saved to: {filepath}")
        
        # Return success code
        summary = results.get("test_summary", {})
        if summary.get("tests_passed", 0) == summary.get("total_tests", 0):
            print("\n🎉 All performance tests passed!")
            return 0
        else:
            print(f"\n⚠️  {summary.get('total_tests', 0) - summary.get('tests_passed', 0)} tests failed")
            return 1
            
    except Exception as e:
        logging.error(f"Performance testing failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
