"""
Audio Pipeline Core Infrastructure

This module provides the core audio processing pipeline including:
- AudioPipeline for coordinating audio flow
- AudioBuffer for real-time audio data management
- AudioProcessor for format conversion and preprocessing
- Real-time audio streaming and buffering
"""

import asyncio
import threading
import time
import numpy as np
from typing import Optional, Callable, List, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum
import logging
from collections import deque
import wave
import io

logger = logging.getLogger(__name__)


class AudioFormat(Enum):
    """Supported audio formats"""
    PCM_16 = "pcm_16"
    PCM_24 = "pcm_24"
    PCM_32 = "pcm_32"
    FLOAT_32 = "float_32"
    WAV = "wav"
    MP3 = "mp3"


class AudioState(Enum):
    """Audio pipeline states"""
    IDLE = "idle"
    RECORDING = "recording"
    PROCESSING = "processing"
    PLAYING = "playing"
    ERROR = "error"


@dataclass
class AudioConfig:
    """Audio configuration parameters"""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    format: AudioFormat = AudioFormat.PCM_16
    buffer_duration: float = 5.0  # seconds
    
    # Processing parameters
    normalize: bool = True
    remove_silence: bool = False
    noise_reduction: bool = False
    
    # Performance settings
    max_buffer_size: int = 10 * 1024 * 1024  # 10MB
    processing_threads: int = 2


@dataclass
class AudioChunk:
    """Audio data chunk with metadata"""
    data: np.ndarray
    timestamp: float
    sample_rate: int
    channels: int
    chunk_id: int
    format: AudioFormat = AudioFormat.PCM_16
    
    def __post_init__(self):
        """Validate audio chunk after initialization"""
        if self.data is None or len(self.data) == 0:
            raise ValueError("Audio data cannot be empty")
        
        if self.sample_rate <= 0:
            raise ValueError("Sample rate must be positive")
        
        if self.channels <= 0:
            raise ValueError("Channels must be positive")
    
    def duration(self) -> float:
        """Get duration of audio chunk in seconds"""
        return len(self.data) / (self.sample_rate * self.channels)
    
    def to_bytes(self) -> bytes:
        """Convert audio data to bytes"""
        if self.format == AudioFormat.PCM_16:
            return (self.data * 32767).astype(np.int16).tobytes()
        elif self.format == AudioFormat.FLOAT_32:
            return self.data.astype(np.float32).tobytes()
        else:
            raise ValueError(f"Unsupported format for byte conversion: {self.format}")
    
    def to_wav_bytes(self) -> bytes:
        """Convert audio data to WAV format bytes"""
        buffer = io.BytesIO()
        
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(self.channels)
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(self.to_bytes())
        
        return buffer.getvalue()


class AudioBuffer:
    """
    Thread-safe circular buffer for real-time audio data management
    
    Features:
    - Circular buffer with configurable size
    - Thread-safe operations
    - Automatic overflow handling
    - Chunk-based data management
    """
    
    def __init__(self, config: AudioConfig):
        """
        Initialize audio buffer
        
        Args:
            config: Audio configuration
        """
        self.config = config
        self.max_chunks = int(config.buffer_duration * config.sample_rate / config.chunk_size)
        
        # Buffer storage
        self._buffer: deque = deque(maxlen=self.max_chunks)
        self._lock = threading.RLock()
        self._chunk_counter = 0
        
        # Statistics
        self._total_chunks_written = 0
        self._total_chunks_read = 0
        self._buffer_overflows = 0
        
        logger.info(f"AudioBuffer initialized with max_chunks: {self.max_chunks}")
    
    def write(self, data: np.ndarray) -> bool:
        """
        Write audio data to buffer
        
        Args:
            data: Audio data array
            
        Returns:
            True if write successful, False if buffer full
        """
        try:
            with self._lock:
                timestamp = time.time()
                chunk = AudioChunk(
                    data=data.copy(),
                    timestamp=timestamp,
                    sample_rate=self.config.sample_rate,
                    channels=self.config.channels,
                    chunk_id=self._chunk_counter,
                    format=self.config.format
                )
                
                # Check if buffer is full
                if len(self._buffer) >= self.max_chunks:
                    self._buffer_overflows += 1
                    logger.warning("Audio buffer overflow, dropping oldest chunk")
                
                self._buffer.append(chunk)
                self._chunk_counter += 1
                self._total_chunks_written += 1
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to write to audio buffer: {e}")
            return False
    
    def read(self, num_chunks: int = 1) -> List[AudioChunk]:
        """
        Read audio chunks from buffer
        
        Args:
            num_chunks: Number of chunks to read
            
        Returns:
            List of audio chunks
        """
        chunks = []
        
        try:
            with self._lock:
                for _ in range(min(num_chunks, len(self._buffer))):
                    if self._buffer:
                        chunk = self._buffer.popleft()
                        chunks.append(chunk)
                        self._total_chunks_read += 1
                
        except Exception as e:
            logger.error(f"Failed to read from audio buffer: {e}")
        
        return chunks
    
    def peek(self, num_chunks: int = 1) -> List[AudioChunk]:
        """
        Peek at audio chunks without removing them
        
        Args:
            num_chunks: Number of chunks to peek at
            
        Returns:
            List of audio chunks
        """
        chunks = []
        
        try:
            with self._lock:
                for i in range(min(num_chunks, len(self._buffer))):
                    chunks.append(self._buffer[i])
                
        except Exception as e:
            logger.error(f"Failed to peek audio buffer: {e}")
        
        return chunks
    
    def clear(self) -> None:
        """Clear all data from buffer"""
        with self._lock:
            self._buffer.clear()
            logger.debug("Audio buffer cleared")
    
    def size(self) -> int:
        """Get current buffer size"""
        with self._lock:
            return len(self._buffer)
    
    def is_empty(self) -> bool:
        """Check if buffer is empty"""
        with self._lock:
            return len(self._buffer) == 0
    
    def is_full(self) -> bool:
        """Check if buffer is full"""
        with self._lock:
            return len(self._buffer) >= self.max_chunks
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get buffer statistics"""
        with self._lock:
            return {
                "current_size": len(self._buffer),
                "max_size": self.max_chunks,
                "total_written": self._total_chunks_written,
                "total_read": self._total_chunks_read,
                "overflows": self._buffer_overflows,
                "utilization": len(self._buffer) / self.max_chunks if self.max_chunks > 0 else 0
            }


class AudioProcessor:
    """
    Audio format conversion and preprocessing
    
    Features:
    - Format conversion between different audio formats
    - Sample rate conversion
    - Channel conversion (mono/stereo)
    - Audio normalization
    - Noise reduction
    - Silence removal
    """
    
    def __init__(self, config: AudioConfig):
        """
        Initialize audio processor
        
        Args:
            config: Audio configuration
        """
        self.config = config
        logger.info("AudioProcessor initialized")
    
    def convert_format(self, chunk: AudioChunk, target_format: AudioFormat) -> AudioChunk:
        """
        Convert audio chunk to target format
        
        Args:
            chunk: Input audio chunk
            target_format: Target audio format
            
        Returns:
            Converted audio chunk
        """
        if chunk.format == target_format:
            return chunk
        
        try:
            converted_data = chunk.data.copy()
            
            # Convert between formats
            if chunk.format == AudioFormat.FLOAT_32 and target_format == AudioFormat.PCM_16:
                # Float32 to PCM16
                converted_data = np.clip(converted_data, -1.0, 1.0)
                converted_data = (converted_data * 32767).astype(np.int16)
            
            elif chunk.format == AudioFormat.PCM_16 and target_format == AudioFormat.FLOAT_32:
                # PCM16 to Float32
                converted_data = converted_data.astype(np.float32) / 32767.0
            
            else:
                logger.warning(f"Format conversion from {chunk.format} to {target_format} not implemented")
                return chunk
            
            return AudioChunk(
                data=converted_data,
                timestamp=chunk.timestamp,
                sample_rate=chunk.sample_rate,
                channels=chunk.channels,
                chunk_id=chunk.chunk_id,
                format=target_format
            )
            
        except Exception as e:
            logger.error(f"Failed to convert audio format: {e}")
            return chunk
    
    def resample(self, chunk: AudioChunk, target_sample_rate: int) -> AudioChunk:
        """
        Resample audio chunk to target sample rate
        
        Args:
            chunk: Input audio chunk
            target_sample_rate: Target sample rate
            
        Returns:
            Resampled audio chunk
        """
        if chunk.sample_rate == target_sample_rate:
            return chunk
        
        try:
            # Simple linear interpolation resampling
            # For production, consider using scipy.signal.resample or librosa
            ratio = target_sample_rate / chunk.sample_rate
            new_length = int(len(chunk.data) * ratio)
            
            # Linear interpolation
            old_indices = np.linspace(0, len(chunk.data) - 1, len(chunk.data))
            new_indices = np.linspace(0, len(chunk.data) - 1, new_length)
            resampled_data = np.interp(new_indices, old_indices, chunk.data)
            
            return AudioChunk(
                data=resampled_data,
                timestamp=chunk.timestamp,
                sample_rate=target_sample_rate,
                channels=chunk.channels,
                chunk_id=chunk.chunk_id,
                format=chunk.format
            )
            
        except Exception as e:
            logger.error(f"Failed to resample audio: {e}")
            return chunk
    
    def normalize(self, chunk: AudioChunk) -> AudioChunk:
        """
        Normalize audio chunk amplitude
        
        Args:
            chunk: Input audio chunk
            
        Returns:
            Normalized audio chunk
        """
        try:
            data = chunk.data.copy()
            
            # Avoid division by zero
            max_val = np.max(np.abs(data))
            if max_val > 0:
                data = data / max_val
            
            return AudioChunk(
                data=data,
                timestamp=chunk.timestamp,
                sample_rate=chunk.sample_rate,
                channels=chunk.channels,
                chunk_id=chunk.chunk_id,
                format=chunk.format
            )
            
        except Exception as e:
            logger.error(f"Failed to normalize audio: {e}")
            return chunk
    
    def remove_silence(self, chunk: AudioChunk, threshold: float = 0.01) -> Optional[AudioChunk]:
        """
        Remove silence from audio chunk
        
        Args:
            chunk: Input audio chunk
            threshold: Silence threshold (0.0 to 1.0)
            
        Returns:
            Audio chunk without silence, or None if all silence
        """
        try:
            data = chunk.data
            
            # Calculate RMS energy
            rms = np.sqrt(np.mean(data ** 2))
            
            # If RMS is below threshold, consider it silence
            if rms < threshold:
                return None
            
            # Find non-silent regions
            energy = np.abs(data)
            non_silent_mask = energy > threshold
            
            if not np.any(non_silent_mask):
                return None
            
            # Extract non-silent data
            non_silent_data = data[non_silent_mask]
            
            return AudioChunk(
                data=non_silent_data,
                timestamp=chunk.timestamp,
                sample_rate=chunk.sample_rate,
                channels=chunk.channels,
                chunk_id=chunk.chunk_id,
                format=chunk.format
            )
            
        except Exception as e:
            logger.error(f"Failed to remove silence: {e}")
            return chunk
    
    def apply_noise_reduction(self, chunk: AudioChunk) -> AudioChunk:
        """
        Apply basic noise reduction to audio chunk
        
        Args:
            chunk: Input audio chunk
            
        Returns:
            Noise-reduced audio chunk
        """
        try:
            data = chunk.data.copy()
            
            # Simple spectral subtraction (placeholder)
            # For production, consider using more sophisticated algorithms
            
            # Apply a simple high-pass filter to remove low-frequency noise
            if len(data) > 1:
                # Simple difference filter
                filtered_data = np.diff(data, prepend=data[0])
                # Normalize
                max_val = np.max(np.abs(filtered_data))
                if max_val > 0:
                    filtered_data = filtered_data / max_val * np.max(np.abs(data))
                data = filtered_data
            
            return AudioChunk(
                data=data,
                timestamp=chunk.timestamp,
                sample_rate=chunk.sample_rate,
                channels=chunk.channels,
                chunk_id=chunk.chunk_id,
                format=chunk.format
            )
            
        except Exception as e:
            logger.error(f"Failed to apply noise reduction: {e}")
            return chunk
    
    def process_chunk(self, chunk: AudioChunk) -> Optional[AudioChunk]:
        """
        Apply all configured processing to audio chunk
        
        Args:
            chunk: Input audio chunk
            
        Returns:
            Processed audio chunk or None if filtered out
        """
        try:
            processed_chunk = chunk
            
            # Apply normalization if enabled
            if self.config.normalize:
                processed_chunk = self.normalize(processed_chunk)
            
            # Apply noise reduction if enabled
            if self.config.noise_reduction:
                processed_chunk = self.apply_noise_reduction(processed_chunk)
            
            # Remove silence if enabled
            if self.config.remove_silence:
                processed_chunk = self.remove_silence(processed_chunk)
                if processed_chunk is None:
                    return None
            
            # Convert to target format
            if processed_chunk.format != self.config.format:
                processed_chunk = self.convert_format(processed_chunk, self.config.format)
            
            # Resample if needed
            if processed_chunk.sample_rate != self.config.sample_rate:
                processed_chunk = self.resample(processed_chunk, self.config.sample_rate)
            
            return processed_chunk
            
        except Exception as e:
            logger.error(f"Failed to process audio chunk: {e}")
            return chunk

class AudioPipeline:
    """
    Main audio pipeline for coordinating audio flow
    
    Features:
    - Real-time audio streaming
    - Asynchronous processing
    - Event-driven architecture
    - Pipeline state management
    - Error handling and recovery
    """
    
    def __init__(self, config: AudioConfig):
        """
        Initialize audio pipeline
        
        Args:
            config: Audio configuration
        """
        self.config = config
        self.state = AudioState.IDLE
        
        # Core components
        self.input_buffer = AudioBuffer(config)
        self.output_buffer = AudioBuffer(config)
        self.processor = AudioProcessor(config)
        
        # Processing control
        self._processing_task: Optional[asyncio.Task] = None
        self._stop_event = asyncio.Event()
        self._state_lock = asyncio.Lock()
        
        # Event callbacks
        self._callbacks: Dict[str, List[Callable]] = {
            "chunk_received": [],
            "chunk_processed": [],
            "state_changed": [],
            "error": []
        }
        
        # Statistics
        self._stats = {
            "chunks_processed": 0,
            "processing_errors": 0,
            "start_time": None,
            "total_duration": 0.0
        }
        
        logger.info("AudioPipeline initialized")
    
    def add_callback(self, event: str, callback: Callable) -> None:
        """
        Add event callback
        
        Args:
            event: Event name (chunk_received, chunk_processed, state_changed, error)
            callback: Callback function
        """
        if event in self._callbacks:
            self._callbacks[event].append(callback)
            logger.debug(f"Added callback for event: {event}")
        else:
            logger.warning(f"Unknown event type: {event}")
    
    def remove_callback(self, event: str, callback: Callable) -> None:
        """
        Remove event callback
        
        Args:
            event: Event name
            callback: Callback function to remove
        """
        if event in self._callbacks and callback in self._callbacks[event]:
            self._callbacks[event].remove(callback)
            logger.debug(f"Removed callback for event: {event}")
    
    async def _emit_event(self, event: str, *args, **kwargs) -> None:
        """
        Emit event to all registered callbacks
        
        Args:
            event: Event name
            *args: Event arguments
            **kwargs: Event keyword arguments
        """
        if event in self._callbacks:
            for callback in self._callbacks[event]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(*args, **kwargs)
                    else:
                        callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Error in callback for event {event}: {e}")
    
    async def _set_state(self, new_state: AudioState) -> None:
        """
        Set pipeline state and emit state change event
        
        Args:
            new_state: New pipeline state
        """
        async with self._state_lock:
            if self.state != new_state:
                old_state = self.state
                self.state = new_state
                logger.info(f"Pipeline state changed: {old_state} -> {new_state}")
                await self._emit_event("state_changed", old_state, new_state)
    
    async def start(self) -> bool:
        """
        Start audio pipeline processing
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            if self.state != AudioState.IDLE:
                logger.warning(f"Cannot start pipeline in state: {self.state}")
                return False
            
            await self._set_state(AudioState.PROCESSING)
            
            # Clear stop event
            self._stop_event.clear()
            
            # Start processing task
            self._processing_task = asyncio.create_task(self._processing_loop())
            
            # Initialize statistics
            self._stats["start_time"] = time.time()
            self._stats["chunks_processed"] = 0
            self._stats["processing_errors"] = 0
            
            logger.info("Audio pipeline started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start audio pipeline: {e}")
            await self._set_state(AudioState.ERROR)
            await self._emit_event("error", e)
            return False
    
    async def stop(self) -> bool:
        """
        Stop audio pipeline processing
        
        Returns:
            True if stopped successfully, False otherwise
        """
        try:
            if self.state == AudioState.IDLE:
                logger.info("Pipeline already stopped")
                return True
            
            # Signal stop
            self._stop_event.set()
            
            # Wait for processing task to complete
            if self._processing_task:
                try:
                    await asyncio.wait_for(self._processing_task, timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning("Processing task did not stop gracefully, cancelling")
                    self._processing_task.cancel()
                    try:
                        await self._processing_task
                    except asyncio.CancelledError:
                        pass
            
            # Clear buffers
            self.input_buffer.clear()
            self.output_buffer.clear()
            
            await self._set_state(AudioState.IDLE)
            
            # Update statistics
            if self._stats["start_time"]:
                self._stats["total_duration"] += time.time() - self._stats["start_time"]
                self._stats["start_time"] = None
            
            logger.info("Audio pipeline stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop audio pipeline: {e}")
            await self._set_state(AudioState.ERROR)
            await self._emit_event("error", e)
            return False
    
    async def _processing_loop(self) -> None:
        """Main processing loop for audio pipeline"""
        logger.info("Audio processing loop started")
        
        try:
            while not self._stop_event.is_set():
                try:
                    # Check for input data
                    if not self.input_buffer.is_empty():
                        # Read chunks from input buffer
                        chunks = self.input_buffer.read(num_chunks=1)
                        
                        for chunk in chunks:
                            # Emit chunk received event
                            await self._emit_event("chunk_received", chunk)
                            
                            # Process chunk
                            processed_chunk = self.processor.process_chunk(chunk)
                            
                            if processed_chunk is not None:
                                # Write to output buffer
                                self.output_buffer.write(processed_chunk.data)
                                
                                # Emit chunk processed event
                                await self._emit_event("chunk_processed", processed_chunk)
                                
                                # Update statistics
                                self._stats["chunks_processed"] += 1
                    
                    # Small delay to prevent busy waiting
                    await asyncio.sleep(0.001)  # 1ms
                    
                except Exception as e:
                    logger.error(f"Error in processing loop: {e}")
                    self._stats["processing_errors"] += 1
                    await self._emit_event("error", e)
                    
                    # Continue processing unless it's a critical error
                    if self._stats["processing_errors"] > 100:
                        logger.error("Too many processing errors, stopping pipeline")
                        break
        
        except asyncio.CancelledError:
            logger.info("Processing loop cancelled")
        except Exception as e:
            logger.error(f"Critical error in processing loop: {e}")
            await self._set_state(AudioState.ERROR)
            await self._emit_event("error", e)
        
        logger.info("Audio processing loop ended")
    
    def write_audio(self, data: np.ndarray) -> bool:
        """
        Write audio data to input buffer
        
        Args:
            data: Audio data array
            
        Returns:
            True if write successful, False otherwise
        """
        return self.input_buffer.write(data)
    
    def read_audio(self, num_chunks: int = 1) -> List[AudioChunk]:
        """
        Read processed audio chunks from output buffer
        
        Args:
            num_chunks: Number of chunks to read
            
        Returns:
            List of processed audio chunks
        """
        return self.output_buffer.read(num_chunks)
    
    def get_input_buffer_size(self) -> int:
        """Get current input buffer size"""
        return self.input_buffer.size()
    
    def get_output_buffer_size(self) -> int:
        """Get current output buffer size"""
        return self.output_buffer.size()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get pipeline statistics
        
        Returns:
            Dictionary with pipeline statistics
        """
        current_time = time.time()
        runtime = 0.0
        
        if self._stats["start_time"]:
            runtime = current_time - self._stats["start_time"]
        
        total_runtime = self._stats["total_duration"] + runtime
        
        return {
            "state": self.state.value,
            "chunks_processed": self._stats["chunks_processed"],
            "processing_errors": self._stats["processing_errors"],
            "runtime_seconds": runtime,
            "total_runtime_seconds": total_runtime,
            "processing_rate": self._stats["chunks_processed"] / total_runtime if total_runtime > 0 else 0,
            "input_buffer": self.input_buffer.get_statistics(),
            "output_buffer": self.output_buffer.get_statistics()
        }
    
    def get_status_report(self) -> str:
        """
        Get formatted status report
        
        Returns:
            Formatted status report string
        """
        stats = self.get_statistics()
        
        lines = [
            "Audio Pipeline Status Report",
            "=" * 30,
            f"State: {stats['state']}",
            f"Chunks Processed: {stats['chunks_processed']}",
            f"Processing Errors: {stats['processing_errors']}",
            f"Runtime: {stats['runtime_seconds']:.2f}s",
            f"Total Runtime: {stats['total_runtime_seconds']:.2f}s",
            f"Processing Rate: {stats['processing_rate']:.2f} chunks/sec",
            "",
            "Input Buffer:",
            f"  Size: {stats['input_buffer']['current_size']}/{stats['input_buffer']['max_size']}",
            f"  Utilization: {stats['input_buffer']['utilization']:.1%}",
            f"  Overflows: {stats['input_buffer']['overflows']}",
            "",
            "Output Buffer:",
            f"  Size: {stats['output_buffer']['current_size']}/{stats['output_buffer']['max_size']}",
            f"  Utilization: {stats['output_buffer']['utilization']:.1%}",
            f"  Overflows: {stats['output_buffer']['overflows']}"
        ]
        
        return "\n".join(lines)
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.stop()


# Utility functions for audio pipeline

def create_audio_pipeline(
    sample_rate: int = 16000,
    channels: int = 1,
    chunk_size: int = 1024,
    buffer_duration: float = 5.0,
    **kwargs
) -> AudioPipeline:
    """
    Create audio pipeline with specified configuration
    
    Args:
        sample_rate: Audio sample rate
        channels: Number of audio channels
        chunk_size: Audio chunk size
        buffer_duration: Buffer duration in seconds
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured AudioPipeline instance
    """
    config = AudioConfig(
        sample_rate=sample_rate,
        channels=channels,
        chunk_size=chunk_size,
        buffer_duration=buffer_duration,
        **kwargs
    )
    
    return AudioPipeline(config)


def generate_test_audio(
    duration: float = 1.0,
    sample_rate: int = 16000,
    frequency: float = 440.0,
    amplitude: float = 0.5
) -> np.ndarray:
    """
    Generate test audio signal
    
    Args:
        duration: Duration in seconds
        sample_rate: Sample rate
        frequency: Frequency in Hz
        amplitude: Amplitude (0.0 to 1.0)
        
    Returns:
        Generated audio data
    """
    t = np.linspace(0, duration, int(duration * sample_rate), False)
    audio_data = amplitude * np.sin(2 * np.pi * frequency * t)
    return audio_data.astype(np.float32)


def audio_chunk_from_file(file_path: str, chunk_size: int = 1024) -> List[AudioChunk]:
    """
    Load audio file and convert to audio chunks
    
    Args:
        file_path: Path to audio file
        chunk_size: Size of each chunk
        
    Returns:
        List of audio chunks
    """
    try:
        import wave
        
        chunks = []
        
        with wave.open(file_path, 'rb') as wav_file:
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            
            chunk_id = 0
            while True:
                frames = wav_file.readframes(chunk_size)
                if not frames:
                    break
                
                # Convert bytes to numpy array
                audio_data = np.frombuffer(frames, dtype=np.int16).astype(np.float32) / 32767.0
                
                chunk = AudioChunk(
                    data=audio_data,
                    timestamp=time.time(),
                    sample_rate=sample_rate,
                    channels=channels,
                    chunk_id=chunk_id,
                    format=AudioFormat.FLOAT_32
                )
                
                chunks.append(chunk)
                chunk_id += 1
        
        logger.info(f"Loaded {len(chunks)} chunks from {file_path}")
        return chunks
        
    except Exception as e:
        logger.error(f"Failed to load audio file {file_path}: {e}")
        return []