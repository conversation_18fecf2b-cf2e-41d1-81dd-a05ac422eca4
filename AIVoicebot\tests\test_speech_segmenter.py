"""
Tests for Speech Segmentation System

This module tests the speech segmentation functionality including:
- SpeechSegmenter boundary detection
- Adaptive threshold adjustment
- Segment timing and validation
"""

import pytest
import numpy as np
from unittest.mock import patch, MagicMock

from src.components.vad.speech_segmenter import (
    SpeechSegmenter,
    SpeechSegment,
    SegmentationConfig,
    SegmentType,
    create_speech_segmenter,
    segment_audio_with_vad
)


class TestSpeechSegment:
    """Test cases for SpeechSegment"""
    
    def test_speech_segment_creation(self):
        """Test SpeechSegment creation and properties"""
        segment = SpeechSegment(
            start_time=1.0,
            end_time=3.0,
            segment_type=SegmentType.SPEECH,
            confidence=0.8
        )
        
        assert segment.start_time == 1.0
        assert segment.end_time == 3.0
        assert segment.segment_type == SegmentType.SPEECH
        assert segment.confidence == 0.8
        assert segment.duration == 2.0
        assert segment.start_sample == 16000  # 1.0 * 16000
        assert segment.end_sample == 48000   # 3.0 * 16000
    
    def test_speech_segment_with_audio_data(self):
        """Test SpeechSegment with audio data"""
        audio_data = np.random.randn(1000).astype(np.float32)
        
        segment = SpeechSegment(
            start_time=0.0,
            end_time=1.0,
            segment_type=SegmentType.SPEECH,
            audio_data=audio_data
        )
        
        assert segment.audio_data is not None
        assert len(segment.audio_data) == 1000
        np.testing.assert_array_equal(segment.audio_data, audio_data)


class TestSegmentationConfig:
    """Test cases for SegmentationConfig"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = SegmentationConfig()
        
        assert config.sample_rate == 16000
        assert config.speech_threshold == 0.5
        assert config.silence_threshold == 0.3
        assert config.min_speech_duration == 0.1
        assert config.min_silence_duration == 0.1
        assert config.max_speech_duration == 30.0
        assert config.adaptive_threshold is True
        assert config.hysteresis_enabled is True
    
    def test_custom_config(self):
        """Test custom configuration values"""
        config = SegmentationConfig(
            sample_rate=8000,
            speech_threshold=0.7,
            min_speech_duration=0.2,
            adaptive_threshold=False
        )
        
        assert config.sample_rate == 8000
        assert config.speech_threshold == 0.7
        assert config.min_speech_duration == 0.2
        assert config.adaptive_threshold is False


class TestSpeechSegmenter:
    """Test cases for SpeechSegmenter"""
    
    def setup_method(self):
        """Set up test environment"""
        self.config = SegmentationConfig(
            min_speech_duration=0.1,
            min_silence_duration=0.1,
            speech_threshold=0.5,
            silence_threshold=0.3
        )
        self.segmenter = SpeechSegmenter(self.config)
    
    def test_segmenter_initialization(self):
        """Test SpeechSegmenter initialization"""
        assert self.segmenter.config == self.config
        assert self.segmenter.current_state == SegmentType.SILENCE
        assert self.segmenter.state_start_time == 0.0
        assert len(self.segmenter.completed_segments) == 0
    
    def test_simple_speech_detection(self):
        """Test basic speech segment detection"""
        # Create simple VAD pattern: silence -> speech -> silence
        vad_probs = np.array([0.1, 0.1, 0.8, 0.8, 0.8, 0.1, 0.1])
        timestamps = np.arange(len(vad_probs)) * 0.1  # 100ms intervals
        
        segments = self.segmenter.process_vad_results(vad_probs, timestamps)
        
        # Should detect one speech segment
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        assert len(speech_segments) >= 1
        
        # Check segment timing
        if speech_segments:
            segment = speech_segments[0]
            assert segment.start_time >= 0.1  # Should start around when speech begins
            assert segment.end_time <= 0.6    # Should end around when speech ends
    
    def test_minimum_duration_filtering(self):
        """Test minimum duration filtering"""
        # Create very short speech burst (should be filtered out)
        vad_probs = np.array([0.1, 0.8, 0.1, 0.1])  # Only 100ms of speech
        timestamps = np.array([0.0, 0.05, 0.1, 0.15])  # 50ms intervals
        
        segments = self.segmenter.process_vad_results(vad_probs, timestamps)
        
        # Should not create segment due to minimum duration
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        assert len(speech_segments) == 0
    
    def test_maximum_duration_splitting(self):
        """Test maximum duration segment splitting"""
        # Create very long speech (longer than max duration)
        config = SegmentationConfig(max_speech_duration=0.5)  # 500ms max
        segmenter = SpeechSegmenter(config)
        
        # 1 second of continuous speech
        vad_probs = np.ones(10) * 0.8  # High VAD probability
        timestamps = np.arange(10) * 0.1  # 100ms intervals
        
        segments = segmenter.process_vad_results(vad_probs, timestamps)
        
        # Should split into multiple segments
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        assert len(speech_segments) >= 1
        
        # Check that segments don't exceed max duration
        for segment in speech_segments:
            assert segment.duration <= config.max_speech_duration + 0.1  # Allow small tolerance
    
    def test_adaptive_threshold_update(self):
        """Test adaptive threshold adjustment"""
        config = SegmentationConfig(adaptive_threshold=True)
        segmenter = SpeechSegmenter(config)
        
        # Process frames with low VAD probabilities (noise)
        noisy_probs = np.array([0.2, 0.15, 0.25, 0.1, 0.18])
        timestamps = np.arange(len(noisy_probs)) * 0.1
        
        original_threshold = segmenter.adaptive_speech_threshold
        segmenter.process_vad_results(noisy_probs, timestamps)
        
        # Threshold should adapt to noise level
        assert segmenter.noise_floor > 0
        # Adaptive threshold might change based on noise floor
    
    def test_hysteresis_behavior(self):
        """Test hysteresis to prevent rapid state changes"""
        config = SegmentationConfig(
            hysteresis_enabled=True,
            speech_threshold=0.5,
            silence_threshold=0.3
        )
        segmenter = SpeechSegmenter(config)
        
        # Create oscillating VAD probabilities around threshold
        vad_probs = np.array([0.1, 0.6, 0.4, 0.6, 0.4, 0.1])  # Oscillates between 0.4 and 0.6
        timestamps = np.arange(len(vad_probs)) * 0.1
        
        segments = segmenter.process_vad_results(vad_probs, timestamps)
        
        # With hysteresis, should be more stable than without
        # (This is more of a behavioral test - exact behavior depends on implementation)
        assert isinstance(segments, list)
    
    def test_finalize_current_segment(self):
        """Test finalizing current segment at end of stream"""
        # Start with speech
        vad_probs = np.array([0.8, 0.8, 0.8])
        timestamps = np.array([0.0, 0.1, 0.2])
        
        self.segmenter.process_vad_results(vad_probs, timestamps)
        
        # Should be in speech state
        assert self.segmenter.current_state == SegmentType.SPEECH
        
        # Finalize current segment
        final_segment = self.segmenter.finalize_current_segment(end_time=0.3)
        
        assert final_segment is not None
        assert final_segment.segment_type == SegmentType.SPEECH
        assert final_segment.start_time == 0.0
        assert final_segment.end_time == 0.3
    
    def test_get_segmentation_stats(self):
        """Test segmentation statistics"""
        # Process some audio with speech segments
        vad_probs = np.array([0.1, 0.1, 0.8, 0.8, 0.8, 0.1, 0.1, 0.8, 0.8, 0.1])
        timestamps = np.arange(len(vad_probs)) * 0.1
        
        self.segmenter.process_vad_results(vad_probs, timestamps)
        self.segmenter.finalize_current_segment(end_time=1.0)
        
        stats = self.segmenter.get_segmentation_stats()
        
        assert "total_segments" in stats
        assert "speech_segments" in stats
        assert "total_speech_time" in stats
        assert "speech_ratio" in stats
        assert "current_state" in stats
        assert "noise_floor" in stats
        
        assert stats["total_segments"] >= 0
        assert stats["speech_ratio"] >= 0.0
        assert stats["speech_ratio"] <= 1.0
    
    def test_clear_segments(self):
        """Test clearing stored segments"""
        # Add some segments
        vad_probs = np.array([0.8, 0.8, 0.1, 0.1])
        timestamps = np.array([0.0, 0.1, 0.2, 0.3])
        
        self.segmenter.process_vad_results(vad_probs, timestamps)
        
        # Should have some segments
        assert len(self.segmenter.get_all_segments()) >= 0
        
        # Clear segments
        self.segmenter.clear_segments()
        
        # Should be empty
        assert len(self.segmenter.get_all_segments()) == 0
    
    def test_get_recent_segments(self):
        """Test getting recent segments"""
        # Create multiple segments
        for i in range(5):
            vad_probs = np.array([0.8, 0.8, 0.1, 0.1])
            timestamps = np.array([i*0.5, i*0.5+0.1, i*0.5+0.2, i*0.5+0.3])
            self.segmenter.process_vad_results(vad_probs, timestamps)
        
        # Get recent segments
        recent = self.segmenter.get_recent_segments(count=3)
        
        assert len(recent) <= 3
        assert len(recent) <= len(self.segmenter.get_all_segments())


class TestConvenienceFunctions:
    """Test cases for convenience functions"""
    
    def test_create_speech_segmenter(self):
        """Test create_speech_segmenter function"""
        segmenter = create_speech_segmenter(
            sample_rate=8000,
            speech_threshold=0.7,
            min_speech_duration=0.2,
            adaptive_threshold=False
        )
        
        assert segmenter.config.sample_rate == 8000
        assert segmenter.config.speech_threshold == 0.7
        assert segmenter.config.min_speech_duration == 0.2
        assert segmenter.config.adaptive_threshold is False
    
    def test_segment_audio_with_vad(self):
        """Test segment_audio_with_vad convenience function"""
        # Create test audio and VAD data
        audio_data = np.random.randn(1600).astype(np.float32)  # 0.1s at 16kHz
        vad_probs = np.array([0.1, 0.8, 0.8, 0.8, 0.1])  # Speech in middle
        
        segments = segment_audio_with_vad(
            audio_data=audio_data,
            vad_probabilities=vad_probs,
            sample_rate=16000,
            min_speech_duration=0.1
        )
        
        assert isinstance(segments, list)
        
        # Check that speech segments have audio data
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        for segment in speech_segments:
            if segment.audio_data is not None:
                assert len(segment.audio_data) > 0
                assert isinstance(segment.audio_data, np.ndarray)


class TestEdgeCases:
    """Test cases for edge cases and error conditions"""
    
    def test_empty_vad_input(self):
        """Test handling of empty VAD input"""
        segmenter = create_speech_segmenter()
        
        empty_probs = np.array([])
        empty_timestamps = np.array([])
        
        segments = segmenter.process_vad_results(empty_probs, empty_timestamps)
        
        assert segments == []
    
    def test_single_frame_input(self):
        """Test handling of single frame input"""
        segmenter = create_speech_segmenter()
        
        single_prob = np.array([0.8])
        single_timestamp = np.array([0.0])
        
        segments = segmenter.process_vad_results(single_prob, single_timestamp)
        
        # Should handle gracefully
        assert isinstance(segments, list)
    
    def test_all_silence_input(self):
        """Test handling of all silence input"""
        segmenter = create_speech_segmenter()
        
        silence_probs = np.array([0.1, 0.05, 0.2, 0.15, 0.1])
        timestamps = np.arange(len(silence_probs)) * 0.1
        
        segments = segmenter.process_vad_results(silence_probs, timestamps)
        
        # Should not create any speech segments
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        assert len(speech_segments) == 0
    
    def test_all_speech_input(self):
        """Test handling of all speech input"""
        segmenter = create_speech_segmenter()
        
        speech_probs = np.array([0.8, 0.9, 0.7, 0.85, 0.8])
        timestamps = np.arange(len(speech_probs)) * 0.1
        
        segments = segmenter.process_vad_results(speech_probs, timestamps)
        
        # Finalize to get the speech segment
        final_segment = segmenter.finalize_current_segment(end_time=0.5)
        if final_segment:
            segments.append(final_segment)
        
        # Should create at least one speech segment
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        assert len(speech_segments) >= 1


if __name__ == "__main__":
    pytest.main([__file__])