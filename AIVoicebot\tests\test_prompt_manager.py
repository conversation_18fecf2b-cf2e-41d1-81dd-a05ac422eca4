"""
Tests for prompt management system.
"""

import asyncio
import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.llm.prompt_manager import (
    PromptManager, PromptManagerConfig, PromptTemplate, PromptContext,
    ConversationTurn, PromptType, create_prompt_manager,
    create_customer_service_context, create_simple_template
)
from src.components.scripts.script_parser import ConversationScript


class TestPromptManager:
    """Test prompt manager functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def manager_config(self):
        """Create test manager configuration."""
        return PromptManagerConfig(
            template_directory="test_prompts",
            max_conversation_history=5,
            max_prompt_length=2000,
            enable_template_caching=True,
            cache_size=10
        )
    
    @pytest.fixture
    def sample_context(self):
        """Create sample prompt context."""
        conversation_history = [
            ConversationTurn(
                user_input="你好",
                assistant_response="您好！欢迎咨询我们的服务。",
                source="script"
            ),
            ConversationTurn(
                user_input="我想了解贷款",
                assistant_response="我们有多种贷款产品可供选择。",
                source="llm"
            )
        ]
        
        matched_scripts = [
            ConversationScript(
                script_id="script_1",
                query="利率咨询",
                response="我们的利率在3.5%-8%之间",
                intent="利率咨询",
                priority=2
            )
        ]
        
        return PromptContext(
            user_query="利率是多少？",
            user_intent="利率咨询",
            conversation_history=conversation_history,
            matched_scripts=matched_scripts,
            script_confidence=0.8,
            customer_info={"name": "张先生", "customer_level": "VIP"},
            system_state={"current_promotion": "新客户优惠活动"}
        )
    
    def test_manager_creation(self, manager_config, mock_config_manager):
        """Test prompt manager creation."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        assert manager.config == manager_config
        assert len(manager.templates) > 0  # Should have default templates
        assert len(manager.default_templates) > 0
    
    @pytest.mark.asyncio
    async def test_manager_lifecycle(self, manager_config, mock_config_manager):
        """Test manager lifecycle."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        assert manager.is_initialized
        assert manager.is_running
        
        await manager.stop()
        await manager.cleanup()
    
    def test_default_templates(self, manager_config, mock_config_manager):
        """Test default template loading."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        # Should have default templates
        assert PromptType.CUSTOMER_SERVICE in manager.default_templates
        assert PromptType.SCRIPT_BASED in manager.default_templates
        assert PromptType.FALLBACK in manager.default_templates
        
        # Check template properties
        cs_template = manager.default_templates[PromptType.CUSTOMER_SERVICE]
        assert cs_template.name == "default_customer_service"
        assert "user_query" in cs_template.required_variables
        assert len(cs_template.template) > 0
    
    def test_prompt_generation(self, manager_config, mock_config_manager, sample_context):
        """Test basic prompt generation."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        # Generate customer service prompt
        prompt = manager.generate_prompt(sample_context)
        
        assert len(prompt) > 0
        assert "利率是多少？" in prompt  # User query should be included
        assert "张先生" in prompt  # Customer info should be included
        assert "你好" in prompt  # Conversation history should be included
        
        # Check statistics
        stats = manager.get_prompt_statistics()
        assert stats["total_prompts_generated"] == 1
    
    def test_template_selection(self, manager_config, mock_config_manager, sample_context):
        """Test template selection logic."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        # Test script-based selection (high confidence)
        sample_context.script_confidence = 0.9
        template = manager._select_template(None, None, sample_context)
        assert template.prompt_type == PromptType.SCRIPT_BASED
        
        # Test fallback selection (low confidence)
        sample_context.script_confidence = 0.3
        template = manager._select_template(None, PromptType.FALLBACK, sample_context)
        assert template.prompt_type == PromptType.FALLBACK
        
        # Test specific template selection
        template = manager._select_template("default_customer_service", None, sample_context)
        assert template.name == "default_customer_service"
    
    def test_conversation_history_formatting(self, manager_config, mock_config_manager):
        """Test conversation history formatting."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        history = [
            ConversationTurn("问题1", "回答1", source="script"),
            ConversationTurn("问题2", "回答2", source="llm"),
            ConversationTurn("问题3", "回答3", source="fallback")
        ]
        
        # Test basic formatting
        formatted = manager._format_conversation_history(history, 5, False, False)
        assert "问题1" in formatted
        assert "回答1" in formatted
        assert "第1轮对话" in formatted
        
        # Test with timestamps and confidence
        formatted_detailed = manager._format_conversation_history(history, 5, True, True)
        assert "时间：" in formatted_detailed
        
        # Test history limiting
        formatted_limited = manager._format_conversation_history(history, 2, False, False)
        assert "问题1" not in formatted_limited  # Should be excluded
        assert "问题2" in formatted_limited  # Should be included
        assert "问题3" in formatted_limited  # Should be included
    
    def test_script_context_formatting(self, manager_config, mock_config_manager):
        """Test script context formatting."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        scripts = [
            ConversationScript("s1", "问题1", "回答1", "分类1", 1),
            ConversationScript("s2", "问题2", "回答2", "分类2", 2),
            ConversationScript("s3", "问题3", "回答3", "分类3", 1)
        ]
        
        formatted = manager._format_script_context(scripts)
        
        assert "问题1" in formatted
        assert "回答1" in formatted
        assert "脚本1" in formatted
        assert "分类1" in formatted
        assert "优先级：2" in formatted  # Should show priority > 1
    
    def test_prompt_caching(self, manager_config, mock_config_manager, sample_context):
        """Test prompt caching functionality."""
        manager_config.enable_template_caching = True
        manager = PromptManager(manager_config, mock_config_manager)
        
        # Generate prompt twice
        prompt1 = manager.generate_prompt(sample_context)
        prompt2 = manager.generate_prompt(sample_context)
        
        assert prompt1 == prompt2
        
        # Check cache statistics
        stats = manager.get_prompt_statistics()
        assert stats["cache_hits"] == 1
        assert stats["total_prompts_generated"] == 2
    
    def test_prompt_compression(self, manager_config, mock_config_manager):
        """Test prompt compression for length limits."""
        manager_config.max_prompt_length = 100  # Very short limit
        manager_config.enable_prompt_compression = True
        manager = PromptManager(manager_config, mock_config_manager)
        
        # Create context with long history
        long_history = [
            ConversationTurn(f"问题{i}", f"很长的回答{i}" * 10, source="script")
            for i in range(10)
        ]
        
        context = PromptContext(
            user_query="测试问题",
            conversation_history=long_history
        )
        
        prompt = manager.generate_prompt(context)
        
        # Should be compressed to fit limit
        assert len(prompt) <= manager_config.max_prompt_length
    
    def test_template_management(self, manager_config, mock_config_manager):
        """Test template management operations."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        # Create custom template
        custom_template = PromptTemplate(
            name="custom_test",
            prompt_type=PromptType.CUSTOMER_SERVICE,
            template="测试模板：{user_query}",
            required_variables=["user_query"]
        )
        
        # Add template
        initial_count = len(manager.templates)
        manager.add_template(custom_template)
        assert len(manager.templates) == initial_count + 1
        assert "custom_test" in manager.templates
        
        # Get template
        retrieved = manager.get_template("custom_test")
        assert retrieved is not None
        assert retrieved.name == "custom_test"
        
        # List templates
        template_names = manager.list_templates()
        assert "custom_test" in template_names
        
        # Remove template
        success = manager.remove_template("custom_test")
        assert success
        assert "custom_test" not in manager.templates
        
        # Try to remove non-existent template
        success = manager.remove_template("non_existent")
        assert not success
    
    def test_context_creation_helper(self):
        """Test context creation helper function."""
        conversation_history = [
            {"user": "你好", "assistant": "您好！"},
            {"user": "贷款", "assistant": "我们有多种贷款产品"}
        ]
        
        scripts = [
            ConversationScript("s1", "利率", "3.5%-8%", "利率", 1)
        ]
        
        context = create_customer_service_context(
            user_query="利率多少？",
            customer_name="李先生",
            customer_level="普通",
            conversation_history=conversation_history,
            matched_scripts=scripts,
            current_promotion="春季优惠"
        )
        
        assert context.user_query == "利率多少？"
        assert context.customer_info["name"] == "李先生"
        assert context.customer_info["customer_level"] == "普通"
        assert len(context.conversation_history) == 2
        assert len(context.matched_scripts) == 1
        assert context.system_state["current_promotion"] == "春季优惠"
    
    @pytest.mark.asyncio
    async def test_template_import_export(self, manager_config, mock_config_manager):
        """Test template import/export functionality."""
        manager = PromptManager(manager_config, mock_config_manager)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            template_file = Path(temp_dir) / "test_template.json"
            
            # Export existing template
            success = manager.export_template("default_customer_service", str(template_file))
            assert success
            assert template_file.exists()
            
            # Verify exported content
            with open(template_file, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)
            
            assert exported_data["name"] == "default_customer_service"
            assert exported_data["prompt_type"] == "customer_service"
            
            # Remove template and import it back
            manager.remove_template("default_customer_service")
            assert "default_customer_service" not in manager.templates
            
            success = manager.import_template(str(template_file))
            assert success
            assert "default_customer_service" in manager.templates


class TestPromptUtilities:
    """Test utility functions."""
    
    def test_simple_template_creation(self):
        """Test simple template creation utility."""
        template = create_simple_template(
            name="test_simple",
            template_text="简单模板：{user_query}",
            prompt_type=PromptType.FALLBACK,
            required_vars=["user_query"]
        )
        
        assert template.name == "test_simple"
        assert template.prompt_type == PromptType.FALLBACK
        assert template.template == "简单模板：{user_query}"
        assert template.required_variables == ["user_query"]
    
    @pytest.mark.asyncio
    async def test_prompt_manager_factory(self):
        """Test prompt manager factory function."""
        manager = await create_prompt_manager(
            template_directory="test_dir",
            max_conversation_history=8,
            config_manager=Mock()
        )
        
        assert manager.is_initialized
        assert manager.is_running
        assert manager.config.template_directory == "test_dir"
        assert manager.config.max_conversation_history == 8
        
        await manager.stop()
        await manager.cleanup()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestPromptUtilities().test_prompt_manager_factory())