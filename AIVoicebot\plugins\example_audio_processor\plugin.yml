# Example Audio Processor Plugin Configuration

name: "example_audio_processor"
version: "1.0.0"
description: "Example audio processor plugin that demonstrates the plugin system"
author: "AI Voice Team"
plugin_type: "audio_processor"

# Plugin dependencies
dependencies: []

# Entry point - class name in the main module
entry_point: "ExampleAudioProcessor"

# System version compatibility
min_system_version: "1.0.0"
max_system_version: ""

# Plugin settings
enabled: true
priority: 100

# Configuration schema
config_schema:
  type: "object"
  properties:
    processing_mode:
      type: "string"
      enum: ["enhance", "filter", "normalize"]
      default: "enhance"
      description: "Audio processing mode"
    
    gain_factor:
      type: "number"
      minimum: 0.1
      maximum: 5.0
      default: 1.0
      description: "Audio gain factor"
    
    enable_noise_reduction:
      type: "boolean"
      default: true
      description: "Enable noise reduction"
    
    sample_rate:
      type: "integer"
      enum: [8000, 16000, 22050, 44100, 48000]
      default: 16000
      description: "Target sample rate"

# Plugin metadata
metadata:
  category: "audio"
  tags: ["audio", "processing", "enhancement"]
  license: "MIT"
  homepage: "https://github.com/aivoice/plugins/example-audio-processor"
  documentation: "https://docs.aivoice.com/plugins/example-audio-processor"
