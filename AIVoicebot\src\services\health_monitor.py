"""
Health Check and Recovery System

This module provides comprehensive health monitoring and automatic recovery
capabilities for all system components including:
- Health check endpoints for all major components
- Automatic recovery mechanisms for transient failures
- System diagnostics and troubleshooting tools
- Component dependency tracking
- Recovery orchestration
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Callable, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod
import uuid
from collections import defaultdict, deque
import json

from ..core.base_component import BaseComponent
from ..core.error_handling import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorSeverity, ErrorCategory


class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"
    RECOVERING = "recovering"
    FAILED = "failed"


class RecoveryAction(Enum):
    """Recovery action types."""
    RESTART = "restart"
    RESET = "reset"
    RECONNECT = "reconnect"
    CLEAR_CACHE = "clear_cache"
    RELOAD_CONFIG = "reload_config"
    ESCALATE = "escalate"
    NONE = "none"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    component_name: str
    status: HealthStatus
    timestamp: datetime = field(default_factory=datetime.now)
    response_time_ms: float = 0.0
    details: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    recovery_suggestions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "component_name": self.component_name,
            "status": self.status.value,
            "timestamp": self.timestamp.isoformat(),
            "response_time_ms": self.response_time_ms,
            "details": self.details,
            "error_message": self.error_message,
            "recovery_suggestions": self.recovery_suggestions
        }


@dataclass
class RecoveryPlan:
    """Recovery plan for a component."""
    component_name: str
    actions: List[RecoveryAction]
    max_attempts: int = 3
    backoff_seconds: float = 1.0
    dependencies: List[str] = field(default_factory=list)
    timeout_seconds: float = 30.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "component_name": self.component_name,
            "actions": [action.value for action in self.actions],
            "max_attempts": self.max_attempts,
            "backoff_seconds": self.backoff_seconds,
            "dependencies": self.dependencies,
            "timeout_seconds": self.timeout_seconds
        }


@dataclass
class RecoveryAttempt:
    """Record of a recovery attempt."""
    component_name: str
    action: RecoveryAction
    attempt_number: int
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = False
    error_message: Optional[str] = None
    duration_ms: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "component_name": self.component_name,
            "action": self.action.value,
            "attempt_number": self.attempt_number,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message,
            "duration_ms": self.duration_ms
        }


class HealthCheckProvider(ABC):
    """Abstract base class for health check providers."""
    
    @abstractmethod
    async def check_health(self) -> HealthCheckResult:
        """Perform health check and return result."""
        pass
    
    @abstractmethod
    def get_component_name(self) -> str:
        """Get the name of the component."""
        pass
    
    @abstractmethod
    async def recover(self, action: RecoveryAction) -> bool:
        """Attempt to recover the component using the specified action."""
        pass


class ComponentHealthChecker(HealthCheckProvider):
    """Health checker for BaseComponent instances."""
    
    def __init__(self, component: BaseComponent):
        self.component = component
        self.last_check_time = None
        self.consecutive_failures = 0
    
    async def check_health(self) -> HealthCheckResult:
        """Check health of the component."""
        start_time = time.time()
        component_name = self.get_component_name()
        
        try:
            # Check if component is initialized and running
            if not self.component.is_initialized:
                return HealthCheckResult(
                    component_name=component_name,
                    status=HealthStatus.CRITICAL,
                    response_time_ms=(time.time() - start_time) * 1000,
                    error_message="Component not initialized",
                    recovery_suggestions=["Initialize component"]
                )
            
            if not self.component.is_running:
                return HealthCheckResult(
                    component_name=component_name,
                    status=HealthStatus.CRITICAL,
                    response_time_ms=(time.time() - start_time) * 1000,
                    error_message="Component not running",
                    recovery_suggestions=["Start component"]
                )
            
            # Perform component-specific health check
            health_details = await self._perform_component_check()

            response_time = (time.time() - start_time) * 1000
            self.last_check_time = datetime.now()
            self.consecutive_failures = 0

            return HealthCheckResult(
                component_name=component_name,
                status=HealthStatus.HEALTHY,
                response_time_ms=response_time,
                details=health_details
            )
            
        except Exception as e:
            self.consecutive_failures += 1
            response_time = (time.time() - start_time) * 1000
            
            status = HealthStatus.WARNING if self.consecutive_failures < 3 else HealthStatus.CRITICAL
            
            return HealthCheckResult(
                component_name=component_name,
                status=status,
                response_time_ms=response_time,
                error_message=str(e),
                recovery_suggestions=self._get_recovery_suggestions()
            )
    
    def get_component_name(self) -> str:
        """Get the component name."""
        return self.component.name
    
    async def recover(self, action: RecoveryAction) -> bool:
        """Attempt to recover the component."""
        try:
            if action == RecoveryAction.RESTART:
                await self.component.stop()
                await self.component.start()
                return True
            elif action == RecoveryAction.RESET:
                await self.component.cleanup()
                await self.component.initialize()
                await self.component.start()
                return True
            elif action == RecoveryAction.RECONNECT:
                # For components that support reconnection
                if hasattr(self.component, 'reconnect'):
                    await self.component.reconnect()
                    return True
            elif action == RecoveryAction.CLEAR_CACHE:
                # For components that support cache clearing
                if hasattr(self.component, 'clear_cache'):
                    await self.component.clear_cache()
                    return True
            elif action == RecoveryAction.RELOAD_CONFIG:
                # For components that support config reloading
                if hasattr(self.component, 'reload_config'):
                    await self.component.reload_config()
                    return True
            
            return False
            
        except Exception as e:
            logging.error(f"Recovery action {action.value} failed for {self.get_component_name()}: {e}")
            return False
    
    async def _perform_component_check(self) -> Dict[str, Any]:
        """Perform component-specific health checks."""
        details = {
            "is_initialized": self.component.is_initialized,
            "is_running": self.component.is_running,
            "last_check": self.last_check_time.isoformat() if self.last_check_time else None,
            "consecutive_failures": self.consecutive_failures
        }

        # Add component-specific metrics if available
        if hasattr(self.component, 'get_metrics'):
            try:
                metrics = await self.component.get_metrics()
                details["metrics"] = metrics
            except Exception as e:
                # If get_metrics fails, this indicates a component issue
                raise Exception(f"Component metrics check failed: {str(e)}")

        return details
    
    def _get_recovery_suggestions(self) -> List[str]:
        """Get recovery suggestions based on component state."""
        suggestions = []
        
        if not self.component.is_initialized:
            suggestions.append("Initialize component")
        elif not self.component.is_running:
            suggestions.append("Start component")
        else:
            suggestions.extend([
                "Restart component",
                "Check component configuration",
                "Verify dependencies"
            ])
        
        if self.consecutive_failures >= 3:
            suggestions.append("Escalate to administrator")
        
        return suggestions


class SystemHealthMonitor(BaseComponent):
    """
    Comprehensive system health monitoring and recovery system.
    
    Monitors all registered components, performs automatic recovery,
    and provides system diagnostics.
    """
    
    def __init__(self, config_manager, logger=None):
        super().__init__("health_monitor", config_manager, logger)
        
        # Configuration
        self.check_interval_seconds = self.config_manager.get_config("health.check_interval_seconds", 30)
        self.recovery_enabled = self.config_manager.get_config("health.recovery_enabled", True)
        self.max_recovery_attempts = self.config_manager.get_config("health.max_recovery_attempts", 3)
        self.escalation_threshold = self.config_manager.get_config("health.escalation_threshold", 5)
        
        # Health check providers
        self.health_providers: Dict[str, HealthCheckProvider] = {}
        self.recovery_plans: Dict[str, RecoveryPlan] = {}
        
        # Health tracking
        self.health_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.current_health: Dict[str, HealthCheckResult] = {}
        self.recovery_attempts: Dict[str, List[RecoveryAttempt]] = defaultdict(list)
        
        # Component dependencies
        self.dependencies: Dict[str, Set[str]] = defaultdict(set)
        
        # Background tasks
        self._monitor_task: Optional[asyncio.Task] = None
        self._recovery_task: Optional[asyncio.Task] = None
        
        # Error handler integration
        self.error_handler: Optional[ErrorHandler] = None
        
        # Recovery callbacks
        self.recovery_callbacks: List[Callable[[str, RecoveryAttempt], None]] = []

    async def _initialize_impl(self) -> None:
        """Initialize the health monitor."""
        self._log.info("Health Monitor initialized")
        self._log.info(f"Check interval: {self.check_interval_seconds}s")
        self._log.info(f"Recovery enabled: {self.recovery_enabled}")

    async def _start_impl(self) -> None:
        """Start the health monitoring system."""
        self._log.info("Starting Health Monitor...")

        # Start monitoring task
        self._monitor_task = asyncio.create_task(self._monitoring_loop())

        # Start recovery task if recovery is enabled
        if self.recovery_enabled:
            self._recovery_task = asyncio.create_task(self._recovery_loop())

        self._log.info("Health Monitor started")

    async def _stop_impl(self) -> None:
        """Stop the health monitoring system."""
        self._log.info("Stopping Health Monitor...")

        # Cancel tasks
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

        if self._recovery_task:
            self._recovery_task.cancel()
            try:
                await self._recovery_task
            except asyncio.CancelledError:
                pass

        self._log.info("Health Monitor stopped")

    async def _cleanup_impl(self) -> None:
        """Clean up the health monitor."""
        self.health_providers.clear()
        self.recovery_plans.clear()
        self.health_history.clear()
        self.current_health.clear()
        self.recovery_attempts.clear()
        self.dependencies.clear()
        self._log.info("Health Monitor cleanup completed")

    # Registration Methods

    def register_component(self, component: BaseComponent, recovery_plan: Optional[RecoveryPlan] = None) -> None:
        """Register a component for health monitoring."""
        component_name = component.name

        # Create health checker
        health_checker = ComponentHealthChecker(component)
        self.health_providers[component_name] = health_checker

        # Set up recovery plan
        if recovery_plan is None:
            recovery_plan = self._create_default_recovery_plan(component_name)
        self.recovery_plans[component_name] = recovery_plan

        self._log.info(f"Registered component for health monitoring: {component_name}")

    def register_health_provider(self, provider: HealthCheckProvider, recovery_plan: Optional[RecoveryPlan] = None) -> None:
        """Register a custom health check provider."""
        component_name = provider.get_component_name()
        self.health_providers[component_name] = provider

        if recovery_plan is None:
            recovery_plan = self._create_default_recovery_plan(component_name)
        self.recovery_plans[component_name] = recovery_plan

        self._log.info(f"Registered health provider: {component_name}")

    def set_error_handler(self, error_handler: ErrorHandler) -> None:
        """Set the error handler for integration."""
        self.error_handler = error_handler

    def add_dependency(self, component: str, depends_on: str) -> None:
        """Add a dependency relationship between components."""
        self.dependencies[component].add(depends_on)
        self._log.info(f"Added dependency: {component} depends on {depends_on}")

    def add_recovery_callback(self, callback: Callable[[str, RecoveryAttempt], None]) -> None:
        """Add a callback to be called when recovery attempts are made."""
        self.recovery_callbacks.append(callback)

    # Health Check Methods

    async def check_component_health(self, component_name: str) -> Optional[HealthCheckResult]:
        """Check health of a specific component."""
        if component_name not in self.health_providers:
            return None

        try:
            provider = self.health_providers[component_name]
            result = await provider.check_health()

            # Store result
            self.current_health[component_name] = result
            self.health_history[component_name].append(result)

            # Log critical issues
            if result.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]:
                self._log.error(f"Component {component_name} health check failed: {result.error_message}")
            elif result.status == HealthStatus.WARNING:
                self._log.warning(f"Component {component_name} health warning: {result.error_message}")

            return result

        except Exception as e:
            self._log.error(f"Health check failed for {component_name}: {e}")

            # Create error result
            error_result = HealthCheckResult(
                component_name=component_name,
                status=HealthStatus.FAILED,
                error_message=str(e),
                recovery_suggestions=["Check component configuration", "Restart component"]
            )

            self.current_health[component_name] = error_result
            self.health_history[component_name].append(error_result)

            return error_result

    async def check_all_components(self) -> Dict[str, HealthCheckResult]:
        """Check health of all registered components."""
        results = {}

        # Check components in dependency order
        check_order = self._get_dependency_order()

        for component_name in check_order:
            result = await self.check_component_health(component_name)
            if result:
                results[component_name] = result

        return results

    def get_system_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive system health summary."""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": self._calculate_overall_status(),
            "component_count": len(self.current_health),  # Count components with health data
            "healthy_components": 0,
            "warning_components": 0,
            "critical_components": 0,
            "failed_components": 0,
            "components": {},
            "recent_recoveries": self._get_recent_recoveries(),
            "system_uptime": self._calculate_system_uptime()
        }

        # Count component statuses
        for component_name, health_result in self.current_health.items():
            status = health_result.status
            summary["components"][component_name] = health_result.to_dict()

            if status == HealthStatus.HEALTHY:
                summary["healthy_components"] += 1
            elif status == HealthStatus.WARNING:
                summary["warning_components"] += 1
            elif status == HealthStatus.CRITICAL:
                summary["critical_components"] += 1
            elif status == HealthStatus.FAILED:
                summary["failed_components"] += 1

        return summary

    def get_component_health_history(self, component_name: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get health history for a specific component."""
        if component_name not in self.health_history:
            return []

        cutoff_time = datetime.now() - timedelta(hours=hours)
        history = self.health_history[component_name]

        return [
            result.to_dict() for result in history
            if result.timestamp >= cutoff_time
        ]

    # Recovery Methods

    async def recover_component(self, component_name: str, force: bool = False) -> bool:
        """Attempt to recover a specific component."""
        if component_name not in self.health_providers:
            self._log.error(f"Cannot recover unknown component: {component_name}")
            return False

        if not self.recovery_enabled and not force:
            self._log.warning(f"Recovery disabled, skipping recovery for {component_name}")
            return False

        recovery_plan = self.recovery_plans.get(component_name)
        if not recovery_plan:
            self._log.error(f"No recovery plan for component: {component_name}")
            return False

        # Check if we've exceeded max attempts recently
        recent_attempts = self._get_recent_recovery_attempts(component_name, hours=1)
        if len(recent_attempts) >= recovery_plan.max_attempts and not force:
            self._log.warning(f"Max recovery attempts exceeded for {component_name}")
            return False

        # Attempt recovery actions
        provider = self.health_providers[component_name]

        for action in recovery_plan.actions:
            attempt = RecoveryAttempt(
                component_name=component_name,
                action=action,
                attempt_number=len(recent_attempts) + 1
            )

            start_time = time.time()

            try:
                self._log.info(f"Attempting recovery action {action.value} for {component_name}")

                success = await asyncio.wait_for(
                    provider.recover(action),
                    timeout=recovery_plan.timeout_seconds
                )

                attempt.duration_ms = (time.time() - start_time) * 1000
                attempt.success = success

                # Record attempt
                self.recovery_attempts[component_name].append(attempt)

                # Trigger callbacks
                for callback in self.recovery_callbacks:
                    try:
                        callback(component_name, attempt)
                    except Exception as e:
                        self._log.error(f"Recovery callback error: {e}")

                if success:
                    self._log.info(f"Recovery successful for {component_name} using {action.value}")

                    # Wait a bit then check health
                    await asyncio.sleep(2)
                    health_result = await self.check_component_health(component_name)

                    if health_result and health_result.status == HealthStatus.HEALTHY:
                        return True
                else:
                    self._log.warning(f"Recovery action {action.value} failed for {component_name}")

                # Wait before next action
                if action != recovery_plan.actions[-1]:  # Not the last action
                    await asyncio.sleep(recovery_plan.backoff_seconds)

            except asyncio.TimeoutError:
                attempt.duration_ms = (time.time() - start_time) * 1000
                attempt.error_message = "Recovery action timed out"
                self.recovery_attempts[component_name].append(attempt)

                self._log.error(f"Recovery action {action.value} timed out for {component_name}")

            except Exception as e:
                attempt.duration_ms = (time.time() - start_time) * 1000
                attempt.error_message = str(e)
                self.recovery_attempts[component_name].append(attempt)

                self._log.error(f"Recovery action {action.value} failed for {component_name}: {e}")

        self._log.error(f"All recovery actions failed for {component_name}")
        return False

    async def recover_failed_components(self) -> Dict[str, bool]:
        """Attempt to recover all failed components."""
        results = {}

        for component_name, health_result in self.current_health.items():
            if health_result.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]:
                self._log.info(f"Attempting recovery for failed component: {component_name}")
                results[component_name] = await self.recover_component(component_name)

        return results

    # Diagnostic Methods

    def diagnose_component(self, component_name: str) -> Dict[str, Any]:
        """Perform comprehensive diagnosis of a component."""
        if component_name not in self.health_providers:
            return {
                "component_name": component_name,
                "error": f"Component {component_name} not found"
            }

        current_health = self.current_health.get(component_name)
        health_history = list(self.health_history[component_name])
        recovery_history = self.recovery_attempts.get(component_name, [])
        dependencies = list(self.dependencies.get(component_name, set()))

        # Calculate health trends
        recent_checks = [h for h in health_history if (datetime.now() - h.timestamp).total_seconds() < 3600]
        failure_rate = len([h for h in recent_checks if h.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]]) / max(len(recent_checks), 1)

        # Check dependency health
        dependency_health = {}
        for dep in dependencies:
            dep_health = self.current_health.get(dep)
            dependency_health[dep] = dep_health.status.value if dep_health else "unknown"

        diagnosis = {
            "component_name": component_name,
            "current_status": current_health.status.value if current_health else "unknown",
            "last_check": current_health.timestamp.isoformat() if current_health else None,
            "error_message": current_health.error_message if current_health else None,
            "failure_rate_last_hour": failure_rate,
            "total_health_checks": len(health_history),
            "total_recovery_attempts": len(recovery_history),
            "recent_recovery_attempts": len(self._get_recent_recovery_attempts(component_name, hours=24)),
            "dependencies": dependencies,
            "dependency_health": dependency_health,
            "recovery_suggestions": current_health.recovery_suggestions if current_health else [],
            "diagnosis": self._generate_diagnosis(component_name, current_health, health_history, recovery_history)
        }

        return diagnosis

    def get_system_diagnostics(self) -> Dict[str, Any]:
        """Get comprehensive system diagnostics."""
        diagnostics = {
            "timestamp": datetime.now().isoformat(),
            "system_health": self.get_system_health_summary(),
            "component_diagnostics": {},
            "dependency_graph": dict(self.dependencies),
            "recovery_statistics": self._get_recovery_statistics(),
            "recommendations": self._generate_system_recommendations()
        }

        # Get diagnostics for all components
        for component_name in self.health_providers.keys():
            diagnostics["component_diagnostics"][component_name] = self.diagnose_component(component_name)

        return diagnostics

    # Background Tasks

    async def _monitoring_loop(self) -> None:
        """Main health monitoring loop."""
        while True:
            try:
                await asyncio.sleep(self.check_interval_seconds)

                # Check all components
                results = await self.check_all_components()

                # Log summary
                healthy = sum(1 for r in results.values() if r.status == HealthStatus.HEALTHY)
                total = len(results)

                if total > 0:
                    self._log.debug(f"Health check completed: {healthy}/{total} components healthy")

                # Trigger recovery for failed components if enabled
                if self.recovery_enabled:
                    failed_components = [
                        name for name, result in results.items()
                        if result.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]
                    ]

                    if failed_components:
                        self._log.warning(f"Failed components detected: {failed_components}")
                        # Recovery will be handled by the recovery loop

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in monitoring loop: {e}")

    async def _recovery_loop(self) -> None:
        """Recovery loop for automatic component recovery."""
        while True:
            try:
                await asyncio.sleep(60)  # Check for recovery every minute

                # Find components that need recovery
                components_to_recover = []

                for component_name, health_result in self.current_health.items():
                    if health_result.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]:
                        # Check if we should attempt recovery
                        recent_attempts = self._get_recent_recovery_attempts(component_name, hours=1)
                        recovery_plan = self.recovery_plans.get(component_name)

                        if recovery_plan and len(recent_attempts) < recovery_plan.max_attempts:
                            components_to_recover.append(component_name)

                # Attempt recovery in dependency order
                if components_to_recover:
                    self._log.info(f"Attempting automatic recovery for: {components_to_recover}")

                    recovery_order = self._get_recovery_order(components_to_recover)

                    for component_name in recovery_order:
                        success = await self.recover_component(component_name)
                        if success:
                            self._log.info(f"Automatic recovery successful for {component_name}")
                        else:
                            self._log.error(f"Automatic recovery failed for {component_name}")

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in recovery loop: {e}")

    # Helper Methods

    def _create_default_recovery_plan(self, component_name: str) -> RecoveryPlan:
        """Create a default recovery plan for a component."""
        return RecoveryPlan(
            component_name=component_name,
            actions=[RecoveryAction.RESTART, RecoveryAction.RESET],
            max_attempts=3,
            backoff_seconds=2.0,
            timeout_seconds=30.0
        )

    def _get_dependency_order(self) -> List[str]:
        """Get components in dependency order (dependencies first)."""
        # Simple topological sort
        visited = set()
        order = []

        def visit(component: str):
            if component in visited:
                return
            visited.add(component)

            # Visit dependencies first
            for dependency in self.dependencies.get(component, set()):
                if dependency in self.health_providers:
                    visit(dependency)

            order.append(component)

        # Visit all components
        for component in self.health_providers.keys():
            visit(component)

        return order

    def _get_recovery_order(self, components: List[str]) -> List[str]:
        """Get components in recovery order (dependencies first)."""
        # Filter dependency order to only include components that need recovery
        full_order = self._get_dependency_order()
        return [comp for comp in full_order if comp in components]

    def _calculate_overall_status(self) -> str:
        """Calculate overall system health status."""
        if not self.current_health:
            return HealthStatus.UNKNOWN.value

        statuses = [result.status for result in self.current_health.values()]

        if any(status == HealthStatus.FAILED for status in statuses):
            return HealthStatus.FAILED.value
        elif any(status == HealthStatus.CRITICAL for status in statuses):
            return HealthStatus.CRITICAL.value
        elif any(status == HealthStatus.WARNING for status in statuses):
            return HealthStatus.WARNING.value
        elif any(status == HealthStatus.RECOVERING for status in statuses):
            return HealthStatus.RECOVERING.value
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY.value
        else:
            return HealthStatus.UNKNOWN.value

    def _calculate_system_uptime(self) -> Dict[str, Any]:
        """Calculate system uptime statistics."""
        # This is a simplified implementation
        # In a real system, you'd track actual start times
        return {
            "uptime_seconds": 0,  # Would be calculated from actual start time
            "uptime_string": "Unknown"
        }

    def _get_recent_recoveries(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent recovery attempts."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_recoveries = []

        for component_name, attempts in self.recovery_attempts.items():
            for attempt in attempts:
                if attempt.timestamp >= cutoff_time:
                    recent_recoveries.append(attempt.to_dict())

        # Sort by timestamp (most recent first)
        recent_recoveries.sort(key=lambda x: x["timestamp"], reverse=True)
        return recent_recoveries[:10]  # Return last 10

    def _get_recent_recovery_attempts(self, component_name: str, hours: int = 1) -> List[RecoveryAttempt]:
        """Get recent recovery attempts for a component."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        attempts = self.recovery_attempts.get(component_name, [])

        return [
            attempt for attempt in attempts
            if attempt.timestamp >= cutoff_time
        ]

    def _get_recovery_statistics(self) -> Dict[str, Any]:
        """Get recovery statistics."""
        total_attempts = sum(len(attempts) for attempts in self.recovery_attempts.values())
        successful_attempts = sum(
            len([a for a in attempts if a.success])
            for attempts in self.recovery_attempts.values()
        )

        success_rate = (successful_attempts / total_attempts * 100) if total_attempts > 0 else 0

        return {
            "total_recovery_attempts": total_attempts,
            "successful_recoveries": successful_attempts,
            "success_rate_percent": success_rate,
            "components_with_recoveries": len([c for c in self.recovery_attempts.keys() if self.recovery_attempts[c]])
        }

    def _generate_diagnosis(
        self,
        component_name: str,
        current_health: Optional[HealthCheckResult],
        health_history: List[HealthCheckResult],
        recovery_history: List[RecoveryAttempt]
    ) -> List[str]:
        """Generate diagnostic insights for a component."""
        diagnosis = []

        if not current_health:
            diagnosis.append("No health data available")
            return diagnosis

        # Analyze current status
        if current_health.status == HealthStatus.FAILED:
            diagnosis.append("Component is in failed state")
            if current_health.error_message:
                diagnosis.append(f"Error: {current_health.error_message}")
        elif current_health.status == HealthStatus.CRITICAL:
            diagnosis.append("Component is in critical state requiring immediate attention")
        elif current_health.status == HealthStatus.WARNING:
            diagnosis.append("Component is experiencing issues but still functional")

        # Analyze response time
        if current_health.response_time_ms > 5000:
            diagnosis.append("Health check response time is very slow (>5s)")
        elif current_health.response_time_ms > 1000:
            diagnosis.append("Health check response time is slow (>1s)")

        # Analyze failure patterns
        if len(health_history) >= 5:
            recent_failures = [h for h in health_history[-10:] if h.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]]
            if len(recent_failures) >= 3:
                diagnosis.append("Component has frequent failures - investigate root cause")

        # Analyze recovery attempts
        if len(recovery_history) > 0:
            recent_recoveries = [r for r in recovery_history if (datetime.now() - r.timestamp).total_seconds() < 3600]
            if len(recent_recoveries) >= 3:
                diagnosis.append("Multiple recent recovery attempts - component may be unstable")

            failed_recoveries = [r for r in recent_recoveries if not r.success]
            if len(failed_recoveries) > 0:
                diagnosis.append(f"{len(failed_recoveries)} recent recovery attempts failed")

        # Check dependencies
        dependencies = self.dependencies.get(component_name, set())
        if dependencies:
            failed_deps = []
            for dep in dependencies:
                dep_health = self.current_health.get(dep)
                if dep_health and dep_health.status in [HealthStatus.CRITICAL, HealthStatus.FAILED]:
                    failed_deps.append(dep)

            if failed_deps:
                diagnosis.append(f"Dependent components are failing: {', '.join(failed_deps)}")

        if not diagnosis:
            diagnosis.append("No specific issues detected")

        return diagnosis

    def _generate_system_recommendations(self) -> List[str]:
        """Generate system-wide recommendations."""
        recommendations = []

        # Analyze overall system health
        failed_count = sum(1 for h in self.current_health.values() if h.status == HealthStatus.FAILED)
        critical_count = sum(1 for h in self.current_health.values() if h.status == HealthStatus.CRITICAL)
        total_count = len(self.current_health)

        if failed_count > 0:
            recommendations.append(f"Immediate attention required: {failed_count} components have failed")

        if critical_count > 0:
            recommendations.append(f"Critical issues detected in {critical_count} components")

        if total_count > 0:
            failure_rate = (failed_count + critical_count) / total_count
            if failure_rate > 0.5:
                recommendations.append("System stability is compromised - consider emergency maintenance")
            elif failure_rate > 0.2:
                recommendations.append("System health is degraded - schedule maintenance")

        # Analyze recovery patterns
        recovery_stats = self._get_recovery_statistics()
        if recovery_stats["success_rate_percent"] < 50 and recovery_stats["total_recovery_attempts"] > 5:
            recommendations.append("Low recovery success rate - review recovery procedures")

        # Check for components with no recent health checks
        stale_components = []
        cutoff_time = datetime.now() - timedelta(minutes=self.check_interval_seconds * 2)

        for component_name, health_result in self.current_health.items():
            if health_result.timestamp < cutoff_time:
                stale_components.append(component_name)

        if stale_components:
            recommendations.append(f"Stale health data for components: {', '.join(stale_components)}")

        if not recommendations:
            recommendations.append("System appears to be operating normally")

        return recommendations


# Utility functions for creating health check endpoints

def create_http_health_endpoint(health_monitor: SystemHealthMonitor):
    """Create HTTP health check endpoint (framework agnostic)."""
    async def health_endpoint():
        """HTTP endpoint for health checks."""
        try:
            summary = health_monitor.get_system_health_summary()

            # Determine HTTP status code based on health
            if summary["overall_status"] in ["healthy", "warning"]:
                status_code = 200
            else:
                status_code = 503  # Service Unavailable

            return {
                "status_code": status_code,
                "body": summary,
                "headers": {"Content-Type": "application/json"}
            }
        except Exception as e:
            return {
                "status_code": 500,
                "body": {"error": str(e)},
                "headers": {"Content-Type": "application/json"}
            }

    return health_endpoint


def create_component_health_endpoint(health_monitor: SystemHealthMonitor, component_name: str):
    """Create HTTP endpoint for specific component health."""
    async def component_endpoint():
        """HTTP endpoint for component health check."""
        try:
            result = await health_monitor.check_component_health(component_name)

            if not result:
                return {
                    "status_code": 404,
                    "body": {"error": f"Component {component_name} not found"},
                    "headers": {"Content-Type": "application/json"}
                }

            status_code = 200 if result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING] else 503

            return {
                "status_code": status_code,
                "body": result.to_dict(),
                "headers": {"Content-Type": "application/json"}
            }
        except Exception as e:
            return {
                "status_code": 500,
                "body": {"error": str(e)},
                "headers": {"Content-Type": "application/json"}
            }

    return component_endpoint
