#!/bin/bash
# AI Voice Customer Service System - Docker Entrypoint Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Environment variables with defaults
ENVIRONMENT=${ENVIRONMENT:-production}
LOG_LEVEL=${LOG_LEVEL:-INFO}
WORKERS=${WORKERS:-4}
MAX_REQUESTS=${MAX_REQUESTS:-1000}
TIMEOUT=${TIMEOUT:-30}

log "Starting AI Voice Customer Service System"
log "Environment: $ENVIRONMENT"
log "Log Level: $LOG_LEVEL"

# Wait for dependencies
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log "Waiting for $service_name at $host:$port..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log "$service_name is ready!"
            return 0
        fi
        sleep 1
    done
    
    error "$service_name is not available after ${timeout}s"
    return 1
}

# Wait for Redis
if [ -n "$REDIS_URL" ]; then
    REDIS_HOST=$(echo $REDIS_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
    REDIS_PORT=$(echo $REDIS_URL | sed -n 's/.*:\([0-9]*\).*/\1/p')
    if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
        wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis" 30
    fi
fi

# Wait for PostgreSQL
if [ -n "$DATABASE_URL" ]; then
    DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
        wait_for_service "$DB_HOST" "$DB_PORT" "PostgreSQL" 60
    fi
fi

# Database migration and setup
setup_database() {
    log "Setting up database..."
    
    # Run database migrations if they exist
    if [ -f "scripts/migrate.py" ]; then
        log "Running database migrations..."
        python scripts/migrate.py
    fi
    
    # Initialize database if needed
    if [ -f "scripts/init_db.py" ]; then
        log "Initializing database..."
        python scripts/init_db.py
    fi
}

# Download and setup models
setup_models() {
    log "Setting up AI models..."
    
    # Create models directory if it doesn't exist
    mkdir -p /app/models
    
    # Download models if they don't exist
    if [ ! -f "/app/models/.models_ready" ]; then
        log "Downloading AI models..."
        
        # Download VAD model
        if [ ! -f "/app/models/silero_vad.onnx" ]; then
            log "Downloading VAD model..."
            # In production, this would download from a model repository
            touch /app/models/silero_vad.onnx
        fi
        
        # Download ASR model
        if [ ! -d "/app/models/SenseVoiceSmall" ]; then
            log "Downloading ASR model..."
            mkdir -p /app/models/SenseVoiceSmall
            touch /app/models/SenseVoiceSmall/model.bin
        fi
        
        # Mark models as ready
        touch /app/models/.models_ready
        log "Models setup completed"
    else
        log "Models already available"
    fi
}

# Setup logging
setup_logging() {
    log "Setting up logging..."
    
    # Create log directories
    mkdir -p /app/logs/app
    mkdir -p /app/logs/access
    mkdir -p /app/logs/error
    
    # Set log file permissions
    chmod 755 /app/logs
    chmod 644 /app/logs/* 2>/dev/null || true
}

# Health check function
health_check() {
    log "Performing health check..."
    
    # Check if the application is responding
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log "Health check passed"
        return 0
    else
        warn "Health check failed"
        return 1
    fi
}

# Cleanup function
cleanup() {
    log "Shutting down gracefully..."
    
    # Kill background processes
    jobs -p | xargs -r kill
    
    # Wait for processes to terminate
    sleep 2
    
    log "Shutdown complete"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main setup
main() {
    log "Starting setup process..."
    
    # Setup logging first
    setup_logging
    
    # Setup database if not in development mode
    if [ "$ENVIRONMENT" != "development" ]; then
        setup_database
    fi
    
    # Setup models
    setup_models
    
    # Set Python path
    export PYTHONPATH="/app:$PYTHONPATH"
    
    log "Setup completed successfully"
    
    # Start the application based on environment
    case "$ENVIRONMENT" in
        "production")
            log "Starting production server with $WORKERS workers..."
            exec gunicorn \
                --bind 0.0.0.0:8000 \
                --workers $WORKERS \
                --worker-class uvicorn.workers.UvicornWorker \
                --max-requests $MAX_REQUESTS \
                --timeout $TIMEOUT \
                --keep-alive 2 \
                --log-level $LOG_LEVEL \
                --access-logfile /app/logs/access/access.log \
                --error-logfile /app/logs/error/error.log \
                main:app
            ;;
        "development")
            log "Starting development server..."
            exec python main.py --debug --reload
            ;;
        "testing")
            log "Starting in testing mode..."
            exec python -m pytest tests/ -v
            ;;
        *)
            log "Starting with custom command: $@"
            exec "$@"
            ;;
    esac
}

# Check if we're being sourced or executed
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
