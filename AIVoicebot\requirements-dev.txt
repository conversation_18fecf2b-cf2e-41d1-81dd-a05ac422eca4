# AI Voice Customer Service System - Development Dependencies

# Include production dependencies
-r requirements.txt

# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
pytest-html==4.1.1
pytest-benchmark==4.0.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pylint==3.0.3
bandit==1.7.5
safety==2.3.5

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
sphinx-autodoc-typehints==1.25.2
mkdocs==1.5.3
mkdocs-material==9.4.8

# Development Tools
ipython==8.17.2
jupyter==1.0.0
jupyterlab==4.0.8
notebook==7.0.6

# Debugging
pdb++==0.10.3
ipdb==0.13.13
pudb==2023.1

# Performance Profiling
py-spy==0.3.14
memory-profiler==0.61.0
line-profiler==4.1.1
psutil==5.9.6

# Load Testing
locust==2.17.0
aiohttp-devtools==1.1.2

# Mock and Fixtures
responses==0.24.1
factory-boy==3.3.0
faker==20.1.0
freezegun==1.2.2

# Database Tools
alembic==1.12.1
sqlalchemy-utils==0.41.1

# API Testing
httpx==0.25.2
requests-mock==1.11.0

# Environment Management
python-dotenv==1.0.0
environs==10.3.0

# File Watching
watchdog==3.0.0

# Pre-commit Hooks
pre-commit==3.6.0

# Type Checking
types-requests==*********
types-PyYAML==*********
types-python-dateutil==*********
types-redis==********

# Linting and Formatting
autopep8==2.0.4
pycodestyle==2.11.1
pydocstyle==6.3.0

# Security Scanning
semgrep==1.45.0

# API Documentation
redoc-cli==0.2.3

# Development Server
uvicorn[standard]==0.24.0

# Database Migration
yoyo-migrations==8.2.0

# Configuration Management
dynaconf==3.2.4

# Logging
colorlog==6.8.0

# Development Utilities
rich==13.7.0
click==8.1.7
typer==0.9.0

# Audio Development Tools
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Data Analysis
pandas==2.1.4
numpy==1.24.4
scipy==1.11.4

# Jupyter Extensions
ipywidgets==8.1.1
jupyter-contrib-nbextensions==0.7.0

# Git Hooks
gitpython==3.1.40

# Container Development
docker==6.1.3
docker-compose==1.29.2
