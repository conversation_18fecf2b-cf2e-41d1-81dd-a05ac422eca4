"""
Response processing and validation system for LLM outputs.

This module provides LLM output validation, response filtering, safety checks,
and fallback mechanisms to script responses when LLM fails.
"""

import re
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json

from ...core.base_component import BaseComponent
from ...core.interfaces import AudioProcessingError, ScriptResponse
from ..scripts.script_parser import ConversationScript
from .qwen_client import QwenResponse


class ResponseQuality(Enum):
    """Response quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    UNACCEPTABLE = "unacceptable"


class ResponseSource(Enum):
    """Response source types."""
    LLM = "llm"
    SCRIPT = "script"
    FALLBACK = "fallback"
    HYBRID = "hybrid"


@dataclass
class ValidationRule:
    """Response validation rule."""
    name: str
    description: str
    pattern: Optional[str] = None  # Regex pattern
    keywords: List[str] = field(default_factory=list)
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    required_elements: List[str] = field(default_factory=list)
    forbidden_elements: List[str] = field(default_factory=list)
    severity: str = "warning"  # "error", "warning", "info"
    
    def validate(self, text: str) -> Tuple[bool, str]:
        """
        Validate text against this rule.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Length validation
        if self.min_length and len(text) < self.min_length:
            return False, f"Text too short (min: {self.min_length})"
        
        if self.max_length and len(text) > self.max_length:
            return False, f"Text too long (max: {self.max_length})"
        
        # Pattern validation
        if self.pattern and not re.search(self.pattern, text, re.IGNORECASE):
            return False, f"Pattern not matched: {self.pattern}"
        
        # Keyword validation
        text_lower = text.lower()
        for keyword in self.keywords:
            if keyword.lower() not in text_lower:
                return False, f"Required keyword missing: {keyword}"
        
        # Required elements
        for element in self.required_elements:
            if element.lower() not in text_lower:
                return False, f"Required element missing: {element}"
        
        # Forbidden elements
        for element in self.forbidden_elements:
            if element.lower() in text_lower:
                return False, f"Forbidden element found: {element}"
        
        return True, ""


@dataclass
class ProcessedResponse:
    """Processed and validated response."""
    text: str
    source: ResponseSource
    quality: ResponseQuality
    confidence: float
    
    # Validation results
    is_valid: bool = True
    validation_errors: List[str] = field(default_factory=list)
    validation_warnings: List[str] = field(default_factory=list)
    
    # Processing metadata
    original_text: str = ""
    processing_time_ms: float = 0.0
    applied_filters: List[str] = field(default_factory=list)
    
    # Fallback information
    fallback_used: bool = False
    fallback_reason: str = ""
    fallback_script: Optional[ConversationScript] = None
    
    # Response metadata
    response_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_script_response(self) -> ScriptResponse:
        """Convert to ScriptResponse format."""
        return ScriptResponse(
            response_id=self.response_id,
            text=self.text,
            confidence=self.confidence,
            source=self.source.value,
            metadata={
                "quality": self.quality.value,
                "is_valid": self.is_valid,
                "validation_errors": self.validation_errors,
                "validation_warnings": self.validation_warnings,
                "processing_time_ms": self.processing_time_ms,
                "applied_filters": self.applied_filters,
                "fallback_used": self.fallback_used,
                "fallback_reason": self.fallback_reason,
                "timestamp": self.timestamp.isoformat()
            }
        )


@dataclass
class ResponseProcessorConfig:
    """Configuration for response processor."""
    # Validation settings
    enable_validation: bool = True
    enable_safety_checks: bool = True
    enable_content_filtering: bool = True
    
    # Quality thresholds
    min_acceptable_quality: ResponseQuality = ResponseQuality.ACCEPTABLE
    min_confidence_threshold: float = 0.6
    
    # Length limits
    min_response_length: int = 5
    max_response_length: int = 1000
    
    # Fallback settings
    enable_fallback: bool = True
    fallback_confidence: float = 0.8
    max_fallback_attempts: int = 3
    
    # Processing settings
    enable_text_normalization: bool = True
    enable_profanity_filter: bool = True
    enable_sensitive_info_filter: bool = True
    
    # Performance settings
    enable_caching: bool = True
    cache_size: int = 500


class ResponseProcessor(BaseComponent):
    """
    Response processing and validation system for LLM outputs.
    
    Provides LLM output validation, response filtering, safety checks,
    and fallback mechanisms to script responses when LLM fails.
    """
    
    def __init__(self, config: ResponseProcessorConfig, config_manager, logger=None):
        """
        Initialize response processor.
        
        Args:
            config: Processor configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("response_processor", config_manager, logger)
        
        self.config = config
        
        # Validation rules
        self.validation_rules: List[ValidationRule] = []
        self.safety_rules: List[ValidationRule] = []
        
        # Response cache
        self._response_cache: Dict[str, ProcessedResponse] = {}
        
        # Statistics
        self._total_processed = 0
        self._validation_failures = 0
        self._fallback_used = 0
        self._quality_distribution: Dict[ResponseQuality, int] = {
            quality: 0 for quality in ResponseQuality
        }
        
        # Load default rules
        self._load_default_validation_rules()
        self._load_default_safety_rules()
    
    async def _initialize_impl(self) -> None:
        """Initialize response processor."""
        self._log.info("Initializing response processor...")
        
        # Reset statistics
        self._total_processed = 0
        self._validation_failures = 0
        self._fallback_used = 0
        self._quality_distribution = {quality: 0 for quality in ResponseQuality}
        
        self._log.info(f"Response processor initialized with {len(self.validation_rules)} validation rules")
    
    async def _start_impl(self) -> None:
        """Start response processor."""
        self._log.info("Starting response processor...")
        
        # Clear cache
        self._response_cache.clear()
        
        self._log.info("Response processor started")
    
    async def _stop_impl(self) -> None:
        """Stop response processor."""
        self._log.info("Stopping response processor...")
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("Response processor stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup response processor resources."""
        self._log.info("Cleaning up response processor...")
        
        # Clear cache and rules
        self._response_cache.clear()
        self.validation_rules.clear()
        self.safety_rules.clear()
        
        self._log.info("Response processor cleanup completed")
    
    def _load_default_validation_rules(self) -> None:
        """Load default validation rules."""
        # Basic length validation
        self.validation_rules.append(ValidationRule(
            name="min_length",
            description="Minimum response length",
            min_length=self.config.min_response_length,
            severity="error"
        ))
        
        self.validation_rules.append(ValidationRule(
            name="max_length",
            description="Maximum response length",
            max_length=self.config.max_response_length,
            severity="error"
        ))
        
        # Professional language validation
        self.validation_rules.append(ValidationRule(
            name="professional_tone",
            description="Professional and polite language",
            forbidden_elements=["傻", "笨", "蠢", "滚", "死"],
            severity="error"
        ))
        
        # Banking context validation
        self.validation_rules.append(ValidationRule(
            name="banking_context",
            description="Banking service context appropriateness",
            forbidden_elements=["不知道", "不清楚", "随便", "算了"],
            severity="warning"
        ))
        
        # Completeness validation
        self.validation_rules.append(ValidationRule(
            name="response_completeness",
            description="Response should be complete and helpful",
            pattern=r"[。！？]$",  # Should end with proper punctuation
            severity="warning"
        ))
    
    def _load_default_safety_rules(self) -> None:
        """Load default safety rules."""
        # Sensitive information protection
        self.safety_rules.append(ValidationRule(
            name="no_personal_info",
            description="No personal information disclosure",
            forbidden_elements=["身份证", "密码", "银行卡号", "手机号"],
            severity="error"
        ))
        
        # Financial advice safety
        self.safety_rules.append(ValidationRule(
            name="financial_safety",
            description="Safe financial advice",
            forbidden_elements=["保证赚钱", "无风险", "100%收益"],
            severity="error"
        ))
        
        # Legal compliance
        self.safety_rules.append(ValidationRule(
            name="legal_compliance",
            description="Legal compliance check",
            forbidden_elements=["违法", "逃税", "洗钱"],
            severity="error"
        ))
    
    async def process_llm_response(
        self,
        llm_response: QwenResponse,
        fallback_scripts: Optional[List[ConversationScript]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ProcessedResponse:
        """
        Process and validate LLM response.
        
        Args:
            llm_response: Response from LLM
            fallback_scripts: Fallback scripts if LLM response is invalid
            context: Additional context for processing
            
        Returns:
            Processed and validated response
        """
        start_time = datetime.now()
        
        try:
            # Check if LLM response is successful
            if not llm_response.is_success or not llm_response.text.strip():
                return await self._handle_llm_failure(
                    llm_response, fallback_scripts, "LLM response failed or empty"
                )
            
            # Process the response
            processed = await self._process_response_text(
                llm_response.text,
                ResponseSource.LLM,
                llm_response.response_id
            )
            
            # Check if response meets quality standards
            if (processed.quality.value in ["poor", "unacceptable"] or 
                not processed.is_valid):
                
                if self.config.enable_fallback and fallback_scripts:
                    return await self._apply_fallback(
                        processed, fallback_scripts, "Quality/validation failure"
                    )
            
            # Update processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            processed.processing_time_ms = processing_time
            
            # Update statistics
            self._update_statistics(processed)
            
            return processed
            
        except Exception as e:
            self._log.error(f"Error processing LLM response: {e}")
            
            if self.config.enable_fallback and fallback_scripts:
                return await self._handle_llm_failure(
                    llm_response, fallback_scripts, f"Processing error: {e}"
                )
            
            # Return error response
            return ProcessedResponse(
                text="抱歉，系统暂时无法处理您的请求，请稍后再试。",
                source=ResponseSource.FALLBACK,
                quality=ResponseQuality.POOR,
                confidence=0.1,
                is_valid=False,
                validation_errors=[str(e)],
                fallback_used=True,
                fallback_reason=f"Processing error: {e}"
            )
    
    async def _process_response_text(
        self,
        text: str,
        source: ResponseSource,
        response_id: str = ""
    ) -> ProcessedResponse:
        """Process response text with validation and filtering."""
        original_text = text
        processed_text = text
        applied_filters = []
        
        # Text normalization
        if self.config.enable_text_normalization:
            processed_text = self._normalize_text(processed_text)
            applied_filters.append("normalization")
        
        # Content filtering
        if self.config.enable_content_filtering:
            processed_text = self._apply_content_filters(processed_text)
            applied_filters.append("content_filtering")
        
        # Profanity filtering
        if self.config.enable_profanity_filter:
            processed_text = self._apply_profanity_filter(processed_text)
            applied_filters.append("profanity_filter")
        
        # Sensitive information filtering
        if self.config.enable_sensitive_info_filter:
            processed_text = self._apply_sensitive_info_filter(processed_text)
            applied_filters.append("sensitive_info_filter")
        
        # Validation
        is_valid, validation_errors, validation_warnings = self._validate_response(processed_text)
        
        # Quality assessment
        quality = self._assess_response_quality(processed_text, is_valid, validation_errors)
        
        # Confidence calculation
        confidence = self._calculate_confidence(processed_text, quality, is_valid)
        
        return ProcessedResponse(
            text=processed_text,
            source=source,
            quality=quality,
            confidence=confidence,
            is_valid=is_valid,
            validation_errors=validation_errors,
            validation_warnings=validation_warnings,
            original_text=original_text,
            applied_filters=applied_filters,
            response_id=response_id or f"resp_{datetime.now().timestamp()}"
        )
    
    def _normalize_text(self, text: str) -> str:
        """Normalize response text."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Fix punctuation spacing
        text = re.sub(r'\s*([，。！？；：])\s*', r'\1', text)
        
        # Ensure proper sentence ending
        if text and not text[-1] in '。！？':
            text += '。'
        
        return text
    
    def _apply_content_filters(self, text: str) -> str:
        """Apply content filtering."""
        # Remove or replace inappropriate content
        filtered_text = text
        
        # Replace uncertain expressions with more confident ones
        uncertain_patterns = [
            (r'我不太确定', '让我为您详细说明'),
            (r'可能是', '根据我们的政策'),
            (r'大概', '具体来说'),
            (r'应该是', '是的')
        ]
        
        for pattern, replacement in uncertain_patterns:
            filtered_text = re.sub(pattern, replacement, filtered_text)
        
        return filtered_text
    
    def _apply_profanity_filter(self, text: str) -> str:
        """Apply profanity filtering."""
        # Simple profanity filter (can be enhanced with more sophisticated methods)
        profanity_words = ['傻', '笨', '蠢', '滚', '死', '操', '靠']
        
        filtered_text = text
        for word in profanity_words:
            if word in filtered_text:
                filtered_text = filtered_text.replace(word, '*' * len(word))
        
        return filtered_text
    
    def _apply_sensitive_info_filter(self, text: str) -> str:
        """Filter sensitive information."""
        # Mask potential sensitive information
        filtered_text = text
        
        # Mask phone numbers
        filtered_text = re.sub(r'1[3-9]\d{9}', '***********', filtered_text)
        
        # Mask ID numbers
        filtered_text = re.sub(r'\d{15}|\d{18}', '******************', filtered_text)
        
        # Mask bank card numbers
        filtered_text = re.sub(r'\d{16,19}', '****************', filtered_text)
        
        return filtered_text
    
    def _validate_response(self, text: str) -> Tuple[bool, List[str], List[str]]:
        """Validate response against all rules."""
        errors = []
        warnings = []
        
        # Apply validation rules
        for rule in self.validation_rules:
            is_valid, error_msg = rule.validate(text)
            if not is_valid:
                if rule.severity == "error":
                    errors.append(f"{rule.name}: {error_msg}")
                else:
                    warnings.append(f"{rule.name}: {error_msg}")
        
        # Apply safety rules
        if self.config.enable_safety_checks:
            for rule in self.safety_rules:
                is_valid, error_msg = rule.validate(text)
                if not is_valid:
                    errors.append(f"SAFETY - {rule.name}: {error_msg}")
        
        return len(errors) == 0, errors, warnings
    
    def _assess_response_quality(
        self,
        text: str,
        is_valid: bool,
        validation_errors: List[str]
    ) -> ResponseQuality:
        """Assess response quality."""
        if not is_valid:
            return ResponseQuality.UNACCEPTABLE
        
        # Quality scoring based on various factors
        score = 100
        
        # Length factor
        if len(text) < 10:
            score -= 30
        elif len(text) > 500:
            score -= 10
        
        # Validation warnings
        score -= len(validation_errors) * 10
        
        # Content quality indicators
        if '您' in text:  # Polite form
            score += 10
        
        if any(word in text for word in ['请', '谢谢', '感谢']):
            score += 5
        
        if text.count('。') >= 2:  # Multiple sentences
            score += 5
        
        # Map score to quality level
        if score >= 90:
            return ResponseQuality.EXCELLENT
        elif score >= 75:
            return ResponseQuality.GOOD
        elif score >= 60:
            return ResponseQuality.ACCEPTABLE
        elif score >= 40:
            return ResponseQuality.POOR
        else:
            return ResponseQuality.UNACCEPTABLE
    
    def _calculate_confidence(
        self,
        text: str,
        quality: ResponseQuality,
        is_valid: bool
    ) -> float:
        """Calculate response confidence score."""
        if not is_valid:
            return 0.1
        
        # Base confidence from quality
        quality_scores = {
            ResponseQuality.EXCELLENT: 0.95,
            ResponseQuality.GOOD: 0.85,
            ResponseQuality.ACCEPTABLE: 0.70,
            ResponseQuality.POOR: 0.40,
            ResponseQuality.UNACCEPTABLE: 0.10
        }
        
        base_confidence = quality_scores[quality]
        
        # Adjust based on text characteristics
        confidence = base_confidence
        
        # Length factor
        if 20 <= len(text) <= 200:
            confidence += 0.05
        
        # Professional language indicators
        if any(word in text for word in ['您好', '请问', '为您', '建议']):
            confidence += 0.05
        
        # Completeness indicators
        if text.endswith(('。', '！', '？')):
            confidence += 0.02
        
        return min(1.0, max(0.0, confidence))
    
    async def _handle_llm_failure(
        self,
        llm_response: QwenResponse,
        fallback_scripts: Optional[List[ConversationScript]],
        reason: str
    ) -> ProcessedResponse:
        """Handle LLM failure with fallback."""
        self._log.warning(f"LLM failure: {reason}")
        
        if self.config.enable_fallback and fallback_scripts:
            return await self._apply_fallback(None, fallback_scripts, reason)
        
        # Default fallback response
        return ProcessedResponse(
            text="抱歉，我暂时无法回答您的问题。请您稍后再试，或联系我们的人工客服。",
            source=ResponseSource.FALLBACK,
            quality=ResponseQuality.ACCEPTABLE,
            confidence=self.config.fallback_confidence,
            fallback_used=True,
            fallback_reason=reason
        )
    
    async def _apply_fallback(
        self,
        failed_response: Optional[ProcessedResponse],
        fallback_scripts: List[ConversationScript],
        reason: str
    ) -> ProcessedResponse:
        """Apply fallback using script responses."""
        if not fallback_scripts:
            return await self._handle_llm_failure(None, None, "No fallback scripts available")
        
        # Use the best matching script
        best_script = fallback_scripts[0]  # Assume scripts are sorted by relevance
        
        # Process script response
        processed = await self._process_response_text(
            best_script.response,
            ResponseSource.SCRIPT,
            f"fallback_{best_script.script_id}"
        )
        
        # Mark as fallback
        processed.fallback_used = True
        processed.fallback_reason = reason
        processed.fallback_script = best_script
        
        # Adjust confidence for fallback
        processed.confidence = min(processed.confidence, self.config.fallback_confidence)
        
        self._fallback_used += 1
        
        return processed

    def add_validation_rule(self, rule: ValidationRule) -> None:
        """Add custom validation rule."""
        self.validation_rules.append(rule)
        self._log.info(f"Added validation rule: {rule.name}")
    
    def add_safety_rule(self, rule: ValidationRule) -> None:
        """Add custom safety rule."""
        self.safety_rules.append(rule)
        self._log.info(f"Added safety rule: {rule.name}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove validation or safety rule by name."""
        # Remove from validation rules
        for i, rule in enumerate(self.validation_rules):
            if rule.name == rule_name:
                del self.validation_rules[i]
                self._log.info(f"Removed validation rule: {rule_name}")
                return True
        
        # Remove from safety rules
        for i, rule in enumerate(self.safety_rules):
            if rule.name == rule_name:
                del self.safety_rules[i]
                self._log.info(f"Removed safety rule: {rule_name}")
                return True
        
        return False
    
    def get_validation_rules(self) -> List[ValidationRule]:
        """Get all validation rules."""
        return self.validation_rules.copy()
    
    def get_safety_rules(self) -> List[ValidationRule]:
        """Get all safety rules."""
        return self.safety_rules.copy()
    
    async def validate_text(self, text: str) -> Tuple[bool, List[str], List[str]]:
        """Validate text against all rules (public interface)."""
        return self._validate_response(text)
    
    async def process_script_response(
        self,
        script: ConversationScript,
        context: Optional[Dict[str, Any]] = None
    ) -> ProcessedResponse:
        """
        Process script-based response.
        
        Args:
            script: Conversation script
            context: Additional context
            
        Returns:
            Processed script response
        """
        processed = await self._process_response_text(
            script.response,
            ResponseSource.SCRIPT,
            f"script_{script.script_id}"
        )
        
        # Script responses typically have higher confidence
        processed.confidence = min(1.0, processed.confidence + 0.1)
        
        self._update_statistics(processed)
        
        return processed
    
    async def create_hybrid_response(
        self,
        llm_response: QwenResponse,
        script_responses: List[ConversationScript],
        context: Optional[Dict[str, Any]] = None
    ) -> ProcessedResponse:
        """
        Create hybrid response combining LLM and script content.
        
        Args:
            llm_response: LLM response
            script_responses: Related script responses
            context: Additional context
            
        Returns:
            Hybrid processed response
        """
        # Process LLM response first
        llm_processed = await self._process_response_text(
            llm_response.text,
            ResponseSource.LLM,
            llm_response.response_id
        )
        
        # If LLM response is good enough, use it
        if (llm_processed.is_valid and 
            llm_processed.quality in [ResponseQuality.EXCELLENT, ResponseQuality.GOOD]):
            return llm_processed
        
        # Otherwise, create hybrid response
        if script_responses:
            # Combine LLM creativity with script accuracy
            script_content = script_responses[0].response
            
            # Simple hybrid strategy: use script as base, enhance with LLM if appropriate
            if llm_processed.is_valid and len(llm_response.text.strip()) > 20:
                hybrid_text = f"{script_content} {llm_response.text}"
            else:
                hybrid_text = script_content
            
            # Process hybrid response
            hybrid_processed = await self._process_response_text(
                hybrid_text,
                ResponseSource.HYBRID,
                f"hybrid_{llm_response.response_id}"
            )
            
            # Adjust confidence for hybrid
            hybrid_processed.confidence = (llm_processed.confidence + 0.8) / 2
            
            return hybrid_processed
        
        return llm_processed
    
    def _update_statistics(self, processed: ProcessedResponse) -> None:
        """Update processing statistics."""
        self._total_processed += 1
        
        if not processed.is_valid:
            self._validation_failures += 1
        
        if processed.fallback_used:
            self._fallback_used += 1
        
        self._quality_distribution[processed.quality] += 1
    
    def get_processor_stats(self) -> Dict[str, Any]:
        """Get processor statistics."""
        total = max(1, self._total_processed)
        
        return {
            "total_processed": self._total_processed,
            "validation_failures": self._validation_failures,
            "validation_failure_rate": self._validation_failures / total,
            "fallback_used": self._fallback_used,
            "fallback_rate": self._fallback_used / total,
            "quality_distribution": {
                quality.value: count for quality, count in self._quality_distribution.items()
            },
            "cache_size": len(self._response_cache),
            "validation_rules_count": len(self.validation_rules),
            "safety_rules_count": len(self.safety_rules)
        }
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_processor_stats()
        
        self._log.info(f"Response processor statistics:")
        self._log.info(f"  Total processed: {stats['total_processed']}")
        self._log.info(f"  Validation failure rate: {stats['validation_failure_rate']:.2%}")
        self._log.info(f"  Fallback rate: {stats['fallback_rate']:.2%}")
        self._log.info(f"  Quality distribution: {stats['quality_distribution']}")
    
    def reset_statistics(self) -> None:
        """Reset processor statistics."""
        self._total_processed = 0
        self._validation_failures = 0
        self._fallback_used = 0
        self._quality_distribution = {quality: 0 for quality in ResponseQuality}
        
        self._log.info("Processor statistics reset")
    
    def clear_cache(self) -> None:
        """Clear response cache."""
        self._response_cache.clear()
        self._log.info("Response cache cleared")
    
    def export_rules(self, file_path: str) -> bool:
        """Export validation and safety rules to JSON file."""
        try:
            rules_data = {
                "validation_rules": [
                    {
                        "name": rule.name,
                        "description": rule.description,
                        "pattern": rule.pattern,
                        "keywords": rule.keywords,
                        "min_length": rule.min_length,
                        "max_length": rule.max_length,
                        "required_elements": rule.required_elements,
                        "forbidden_elements": rule.forbidden_elements,
                        "severity": rule.severity
                    }
                    for rule in self.validation_rules
                ],
                "safety_rules": [
                    {
                        "name": rule.name,
                        "description": rule.description,
                        "pattern": rule.pattern,
                        "keywords": rule.keywords,
                        "min_length": rule.min_length,
                        "max_length": rule.max_length,
                        "required_elements": rule.required_elements,
                        "forbidden_elements": rule.forbidden_elements,
                        "severity": rule.severity
                    }
                    for rule in self.safety_rules
                ]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, ensure_ascii=False, indent=2)
            
            self._log.info(f"Exported rules to {file_path}")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to export rules: {e}")
            return False
    
    def import_rules(self, file_path: str) -> bool:
        """Import validation and safety rules from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)
            
            # Import validation rules
            if "validation_rules" in rules_data:
                for rule_data in rules_data["validation_rules"]:
                    rule = ValidationRule(
                        name=rule_data["name"],
                        description=rule_data["description"],
                        pattern=rule_data.get("pattern"),
                        keywords=rule_data.get("keywords", []),
                        min_length=rule_data.get("min_length"),
                        max_length=rule_data.get("max_length"),
                        required_elements=rule_data.get("required_elements", []),
                        forbidden_elements=rule_data.get("forbidden_elements", []),
                        severity=rule_data.get("severity", "warning")
                    )
                    self.validation_rules.append(rule)
            
            # Import safety rules
            if "safety_rules" in rules_data:
                for rule_data in rules_data["safety_rules"]:
                    rule = ValidationRule(
                        name=rule_data["name"],
                        description=rule_data["description"],
                        pattern=rule_data.get("pattern"),
                        keywords=rule_data.get("keywords", []),
                        min_length=rule_data.get("min_length"),
                        max_length=rule_data.get("max_length"),
                        required_elements=rule_data.get("required_elements", []),
                        forbidden_elements=rule_data.get("forbidden_elements", []),
                        severity=rule_data.get("severity", "error")
                    )
                    self.safety_rules.append(rule)
            
            self._log.info(f"Imported rules from {file_path}")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to import rules: {e}")
            return False


# Utility functions for response processing

async def create_response_processor(
    enable_validation: bool = True,
    enable_safety_checks: bool = True,
    min_confidence_threshold: float = 0.6,
    enable_fallback: bool = True,
    config_manager=None,
    **kwargs
) -> ResponseProcessor:
    """
    Create and initialize response processor.
    
    Args:
        enable_validation: Enable response validation
        enable_safety_checks: Enable safety checks
        min_confidence_threshold: Minimum confidence threshold
        enable_fallback: Enable fallback mechanism
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized response processor
    """
    config = ResponseProcessorConfig(
        enable_validation=enable_validation,
        enable_safety_checks=enable_safety_checks,
        min_confidence_threshold=min_confidence_threshold,
        enable_fallback=enable_fallback,
        **kwargs
    )
    
    processor = ResponseProcessor(config, config_manager)
    await processor.initialize()
    await processor.start()
    
    return processor


def create_validation_rule(
    name: str,
    description: str,
    **kwargs
) -> ValidationRule:
    """
    Create validation rule with parameters.
    
    Args:
        name: Rule name
        description: Rule description
        **kwargs: Rule parameters
        
    Returns:
        Validation rule
    """
    return ValidationRule(
        name=name,
        description=description,
        **kwargs
    )


def create_banking_safety_rules() -> List[ValidationRule]:
    """Create banking-specific safety rules."""
    return [
        ValidationRule(
            name="no_financial_guarantees",
            description="No unrealistic financial guarantees",
            forbidden_elements=["保证盈利", "零风险", "必赚", "稳赚不赔"],
            severity="error"
        ),
        ValidationRule(
            name="no_unauthorized_advice",
            description="No unauthorized financial advice",
            forbidden_elements=["建议您投资", "推荐买入", "一定要购买"],
            severity="error"
        ),
        ValidationRule(
            name="privacy_protection",
            description="Protect customer privacy",
            forbidden_elements=["告诉我您的密码", "提供身份证号", "银行卡密码"],
            severity="error"
        ),
        ValidationRule(
            name="regulatory_compliance",
            description="Regulatory compliance",
            required_elements=["风险提示", "详情请咨询"],
            severity="warning"
        )
    ]


def assess_response_appropriateness(
    response_text: str,
    user_query: str,
    context: Optional[Dict[str, Any]] = None
) -> Tuple[bool, float, List[str]]:
    """
    Assess if response is appropriate for the query.
    
    Args:
        response_text: Response to assess
        user_query: Original user query
        context: Additional context
        
    Returns:
        Tuple of (is_appropriate, relevance_score, issues)
    """
    issues = []
    relevance_score = 0.5  # Base score
    
    # Check if response addresses the query
    query_keywords = set(user_query.lower().split())
    response_keywords = set(response_text.lower().split())
    
    # Calculate keyword overlap
    overlap = len(query_keywords.intersection(response_keywords))
    if overlap > 0:
        relevance_score += 0.3 * (overlap / len(query_keywords))
    else:
        issues.append("Response doesn't address query keywords")
    
    # Check response length appropriateness
    if len(response_text) < 10:
        issues.append("Response too short")
        relevance_score -= 0.2
    elif len(response_text) > 500:
        issues.append("Response too long")
        relevance_score -= 0.1
    
    # Check for generic responses
    generic_phrases = ["谢谢您的咨询", "请稍等", "我不知道"]
    if any(phrase in response_text for phrase in generic_phrases):
        issues.append("Response appears generic")
        relevance_score -= 0.1
    
    # Check for helpful elements
    helpful_phrases = ["为您", "建议", "可以", "帮助"]
    if any(phrase in response_text for phrase in helpful_phrases):
        relevance_score += 0.1
    
    # Ensure score is in valid range
    relevance_score = max(0.0, min(1.0, relevance_score))
    
    is_appropriate = relevance_score >= 0.6 and len(issues) <= 1
    
    return is_appropriate, relevance_score, issues