"""
Model configuration and path management for AI models.

This module provides centralized management of AI model configurations,
paths, and loading parameters for all models used in the system.
"""

import os
import torch
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import json

from .interfaces import ConfigurationError, IConfigManager


class ModelType(Enum):
    """Enumeration of supported model types."""
    SPEECH_RECOGNITION = "speech_recognition"
    VOICE_ACTIVITY_DETECTION = "voice_activity_detection"
    TEXT_TO_SPEECH = "text_to_speech"
    LANGUAGE_MODEL = "language_model"


class DeviceType(Enum):
    """Enumeration of supported device types."""
    AUTO = "auto"
    CPU = "cpu"
    CUDA = "cuda"
    MPS = "mps"  # Apple Metal Performance Shaders


@dataclass
class ModelInfo:
    """Information about a model."""
    name: str
    type: ModelType
    path: str
    device: DeviceType = DeviceType.AUTO
    config: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_loaded: bool = False
    load_time: Optional[float] = None


@dataclass
class SenseVoiceConfig:
    """Configuration for SenseVoiceSmall model."""
    model_path: str
    device: DeviceType = DeviceType.AUTO
    batch_size: int = 1
    max_length: int = 30000
    language: str = "zh"
    use_itn: bool = True
    use_timestamp: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "model_path": self.model_path,
            "device": self.device.value,
            "batch_size": self.batch_size,
            "max_length": self.max_length,
            "language": self.language,
            "use_itn": self.use_itn,
            "use_timestamp": self.use_timestamp
        }


@dataclass
class SileroVADConfig:
    """Configuration for SileroVAD model."""
    model_path: str
    device: DeviceType = DeviceType.AUTO
    threshold: float = 0.5
    min_speech_duration_ms: int = 250
    max_speech_duration_s: int = 30
    min_silence_duration_ms: int = 100
    window_size_samples: int = 512
    speech_pad_ms: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "model_path": self.model_path,
            "device": self.device.value,
            "threshold": self.threshold,
            "min_speech_duration_ms": self.min_speech_duration_ms,
            "max_speech_duration_s": self.max_speech_duration_s,
            "min_silence_duration_ms": self.min_silence_duration_ms,
            "window_size_samples": self.window_size_samples,
            "speech_pad_ms": self.speech_pad_ms
        }


@dataclass
class QwenConfig:
    """Configuration for Qwen language model."""
    api_key: str
    api_base_url: str = "https://dashscope.aliyuncs.com/api/v1"
    model: str = "qwen-turbo"
    max_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 0.9
    timeout: int = 30
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "api_key": "***",  # Don't expose API key in logs
            "api_base_url": self.api_base_url,
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "timeout": self.timeout,
            "max_retries": self.max_retries
        }


@dataclass
class EdgeTTSConfig:
    """Configuration for EdgeTTS."""
    voice: str = "zh-CN-XiaoxiaoNeural"
    rate: str = "+0%"
    pitch: str = "+0Hz"
    volume: str = "+0%"
    timeout: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "voice": self.voice,
            "rate": self.rate,
            "pitch": self.pitch,
            "volume": self.volume,
            "timeout": self.timeout
        }


class ModelConfigManager:
    """
    Centralized model configuration and path management.
    
    Manages configuration for all AI models used in the system,
    including path validation, device selection, and loading parameters.
    """
    
    def __init__(self, config_manager: IConfigManager):
        """
        Initialize model configuration manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.models: Dict[str, ModelInfo] = {}
        self._logger = logging.getLogger("aivoice.model_config")
        
        # Initialize model configurations
        self._initialize_model_configs()
    
    def _initialize_model_configs(self) -> None:
        """Initialize model configurations from config manager."""
        try:
            # Initialize SenseVoice configuration
            self._init_sensevoice_config()
            
            # Initialize SileroVAD configuration
            self._init_silero_vad_config()
            
            # Initialize Qwen configuration
            self._init_qwen_config()
            
            # Initialize EdgeTTS configuration
            self._init_edge_tts_config()
            
            self._logger.info(f"Initialized {len(self.models)} model configurations")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize model configurations: {e}")
            raise ConfigurationError(f"Model configuration initialization failed: {e}")
    
    def _init_sensevoice_config(self) -> None:
        """Initialize SenseVoice model configuration."""
        model_path = self.config_manager.get_config("models.sensevoice_path", "models/SenseVoiceSmall/model.pt")
        device = self.config_manager.get_config("models.device", "auto")
        batch_size = self.config_manager.get_config("models.batch_size", 1)
        
        # Validate model path
        if not self._validate_model_path(model_path):
            raise ConfigurationError(f"SenseVoice model not found at: {model_path}")
        
        config = SenseVoiceConfig(
            model_path=model_path,
            device=DeviceType(device),
            batch_size=batch_size,
            language=self.config_manager.get_config("models.sensevoice.language", "zh"),
            use_itn=self.config_manager.get_config("models.sensevoice.use_itn", True),
            use_timestamp=self.config_manager.get_config("models.sensevoice.use_timestamp", False)
        )
        
        self.models["sensevoice"] = ModelInfo(
            name="SenseVoiceSmall",
            type=ModelType.SPEECH_RECOGNITION,
            path=model_path,
            device=DeviceType(device),
            config=config.to_dict()
        )
    
    def _init_silero_vad_config(self) -> None:
        """Initialize SileroVAD model configuration."""
        model_path = self.config_manager.get_config("models.silero_vad_path", "models/snakers4_silero-vad")
        device = self.config_manager.get_config("models.device", "auto")
        
        # Validate model path
        if not self._validate_model_path(model_path):
            raise ConfigurationError(f"SileroVAD model not found at: {model_path}")
        
        config = SileroVADConfig(
            model_path=model_path,
            device=DeviceType(device),
            threshold=self.config_manager.get_config("performance.vad_threshold", 0.5),
            min_speech_duration_ms=self.config_manager.get_config("performance.min_speech_duration", 0.3) * 1000,
            window_size_samples=self.config_manager.get_config("performance.vad_window_size", 512),
            speech_pad_ms=self.config_manager.get_config("performance.speech_pad_ms", 30)
        )
        
        self.models["silero_vad"] = ModelInfo(
            name="SileroVAD",
            type=ModelType.VOICE_ACTIVITY_DETECTION,
            path=model_path,
            device=DeviceType(device),
            config=config.to_dict()
        )
    
    def _init_qwen_config(self) -> None:
        """Initialize Qwen language model configuration."""
        api_key = self.config_manager.get_config("qwen.api_key", "")
        if not api_key:
            raise ConfigurationError("Qwen API key not configured. Set AIVOICE_QWEN_API_KEY environment variable.")
        
        config = QwenConfig(
            api_key=api_key,
            api_base_url=self.config_manager.get_config("qwen.api_base_url", "https://dashscope.aliyuncs.com/api/v1"),
            model=self.config_manager.get_config("qwen.model", "qwen-turbo"),
            max_tokens=self.config_manager.get_config("qwen.max_tokens", 2000),
            temperature=self.config_manager.get_config("qwen.temperature", 0.7),
            top_p=self.config_manager.get_config("qwen.top_p", 0.9),
            timeout=self.config_manager.get_config("qwen.timeout", 30),
            max_retries=self.config_manager.get_config("qwen.max_retries", 3)
        )
        
        self.models["qwen"] = ModelInfo(
            name="Qwen-turbo",
            type=ModelType.LANGUAGE_MODEL,
            path="api",  # API-based model
            config=config.to_dict()
        )
    
    def _init_edge_tts_config(self) -> None:
        """Initialize EdgeTTS configuration."""
        config = EdgeTTSConfig(
            voice=self.config_manager.get_config("edge_tts.voice", "zh-CN-XiaoxiaoNeural"),
            rate=self.config_manager.get_config("edge_tts.rate", "+0%"),
            pitch=self.config_manager.get_config("edge_tts.pitch", "+0Hz"),
            volume=self.config_manager.get_config("edge_tts.volume", "+0%"),
            timeout=self.config_manager.get_config("edge_tts.timeout", 30)
        )
        
        self.models["edge_tts"] = ModelInfo(
            name="EdgeTTS",
            type=ModelType.TEXT_TO_SPEECH,
            path="api",  # API-based model
            config=config.to_dict()
        )
    
    def _validate_model_path(self, path: str) -> bool:
        """
        Validate that a model path exists.
        
        Args:
            path: Path to validate
            
        Returns:
            True if path exists, False otherwise
        """
        model_path = Path(path)
        
        # For files, check if file exists
        if model_path.suffix:
            return model_path.exists() and model_path.is_file()
        
        # For directories, check if directory exists
        return model_path.exists() and model_path.is_dir()
    
    def get_model_config(self, model_name: str) -> Optional[ModelInfo]:
        """
        Get model configuration by name.
        
        Args:
            model_name: Name of the model
            
        Returns:
            ModelInfo if found, None otherwise
        """
        return self.models.get(model_name)
    
    def get_sensevoice_config(self) -> SenseVoiceConfig:
        """Get SenseVoice model configuration."""
        model_info = self.models.get("sensevoice")
        if not model_info:
            raise ConfigurationError("SenseVoice configuration not found")
        
        return SenseVoiceConfig(
            model_path=model_info.config["model_path"],
            device=DeviceType(model_info.config["device"]),
            batch_size=model_info.config["batch_size"],
            language=model_info.config["language"],
            use_itn=model_info.config["use_itn"],
            use_timestamp=model_info.config["use_timestamp"]
        )
    
    def get_silero_vad_config(self) -> SileroVADConfig:
        """Get SileroVAD model configuration."""
        model_info = self.models.get("silero_vad")
        if not model_info:
            raise ConfigurationError("SileroVAD configuration not found")
        
        return SileroVADConfig(
            model_path=model_info.config["model_path"],
            device=DeviceType(model_info.config["device"]),
            threshold=model_info.config["threshold"],
            min_speech_duration_ms=model_info.config["min_speech_duration_ms"],
            max_speech_duration_s=model_info.config["max_speech_duration_s"],
            min_silence_duration_ms=model_info.config["min_silence_duration_ms"],
            window_size_samples=model_info.config["window_size_samples"],
            speech_pad_ms=model_info.config["speech_pad_ms"]
        )
    
    def get_qwen_config(self) -> QwenConfig:
        """Get Qwen language model configuration."""
        model_info = self.models.get("qwen")
        if not model_info:
            raise ConfigurationError("Qwen configuration not found")
        
        # Get actual API key from config (not the masked version)
        api_key = self.config_manager.get_config("qwen.api_key", "")
        
        return QwenConfig(
            api_key=api_key,
            api_base_url=model_info.config["api_base_url"],
            model=model_info.config["model"],
            max_tokens=model_info.config["max_tokens"],
            temperature=model_info.config["temperature"],
            top_p=model_info.config["top_p"],
            timeout=model_info.config["timeout"],
            max_retries=model_info.config["max_retries"]
        )
    
    def get_edge_tts_config(self) -> EdgeTTSConfig:
        """Get EdgeTTS configuration."""
        model_info = self.models.get("edge_tts")
        if not model_info:
            raise ConfigurationError("EdgeTTS configuration not found")
        
        return EdgeTTSConfig(
            voice=model_info.config["voice"],
            rate=model_info.config["rate"],
            pitch=model_info.config["pitch"],
            volume=model_info.config["volume"],
            timeout=model_info.config["timeout"]
        )
    
    def get_optimal_device(self, preferred_device: Optional[DeviceType] = None) -> str:
        """
        Get optimal device for model loading.
        
        Args:
            preferred_device: Preferred device type
            
        Returns:
            Device string for PyTorch/model loading
        """
        if preferred_device and preferred_device != DeviceType.AUTO:
            device_str = preferred_device.value
        else:
            # Auto-detect optimal device
            if torch.cuda.is_available():
                device_str = "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                device_str = "mps"
            else:
                device_str = "cpu"
        
        # Validate device availability
        try:
            torch.device(device_str)
            self._logger.info(f"Selected device: {device_str}")
            return device_str
        except Exception as e:
            self._logger.warning(f"Device {device_str} not available, falling back to CPU: {e}")
            return "cpu"
    
    def update_model_status(self, model_name: str, is_loaded: bool, load_time: Optional[float] = None) -> None:
        """
        Update model loading status.
        
        Args:
            model_name: Name of the model
            is_loaded: Whether model is loaded
            load_time: Time taken to load model (seconds)
        """
        if model_name in self.models:
            self.models[model_name].is_loaded = is_loaded
            if load_time is not None:
                self.models[model_name].load_time = load_time
            
            self._logger.info(f"Updated model status - {model_name}: loaded={is_loaded}, load_time={load_time}s")
    
    def get_model_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all models.
        
        Returns:
            Dictionary with model status information
        """
        return {
            name: {
                "name": info.name,
                "type": info.type.value,
                "path": info.path,
                "device": info.device.value,
                "is_loaded": info.is_loaded,
                "load_time": info.load_time
            }
            for name, info in self.models.items()
        }
    
    def validate_all_models(self) -> List[str]:
        """
        Validate all model configurations.
        
        Returns:
            List of validation errors (empty if all valid)
        """
        errors = []
        
        for name, model_info in self.models.items():
            try:
                # Skip API-based models
                if model_info.path == "api":
                    continue
                
                # Validate path exists
                if not self._validate_model_path(model_info.path):
                    errors.append(f"Model {name} path not found: {model_info.path}")
                
                # Validate device
                try:
                    torch.device(model_info.device.value)
                except Exception:
                    errors.append(f"Model {name} device not available: {model_info.device.value}")
                
            except Exception as e:
                errors.append(f"Model {name} validation error: {e}")
        
        return errors
    
    def get_model_info_summary(self) -> str:
        """
        Get a summary of all model configurations.
        
        Returns:
            Formatted string with model information
        """
        lines = ["Model Configuration Summary:"]
        lines.append("=" * 40)
        
        for name, info in self.models.items():
            lines.append(f"Model: {info.name} ({name})")
            lines.append(f"  Type: {info.type.value}")
            lines.append(f"  Path: {info.path}")
            lines.append(f"  Device: {info.device.value}")
            lines.append(f"  Loaded: {info.is_loaded}")
            if info.load_time:
                lines.append(f"  Load Time: {info.load_time:.2f}s")
            lines.append("")
        
        return "\n".join(lines)
    
    def reload_configurations(self) -> None:
        """Reload all model configurations from config manager."""
        self._logger.info("Reloading model configurations...")
        self.models.clear()
        self._initialize_model_configs()
        self._logger.info("Model configurations reloaded successfully")