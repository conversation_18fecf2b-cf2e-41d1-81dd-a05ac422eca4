# AI Voice Customer Service System

一个完整的AI驱动语音客服系统，集成了语音识别、文本转语音、大语言模型和语音活动检测，用于自动化客户通话处理。

## 🌟 主要特性

### 🎯 核心功能
- **实时语音识别**: 使用SenseVoiceSmall模型进行准确的中文语音识别
- **自然语音合成**: EdgeTTS集成，提供类人语音合成
- **智能对话**: Qwen-turbo LLM提供上下文感知的响应
- **语音活动检测**: SileroVAD精确语音分割，支持自适应阈值
- **实时音频流**: 低延迟音频处理，支持自适应缓冲
- **多流管理**: 并发音频流处理，支持负载均衡
- **话术管理**: 基于Excel的专业话术库，支持热重载和智能匹配
- **通话管理**: 完整的外呼通话生命周期管理
- **监控日志**: 全面的日志记录和性能监控
- **错误处理**: 熔断器、重试逻辑和优雅降级

### 🔧 技术特性
- **微服务架构**: 模块化设计，易于扩展和维护
- **专业话术库**: 593条经过优化的复贷话术，支持智能匹配
- **插件系统**: 支持功能扩展和自定义组件
- **资源管理**: 智能资源池管理和自动扩缩容
- **性能监控**: 实时性能监控和告警系统
- **容错设计**: 完整的错误处理和自动恢复机制

### 🚀 部署特性
- **容器化部署**: Docker和Kubernetes支持
- **多环境配置**: 开发、测试、生产环境配置
- **自动化部署**: CI/CD集成和自动化部署脚本
- **监控告警**: 生产级监控和告警系统
- **扩展性**: 水平扩展和负载均衡支持

## 📋 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 20.04+), Windows 10+, macOS 10.15+
- **Python**: 3.9+
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB+ RAM
- **存储**: SSD 20GB+ 可用空间
- **GPU**: NVIDIA GPU (可选，用于AI模型加速)

### 外部依赖
- **数据库**: PostgreSQL 12+
- **缓存**: Redis 6+
- **消息队列**: (可选) RabbitMQ 或 Apache Kafka
- **监控**: (可选) Prometheus + Grafana

## 🏗️ 系统架构

系统采用模块化、事件驱动的架构，包含以下核心组件：

```
┌─────────────────────────────────────────────────────────────┐
│                    AI语音客服系统                            │
├─────────────────────────────────────────────────────────────┤
│  Web API 层                                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   FastAPI   │  │  WebSocket  │  │   健康检查   │        │
│  │   服务器     │  │   处理器     │  │   接口      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  核心系统层                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   系统      │  │  扩展       │  │ 组件        │        │
│  │   集成器     │  │  管理器     │  │ 协调器      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  服务层                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    通话     │  │    对话     │  │   提示      │        │
│  │   管理器     │  │   管理器     │  │   管理器     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  处理层                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    音频     │  │  电话系统   │  │    LLM      │        │
│  │   处理管道   │  │   管理器     │  │   集成      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  基础设施层                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 性能监控器   │  │   健康      │  │ 对话日志器   │        │
│  │             │  │   监控器     │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ PostgreSQL  │  │    Redis    │  │ 文件系统     │        │
│  │  数据库     │  │    缓存     │  │   存储      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件详解

- **通话管理器**: 协调通话生命周期和会话管理
- **音频流系统**: 实时音频处理，支持低延迟缓冲
  - **循环音频缓冲区**: 高效的音频数据循环缓冲
  - **实时音频流**: 低延迟音频流处理
  - **音频流管理器**: 多流管理和协调
  - **音频处理器**: 音频格式转换和预处理
  - **音频处理管道**: 多阶段音频处理流水线
- **语音活动检测**: 使用SileroVAD检测语音活动
  - **SileroVAD检测器**: 实时语音活动检测，支持自适应阈值
  - **语音分割器**: 语音边界识别和分割
  - **自适应阈值**: 基于背景噪声水平的自动调整
- **语音识别**: 使用SenseVoiceSmall将语音转换为文本
- **对话引擎**: 管理对话流程和上下文
- **话术管理器**: 处理专业复贷话术Excel文件，支持智能匹配和流程控制
- **语言模型**: 使用Qwen-turbo生成智能响应
- **文本转语音**: 使用EdgeTTS将响应转换为自然语音
- **配置管理器**: 集中化配置管理
- **日志监控**: 全面的系统监控

## 🚀 快速开始

### 环境准备

- Python 3.9 或更高版本
- Git
- 虚拟环境工具 (venv 或 conda)

### 快速安装

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd AIVoicebot
   ```

2. **创建并激活虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置系统**
   ```bash
   # 复制配置模板
   cp config/config.yaml.template config/config.yaml
   # 编辑配置文件
   nano config/config.yaml
   ```

5. **启动系统**
   ```bash
   python src/main.py
   ```

### Docker 快速部署

1. **使用 Docker Compose**
   ```bash
   # 启动完整系统
   docker-compose up -d

   # 查看服务状态
   docker-compose ps

   # 查看日志
   docker-compose logs -f
   ```

2. **验证部署**
   ```bash
   # 检查系统健康状态
   curl http://localhost:8000/health

   # 查看系统状态
   curl http://localhost:8000/status
   ```

## ⚙️ 配置说明

### 环境变量配置

在项目根目录创建 `.env` 文件：

```env
# Qwen API 配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/aivoice
REDIS_URL=redis://localhost:6379

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Audio Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_CHUNK_SIZE=1024
AUDIO_BUFFER_SIZE=4096
AUDIO_TARGET_LATENCY_MS=100
AUDIO_MAX_CONCURRENT_STREAMS=10

# Model Paths
SENSEVOICE_MODEL_PATH=models/SenseVoiceSmall/model.pt
SILERO_VAD_MODEL_PATH=models/snakers4_silero-vad

# Telephony Configuration (depends on your telephony system)
TELEPHONY_PROVIDER=custom
TELEPHONY_CONFIG_PATH=config/telephony.yaml
```

### 配置文件说明

系统使用位于 `config/` 目录下的 YAML 配置文件：

- `config/config.yaml`: 主应用配置文件
- `config/telephony.yaml`: 电话系统配置
- `config/environments/`: 环境特定配置
  - `development.yml`: 开发环境配置
  - `testing.yml`: 测试环境配置
  - `production.yml`: 生产环境配置
- `docs/`: 专业话术文件和文档
  - `言犀复贷话术.xlsx`: 基础复贷话术库 (37条话术)
  - `零犀复贷AI话术调优240914.xlsx`: 优化版AI话术库 (556条话术)
  - 其他技术文档和说明

**主配置文件示例** (`config/config.yaml`):

```yaml
app:
  name: "AI语音客服系统"
  version: "1.0.0"
  debug: false
  environment: "development"

server:
  host: "0.0.0.0"
  port: 8000
  workers: 1

database:
  url: "${DATABASE_URL}"
  pool_size: 10
  max_overflow: 20

audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  buffer_size: 4096
  enable_compression: true

models:
  asr:
    model_name: "SenseVoiceSmall"
    language: "zh"
    confidence_threshold: 0.8

  llm:
    model_name: "qwen-turbo"
    max_tokens: 1000
    temperature: 0.7
    api_key: "${QWEN_API_KEY}"

  tts:
    voice: "zh-CN-XiaoxiaoNeural"
    rate: "+0%"
    volume: "+0%"
    quality: "high"

vad:
  threshold: 0.5
  min_speech_duration: 0.25
  min_silence_duration: 0.5
  window_size: 512
  adaptive_threshold: true

telephony:
  enabled: true
  provider: "mock"  # 或 "twilio", "asterisk" 等
  max_concurrent_calls: 100

# 话术配置
conversation:
  script_files:
    - docs/言犀复贷话术.xlsx
    - docs/零犀复贷AI话术调优240914.xlsx
  auto_reload_scripts: true
  script_check_interval: 60
  default_script_id: default
  max_conversation_turns: 50

monitoring:
  enabled: true
  metrics_retention_hours: 24
  alert_check_interval: 30

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_size: "100MB"
  backup_count: 10
```

## 📖 使用指南

### 基本使用

#### 话术系统

系统内置了专业的复贷话术库，包含593条经过优化的话术：

```python
from src.components.scripts.script_manager import ScriptManager, ScriptManagerConfig

async def huashu_example():
    # 创建话术管理器
    config = ScriptManagerConfig(
        script_directory="docs",
        preload_scripts=True,
        enable_hot_reload=True
    )

    manager = ScriptManager(config, config_manager)
    await manager.initialize()
    await manager.start()

    # 加载话术文件
    await manager.load_scripts()

    # 根据用户输入匹配话术
    user_input = "我想了解一下贷款产品"
    match_result = await manager.find_best_response(user_input, context)

    print(f"匹配的话术: {match_result.response}")
    print(f"置信度: {match_result.confidence}")

    await manager.stop()
    await manager.cleanup()

# 运行示例
asyncio.run(huashu_example())
```

**话术文件说明**:
- `言犀复贷话术.xlsx`: 37条基础复贷话术
- `零犀复贷AI话术调优240914.xlsx`: 556条优化版AI话术，包含6个工作表
- 支持流程节点控制和智能匹配
- 自动热重载，话术更新后立即生效

#### 音频流系统

```python
import asyncio
from src.components.audio import AudioStreamManager, StreamManagerConfig, StreamConfig

async def audio_streaming_example():
    # Create audio stream manager
    manager_config = StreamManagerConfig(
        max_concurrent_streams=5,
        enable_load_balancing=True
    )
    
    manager = AudioStreamManager(manager_config, config_manager)
    await manager.initialize()
    await manager.start()
    
    # Create audio stream for voice input
    stream_config = StreamConfig(
        stream_id="voice_input",
        sample_rate=16000,
        channels=1,
        target_latency_ms=100
    )
    
    stream = await manager.create_stream(stream_config)
    
    # Process audio data
    async for audio_chunk in manager.get_stream_generator("voice_input"):
        # Process each audio chunk
        processed = await process_audio(audio_chunk)
        print(f"Processed {len(audio_chunk.data)} bytes of audio")
    
    await manager.stop()

# Run the example
asyncio.run(audio_streaming_example())
```

#### Complete Call Management

```python
import asyncio
from src.core.interfaces import ICallManager
from src.services.call_manager import CallManager

async def main():
    # Initialize the call manager
    call_manager = CallManager()
    await call_manager.initialize()
    
    # Start a call
    session = await call_manager.initiate_call(
        phone_number="+1234567890",
        script_id="default"
    )
    
    print(f"Call started with session ID: {session.session_id}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Running the System

```bash
# Start the AI voice service
python -m src.main

# Run with specific configuration
python -m src.main --config config/production.yaml

# Run in development mode
python -m src.main --dev
```

## Development

### Project Structure

```
AIVoicebot/
├── src/                     # 源代码
│   ├── core/                # 核心系统
│   │   ├── system_integrator.py    # 系统集成器
│   │   ├── base_component.py       # 基础组件类
│   │   ├── config_manager.py       # 配置管理器
│   │   └── plugin_system.py        # 插件系统
│   ├── components/          # 组件实现
│   │   ├── audio/           # 音频处理组件
│   │   ├── conversation/    # 对话管理组件
│   │   ├── scripts/         # 话术管理组件
│   │   ├── telephony/       # 电话系统组件
│   │   └── ai_models/       # AI模型组件
│   ├── services/            # 业务服务层
│   ├── integrations/        # 外部集成
│   ├── monitoring/          # 监控系统
│   ├── optimization/        # 性能优化
│   ├── coordination/        # 组件协调
│   ├── logging/             # 日志系统
│   └── api/                 # API接口
├── config/                  # 配置文件
│   ├── config.yaml          # 主配置文件
│   ├── environments/        # 环境配置
│   └── monitoring/          # 监控配置
├── docs/                    # 文档和话术文件
│   ├── 言犀复贷话术.xlsx     # 基础复贷话术 (37条)
│   ├── 零犀复贷AI话术调优240914.xlsx  # 优化话术 (556条)
│   ├── api/                 # API文档
│   ├── deployment/          # 部署文档
│   └── architecture/        # 架构文档
├── tests/                   # 测试套件
│   ├── unit/                # 单元测试
│   ├── integration/         # 集成测试
│   └── performance/         # 性能测试
├── scripts/                 # 工具脚本
├── deployment/              # 部署配置
├── k8s/                     # Kubernetes配置
├── plugins/                 # 插件示例
├── examples/                # 使用示例
├── models/                  # AI模型文件
└── logs/                    # 日志文件
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run audio streaming tests
pytest tests/test_audio_streaming.py -v

# Run specific test file
pytest tests/test_speech_recognition.py

# Run async tests
pytest -v tests/test_async_components.py

# Run audio streaming examples
python examples/audio_streaming_example.py
```

### Code Quality

```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/

# Run pre-commit hooks
pre-commit run --all-files
```

## Models

### SenseVoiceSmall
- **Location**: `models/SenseVoiceSmall/model.pt`
- **Purpose**: Speech recognition for Chinese language
- **Input**: 16kHz mono audio
- **Output**: Transcribed text with confidence scores

### SileroVAD
- **Location**: `models/snakers4_silero-vad/`
- **Purpose**: Voice activity detection
- **Input**: Audio chunks
- **Output**: Voice activity probabilities

## API Integration

### Qwen-turbo LLM
- **Provider**: Alibaba Cloud
- **Model**: qwen-turbo
- **Authentication**: API Key
- **Usage**: Intelligent response generation

### EdgeTTS
- **Provider**: Microsoft Edge
- **Purpose**: Text-to-speech synthesis
- **Languages**: Chinese (primary)
- **Output**: High-quality audio

## Monitoring

The system provides comprehensive monitoring through:

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Performance Metrics**: Response times, accuracy rates, error rates
- **Health Checks**: Component health monitoring
- **Error Tracking**: Detailed error logging and alerting

### Log Files

- `logs/aivoice.log`: Main application log
- `logs/aivoice_errors.log`: Error-specific log
- `logs/conversations/`: Individual conversation transcripts

## Troubleshooting

### Common Issues

1. **Model Loading Errors**
   - Ensure model files are present in the correct directories
   - Check file permissions and disk space

2. **API Connection Issues**
   - Verify API keys and network connectivity
   - Check rate limits and quotas

3. **Audio Processing Issues**
   - Verify audio device configuration
   - Check sample rate and format compatibility

4. **Telephony Integration**
   - Ensure telephony system is properly configured
   - Check network connectivity and firewall settings

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
export LOG_LEVEL=DEBUG
python -m src.main
```

## 📊 性能指标

### 典型性能表现
- **响应延迟**: < 500ms (端到端)
- **并发通话**: 100+ 并发会话
- **音频质量**: 16kHz, 16-bit PCM
- **准确率**: > 95% (意图识别)
- **可用性**: 99.9% 系统可用性
- **话术匹配**: < 10ms (单次查询)
- **话术库**: 593条专业话术，100%加载成功

### 扩展能力
- **水平扩展**: 支持多实例部署
- **负载均衡**: 智能负载分配
- **自动扩缩容**: 基于负载自动调整
- **资源优化**: 智能资源管理

## 🔌 插件系统

系统支持插件扩展，可以轻松添加新功能：

### 支持的插件类型
- **音频处理插件**: 自定义音频处理算法
- **对话处理插件**: 自定义对话逻辑
- **电话适配插件**: 支持新的电话系统
- **分析插件**: 自定义分析和报告
- **存储插件**: 自定义存储后端
- **通知插件**: 自定义通知方式

### 插件开发示例
```python
from src.core.plugin_system import AudioProcessorPlugin

class MyAudioProcessor(AudioProcessorPlugin):
    async def process_audio(self, audio_data, context):
        # 自定义音频处理逻辑
        processed_data = your_processing_logic(audio_data)
        return processed_data

    async def initialize(self, config):
        # 插件初始化
        self.config = config
        return True
```

## 📖 文档目录

### 用户文档
- [用户手册](docs/user-guide/README.md) - 系统使用指南
- [API文档](docs/api/README.md) - REST API和WebSocket接口
- [配置指南](docs/configuration/README.md) - 系统配置说明

### 开发文档
- [开发者指南](docs/developer-guide/README.md) - 开发环境搭建和开发指南
- [架构文档](docs/architecture/README.md) - 系统架构和设计文档
- [插件开发](docs/plugins/README.md) - 插件开发指南

### 运维文档
- [部署指南](docs/deployment/README.md) - 生产环境部署指南
- [监控运维](docs/operations/README.md) - 系统监控和运维指南
- [故障排除](docs/troubleshooting/README.md) - 常见问题和故障排除

## 🛠️ 开发和贡献

### 开发环境搭建
```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行测试
pytest tests/

# 代码格式化
black src/ tests/
isort src/ tests/
```

### 贡献指南
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 遵循 PEP 8 Python 代码规范
- 使用类型提示 (Type Hints)
- 编写完整的文档字符串
- 保持测试覆盖率 > 80%

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_audio_pipeline.py

# 运行带覆盖率的测试
pytest --cov=src tests/

# 运行性能测试
pytest tests/performance/ -v
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 支持和社区

### 获取帮助
- [GitHub Issues](https://github.com/your-org/ai-voice-customer-service/issues) - 报告问题和功能请求
- [讨论区](https://github.com/your-org/ai-voice-customer-service/discussions) - 社区讨论
- [Wiki](https://github.com/your-org/ai-voice-customer-service/wiki) - 详细文档和教程

### 联系方式
- 邮箱: <EMAIL>
- 官网: https://yourcompany.com/ai-voice-service
- 文档: https://docs.yourcompany.com/ai-voice-service

## 🎯 路线图

### 即将推出的功能
- [ ] 话术效果分析和统计
- [ ] 基于机器学习的话术优化
- [ ] 多语言话术支持
- [ ] 高级分析仪表板
- [ ] 移动端SDK
- [ ] 云原生部署模板
- [ ] 企业级安全增强

### 长期规划
- [ ] 话术个性化定制
- [ ] 实时话术推荐
- [ ] 边缘计算支持
- [ ] 实时语音翻译
- [ ] 情感分析集成
- [ ] 自动化测试平台
- [ ] 机器学习模型优化

---

**感谢使用 AI语音客服系统！** 🎉

如果这个项目对您有帮助，请给我们一个 ⭐️