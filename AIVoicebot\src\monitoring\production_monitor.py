"""
Production Monitoring and Alerting System

This module provides comprehensive production monitoring capabilities
including metrics collection, alerting, and dashboard integration.
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics

from ..core.base_component import BaseComponent


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"
    SUPPRESSED = "suppressed"


@dataclass
class Alert:
    """Alert data structure."""
    id: str
    name: str
    description: str
    severity: AlertSeverity
    status: AlertStatus
    component: str
    metric_name: str
    current_value: float
    threshold_value: float
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    suppressed_until: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "severity": self.severity.value,
            "status": self.status.value,
            "component": self.component,
            "metric_name": self.metric_name,
            "current_value": self.current_value,
            "threshold_value": self.threshold_value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "acknowledged_by": self.acknowledged_by,
            "suppressed_until": self.suppressed_until.isoformat() if self.suppressed_until else None
        }


@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    metric_name: str
    component: str
    condition: str  # "greater_than", "less_than", "equals", "not_equals"
    threshold: float
    severity: AlertSeverity
    duration_seconds: int = 60  # How long condition must persist
    cooldown_seconds: int = 300  # Minimum time between alerts
    enabled: bool = True
    
    def evaluate(self, value: float) -> bool:
        """Evaluate if the rule condition is met."""
        if self.condition == "greater_than":
            return value > self.threshold
        elif self.condition == "less_than":
            return value < self.threshold
        elif self.condition == "equals":
            return value == self.threshold
        elif self.condition == "not_equals":
            return value != self.threshold
        return False


@dataclass
class MetricData:
    """Metric data point."""
    name: str
    value: float
    timestamp: datetime
    component: str = ""
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "component": self.component,
            "tags": self.tags
        }


class ProductionMonitor(BaseComponent):
    """Production monitoring and alerting system."""
    
    def __init__(self, config_manager, logger=None):
        super().__init__("production_monitor", config_manager, logger)
        
        # Monitoring configuration
        self.metrics_retention_hours = self.config_manager.get_config("monitoring.metrics_retention_hours", 24)
        self.alert_check_interval = self.config_manager.get_config("monitoring.alert_check_interval", 30)
        self.enable_external_alerts = self.config_manager.get_config("monitoring.enable_external_alerts", True)
        
        # Data storage
        self.metrics: List[MetricData] = []
        self.alerts: Dict[str, Alert] = {}
        self.alert_rules: List[AlertRule] = []
        
        # Alert state tracking
        self.rule_states: Dict[str, Dict[str, Any]] = {}
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Alert handlers
        self.alert_handlers: List[Callable[[Alert], None]] = []
        
        # Initialize default alert rules
        self._initialize_default_alert_rules()
        
    async def _initialize_impl(self) -> None:
        """Initialize the production monitor."""
        self._log.info("Production Monitor initialized")
        
    async def _start_impl(self) -> None:
        """Start the production monitor."""
        self._log.info("Starting Production Monitor...")
        
        # Start background tasks
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self._log.info("Production Monitor started")
        
    async def _stop_impl(self) -> None:
        """Stop the production monitor."""
        self._log.info("Stopping Production Monitor...")
        
        # Cancel background tasks
        if self.monitoring_task:
            self.monitoring_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
            
        self._log.info("Production Monitor stopped")
        
    async def _cleanup_impl(self) -> None:
        """Clean up the production monitor."""
        self.metrics.clear()
        self.alerts.clear()
        self.alert_rules.clear()
        self.rule_states.clear()
        self.alert_handlers.clear()
        self._log.info("Production Monitor cleanup completed")
    
    def _initialize_default_alert_rules(self) -> None:
        """Initialize default alert rules."""
        default_rules = [
            AlertRule(
                name="High CPU Usage",
                metric_name="cpu_utilization",
                component="system",
                condition="greater_than",
                threshold=80.0,
                severity=AlertSeverity.WARNING,
                duration_seconds=120
            ),
            AlertRule(
                name="Critical CPU Usage",
                metric_name="cpu_utilization",
                component="system",
                condition="greater_than",
                threshold=95.0,
                severity=AlertSeverity.CRITICAL,
                duration_seconds=60
            ),
            AlertRule(
                name="High Memory Usage",
                metric_name="memory_utilization",
                component="system",
                condition="greater_than",
                threshold=85.0,
                severity=AlertSeverity.WARNING,
                duration_seconds=120
            ),
            AlertRule(
                name="Critical Memory Usage",
                metric_name="memory_utilization",
                component="system",
                condition="greater_than",
                threshold=95.0,
                severity=AlertSeverity.CRITICAL,
                duration_seconds=60
            ),
            AlertRule(
                name="High Response Time",
                metric_name="response_time_ms",
                component="api",
                condition="greater_than",
                threshold=2000.0,
                severity=AlertSeverity.WARNING,
                duration_seconds=180
            ),
            AlertRule(
                name="High Error Rate",
                metric_name="error_rate",
                component="system",
                condition="greater_than",
                threshold=5.0,
                severity=AlertSeverity.ERROR,
                duration_seconds=120
            ),
            AlertRule(
                name="Low Disk Space",
                metric_name="disk_usage_percent",
                component="system",
                condition="greater_than",
                threshold=90.0,
                severity=AlertSeverity.WARNING,
                duration_seconds=300
            )
        ]
        
        self.alert_rules.extend(default_rules)
        
        # Initialize rule states
        for rule in self.alert_rules:
            self.rule_states[rule.name] = {
                "condition_start": None,
                "last_alert": None,
                "consecutive_violations": 0
            }
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while True:
            try:
                await asyncio.sleep(self.alert_check_interval)
                await self._check_alert_rules()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in monitoring loop: {e}")
    
    async def _cleanup_loop(self) -> None:
        """Cleanup old metrics and resolved alerts."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_old_data()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in cleanup loop: {e}")
    
    async def _check_alert_rules(self) -> None:
        """Check all alert rules against current metrics."""
        current_time = datetime.now()
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
                
            try:
                # Get recent metrics for this rule
                recent_metrics = self._get_recent_metrics(rule.metric_name, rule.component, 300)  # Last 5 minutes
                
                if not recent_metrics:
                    continue
                
                # Calculate current value (average of recent metrics)
                current_value = statistics.mean([m.value for m in recent_metrics])
                
                # Check if condition is met
                condition_met = rule.evaluate(current_value)
                
                rule_state = self.rule_states[rule.name]
                
                if condition_met:
                    # Condition is violated
                    if rule_state["condition_start"] is None:
                        rule_state["condition_start"] = current_time
                        rule_state["consecutive_violations"] = 1
                    else:
                        rule_state["consecutive_violations"] += 1
                    
                    # Check if duration threshold is met
                    duration = (current_time - rule_state["condition_start"]).total_seconds()
                    
                    if duration >= rule.duration_seconds:
                        # Check cooldown
                        last_alert = rule_state["last_alert"]
                        if last_alert is None or (current_time - last_alert).total_seconds() >= rule.cooldown_seconds:
                            # Create alert
                            await self._create_alert(rule, current_value, current_time)
                            rule_state["last_alert"] = current_time
                else:
                    # Condition is not violated, reset state
                    if rule_state["condition_start"] is not None:
                        # Check if we should resolve existing alerts
                        await self._resolve_alerts_for_rule(rule.name)
                    
                    rule_state["condition_start"] = None
                    rule_state["consecutive_violations"] = 0
                    
            except Exception as e:
                self._log.error(f"Error checking alert rule {rule.name}: {e}")
    
    def _get_recent_metrics(self, metric_name: str, component: str, seconds: int) -> List[MetricData]:
        """Get recent metrics for a specific metric and component."""
        cutoff_time = datetime.now() - timedelta(seconds=seconds)
        
        return [
            m for m in self.metrics
            if m.name == metric_name and m.component == component and m.timestamp >= cutoff_time
        ]
    
    async def _create_alert(self, rule: AlertRule, current_value: float, timestamp: datetime) -> None:
        """Create a new alert."""
        alert_id = f"{rule.name}_{int(timestamp.timestamp())}"
        
        alert = Alert(
            id=alert_id,
            name=rule.name,
            description=f"{rule.name}: {rule.metric_name} is {current_value:.2f}, threshold is {rule.threshold}",
            severity=rule.severity,
            status=AlertStatus.ACTIVE,
            component=rule.component,
            metric_name=rule.metric_name,
            current_value=current_value,
            threshold_value=rule.threshold,
            created_at=timestamp,
            updated_at=timestamp
        )
        
        self.alerts[alert_id] = alert
        
        self._log.warning(f"Alert created: {alert.name} - {alert.description}")
        
        # Notify alert handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self._log.error(f"Error in alert handler: {e}")
    
    async def _resolve_alerts_for_rule(self, rule_name: str) -> None:
        """Resolve active alerts for a specific rule."""
        current_time = datetime.now()
        
        for alert in self.alerts.values():
            if alert.name == rule_name and alert.status == AlertStatus.ACTIVE:
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = current_time
                alert.updated_at = current_time
                
                self._log.info(f"Alert resolved: {alert.name}")
    
    async def _cleanup_old_data(self) -> None:
        """Clean up old metrics and resolved alerts."""
        cutoff_time = datetime.now() - timedelta(hours=self.metrics_retention_hours)
        
        # Clean up old metrics
        old_count = len(self.metrics)
        self.metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
        new_count = len(self.metrics)
        
        if old_count != new_count:
            self._log.info(f"Cleaned up {old_count - new_count} old metrics")
        
        # Clean up resolved alerts older than 7 days
        alert_cutoff = datetime.now() - timedelta(days=7)
        old_alerts = []
        
        for alert_id, alert in list(self.alerts.items()):
            if alert.status == AlertStatus.RESOLVED and alert.resolved_at and alert.resolved_at < alert_cutoff:
                old_alerts.append(alert_id)
        
        for alert_id in old_alerts:
            del self.alerts[alert_id]
        
        if old_alerts:
            self._log.info(f"Cleaned up {len(old_alerts)} old resolved alerts")
    
    # Public API
    
    def add_metric(self, name: str, value: float, component: str = "", tags: Dict[str, str] = None) -> None:
        """Add a metric data point."""
        metric = MetricData(
            name=name,
            value=value,
            timestamp=datetime.now(),
            component=component,
            tags=tags or {}
        )
        self.metrics.append(metric)
    
    def add_alert_rule(self, rule: AlertRule) -> None:
        """Add a custom alert rule."""
        self.alert_rules.append(rule)
        self.rule_states[rule.name] = {
            "condition_start": None,
            "last_alert": None,
            "consecutive_violations": 0
        }
        self._log.info(f"Added alert rule: {rule.name}")
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add an alert handler."""
        self.alert_handlers.append(handler)
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge an alert."""
        if alert_id in self.alerts:
            alert = self.alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_by = acknowledged_by
            alert.updated_at = datetime.now()
            
            self._log.info(f"Alert acknowledged: {alert.name} by {acknowledged_by}")
            return True
        
        return False
    
    def suppress_alert(self, alert_id: str, duration_minutes: int) -> bool:
        """Suppress an alert for a specified duration."""
        if alert_id in self.alerts:
            alert = self.alerts[alert_id]
            alert.status = AlertStatus.SUPPRESSED
            alert.suppressed_until = datetime.now() + timedelta(minutes=duration_minutes)
            alert.updated_at = datetime.now()
            
            self._log.info(f"Alert suppressed: {alert.name} for {duration_minutes} minutes")
            return True
        
        return False
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts."""
        return [
            alert.to_dict() for alert in self.alerts.values()
            if alert.status == AlertStatus.ACTIVE
        ]
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary statistics."""
        total_alerts = len(self.alerts)
        active_alerts = len([a for a in self.alerts.values() if a.status == AlertStatus.ACTIVE])
        critical_alerts = len([a for a in self.alerts.values() 
                              if a.status == AlertStatus.ACTIVE and a.severity == AlertSeverity.CRITICAL])
        
        return {
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "critical_alerts": critical_alerts,
            "alert_rules_count": len(self.alert_rules),
            "metrics_count": len(self.metrics),
            "last_updated": datetime.now().isoformat()
        }
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get metrics summary for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
        
        # Group by metric name
        metric_groups = {}
        for metric in recent_metrics:
            if metric.name not in metric_groups:
                metric_groups[metric.name] = []
            metric_groups[metric.name].append(metric.value)
        
        # Calculate statistics
        summary = {}
        for name, values in metric_groups.items():
            if values:
                summary[name] = {
                    "count": len(values),
                    "mean": statistics.mean(values),
                    "min": min(values),
                    "max": max(values),
                    "latest": values[-1] if values else 0
                }
        
        return {
            "time_period_hours": hours,
            "total_data_points": len(recent_metrics),
            "metrics": summary,
            "generated_at": datetime.now().isoformat()
        }
