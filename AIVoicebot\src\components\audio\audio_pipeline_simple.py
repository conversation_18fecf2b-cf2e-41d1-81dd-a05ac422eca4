"""
Simple Audio Pipeline Core Infrastructure
"""

import numpy as np
from typing import Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
import logging
import threading
import queue

logger = logging.getLogger(__name__)


class AudioFormat(Enum):
    """Supported audio formats"""
    PCM_16 = "pcm_16"
    FLOAT32 = "float32"


@dataclass
class AudioConfig:
    """Audio configuration parameters"""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    format: AudioFormat = AudioFormat.PCM_16
    buffer_duration: float = 5.0


class AudioBuffer:
    """Real-time audio data buffer"""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.buffer_size = int(config.sample_rate * config.buffer_duration)
        self.buffer = np.zeros(self.buffer_size, dtype=np.float32)
        self.write_pos = 0
        self.read_pos = 0
        self.lock = threading.Lock()
        
        logger.info(f"AudioBuffer initialized: {self.buffer_size} samples")
    
    def write(self, data: np.ndarray) -> bool:
        """Write audio data to buffer"""
        with self.lock:
            data_len = len(data)
            available_space = self.buffer_size - self._get_data_length()
            
            if data_len > available_space:
                return False
            
            end_pos = self.write_pos + data_len
            if end_pos <= self.buffer_size:
                self.buffer[self.write_pos:end_pos] = data
            else:
                first_part = self.buffer_size - self.write_pos
                self.buffer[self.write_pos:] = data[:first_part]
                self.buffer[:data_len - first_part] = data[first_part:]
            
            self.write_pos = end_pos % self.buffer_size
            return True
    
    def read(self, length: int) -> Optional[np.ndarray]:
        """Read audio data from buffer"""
        with self.lock:
            available_data = self._get_data_length()
            if available_data < length:
                return None
            
            data = np.zeros(length, dtype=np.float32)
            end_pos = self.read_pos + length
            
            if end_pos <= self.buffer_size:
                data = self.buffer[self.read_pos:end_pos].copy()
            else:
                first_part = self.buffer_size - self.read_pos
                data[:first_part] = self.buffer[self.read_pos:]
                data[first_part:] = self.buffer[:length - first_part]
            
            self.read_pos = end_pos % self.buffer_size
            return data
    
    def _get_data_length(self) -> int:
        """Get current amount of data in buffer"""
        if self.write_pos >= self.read_pos:
            return self.write_pos - self.read_pos
        else:
            return self.buffer_size - self.read_pos + self.write_pos
    
    def get_available_data(self) -> int:
        """Get amount of available data"""
        with self.lock:
            return self._get_data_length()
    
    def clear(self):
        """Clear buffer"""
        with self.lock:
            self.buffer.fill(0)
            self.write_pos = 0
            self.read_pos = 0


class AudioProcessor:
    """Audio format conversion and preprocessing"""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        logger.info(f"AudioProcessor initialized: {config.sample_rate}Hz")
    
    def convert_format(self, data: np.ndarray, target_format: AudioFormat) -> np.ndarray:
        """Convert audio data format"""
        if target_format == AudioFormat.FLOAT32:
            if data.dtype == np.int16:
                return data.astype(np.float32) / 32768.0
            else:
                return data.astype(np.float32)
        elif target_format == AudioFormat.PCM_16:
            if data.dtype == np.float32:
                return (data * 32767).astype(np.int16)
            else:
                return data.astype(np.int16)
        return data
    
    def normalize_audio(self, data: np.ndarray) -> np.ndarray:
        """Normalize audio data"""
        if len(data) == 0:
            return data
        
        float_data = self.convert_format(data, AudioFormat.FLOAT32)
        max_val = np.max(np.abs(float_data))
        if max_val > 0:
            float_data = float_data / max_val
        
        return float_data


class AudioPipeline:
    """Main audio processing pipeline coordinator"""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.buffer = AudioBuffer(config)
        self.processor = AudioProcessor(config)
        
        self.input_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.processors: List[Callable[[np.ndarray], np.ndarray]] = []
        
        self.running = False
        self.processing_thread = None
        
        logger.info("AudioPipeline initialized")
    
    def add_processor(self, processor: Callable[[np.ndarray], np.ndarray]):
        """Add audio processor to pipeline"""
        self.processors.append(processor)
        logger.info(f"Added processor")
    
    def start(self):
        """Start audio processing pipeline"""
        if self.running:
            return
        
        self.running = True
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processing_thread.start()
        logger.info("AudioPipeline started")
    
    def stop(self):
        """Stop audio processing pipeline"""
        if not self.running:
            return
        
        self.running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=1.0)
        
        logger.info("AudioPipeline stopped")
    
    def process_audio(self, data: np.ndarray) -> np.ndarray:
        """Process audio data through pipeline"""
        processed_data = data.copy()
        
        for processor in self.processors:
            try:
                processed_data = processor(processed_data)
            except Exception as e:
                logger.error(f"Error in processor: {e}")
                continue
        
        return processed_data
    
    def write_audio(self, data: np.ndarray) -> bool:
        """Write audio data to pipeline"""
        try:
            self.input_queue.put(data, timeout=0.1)
            return True
        except queue.Full:
            return False
    
    def read_audio(self, timeout: float = 0.1) -> Optional[np.ndarray]:
        """Read processed audio data from pipeline"""
        try:
            return self.output_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def _processing_loop(self):
        """Main processing loop"""
        while self.running:
            try:
                data = self.input_queue.get(timeout=0.1)
                processed_data = self.process_audio(data)
                self.buffer.write(processed_data)
                
                try:
                    self.output_queue.put(processed_data, timeout=0.01)
                except queue.Full:
                    try:
                        self.output_queue.get_nowait()
                        self.output_queue.put(processed_data, timeout=0.01)
                    except (queue.Empty, queue.Full):
                        pass
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                continue
    
    def get_buffer_status(self):
        """Get buffer status information"""
        available_data = self.buffer.get_available_data()
        return {
            "available_samples": available_data,
            "buffer_size": self.buffer.buffer_size,
            "input_queue_size": self.input_queue.qsize(),
            "output_queue_size": self.output_queue.qsize()
        }


def create_audio_pipeline(sample_rate: int = 16000, 
                         channels: int = 1, 
                         chunk_size: int = 1024,
                         buffer_duration: float = 5.0) -> AudioPipeline:
    """Create audio pipeline with specified configuration"""
    config = AudioConfig(
        sample_rate=sample_rate,
        channels=channels,
        chunk_size=chunk_size,
        buffer_duration=buffer_duration
    )
    return AudioPipeline(config)


def create_simple_processor(gain_db: float = 0.0, 
                          normalize: bool = True) -> Callable[[np.ndarray], np.ndarray]:
    """Create simple audio processor"""
    def processor(data: np.ndarray) -> np.ndarray:
        temp_config = AudioConfig()
        temp_processor = AudioProcessor(temp_config)
        
        processed = data.copy()
        
        if normalize:
            processed = temp_processor.normalize_audio(processed)
        
        if gain_db != 0.0:
            gain_linear = 10 ** (gain_db / 20.0)
            processed = processed * gain_linear
        
        return processed
    
    return processor