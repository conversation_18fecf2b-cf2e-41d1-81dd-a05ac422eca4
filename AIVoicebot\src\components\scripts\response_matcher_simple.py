"""
Simple Response Matching and Script System

This module provides script matching functionality including:
- ResponseMatcher for query-to-script matching
- Fuzzy matching and intent classification
- Support for combining multiple script elements
"""

import re
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class MatchType(Enum):
    """Types of matching algorithms"""
    EXACT = "exact"
    FUZZY = "fuzzy"
    KEYWORD = "keyword"
    INTENT = "intent"


@dataclass
class ScriptEntry:
    """Represents a conversation script entry"""
    id: str
    category: str
    keywords: List[str]
    patterns: List[str]
    response: str
    confidence_threshold: float = 0.7
    priority: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MatchResult:
    """Represents a matching result"""
    script_entry: ScriptEntry
    confidence: float
    match_type: MatchType
    matched_text: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class ResponseMatcher:
    """
    Response matcher for query-to-script matching
    
    Features:
    - Multiple matching algorithms
    - Fuzzy string matching
    - Keyword-based matching
    - Intent classification
    """
    
    def __init__(self, fuzzy_threshold: float = 0.6):
        self.fuzzy_threshold = fuzzy_threshold
        self.script_entries: List[ScriptEntry] = []
        self.keyword_index: Dict[str, List[ScriptEntry]] = {}
        
        logger.info(f"ResponseMatcher initialized with fuzzy_threshold: {fuzzy_threshold}")
    
    def add_script_entry(self, entry: ScriptEntry):
        """Add a script entry to the matcher"""
        self.script_entries.append(entry)
        
        # Build keyword index
        for keyword in entry.keywords:
            keyword_lower = keyword.lower()
            if keyword_lower not in self.keyword_index:
                self.keyword_index[keyword_lower] = []
            self.keyword_index[keyword_lower].append(entry)
        
        logger.debug(f"Added script entry: {entry.id}")
    
    def load_script_entries(self, entries: List[ScriptEntry]):
        """Load multiple script entries"""
        self.script_entries.clear()
        self.keyword_index.clear()
        
        for entry in entries:
            self.add_script_entry(entry)
        
        logger.info(f"Loaded {len(entries)} script entries")
    
    def exact_match(self, query: str) -> List[MatchResult]:
        """Find exact matches"""
        results = []
        query_lower = query.lower().strip()
        
        for entry in self.script_entries:
            for pattern in entry.patterns:
                if pattern.lower().strip() == query_lower:
                    result = MatchResult(
                        script_entry=entry,
                        confidence=1.0,
                        match_type=MatchType.EXACT,
                        matched_text=pattern
                    )
                    results.append(result)
        
        return results
    
    def fuzzy_match(self, query: str) -> List[MatchResult]:
        """Find fuzzy matches using simple string similarity"""
        results = []
        query_lower = query.lower().strip()
        
        for entry in self.script_entries:
            best_confidence = 0.0
            best_pattern = ""
            
            # Check patterns
            for pattern in entry.patterns:
                pattern_lower = pattern.lower().strip()
                confidence = self._calculate_similarity(query_lower, pattern_lower)
                
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_pattern = pattern
            
            # Check keywords
            for keyword in entry.keywords:
                keyword_lower = keyword.lower().strip()
                confidence = self._calculate_similarity(query_lower, keyword_lower)
                
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_pattern = keyword
            
            if best_confidence >= self.fuzzy_threshold:
                result = MatchResult(
                    script_entry=entry,
                    confidence=best_confidence,
                    match_type=MatchType.FUZZY,
                    matched_text=best_pattern
                )
                results.append(result)
        
        return results
    
    def keyword_match(self, query: str) -> List[MatchResult]:
        """Find keyword-based matches"""
        results = []
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        for entry in self.script_entries:
            matched_keywords = []
            total_score = 0.0
            
            for keyword in entry.keywords:
                keyword_lower = keyword.lower()
                
                # Check if keyword appears in query
                if keyword_lower in query_lower:
                    matched_keywords.append(keyword)
                    total_score += 1.0
                
                # Check word-level matching
                keyword_words = set(keyword_lower.split())
                overlap = len(query_words.intersection(keyword_words))
                if overlap > 0:
                    word_score = overlap / len(keyword_words)
                    total_score += word_score * 0.5
            
            if matched_keywords or total_score > 0:
                confidence = min(total_score / len(entry.keywords), 1.0)
                
                if confidence >= entry.confidence_threshold:
                    result = MatchResult(
                        script_entry=entry,
                        confidence=confidence,
                        match_type=MatchType.KEYWORD,
                        matched_text=", ".join(matched_keywords),
                        metadata={"matched_keywords": matched_keywords, "score": total_score}
                    )
                    results.append(result)
        
        return results
    
    def intent_match(self, query: str) -> List[MatchResult]:
        """Simple intent-based matching"""
        results = []
        query_lower = query.lower()
        
        # Simple intent patterns
        intent_patterns = {
            "greeting": ["你好", "hello", "hi", "早上好", "下午好"],
            "question": ["什么", "怎么", "为什么", "如何", "what", "how", "why"],
            "request": ["请", "帮助", "需要", "想要", "please", "help", "need", "want"],
            "complaint": ["问题", "故障", "不行", "坏了", "problem", "issue", "broken"],
            "goodbye": ["再见", "拜拜", "goodbye", "bye"]
        }
        
        detected_intents = []
        for intent, patterns in intent_patterns.items():
            for pattern in patterns:
                if pattern in query_lower:
                    detected_intents.append(intent)
                    break
        
        # Match entries by category/intent
        for entry in self.script_entries:
            entry_category = entry.category.lower()
            
            for intent in detected_intents:
                if intent in entry_category or entry_category in intent:
                    confidence = 0.8  # Base intent confidence
                    
                    result = MatchResult(
                        script_entry=entry,
                        confidence=confidence,
                        match_type=MatchType.INTENT,
                        matched_text=intent,
                        metadata={"detected_intents": detected_intents}
                    )
                    results.append(result)
        
        return results
    
    def match_query(self, query: str, max_results: int = 5) -> List[MatchResult]:
        """
        Match query against all script entries using multiple algorithms
        
        Args:
            query: Input query text
            max_results: Maximum number of results to return
            
        Returns:
            List of MatchResult sorted by confidence
        """
        all_results = []
        
        # Try different matching algorithms
        exact_results = self.exact_match(query)
        fuzzy_results = self.fuzzy_match(query)
        keyword_results = self.keyword_match(query)
        intent_results = self.intent_match(query)
        
        all_results.extend(exact_results)
        all_results.extend(fuzzy_results)
        all_results.extend(keyword_results)
        all_results.extend(intent_results)
        
        # Remove duplicates (same script entry)
        unique_results = {}
        for result in all_results:
            entry_id = result.script_entry.id
            if entry_id not in unique_results or result.confidence > unique_results[entry_id].confidence:
                unique_results[entry_id] = result
        
        # Sort by confidence and priority
        sorted_results = sorted(
            unique_results.values(),
            key=lambda r: (r.confidence, r.script_entry.priority),
            reverse=True
        )
        
        return sorted_results[:max_results]
    
    def get_best_response(self, query: str) -> Optional[str]:
        """Get the best response for a query"""
        results = self.match_query(query, max_results=1)
        
        if results:
            return results[0].script_entry.response
        
        return None
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate simple string similarity"""
        if not str1 or not str2:
            return 0.0
        
        if str1 == str2:
            return 1.0
        
        # Simple character-based similarity
        len1, len2 = len(str1), len(str2)
        max_len = max(len1, len2)
        
        if max_len == 0:
            return 1.0
        
        # Count common characters
        common = 0
        for i in range(min(len1, len2)):
            if str1[i] == str2[i]:
                common += 1
        
        # Add bonus for substring matches
        if str1 in str2 or str2 in str1:
            common += min(len1, len2) * 0.5
        
        similarity = common / max_len
        return min(similarity, 1.0)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get matcher statistics"""
        categories = {}
        for entry in self.script_entries:
            category = entry.category
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        return {
            "total_entries": len(self.script_entries),
            "categories": categories,
            "keyword_index_size": len(self.keyword_index),
            "fuzzy_threshold": self.fuzzy_threshold
        }


def create_sample_scripts() -> List[ScriptEntry]:
    """Create sample script entries for testing"""
    return [
        ScriptEntry(
            id="greeting_1",
            category="greeting",
            keywords=["你好", "hello", "hi"],
            patterns=["你好", "您好", "hello", "hi"],
            response="您好！欢迎致电客服中心，我是智能助手，很高兴为您服务！"
        ),
        ScriptEntry(
            id="account_balance",
            category="account",
            keywords=["余额", "balance", "账户", "钱"],
            patterns=["查询余额", "账户余额", "还有多少钱"],
            response="请稍等，我来为您查询账户余额信息..."
        ),
        ScriptEntry(
            id="password_reset",
            category="security",
            keywords=["密码", "password", "重置", "忘记"],
            patterns=["忘记密码", "重置密码", "密码找回"],
            response="为了保护您的账户安全，我将为您转接到人工客服处理密码重置业务。"
        ),
        ScriptEntry(
            id="complaint",
            category="complaint",
            keywords=["投诉", "问题", "故障", "不满意"],
            patterns=["我要投诉", "有问题", "服务不好"],
            response="非常抱歉给您带来不便，我会认真记录您的问题并及时处理。"
        ),
        ScriptEntry(
            id="goodbye",
            category="goodbye",
            keywords=["再见", "拜拜", "goodbye", "bye"],
            patterns=["再见", "拜拜", "挂了"],
            response="感谢您的来电，祝您生活愉快，再见！"
        )
    ]


def create_response_matcher(fuzzy_threshold: float = 0.6) -> ResponseMatcher:
    """Create response matcher with sample scripts"""
    matcher = ResponseMatcher(fuzzy_threshold)
    sample_scripts = create_sample_scripts()
    matcher.load_script_entries(sample_scripts)
    return matcher