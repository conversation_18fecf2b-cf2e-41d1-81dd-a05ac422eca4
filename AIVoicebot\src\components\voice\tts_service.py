"""
Text-to-Speech (TTS) Service
============================

This module provides text-to-speech capabilities for the AI Voice Customer Service System.
It supports multiple TTS engines and provides a unified interface for text-to-speech conversion.
"""

import asyncio
import logging
import pyttsx3
import io
import wave
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
import threading

from ...core.base_component import BaseComponent

# Try to import Edge-TTS
try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

class TTSEngine(Enum):
    """Supported TTS engines."""
    PYTTSX3 = "pyttsx3"
    EDGE_TTS = "edge_tts"
    AZURE = "azure"
    AWS = "aws"
    GOOGLE = "google"

@dataclass
class TTSConfig:
    """Configuration for TTS service."""
    engine: TTSEngine = TTSEngine.PYTTSX3
    voice_id: Optional[str] = None
    rate: int = 200  # Words per minute
    volume: float = 0.9  # 0.0 to 1.0
    language: str = "en-US"
    output_format: str = "wav"
    sample_rate: int = 16000

@dataclass
class TTSResult:
    """Result from TTS processing."""
    audio_data: bytes
    text: str
    duration: float
    processing_time: float
    engine_used: str
    format: str
    sample_rate: int

class TTSService(BaseComponent):
    """Text-to-Speech service."""
    
    def __init__(self, config: TTSConfig, logger: Optional[logging.Logger] = None):
        super().__init__("tts_service", logger)
        self.config = config
        self.engine = None
        self._engine_lock = threading.Lock()
        
        self._log.info(f"TTS Service initialized with engine: {config.engine.value}")
    
    # Implement abstract methods from BaseComponent
    async def _initialize_impl(self) -> bool:
        """Implementation of initialization logic."""
        try:
            if self.config.engine == TTSEngine.PYTTSX3:
                await self._initialize_pyttsx3()
            elif self.config.engine == TTSEngine.EDGE_TTS:
                await self._initialize_edge_tts()
            else:
                raise ValueError(f"Unsupported TTS engine: {self.config.engine}")

            self._log.info("TTS Service initialized successfully")
            return True

        except Exception as e:
            self._log.error(f"Failed to initialize TTS Service: {e}")
            return False

    async def _initialize_pyttsx3(self):
        """Initialize pyttsx3 engine."""
        def init_engine():
            engine = pyttsx3.init()

            # Configure voice properties
            voices = engine.getProperty('voices')
            if voices:
                if self.config.voice_id:
                    # Try to find specific voice
                    for voice in voices:
                        if self.config.voice_id in voice.id:
                            engine.setProperty('voice', voice.id)
                            break
                else:
                    # Use first available voice
                    engine.setProperty('voice', voices[0].id)

            # Set rate and volume
            engine.setProperty('rate', self.config.rate)
            engine.setProperty('volume', self.config.volume)

            return engine

        # Initialize in thread to avoid blocking
        loop = asyncio.get_event_loop()
        self.engine = await loop.run_in_executor(None, init_engine)

        self._log.info("pyttsx3 engine initialized")

    async def _initialize_edge_tts(self):
        """Initialize Edge-TTS engine."""
        if not EDGE_TTS_AVAILABLE:
            raise ValueError("Edge-TTS not available. Install with: pip install edge-tts")

        # Get voice from config or use default
        self.edge_voice = self.config.voice_id or "zh-CN-XiaoxiaoNeural"

        # Test Edge-TTS availability
        try:
            communicate = edge_tts.Communicate("test", self.edge_voice)
            async for _ in communicate.stream():
                break  # Just test if we can create a stream
        except Exception as e:
            self._log.warning(f"Edge-TTS voice {self.edge_voice} not available, using default")
            self.edge_voice = "zh-CN-XiaoxiaoNeural"

        self._log.info(f"Edge-TTS initialized with voice: {self.edge_voice}")

    async def _start_impl(self) -> bool:
        """Implementation of start logic."""
        self._log.info("TTS Service started")
        return True

    async def _stop_impl(self) -> bool:
        """Implementation of stop logic."""
        if self.engine:
            with self._engine_lock:
                try:
                    self.engine.stop()
                except:
                    pass
        self._log.info("TTS Service stopped")
        return True
    
    async def synthesize_speech(self, text: str) -> TTSResult:
        """
        Convert text to speech.
        
        Args:
            text: Text to convert to speech
            
        Returns:
            TTSResult with audio data and metadata
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            if self.config.engine == TTSEngine.PYTTSX3:
                audio_data = await self._synthesize_pyttsx3(text)
            elif self.config.engine == TTSEngine.EDGE_TTS:
                audio_data = await self._synthesize_edge_tts(text)
            else:
                raise ValueError(f"Unsupported TTS engine: {self.config.engine}")

            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            # Estimate audio duration (rough calculation)
            words = len(text.split())
            estimated_duration = (words / self.config.rate) * 60  # Convert WPM to seconds
            
            return TTSResult(
                audio_data=audio_data,
                text=text,
                duration=estimated_duration,
                processing_time=processing_time,
                engine_used=self.config.engine.value,
                format=self.config.output_format,
                sample_rate=self.config.sample_rate
            )
            
        except Exception as e:
            self._log.error(f"TTS synthesis failed: {e}")
            raise
    
    async def _synthesize_pyttsx3(self, text: str) -> bytes:
        """Synthesize speech using pyttsx3 with timeout protection."""
        def synthesize():
            with self._engine_lock:
                # Create a temporary file to capture audio
                import tempfile
                import os

                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_path = temp_file.name

                try:
                    # Save to file
                    self.engine.save_to_file(text, temp_path)
                    self.engine.runAndWait()

                    # Read the file back
                    with open(temp_path, 'rb') as f:
                        audio_data = f.read()

                    return audio_data
                finally:
                    # Clean up temp file
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

        loop = asyncio.get_event_loop()

        # Add timeout protection for TTS synthesis
        try:
            return await asyncio.wait_for(
                loop.run_in_executor(None, synthesize),
                timeout=10.0  # 10 second timeout
            )
        except asyncio.TimeoutError:
            self._log.error(f"TTS synthesis timed out for text: '{text[:50]}...'")
            # Return empty audio data as fallback
            return b''

    async def _synthesize_edge_tts(self, text: str) -> bytes:
        """Synthesize speech using Edge-TTS."""
        if not EDGE_TTS_AVAILABLE:
            self._log.error("Edge-TTS not available")
            return b''

        try:
            # Create Edge-TTS communicate object
            communicate = edge_tts.Communicate(text, self.edge_voice)

            # Collect audio data
            audio_data = b''
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]

            self._log.debug(f"Edge-TTS generated {len(audio_data)} bytes for text: '{text[:50]}...'")
            return audio_data

        except Exception as e:
            self._log.error(f"Edge-TTS synthesis failed: {e}")
            return b''
    
    async def speak_text(self, text: str) -> bool:
        """
        Speak text directly (for testing purposes).
        
        Args:
            text: Text to speak
            
        Returns:
            True if successful
        """
        try:
            def speak():
                with self._engine_lock:
                    self.engine.say(text)
                    self.engine.runAndWait()
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, speak)
            
            self._log.info(f"Spoke text: {text[:50]}...")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to speak text: {e}")
            return False
    

    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices."""
        if not self.engine:
            return []
        
        try:
            with self._engine_lock:
                voices = self.engine.getProperty('voices')
                return [
                    {
                        "id": voice.id,
                        "name": voice.name,
                        "languages": getattr(voice, 'languages', []),
                        "gender": getattr(voice, 'gender', 'unknown'),
                        "age": getattr(voice, 'age', 'unknown')
                    }
                    for voice in voices
                ] if voices else []
        except Exception as e:
            self._log.error(f"Failed to get available voices: {e}")
            return []
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about the current TTS engine."""
        return {
            "engine": self.config.engine.value,
            "language": self.config.language,
            "rate": self.config.rate,
            "volume": self.config.volume,
            "output_format": self.config.output_format,
            "sample_rate": self.config.sample_rate,
            "voice_id": self.config.voice_id
        }
    
    async def set_voice_properties(self, rate: Optional[int] = None, 
                                 volume: Optional[float] = None,
                                 voice_id: Optional[str] = None) -> bool:
        """
        Update voice properties dynamically.
        
        Args:
            rate: Speech rate in words per minute
            volume: Volume level (0.0 to 1.0)
            voice_id: Voice identifier
            
        Returns:
            True if successful
        """
        try:
            with self._engine_lock:
                if rate is not None:
                    self.engine.setProperty('rate', rate)
                    self.config.rate = rate
                
                if volume is not None:
                    self.engine.setProperty('volume', volume)
                    self.config.volume = volume
                
                if voice_id is not None:
                    voices = self.engine.getProperty('voices')
                    for voice in voices:
                        if voice_id in voice.id:
                            self.engine.setProperty('voice', voice.id)
                            self.config.voice_id = voice_id
                            break
            
            self._log.info("Voice properties updated successfully")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to update voice properties: {e}")
            return False

    async def _cleanup_impl(self) -> bool:
        """Implementation of cleanup logic."""
        self.engine = None
        self._log.info("TTS Service cleanup completed")
        return True
