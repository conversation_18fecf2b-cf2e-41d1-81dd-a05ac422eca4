"""
Example usage of the audio streaming and buffering system.

This example demonstrates how to use the real-time audio streaming
components for processing audio in a voice customer service system.
"""

import asyncio
import numpy as np
import logging
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import audio streaming components
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.interfaces import AudioChunk, AudioFormat
from src.components.audio import (
    CircularAudioBuffer, BufferConfig,
    RealTimeAudioStream, StreamConfig, StreamState,
    AudioStreamManager, StreamManagerConfig,
    create_default_stream_manager, create_audio_stream
)


class MockConfigManager:
    """Mock configuration manager for example."""
    
    def get(self, key: str, default=None):
        return default


async def basic_buffer_example():
    """Demonstrate basic circular buffer usage."""
    logger.info("=== Basic Buffer Example ===")
    
    # Create buffer configuration
    config = BufferConfig(
        max_size=10,  # Hold up to 10 chunks
        chunk_size=1024,  # 1KB chunks
        sample_rate=16000,
        channels=1
    )
    
    # Create circular buffer
    buffer = CircularAudioBuffer(config)
    logger.info(f"Created buffer with capacity: {config.max_size} chunks")
    
    # Generate some test audio data
    for i in range(5):
        # Create random audio data (simulating microphone input)
        audio_data = np.random.randint(-32768, 32767, 1024, dtype=np.int16).tobytes()
        
        chunk = AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=64,  # 1024 samples at 16kHz = 64ms
            sample_rate=16000
        )
        
        # Write to buffer
        success = buffer.write(chunk)
        logger.info(f"Wrote chunk {i}: {'success' if success else 'failed'}")
        logger.info(f"Buffer size: {buffer.size()}, Fill level: {buffer.fill_level():.2%}")
    
    # Read from buffer
    logger.info("Reading from buffer:")
    while not buffer.is_empty():
        chunk = buffer.read()
        if chunk:
            logger.info(f"Read chunk with timestamp: {chunk.timestamp:.3f}s")
    
    # Show buffer statistics
    stats = buffer.get_stats()
    logger.info(f"Buffer stats: {stats}")


async def audio_stream_example():
    """Demonstrate real-time audio stream usage."""
    logger.info("\n=== Audio Stream Example ===")
    
    # Create mock config manager
    config_manager = MockConfigManager()
    
    # Create stream configuration
    stream_config = StreamConfig(
        stream_id="example_stream",
        sample_rate=16000,
        channels=1,
        buffer_size=2048,
        chunk_size=512,
        target_latency_ms=100,
        enable_adaptive_buffering=True,
        enable_jitter_compensation=True
    )
    
    # Create and initialize stream
    stream = RealTimeAudioStream(stream_config, config_manager, logger)
    
    # Add event handlers
    async def on_chunk_processed(chunk: AudioChunk):
        logger.info(f"Processed chunk: {len(chunk.data)} bytes at {chunk.timestamp:.3f}s")
    
    def on_error(error: Exception):
        logger.error(f"Stream error: {error}")
    
    async def on_state_change(stream_id: str, old_state: StreamState, new_state: StreamState):
        logger.info(f"Stream {stream_id} state: {old_state.value} -> {new_state.value}")
    
    stream.add_chunk_handler(on_chunk_processed)
    stream.add_error_handler(on_error)
    stream.add_state_handler(on_state_change)
    
    try:
        # Initialize and start stream
        await stream.initialize()
        await stream.start()
        
        logger.info(f"Stream started: {stream.stream_id}")
        logger.info(f"Stream active: {stream.is_active}")
        
        # Simulate audio input
        for i in range(10):
            # Generate test audio chunk
            audio_data = np.random.randint(-32768, 32767, 512, dtype=np.int16).tobytes()
            
            chunk = AudioChunk(
                data=audio_data,
                format=AudioFormat.PCM_16KHZ_MONO,
                timestamp=datetime.now(),
                duration_ms=32,  # 512 samples at 16kHz = 32ms
                sample_rate=16000
            )
            
            # Write to stream
            success = await stream.write_audio(chunk)
            if success:
                logger.info(f"Wrote audio chunk {i}")
            else:
                logger.warning(f"Failed to write audio chunk {i}")
            
            # Small delay to simulate real-time
            await asyncio.sleep(0.05)
        
        # Let processing complete
        await asyncio.sleep(0.5)
        
        # Try to read processed audio
        logger.info("Reading processed audio:")
        for i in range(5):
            processed_chunk = await stream.read_audio()
            if processed_chunk:
                logger.info(f"Read processed chunk: {len(processed_chunk.data)} bytes")
            else:
                logger.info("No processed audio available")
            await asyncio.sleep(0.1)
        
        # Test pause/resume
        logger.info("Testing pause/resume...")
        await stream.pause()
        logger.info(f"Stream paused: {stream.is_paused}")
        
        await asyncio.sleep(0.2)
        
        await stream.resume()
        logger.info(f"Stream resumed: {stream.is_active}")
        
        # Get stream statistics
        stats = stream.get_stream_stats()
        logger.info(f"Stream stats:")
        logger.info(f"  Bytes processed: {stats.bytes_processed}")
        logger.info(f"  Chunks processed: {stats.chunks_processed}")
        logger.info(f"  Average latency: {stats.average_latency_ms:.2f}ms")
        logger.info(f"  Buffer overruns: {stats.buffer_overruns}")
        logger.info(f"  Buffer underruns: {stats.buffer_underruns}")
        
        # Get buffer health
        buffer_health = stream.buffer_health
        logger.info(f"Buffer health: {buffer_health}")
        
    finally:
        # Clean up
        await stream.stop()
        await stream.cleanup()
        logger.info("Stream stopped and cleaned up")


async def stream_manager_example():
    """Demonstrate audio stream manager usage."""
    logger.info("\n=== Stream Manager Example ===")
    
    # Create mock config manager
    config_manager = MockConfigManager()
    
    # Create stream manager with custom configuration
    manager_config = StreamManagerConfig(
        max_concurrent_streams=5,
        enable_load_balancing=True,
        enable_resource_monitoring=False,  # Disable for example
        health_check_interval_seconds=10
    )
    
    manager = AudioStreamManager(manager_config, config_manager, logger)
    
    try:
        # Initialize and start manager
        await manager.initialize()
        await manager.start()
        
        logger.info("Stream manager started")
        
        # Create multiple streams
        stream_ids = ["voice_input", "voice_output", "background_audio"]
        
        for stream_id in stream_ids:
            stream_config = StreamConfig(
                stream_id=stream_id,
                sample_rate=16000,
                channels=1,
                buffer_size=1024,
                chunk_size=256
            )
            
            stream = await manager.create_stream(stream_config)
            logger.info(f"Created stream: {stream_id}")
        
        logger.info(f"Active streams: {manager.get_stream_count()}")
        logger.info(f"Stream list: {manager.list_streams()}")
        
        # Test writing to specific streams
        audio_data = np.random.randint(-32768, 32767, 256, dtype=np.int16).tobytes()
        chunk = AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=16,  # 256 samples at 16kHz = 16ms
            sample_rate=16000
        )
        
        # Write to voice input stream
        success = await manager.write_to_stream("voice_input", chunk)
        logger.info(f"Wrote to voice_input: {'success' if success else 'failed'}")
        
        # Pause and resume a stream
        await manager.pause_stream("background_audio")
        logger.info("Paused background_audio stream")
        
        await asyncio.sleep(0.2)
        
        await manager.resume_stream("background_audio")
        logger.info("Resumed background_audio stream")
        
        # Get manager statistics
        stats = manager.get_manager_stats()
        logger.info(f"Manager stats:")
        logger.info(f"  Active streams: {stats.active_streams}")
        logger.info(f"  Total created: {stats.total_streams_created}")
        logger.info(f"  Average latency: {stats.average_latency_ms:.2f}ms")
        
        # Get system health
        health = manager.get_system_health()
        logger.info(f"System health: {health['status']}")
        if health['issues']:
            logger.info(f"Issues: {health['issues']}")
        
        # Get all stream statistics
        all_stats = manager.get_all_stream_stats()
        for stream_id, stream_stats in all_stats.items():
            logger.info(f"Stream {stream_id}: {stream_stats.chunks_processed} chunks processed")
        
        # Test stream destruction
        success = await manager.destroy_stream("background_audio")
        logger.info(f"Destroyed background_audio: {'success' if success else 'failed'}")
        logger.info(f"Remaining streams: {manager.list_streams()}")
        
    finally:
        # Clean up all streams
        await manager.stop()
        await manager.cleanup()
        logger.info("Stream manager stopped and cleaned up")


async def utility_functions_example():
    """Demonstrate utility functions."""
    logger.info("\n=== Utility Functions Example ===")
    
    config_manager = MockConfigManager()
    
    # Create default stream manager
    manager = await create_default_stream_manager(config_manager, logger)
    
    try:
        logger.info("Created default stream manager")
        
        # Create stream using utility function
        stream = await create_audio_stream(
            stream_id="utility_stream",
            manager=manager,
            sample_rate=16000,
            channels=1,
            buffer_size=2048,
            target_latency_ms=150
        )
        
        logger.info(f"Created stream using utility: {stream.stream_id}")
        
        # Test the stream
        audio_data = np.random.randint(-32768, 32767, 512, dtype=np.int16).tobytes()
        chunk = AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=32,  # 512 samples at 16kHz = 32ms
            sample_rate=16000
        )
        
        success = await stream.write_audio(chunk)
        logger.info(f"Wrote test chunk: {'success' if success else 'failed'}")
        
        await asyncio.sleep(0.1)
        
        processed = await stream.read_audio()
        logger.info(f"Read processed chunk: {'success' if processed else 'no data'}")
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def async_generator_example():
    """Demonstrate async generator usage for streaming."""
    logger.info("\n=== Async Generator Example ===")
    
    config_manager = MockConfigManager()
    manager = await create_default_stream_manager(config_manager, logger)
    
    try:
        # Create stream
        stream = await create_audio_stream(
            stream_id="generator_stream",
            manager=manager,
            sample_rate=16000,
            channels=1
        )
        
        # Start a task to feed audio data
        async def audio_feeder():
            for i in range(5):
                audio_data = np.random.randint(-32768, 32767, 512, dtype=np.int16).tobytes()
                chunk = AudioChunk(
                    data=audio_data,
                    format=AudioFormat.PCM_16KHZ_MONO,
                    timestamp=datetime.now(),
                    duration_ms=32,  # 512 samples at 16kHz = 32ms
                    sample_rate=16000
                )
                
                await stream.write_audio(chunk)
                await asyncio.sleep(0.1)
        
        # Start feeder task
        feeder_task = asyncio.create_task(audio_feeder())
        
        # Use async generator to read processed audio
        logger.info("Reading audio using async generator:")
        chunk_count = 0
        
        async for processed_chunk in manager.get_stream_generator("generator_stream"):
            logger.info(f"Received chunk {chunk_count}: {len(processed_chunk.data)} bytes")
            chunk_count += 1
            
            # Stop after receiving a few chunks
            if chunk_count >= 3:
                break
        
        # Wait for feeder to complete
        await feeder_task
        
    finally:
        await manager.stop()
        await manager.cleanup()


async def main():
    """Run all examples."""
    logger.info("Audio Streaming System Examples")
    logger.info("=" * 50)
    
    try:
        await basic_buffer_example()
        await audio_stream_example()
        await stream_manager_example()
        await utility_functions_example()
        await async_generator_example()
        
        logger.info("\n" + "=" * 50)
        logger.info("All examples completed successfully!")
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())