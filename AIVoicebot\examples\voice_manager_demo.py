"""
Demo script for voice manager.

This script demonstrates how to use the voice manager for consistent voice
characteristics, context-aware parameter adjustment, and audio optimization.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.tts.voice_manager import (
    VoiceManager, VoiceManagerConfig, VoiceProfile, VoicePersonality,
    SpeechContext, CompressionLevel, AudioOptimizationSettings,
    create_voice_manager, create_telephony_optimization,
    create_streaming_optimization, create_custom_voice_profile,
    PYDUB_AVAILABLE
)
from src.components.tts.edge_tts_generator import (
    EdgeTTSGenerator, EdgeTTSConfig, VoiceGender, AudioFormat,
    create_edge_tts_generator, EDGE_TTS_AVAILABLE
)
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_voice_profiles():
    """Demonstrate voice profile management."""
    logger.info("=== Voice Profile Management Demo ===")
    
    if not EDGE_TTS_AVAILABLE or not PYDUB_AVAILABLE:
        logger.warning("Required dependencies not available")
        return
    
    try:
        # Create TTS generator and voice manager
        tts_generator = await create_edge_tts_generator(config_manager=Mock())
        voice_manager = await create_voice_manager(
            tts_generator=tts_generator,
            default_personality=VoicePersonality.PROFESSIONAL,
            config_manager=Mock()
        )
        
        # List available profiles
        profiles = voice_manager.list_voice_profiles()
        logger.info(f"Available voice profiles: {len(profiles)}")
        
        for profile in profiles:
            logger.info(f"  {profile.name} - {profile.personality.value} {profile.gender.value}")
            logger.info(f"    Traits: W{profile.warmth:.1f} A{profile.authority:.1f} E{profile.energy:.1f} C{profile.clarity:.1f}")
        
        # Test profile filtering
        professional_profiles = voice_manager.get_profile_by_personality(VoicePersonality.PROFESSIONAL)
        logger.info(f"\nProfessional profiles: {len(professional_profiles)}")
        
        female_profiles = voice_manager.get_profile_by_gender(VoiceGender.FEMALE)
        logger.info(f"Female profiles: {len(female_profiles)}")
        
        # Set active profile
        if profiles:
            voice_manager.set_active_profile("professional_female")
            active = voice_manager.get_active_profile()
            logger.info(f"Active profile: {active.name if active else 'None'}")
        
    except Exception as e:
        logger.error(f"Voice profiles demo failed: {e}")
    
    finally:
        if 'voice_manager' in locals():
            await voice_manager.stop()
            await voice_manager.cleanup()
        if 'tts_generator' in locals():
            await tts_generator.stop()
            await tts_generator.cleanup()


async def demo_context_aware_speech():
    """Demonstrate context-aware speech generation."""
    logger.info("=== Context-Aware Speech Demo ===")
    
    if not EDGE_TTS_AVAILABLE or not PYDUB_AVAILABLE:
        logger.warning("Required dependencies not available")
        return
    
    try:
        # Create components
        tts_generator = await create_edge_tts_generator(config_manager=Mock())
        voice_manager = await create_voice_manager(
            tts_generator=tts_generator,
            config_manager=Mock()
        )
        
        # Test different contexts with appropriate texts
        context_tests = [
            {
                "context": SpeechContext.GREETING,
                "text": "您好！欢迎致电我们银行，我是您的专属客服小雅。"
            },
            {
                "context": SpeechContext.EXPLANATION,
                "text": "根据您的信用评估，我们可以为您提供年化利率3.5%到6.8%的个人贷款。"
            },
            {
                "context": SpeechContext.APOLOGY,
                "text": "非常抱歉让您久等了，由于系统升级造成的延误，我们深表歉意。"
            },
            {
                "context": SpeechContext.CLOSING,
                "text": "感谢您选择我们的服务，祝您生活愉快，再见！"
            },
            {
                "context": SpeechContext.URGENT,
                "text": "请注意，您的账户出现异常交易，请立即联系我们确认。"
            }
        ]
        
        for test in context_tests:
            context = test["context"]
            text = test["text"]
            
            logger.info(f"\nGenerating {context.value} speech:")
            logger.info(f"Text: {text}")
            
            # Generate speech with context
            result = await voice_manager.generate_speech(text, context=context)
            
            logger.info(f"Voice used: {result.voice_used}")
            logger.info(f"Duration: {result.duration_ms:.0f}ms")
            logger.info(f"Optimized: {result.optimized}")
            
            # Save with context name
            filename = f"demo_context_{context.value}.wav"
            if result.save_to_file(filename):
                logger.info(f"Saved to: {filename}")
    
    except Exception as e:
        logger.error(f"Context-aware speech demo failed: {e}")
    
    finally:
        if 'voice_manager' in locals():
            await voice_manager.stop()
            await voice_manager.cleanup()
        if 'tts_generator' in locals():
            await tts_generator.stop()
            await tts_generator.cleanup()


async def demo_scenario_recommendations():
    """Demonstrate scenario-based voice recommendations."""
    logger.info("=== Scenario Recommendations Demo ===")
    
    if not EDGE_TTS_AVAILABLE or not PYDUB_AVAILABLE:
        logger.warning("Required dependencies not available")
        return
    
    try:
        # Create components
        tts_generator = await create_edge_tts_generator(config_manager=Mock())
        voice_manager = await create_voice_manager(
            tts_generator=tts_generator,
            config_manager=Mock()
        )
        
        # Test different scenarios
        scenarios = [
            {"scenario": "greeting", "customer": "general", "urgency": "normal"},
            {"scenario": "support", "customer": "vip", "urgency": "normal"},
            {"scenario": "complaint", "customer": "general", "urgency": "high"},
            {"scenario": "sales", "customer": "young", "urgency": "low"},
            {"scenario": "technical", "customer": "business", "urgency": "normal"}
        ]
        
        for test in scenarios:
            logger.info(f"\nScenario: {test['scenario']}")
            logger.info(f"Customer: {test['customer']}, Urgency: {test['urgency']}")
            
            # Get recommendation
            recommended = voice_manager.recommend_profile_for_scenario(
                scenario=test["scenario"],
                customer_type=test["customer"],
                urgency=test["urgency"]
            )
            
            if recommended:
                logger.info(f"Recommended: {recommended.name}")
                logger.info(f"  Personality: {recommended.personality.value}")
                logger.info(f"  Gender: {recommended.gender.value}")
                logger.info(f"  Traits: W{recommended.warmth:.1f} A{recommended.authority:.1f} E{recommended.energy:.1f}")
            else:
                logger.info("No specific recommendation")
    
    except Exception as e:
        logger.error(f"Scenario recommendations demo failed: {e}")
    
    finally:
        if 'voice_manager' in locals():
            await voice_manager.stop()
            await voice_manager.cleanup()
        if 'tts_generator' in locals():
            await tts_generator.stop()
            await tts_generator.cleanup()


async def demo_audio_optimization():
    """Demonstrate audio optimization features."""
    logger.info("=== Audio Optimization Demo ===")
    
    if not EDGE_TTS_AVAILABLE or not PYDUB_AVAILABLE:
        logger.warning("Required dependencies not available")
        return
    
    try:
        # Create components
        tts_generator = await create_edge_tts_generator(config_manager=Mock())
        voice_manager = await create_voice_manager(
            tts_generator=tts_generator,
            enable_optimization=True,
            config_manager=Mock()
        )
        
        test_text = "我们的个人贷款产品具有利率优惠、审批快速、还款灵活等特点。"
        
        # Test different optimization settings
        optimization_tests = [
            {
                "name": "No Optimization",
                "settings": AudioOptimizationSettings(
                    compression_level=CompressionLevel.NONE,
                    normalize_volume=False,
                    dynamic_range_compression=False
                )
            },
            {
                "name": "Telephony Optimization",
                "settings": create_telephony_optimization()
            },
            {
                "name": "Streaming Optimization", 
                "settings": create_streaming_optimization()
            },
            {
                "name": "Maximum Compression",
                "settings": AudioOptimizationSettings(
                    compression_level=CompressionLevel.MAXIMUM,
                    target_bitrate_kbps=32,
                    normalize_volume=True,
                    dynamic_range_compression=True
                )
            }
        ]
        
        for test in optimization_tests:
            logger.info(f"\nTesting: {test['name']}")
            
            # Generate speech with specific optimization
            result = await voice_manager.generate_speech(
                test_text,
                optimization_settings=test["settings"]
            )
            
            logger.info(f"  File size: {result.file_size_bytes} bytes")
            logger.info(f"  Duration: {result.duration_ms:.0f}ms")
            logger.info(f"  Sample rate: {result.sample_rate}Hz")
            logger.info(f"  Optimized: {result.optimized}")
            
            # Calculate compression ratio if we have a baseline
            if test["name"] != "No Optimization":
                # This would be calculated against the unoptimized version
                logger.info(f"  Compression level: {test['settings'].compression_level.value}")
            
            # Save optimized audio
            filename = f"demo_optimization_{test['name'].lower().replace(' ', '_')}.wav"
            if result.save_to_file(filename):
                logger.info(f"  Saved to: {filename}")
    
    except Exception as e:
        logger.error(f"Audio optimization demo failed: {e}")
    
    finally:
        if 'voice_manager' in locals():
            await voice_manager.stop()
            await voice_manager.cleanup()
        if 'tts_generator' in locals():
            await tts_generator.stop()
            await tts_generator.cleanup()


async def demo_custom_profiles():
    """Demonstrate custom voice profile creation."""
    logger.info("=== Custom Voice Profiles Demo ===")
    
    if not EDGE_TTS_AVAILABLE or not PYDUB_AVAILABLE:
        logger.warning("Required dependencies not available")
        return
    
    try:
        # Create components
        tts_generator = await create_edge_tts_generator(config_manager=Mock())
        voice_manager = await create_voice_manager(
            tts_generator=tts_generator,
            config_manager=Mock()
        )
        
        # Create custom profiles for different scenarios
        custom_profiles = [
            create_custom_voice_profile(
                name="VIP Service Voice",
                voice_id="zh-CN-XiaoxiaoNeural",
                personality=VoicePersonality.PROFESSIONAL,
                gender=VoiceGender.FEMALE,
                warmth=0.7,
                authority=0.9,
                energy=0.6,
                clarity=0.95
            ),
            create_custom_voice_profile(
                name="Youth Marketing Voice",
                voice_id="zh-CN-XiaomoNeural", 
                personality=VoicePersonality.ENERGETIC,
                gender=VoiceGender.FEMALE,
                warmth=0.8,
                authority=0.5,
                energy=0.95,
                clarity=0.8
            ),
            create_custom_voice_profile(
                name="Senior Care Voice",
                voice_id="zh-CN-XiaoyiNeural",
                personality=VoicePersonality.WARM,
                gender=VoiceGender.FEMALE,
                warmth=0.95,
                authority=0.6,
                energy=0.5,
                clarity=0.9
            )
        ]
        
        # Add custom profiles
        for profile in custom_profiles:
            voice_manager.add_voice_profile(profile)
            logger.info(f"Added custom profile: {profile.name}")
            
            # Add context adjustments
            if profile.name == "VIP Service Voice":
                profile.context_adjustments[SpeechContext.GREETING] = {
                    "speed": "-5%", "pitch": "+5Hz", "volume": "+5%"
                }
                profile.context_adjustments[SpeechContext.EXPLANATION] = {
                    "speed": "-10%", "pitch": "+0Hz", "volume": "+5%"
                }
            elif profile.name == "Youth Marketing Voice":
                profile.context_adjustments[SpeechContext.PROMOTIONAL] = {
                    "speed": "+15%", "pitch": "+20Hz", "volume": "+10%"
                }
                profile.context_adjustments[SpeechContext.GREETING] = {
                    "speed": "+10%", "pitch": "+15Hz", "volume": "+5%"
                }
        
        # Test custom profiles
        test_scenarios = [
            {
                "profile": "vip_service_voice",
                "text": "尊敬的VIP客户，感谢您选择我们的专属服务。",
                "context": SpeechContext.GREETING
            },
            {
                "profile": "youth_marketing_voice",
                "text": "年轻人的第一张信用卡，就选我们银行！",
                "context": SpeechContext.PROMOTIONAL
            },
            {
                "profile": "senior_care_voice",
                "text": "请您慢慢说，我会耐心为您解答每一个问题。",
                "context": SpeechContext.EXPLANATION
            }
        ]
        
        for test in test_scenarios:
            logger.info(f"\nTesting custom profile: {test['profile']}")
            logger.info(f"Text: {test['text']}")
            
            # Generate speech with custom profile
            result = await voice_manager.generate_speech(
                test["text"],
                context=test["context"],
                profile_name=test["profile"]
            )
            
            logger.info(f"Voice used: {result.voice_used}")
            logger.info(f"Duration: {result.duration_ms:.0f}ms")
            
            # Save custom profile audio
            filename = f"demo_custom_{test['profile']}.wav"
            if result.save_to_file(filename):
                logger.info(f"Saved to: {filename}")
    
    except Exception as e:
        logger.error(f"Custom profiles demo failed: {e}")
    
    finally:
        if 'voice_manager' in locals():
            await voice_manager.stop()
            await voice_manager.cleanup()
        if 'tts_generator' in locals():
            await tts_generator.stop()
            await tts_generator.cleanup()


async def demo_statistics_and_monitoring():
    """Demonstrate statistics and monitoring features."""
    logger.info("=== Statistics and Monitoring Demo ===")
    
    if not EDGE_TTS_AVAILABLE or not PYDUB_AVAILABLE:
        logger.warning("Required dependencies not available")
        return
    
    try:
        # Create components with monitoring enabled
        tts_generator = await create_edge_tts_generator(config_manager=Mock())
        
        config = VoiceManagerConfig(
            enable_quality_monitoring=True,
            quality_feedback_learning=True,
            context_learning=True
        )
        
        voice_manager = VoiceManager(config, tts_generator, Mock())
        await voice_manager.initialize()
        await voice_manager.start()
        
        # Generate multiple speeches to collect statistics
        test_cases = [
            {"text": "欢迎致电", "context": SpeechContext.GREETING},
            {"text": "请稍等", "context": SpeechContext.EXPLANATION},
            {"text": "谢谢您", "context": SpeechContext.CLOSING},
            {"text": "很抱歉", "context": SpeechContext.APOLOGY},
            {"text": "紧急通知", "context": SpeechContext.URGENT}
        ]
        
        for test in test_cases:
            await voice_manager.generate_speech(
                test["text"],
                context=test["context"]
            )
        
        # Get comprehensive statistics
        stats = voice_manager.get_voice_manager_stats()
        
        logger.info("\nVoice Manager Statistics:")
        logger.info(f"  Active profile: {stats['active_profile']}")
        logger.info(f"  Total profiles: {stats['total_profiles']}")
        
        # Context statistics
        context_stats = stats["context_statistics"]
        logger.info(f"\nContext Usage:")
        logger.info(f"  Total contexts used: {context_stats['total_contexts_used']}")
        logger.info(f"  Total usage count: {context_stats['total_usage_count']}")
        
        for context, data in context_stats["context_distribution"].items():
            logger.info(f"  {context}: {data['count']} times ({data['percentage']:.1f}%)")
        
        # Quality monitoring
        quality_stats = stats["quality_monitoring"]
        logger.info(f"\nQuality Monitoring:")
        logger.info(f"  Enabled: {quality_stats['enabled']}")
        logger.info(f"  Average quality: {quality_stats['average_quality']:.3f}")
        logger.info(f"  Quality samples: {quality_stats['quality_samples']}")
        
        # Optimization statistics
        opt_stats = stats["optimization_statistics"]
        logger.info(f"\nOptimization Statistics:")
        logger.info(f"  Cache size: {opt_stats['cache_size']} items")
        logger.info(f"  Cache memory: {opt_stats['cache_memory_mb']:.2f} MB")
        
        # Profile usage statistics
        logger.info(f"\nProfile Usage:")
        for name, profile_data in stats["profile_statistics"].items():
            logger.info(f"  {name}: {profile_data['usage_count']} times")
            logger.info(f"    Personality: {profile_data['personality']}")
            logger.info(f"    Last used: {profile_data['last_used'] or 'Never'}")
    
    except Exception as e:
        logger.error(f"Statistics demo failed: {e}")
    
    finally:
        if 'voice_manager' in locals():
            await voice_manager.stop()
            await voice_manager.cleanup()
        if 'tts_generator' in locals():
            await tts_generator.stop()
            await tts_generator.cleanup()


async def main():
    """Run all demos."""
    logger.info("Starting Voice Manager Demo")
    
    if not EDGE_TTS_AVAILABLE:
        logger.error("EdgeTTS not available. Please install with: pip install edge-tts")
        return
    
    if not PYDUB_AVAILABLE:
        logger.error("Pydub not available. Please install with: pip install pydub")
        return
    
    try:
        await demo_voice_profiles()
        await demo_context_aware_speech()
        await demo_scenario_recommendations()
        await demo_audio_optimization()
        await demo_custom_profiles()
        await demo_statistics_and_monitoring()
        
        logger.info("All demos completed successfully!")
        logger.info("Check the generated .wav files to hear the results.")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())