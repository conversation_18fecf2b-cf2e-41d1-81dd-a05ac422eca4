"""
Component Coordination System

This module provides real-time coordination between audio processing components
including:
- Event-driven communication between VAD, ASR, and TTS components
- Proper synchronization and conflict resolution for simultaneous audio operations
- Audio pipeline state management and flow control
- Resource allocation and scheduling for audio processing
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Callable, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from abc import ABC, abstractmethod
import uuid
from collections import defaultdict, deque
import threading

from ..core.base_component import BaseComponent


class ComponentState(Enum):
    """Component operational states."""
    IDLE = "idle"
    ACTIVE = "active"
    BUSY = "busy"
    WAITING = "waiting"
    ERROR = "error"
    SUSPENDED = "suspended"


class EventType(Enum):
    """Event types for component coordination."""
    # Audio events
    AUDIO_START = "audio_start"
    AUDIO_END = "audio_end"
    AUDIO_DATA = "audio_data"
    
    # VAD events
    SPEECH_DETECTED = "speech_detected"
    SPEECH_ENDED = "speech_ended"
    SILENCE_DETECTED = "silence_detected"
    
    # ASR events
    TRANSCRIPTION_START = "transcription_start"
    TRANSCRIPTION_PARTIAL = "transcription_partial"
    TRANSCRIPTION_COMPLETE = "transcription_complete"
    TRANSCRIPTION_ERROR = "transcription_error"
    
    # TTS events
    SYNTHESIS_START = "synthesis_start"
    SYNTHESIS_PROGRESS = "synthesis_progress"
    SYNTHESIS_COMPLETE = "synthesis_complete"
    SYNTHESIS_ERROR = "synthesis_error"
    
    # LLM events
    LLM_PROCESSING_START = "llm_processing_start"
    LLM_PROCESSING_COMPLETE = "llm_processing_complete"
    LLM_ERROR = "llm_error"
    
    # Coordination events
    COMPONENT_STATE_CHANGE = "component_state_change"
    RESOURCE_REQUEST = "resource_request"
    RESOURCE_RELEASE = "resource_release"
    CONFLICT_DETECTED = "conflict_detected"
    CONFLICT_RESOLVED = "conflict_resolved"
    
    # System events
    SYSTEM_PAUSE = "system_pause"
    SYSTEM_RESUME = "system_resume"
    EMERGENCY_STOP = "emergency_stop"


class Priority(Enum):
    """Event priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


@dataclass
class CoordinationEvent:
    """Event for component coordination."""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.AUDIO_DATA
    source_component: str = ""
    target_component: Optional[str] = None  # None means broadcast
    priority: Priority = Priority.NORMAL
    timestamp: datetime = field(default_factory=datetime.now)
    data: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.value,
            "source_component": self.source_component,
            "target_component": self.target_component,
            "priority": self.priority.value,
            "timestamp": self.timestamp.isoformat(),
            "data": self.data,
            "correlation_id": self.correlation_id,
            "session_id": self.session_id
        }


@dataclass
class ComponentInfo:
    """Information about a registered component."""
    component_name: str
    component_type: str
    state: ComponentState = ComponentState.IDLE
    capabilities: Set[str] = field(default_factory=set)
    dependencies: Set[str] = field(default_factory=set)
    conflicts_with: Set[str] = field(default_factory=set)
    last_activity: datetime = field(default_factory=datetime.now)
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "component_name": self.component_name,
            "component_type": self.component_type,
            "state": self.state.value,
            "capabilities": list(self.capabilities),
            "dependencies": list(self.dependencies),
            "conflicts_with": list(self.conflicts_with),
            "last_activity": self.last_activity.isoformat(),
            "resource_usage": self.resource_usage
        }


@dataclass
class ResourceRequest:
    """Resource request from a component."""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    component_name: str = ""
    resource_type: str = ""
    resource_amount: float = 1.0
    priority: Priority = Priority.NORMAL
    timeout_seconds: float = 30.0
    timestamp: datetime = field(default_factory=datetime.now)
    granted: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "request_id": self.request_id,
            "component_name": self.component_name,
            "resource_type": self.resource_type,
            "resource_amount": self.resource_amount,
            "priority": self.priority.value,
            "timeout_seconds": self.timeout_seconds,
            "timestamp": self.timestamp.isoformat(),
            "granted": self.granted
        }


class EventHandler(ABC):
    """Abstract base class for event handlers."""
    
    @abstractmethod
    async def handle_event(self, event: CoordinationEvent) -> bool:
        """Handle a coordination event. Return True if handled successfully."""
        pass
    
    @abstractmethod
    def get_handled_events(self) -> Set[EventType]:
        """Get the set of event types this handler can process."""
        pass
    
    @abstractmethod
    def get_component_name(self) -> str:
        """Get the name of the component this handler represents."""
        pass


class ComponentCoordinator(BaseComponent):
    """
    Real-time component coordination system.
    
    Manages event-driven communication between audio processing components,
    handles resource allocation, and resolves conflicts.
    """
    
    def __init__(self, config_manager, logger=None):
        super().__init__("component_coordinator", config_manager, logger)
        
        # Configuration
        self.max_queue_size = self.config_manager.get_config("coordination.max_queue_size", 1000)
        self.event_timeout_seconds = self.config_manager.get_config("coordination.event_timeout_seconds", 30.0)
        self.conflict_resolution_timeout = self.config_manager.get_config("coordination.conflict_resolution_timeout", 10.0)
        
        # Event management
        self.event_queue: asyncio.PriorityQueue = asyncio.PriorityQueue(maxsize=self.max_queue_size)
        self.event_handlers: Dict[str, EventHandler] = {}
        self.event_subscriptions: Dict[EventType, Set[str]] = defaultdict(set)
        
        # Component management
        self.components: Dict[str, ComponentInfo] = {}
        self.component_states: Dict[str, ComponentState] = {}
        
        # Resource management
        self.resource_pools: Dict[str, float] = {
            "audio_input": 1.0,
            "audio_output": 1.0,
            "cpu_intensive": 4.0,
            "memory_intensive": 2.0,
            "network": 10.0
        }
        self.resource_allocations: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.pending_requests: Dict[str, ResourceRequest] = {}
        
        # Conflict resolution
        self.active_conflicts: Dict[str, List[str]] = {}
        self.conflict_resolution_strategies: Dict[str, Callable] = {}
        
        # Event history and metrics
        self.event_history: deque = deque(maxlen=1000)
        self.event_metrics: Dict[str, int] = defaultdict(int)
        
        # Background tasks
        self._event_processor_task: Optional[asyncio.Task] = None
        self._resource_manager_task: Optional[asyncio.Task] = None
        self._conflict_resolver_task: Optional[asyncio.Task] = None
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Initialize default conflict resolution strategies
        self._setup_default_conflict_strategies()

    async def _initialize_impl(self) -> None:
        """Initialize the component coordinator."""
        self._log.info("Component Coordinator initialized")
        self._log.info(f"Max queue size: {self.max_queue_size}")
        self._log.info(f"Event timeout: {self.event_timeout_seconds}s")

    async def _start_impl(self) -> None:
        """Start the component coordination system."""
        self._log.info("Starting Component Coordinator...")

        # Start background tasks
        self._event_processor_task = asyncio.create_task(self._event_processor_loop())
        self._resource_manager_task = asyncio.create_task(self._resource_manager_loop())
        self._conflict_resolver_task = asyncio.create_task(self._conflict_resolver_loop())

        self._log.info("Component Coordinator started")

    async def _stop_impl(self) -> None:
        """Stop the component coordination system."""
        self._log.info("Stopping Component Coordinator...")

        # Cancel background tasks
        tasks = [self._event_processor_task, self._resource_manager_task, self._conflict_resolver_task]
        for task in tasks:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self._log.info("Component Coordinator stopped")

    async def _cleanup_impl(self) -> None:
        """Clean up the component coordinator."""
        with self._lock:
            self.event_handlers.clear()
            self.event_subscriptions.clear()
            self.components.clear()
            self.component_states.clear()
            self.resource_allocations.clear()
            self.pending_requests.clear()
            self.active_conflicts.clear()

        self._log.info("Component Coordinator cleanup completed")

    # Component Registration Methods

    def register_component(
        self,
        component_name: str,
        component_type: str,
        capabilities: Set[str] = None,
        dependencies: Set[str] = None,
        conflicts_with: Set[str] = None
    ) -> None:
        """Register a component for coordination."""
        with self._lock:
            component_info = ComponentInfo(
                component_name=component_name,
                component_type=component_type,
                capabilities=capabilities or set(),
                dependencies=dependencies or set(),
                conflicts_with=conflicts_with or set()
            )

            self.components[component_name] = component_info
            self.component_states[component_name] = ComponentState.IDLE

        self._log.info(f"Registered component: {component_name} ({component_type})")

    def register_event_handler(self, handler: EventHandler) -> None:
        """Register an event handler."""
        component_name = handler.get_component_name()

        with self._lock:
            self.event_handlers[component_name] = handler

            # Subscribe to handled events
            for event_type in handler.get_handled_events():
                self.event_subscriptions[event_type].add(component_name)

        self._log.info(f"Registered event handler for component: {component_name}")

    def unregister_component(self, component_name: str) -> None:
        """Unregister a component."""
        with self._lock:
            # Remove from components
            self.components.pop(component_name, None)
            self.component_states.pop(component_name, None)

            # Remove event handler
            self.event_handlers.pop(component_name, None)

            # Remove from subscriptions
            for event_type, subscribers in self.event_subscriptions.items():
                subscribers.discard(component_name)

            # Release resources
            if component_name in self.resource_allocations:
                for resource_type, amount in self.resource_allocations[component_name].items():
                    self.resource_pools[resource_type] += amount
                del self.resource_allocations[component_name]

        self._log.info(f"Unregistered component: {component_name}")

    # Event Management Methods

    async def publish_event(self, event: CoordinationEvent) -> bool:
        """Publish an event to the coordination system."""
        try:
            # Add to queue with priority
            priority_value = 6 - event.priority.value  # Lower number = higher priority
            await self.event_queue.put((priority_value, time.time(), event))

            # Update metrics
            self.event_metrics[event.event_type.value] += 1
            self.event_metrics["total_events"] += 1

            return True

        except asyncio.QueueFull:
            self._log.error(f"Event queue full, dropping event: {event.event_type.value}")
            return False
        except Exception as e:
            self._log.error(f"Error publishing event: {e}")
            return False

    async def publish_event_simple(
        self,
        event_type: EventType,
        source_component: str,
        data: Dict[str, Any] = None,
        target_component: str = None,
        priority: Priority = Priority.NORMAL,
        session_id: str = None
    ) -> bool:
        """Publish an event with simplified parameters."""
        event = CoordinationEvent(
            event_type=event_type,
            source_component=source_component,
            target_component=target_component,
            priority=priority,
            data=data or {},
            session_id=session_id
        )

        return await self.publish_event(event)

    def subscribe_to_events(self, component_name: str, event_types: Set[EventType]) -> None:
        """Subscribe a component to specific event types."""
        with self._lock:
            for event_type in event_types:
                self.event_subscriptions[event_type].add(component_name)

        self._log.info(f"Component {component_name} subscribed to {len(event_types)} event types")

    def unsubscribe_from_events(self, component_name: str, event_types: Set[EventType]) -> None:
        """Unsubscribe a component from specific event types."""
        with self._lock:
            for event_type in event_types:
                self.event_subscriptions[event_type].discard(component_name)

        self._log.info(f"Component {component_name} unsubscribed from {len(event_types)} event types")

    # State Management Methods

    def update_component_state(self, component_name: str, new_state: ComponentState) -> bool:
        """Update the state of a component."""
        if component_name not in self.components:
            self._log.warning(f"Cannot update state for unknown component: {component_name}")
            return False

        with self._lock:
            old_state = self.component_states.get(component_name, ComponentState.IDLE)
            self.component_states[component_name] = new_state
            self.components[component_name].state = new_state
            self.components[component_name].last_activity = datetime.now()

        # Publish state change event (only if event loop is running)
        try:
            loop = asyncio.get_running_loop()
            asyncio.create_task(self.publish_event_simple(
                event_type=EventType.COMPONENT_STATE_CHANGE,
                source_component=component_name,
                data={
                    "old_state": old_state.value,
                    "new_state": new_state.value,
                    "timestamp": datetime.now().isoformat()
                }
            ))
        except RuntimeError:
            # No event loop running, skip event publishing
            pass

        self._log.debug(f"Component {component_name} state changed: {old_state.value} -> {new_state.value}")
        return True

    def get_component_state(self, component_name: str) -> Optional[ComponentState]:
        """Get the current state of a component."""
        return self.component_states.get(component_name)

    def get_all_component_states(self) -> Dict[str, ComponentState]:
        """Get the states of all registered components."""
        with self._lock:
            return dict(self.component_states)

    def get_components_by_state(self, state: ComponentState) -> List[str]:
        """Get all components in a specific state."""
        with self._lock:
            return [name for name, comp_state in self.component_states.items() if comp_state == state]

    def get_component_info(self, component_name: str) -> Optional[ComponentInfo]:
        """Get detailed information about a component."""
        return self.components.get(component_name)

    # Resource Management Methods

    async def request_resource(
        self,
        component_name: str,
        resource_type: str,
        amount: float = 1.0,
        priority: Priority = Priority.NORMAL,
        timeout_seconds: float = None
    ) -> bool:
        """Request a resource allocation for a component."""
        if timeout_seconds is None:
            timeout_seconds = self.event_timeout_seconds

        request = ResourceRequest(
            component_name=component_name,
            resource_type=resource_type,
            resource_amount=amount,
            priority=priority,
            timeout_seconds=timeout_seconds
        )

        # Check if resource is available
        with self._lock:
            available = self.resource_pools.get(resource_type, 0.0)
            if available >= amount:
                # Grant immediately
                self.resource_pools[resource_type] -= amount
                if component_name not in self.resource_allocations:
                    self.resource_allocations[component_name] = {}
                self.resource_allocations[component_name][resource_type] = amount
                request.granted = True

                self._log.debug(f"Resource granted immediately: {component_name} -> {resource_type}({amount})")
                return True
            else:
                # Queue the request
                self.pending_requests[request.request_id] = request

                # Publish resource request event
                await self.publish_event_simple(
                    event_type=EventType.RESOURCE_REQUEST,
                    source_component=component_name,
                    data=request.to_dict()
                )

                self._log.debug(f"Resource request queued: {component_name} -> {resource_type}({amount})")
                return False

    def release_resource(self, component_name: str, resource_type: str, amount: float = None) -> bool:
        """Release a resource allocation."""
        with self._lock:
            if component_name not in self.resource_allocations:
                return False

            if resource_type not in self.resource_allocations[component_name]:
                return False

            allocated_amount = self.resource_allocations[component_name][resource_type]
            release_amount = amount if amount is not None else allocated_amount

            # Release the resource
            self.resource_pools[resource_type] += release_amount
            self.resource_allocations[component_name][resource_type] -= release_amount

            if self.resource_allocations[component_name][resource_type] <= 0:
                del self.resource_allocations[component_name][resource_type]

            if not self.resource_allocations[component_name]:
                del self.resource_allocations[component_name]

        # Publish resource release event (only if event loop is running)
        try:
            loop = asyncio.get_running_loop()
            asyncio.create_task(self.publish_event_simple(
                event_type=EventType.RESOURCE_RELEASE,
                source_component=component_name,
                data={
                    "resource_type": resource_type,
                    "amount": release_amount
                }
            ))
        except RuntimeError:
            # No event loop running, skip event publishing
            pass

        self._log.debug(f"Resource released: {component_name} -> {resource_type}({release_amount})")
        return True

    def get_resource_usage(self) -> Dict[str, Any]:
        """Get current resource usage statistics."""
        with self._lock:
            usage = {
                "resource_pools": dict(self.resource_pools),
                "allocations": dict(self.resource_allocations),
                "pending_requests": len(self.pending_requests),
                "utilization": {}
            }

            # Calculate utilization percentages
            for resource_type, total in self.resource_pools.items():
                allocated = sum(
                    allocations.get(resource_type, 0)
                    for allocations in self.resource_allocations.values()
                )
                original_total = total + allocated
                utilization = (allocated / original_total * 100) if original_total > 0 else 0
                usage["utilization"][resource_type] = utilization

        return usage

    # Conflict Detection and Resolution Methods

    def detect_conflicts(self, component_name: str, proposed_action: str) -> List[str]:
        """Detect potential conflicts with other components."""
        conflicts = []

        if component_name not in self.components:
            return conflicts

        component_info = self.components[component_name]

        with self._lock:
            # Check for direct conflicts
            for conflict_component in component_info.conflicts_with:
                if conflict_component in self.component_states:
                    conflict_state = self.component_states[conflict_component]
                    if conflict_state in [ComponentState.ACTIVE, ComponentState.BUSY]:
                        conflicts.append(conflict_component)

            # Check for resource conflicts
            if proposed_action in ["audio_input", "audio_output"]:
                for other_component, allocations in self.resource_allocations.items():
                    if other_component != component_name and proposed_action in allocations:
                        conflicts.append(other_component)

        return conflicts

    async def resolve_conflict(self, conflict_id: str, conflicting_components: List[str]) -> bool:
        """Resolve a conflict between components."""
        if conflict_id in self.active_conflicts:
            self._log.warning(f"Conflict {conflict_id} is already being resolved")
            return False

        self.active_conflicts[conflict_id] = conflicting_components

        try:
            # Determine conflict type and apply appropriate strategy
            conflict_type = self._determine_conflict_type(conflicting_components)
            strategy = self.conflict_resolution_strategies.get(conflict_type)

            if strategy:
                success = await strategy(conflicting_components)

                if success:
                    await self.publish_event_simple(
                        event_type=EventType.CONFLICT_RESOLVED,
                        source_component="coordinator",
                        data={
                            "conflict_id": conflict_id,
                            "conflicting_components": conflicting_components,
                            "resolution_strategy": conflict_type
                        }
                    )
                    self._log.info(f"Conflict resolved: {conflict_id}")
                else:
                    self._log.error(f"Failed to resolve conflict: {conflict_id}")

                return success
            else:
                self._log.error(f"No resolution strategy for conflict type: {conflict_type}")
                return False

        finally:
            self.active_conflicts.pop(conflict_id, None)

    def _determine_conflict_type(self, conflicting_components: List[str]) -> str:
        """Determine the type of conflict based on components involved."""
        component_types = set()

        for component_name in conflicting_components:
            if component_name in self.components:
                component_types.add(self.components[component_name].component_type)

        # Audio input/output conflicts
        if "vad" in component_types and "asr" in component_types:
            return "audio_input_conflict"
        elif "tts" in component_types and "asr" in component_types:
            return "audio_duplex_conflict"
        elif len([t for t in component_types if t in ["tts", "audio_output"]]) > 1:
            return "audio_output_conflict"
        else:
            return "general_conflict"

    def _setup_default_conflict_strategies(self) -> None:
        """Set up default conflict resolution strategies."""
        self.conflict_resolution_strategies = {
            "audio_input_conflict": self._resolve_audio_input_conflict,
            "audio_output_conflict": self._resolve_audio_output_conflict,
            "audio_duplex_conflict": self._resolve_audio_duplex_conflict,
            "general_conflict": self._resolve_general_conflict
        }

    async def _resolve_audio_input_conflict(self, conflicting_components: List[str]) -> bool:
        """Resolve conflicts over audio input resources."""
        # Priority: VAD > ASR > others
        priority_order = ["vad", "asr"]

        # Find highest priority component
        winner = None
        for component_type in priority_order:
            for component_name in conflicting_components:
                if component_name in self.components:
                    if self.components[component_name].component_type == component_type:
                        winner = component_name
                        break
            if winner:
                break

        if not winner:
            winner = conflicting_components[0]  # Fallback to first component

        # Suspend other components
        for component_name in conflicting_components:
            if component_name != winner:
                self.update_component_state(component_name, ComponentState.SUSPENDED)

        # Ensure winner is active
        self.update_component_state(winner, ComponentState.ACTIVE)

        self._log.info(f"Audio input conflict resolved: {winner} wins")
        return True

    async def _resolve_audio_output_conflict(self, conflicting_components: List[str]) -> bool:
        """Resolve conflicts over audio output resources."""
        # TTS has priority over other audio output
        tts_components = [
            name for name in conflicting_components
            if name in self.components and self.components[name].component_type == "tts"
        ]

        if tts_components:
            winner = tts_components[0]
        else:
            winner = conflicting_components[0]

        # Suspend other components
        for component_name in conflicting_components:
            if component_name != winner:
                self.update_component_state(component_name, ComponentState.WAITING)

        self.update_component_state(winner, ComponentState.ACTIVE)

        self._log.info(f"Audio output conflict resolved: {winner} wins")
        return True

    async def _resolve_audio_duplex_conflict(self, conflicting_components: List[str]) -> bool:
        """Resolve conflicts between input and output audio operations."""
        # In duplex conflicts, output (TTS) typically takes priority
        # to avoid interrupting user experience

        tts_components = []
        input_components = []

        for component_name in conflicting_components:
            if component_name in self.components:
                comp_type = self.components[component_name].component_type
                if comp_type == "tts":
                    tts_components.append(component_name)
                elif comp_type in ["vad", "asr"]:
                    input_components.append(component_name)

        # Pause input components while TTS is active
        for component_name in input_components:
            self.update_component_state(component_name, ComponentState.WAITING)

        # Ensure TTS components are active
        for component_name in tts_components:
            self.update_component_state(component_name, ComponentState.ACTIVE)

        self._log.info(f"Audio duplex conflict resolved: TTS priority")
        return True

    async def _resolve_general_conflict(self, conflicting_components: List[str]) -> bool:
        """Resolve general conflicts using priority-based approach."""
        # Sort by component priority (based on type and current state)
        def get_priority(component_name: str) -> int:
            if component_name not in self.components:
                return 0

            comp_type = self.components[component_name].component_type
            priority_map = {
                "emergency": 100,
                "tts": 80,
                "vad": 70,
                "asr": 60,
                "llm": 50,
                "audio": 40
            }
            return priority_map.get(comp_type, 30)

        sorted_components = sorted(conflicting_components, key=get_priority, reverse=True)
        winner = sorted_components[0]

        # Suspend lower priority components
        for component_name in sorted_components[1:]:
            self.update_component_state(component_name, ComponentState.WAITING)

        self.update_component_state(winner, ComponentState.ACTIVE)

        self._log.info(f"General conflict resolved: {winner} wins (priority-based)")
        return True

    # Background Tasks

    async def _event_processor_loop(self) -> None:
        """Main event processing loop."""
        while True:
            try:
                # Get event from queue with timeout
                try:
                    priority, timestamp, event = await asyncio.wait_for(
                        self.event_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # Process the event
                await self._process_event(event)

                # Mark task as done
                self.event_queue.task_done()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in event processor loop: {e}")

    async def _process_event(self, event: CoordinationEvent) -> None:
        """Process a single coordination event."""
        try:
            # Add to history
            self.event_history.append(event)

            # Determine target components
            if event.target_component:
                # Targeted event
                target_components = [event.target_component]
            else:
                # Broadcast event - find subscribers
                target_components = list(self.event_subscriptions.get(event.event_type, set()))

            # Deliver event to handlers
            for component_name in target_components:
                if component_name in self.event_handlers:
                    handler = self.event_handlers[component_name]
                    try:
                        success = await asyncio.wait_for(
                            handler.handle_event(event),
                            timeout=self.event_timeout_seconds
                        )

                        if not success:
                            self._log.warning(f"Event handler {component_name} failed to process {event.event_type.value}")

                    except asyncio.TimeoutError:
                        self._log.error(f"Event handler {component_name} timed out processing {event.event_type.value}")
                    except Exception as e:
                        self._log.error(f"Error in event handler {component_name}: {e}")

            # Handle special coordination events
            await self._handle_coordination_event(event)

        except Exception as e:
            self._log.error(f"Error processing event {event.event_type.value}: {e}")

    async def _handle_coordination_event(self, event: CoordinationEvent) -> None:
        """Handle special coordination events."""
        if event.event_type == EventType.COMPONENT_STATE_CHANGE:
            # Check for conflicts when component becomes active
            if event.data.get("new_state") == ComponentState.ACTIVE.value:
                conflicts = self.detect_conflicts(event.source_component, "activate")
                if conflicts:
                    conflict_id = str(uuid.uuid4())
                    await self.publish_event_simple(
                        event_type=EventType.CONFLICT_DETECTED,
                        source_component="coordinator",
                        data={
                            "conflict_id": conflict_id,
                            "conflicting_components": [event.source_component] + conflicts
                        }
                    )

        elif event.event_type == EventType.CONFLICT_DETECTED:
            # Automatically resolve conflicts
            conflict_id = event.data.get("conflict_id")
            conflicting_components = event.data.get("conflicting_components", [])
            if conflict_id and conflicting_components:
                await self.resolve_conflict(conflict_id, conflicting_components)

    async def _resource_manager_loop(self) -> None:
        """Resource management loop."""
        while True:
            try:
                await asyncio.sleep(5)  # Check every 5 seconds

                # Process pending resource requests
                await self._process_pending_requests()

                # Clean up expired requests
                self._cleanup_expired_requests()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in resource manager loop: {e}")

    async def _process_pending_requests(self) -> None:
        """Process pending resource requests."""
        granted_requests = []

        with self._lock:
            # Sort requests by priority and timestamp
            sorted_requests = sorted(
                self.pending_requests.values(),
                key=lambda r: (r.priority.value, r.timestamp),
                reverse=True
            )

            for request in sorted_requests:
                available = self.resource_pools.get(request.resource_type, 0.0)

                if available >= request.resource_amount:
                    # Grant the request
                    self.resource_pools[request.resource_type] -= request.resource_amount

                    if request.component_name not in self.resource_allocations:
                        self.resource_allocations[request.component_name] = {}

                    self.resource_allocations[request.component_name][request.resource_type] = request.resource_amount
                    request.granted = True
                    granted_requests.append(request)

        # Remove granted requests and notify components
        for request in granted_requests:
            self.pending_requests.pop(request.request_id, None)

            await self.publish_event_simple(
                event_type=EventType.RESOURCE_REQUEST,
                source_component="coordinator",
                target_component=request.component_name,
                data={
                    "request_id": request.request_id,
                    "granted": True,
                    "resource_type": request.resource_type,
                    "amount": request.resource_amount
                }
            )

            self._log.debug(f"Resource request granted: {request.component_name} -> {request.resource_type}({request.resource_amount})")

    def _cleanup_expired_requests(self) -> None:
        """Clean up expired resource requests."""
        current_time = datetime.now()
        expired_requests = []

        with self._lock:
            for request_id, request in self.pending_requests.items():
                if (current_time - request.timestamp).total_seconds() > request.timeout_seconds:
                    expired_requests.append(request_id)

        for request_id in expired_requests:
            request = self.pending_requests.pop(request_id, None)
            if request:
                self._log.warning(f"Resource request expired: {request.component_name} -> {request.resource_type}")

    async def _conflict_resolver_loop(self) -> None:
        """Conflict resolution monitoring loop."""
        while True:
            try:
                await asyncio.sleep(2)  # Check every 2 seconds

                # Monitor for long-running conflicts
                current_time = datetime.now()

                for conflict_id, components in list(self.active_conflicts.items()):
                    # If conflict has been active too long, escalate
                    # This is a simplified check - in practice you'd track conflict start time
                    if len(components) > 2:  # Complex conflicts need attention
                        self._log.warning(f"Complex conflict detected: {conflict_id} with {len(components)} components")

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in conflict resolver loop: {e}")

    # Statistics and Monitoring Methods

    def get_coordination_statistics(self) -> Dict[str, Any]:
        """Get comprehensive coordination statistics."""
        with self._lock:
            stats = {
                "timestamp": datetime.now().isoformat(),
                "registered_components": len(self.components),
                "active_components": len(self.get_components_by_state(ComponentState.ACTIVE)),
                "busy_components": len(self.get_components_by_state(ComponentState.BUSY)),
                "suspended_components": len(self.get_components_by_state(ComponentState.SUSPENDED)),
                "event_queue_size": self.event_queue.qsize(),
                "total_events_processed": self.event_metrics.get("total_events", 0),
                "event_types": dict(self.event_metrics),
                "resource_usage": self.get_resource_usage(),
                "active_conflicts": len(self.active_conflicts),
                "pending_resource_requests": len(self.pending_requests),
                "component_states": {name: state.value for name, state in self.component_states.items()}
            }

        return stats

    def get_component_coordination_info(self, component_name: str) -> Dict[str, Any]:
        """Get coordination information for a specific component."""
        if component_name not in self.components:
            return {"error": f"Component {component_name} not found"}

        with self._lock:
            component_info = self.components[component_name]

            info = {
                "component_info": component_info.to_dict(),
                "current_state": self.component_states.get(component_name, ComponentState.IDLE).value,
                "resource_allocations": self.resource_allocations.get(component_name, {}),
                "subscribed_events": [
                    event_type.value for event_type, subscribers in self.event_subscriptions.items()
                    if component_name in subscribers
                ],
                "conflicts_with": list(component_info.conflicts_with),
                "dependencies": list(component_info.dependencies)
            }

        return info
