#!/usr/bin/env python3
"""
AI Voice Customer Service System - Monitoring Dashboard
======================================================

This file creates a simple monitoring dashboard that continuously
monitors the AI Voice Customer Service System.

Prerequisites:
- System running on http://localhost:8000
- Python with requests library installed
"""

import requests
import json
import time
import os
from datetime import datetime
from typing import Dict, Any

# Base URL for the API
BASE_URL = "http://localhost:8000"

class MonitoringDashboard:
    """Simple monitoring dashboard for the AI Voice System."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.start_time = time.time()
    
    def clear_screen(self):
        """Clear the terminal screen."""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status."""
        response = self.session.get(f"{self.base_url}/status")
        response.raise_for_status()
        return response.json()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status."""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def format_uptime(self, seconds: float) -> str:
        """Format uptime in a readable format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def display_dashboard(self):
        """Display the monitoring dashboard."""
        try:
            # Get current status
            status = self.get_system_status()
            health = self.get_health_status()
            
            # Clear screen and display header
            self.clear_screen()
            print("🤖 AI Voice Customer Service System - Live Monitoring Dashboard")
            print("=" * 70)
            print(f"📅 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  Dashboard Uptime: {self.format_uptime(time.time() - self.start_time)}")
            print()
            
            # System Overview
            sys_status = status['system_status']
            print("📊 SYSTEM OVERVIEW")
            print("-" * 30)
            print(f"🟢 Status: {health['status'].upper()}")
            print(f"⚡ State: {sys_status['state'].upper()}")
            print(f"⏰ System Uptime: {self.format_uptime(sys_status['uptime'])}")
            print(f"📞 Active Calls: {health['active_calls']}")
            print(f"📈 Total Calls Handled: {sys_status['total_calls_handled']}")
            print()
            
            # Component Status
            print("🔧 COMPONENT STATUS")
            print("-" * 30)
            print(f"✅ Components Initialized: {sys_status['components_initialized']}")
            print(f"🏃 Components Running: {sys_status['components_running']}")
            print(f"❌ Components with Errors: {sys_status['components_error']}")
            print()
            
            # Performance Metrics
            print("📈 PERFORMANCE METRICS")
            print("-" * 30)
            perf_metrics = status.get('performance_metrics', {})
            if perf_metrics:
                print(f"💾 Memory Usage: {perf_metrics.get('memory_usage', 'N/A')}")
                print(f"🖥️  CPU Usage: {perf_metrics.get('cpu_usage', 'N/A')}")
                print(f"💽 Disk Usage: {perf_metrics.get('disk_usage', 'N/A')}")
            else:
                print("   📊 Performance metrics collecting...")
            print()
            
            # Audio Pipeline Status
            print("🎵 AUDIO PIPELINE")
            print("-" * 30)
            print("   ✅ Audio processing active")
            print("   🔄 Real-time audio buffer management")
            print("   🎯 Ready for ASR/TTS integration")
            print()
            
            # Call Management
            print("📞 CALL MANAGEMENT")
            print("-" * 30)
            print("   ✅ Call manager active")
            print("   📋 Telephony interface ready")
            print("   🗣️  Conversation logging enabled")
            print()
            
            # System Health
            print("🏥 SYSTEM HEALTH")
            print("-" * 30)
            if health['status'] == 'healthy':
                print("   🟢 All systems operational")
                print("   ✅ No critical issues detected")
                print("   📊 Monitoring active")
            else:
                print("   🟡 System issues detected")
                print("   ⚠️  Check logs for details")
            print()
            
            # Footer
            print("=" * 70)
            print("Press Ctrl+C to exit | Refreshing every 5 seconds...")
            
        except requests.exceptions.ConnectionError:
            self.clear_screen()
            print("❌ CONNECTION ERROR")
            print("=" * 30)
            print("Cannot connect to AI Voice System")
            print("Make sure the system is running on http://localhost:8000")
            print()
            print("Press Ctrl+C to exit...")
            
        except Exception as e:
            print(f"❌ Error updating dashboard: {e}")
    
    def run(self, refresh_interval: int = 5):
        """Run the monitoring dashboard with auto-refresh."""
        print("🚀 Starting AI Voice System Monitoring Dashboard...")
        print("Press Ctrl+C to exit")
        time.sleep(2)
        
        try:
            while True:
                self.display_dashboard()
                time.sleep(refresh_interval)
        except KeyboardInterrupt:
            self.clear_screen()
            print("👋 Monitoring dashboard stopped.")
            print("Thank you for using AI Voice Customer Service System!")

def main():
    """Run the monitoring dashboard."""
    dashboard = MonitoringDashboard()
    dashboard.run()

if __name__ == "__main__":
    main()
