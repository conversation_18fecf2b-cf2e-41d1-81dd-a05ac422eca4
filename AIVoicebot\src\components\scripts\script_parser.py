"""
Excel script parser for conversation scripts.

This module provides parsing capabilities for Excel-based conversation scripts,
converting them to internal data structures with validation and error checking.
"""

import pandas as pd
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import os
import re

from ...core.interfaces import ScriptResponse, ConversationError
from ...core.base_component import BaseComponent


@dataclass
class ScriptParserConfig:
    """Configuration for script parser."""
    # File settings
    script_directory: str = "docs"
    supported_formats: List[str] = field(default_factory=lambda: [".xlsx", ".xls"])
    encoding: str = "utf-8"
    
    # Column mapping (可配置的列名映射)
    query_column: str = "用户问题"
    response_column: str = "回复内容"
    intent_column: str = "意图分类"
    priority_column: str = "优先级"
    conditions_column: str = "触发条件"
    actions_column: str = "后续动作"
    
    # Validation settings
    enable_validation: bool = True
    require_intent: bool = False
    min_response_length: int = 1
    max_response_length: int = 1000
    
    # Processing settings
    normalize_text: bool = True
    remove_duplicates: bool = True
    case_sensitive: bool = False


@dataclass
class ConversationScript:
    """Represents a conversation script entry."""
    script_id: str
    query: str
    response: str
    intent: Optional[str] = None
    priority: int = 1
    conditions: List[str] = field(default_factory=list)
    actions: List[str] = field(default_factory=list)
    
    # Metadata
    source_file: str = ""
    row_number: int = 0
    created_time: datetime = field(default_factory=datetime.now)
    
    def to_script_response(self) -> ScriptResponse:
        """Convert to ScriptResponse format."""
        return ScriptResponse(
            response_id=self.script_id,
            text=self.response,
            conditions=self.conditions,
            follow_up_actions=self.actions,
            priority=self.priority,
            metadata={
                "intent": self.intent,
                "query": self.query,
                "source_file": self.source_file,
                "row_number": self.row_number
            }
        )


class ScriptParser(BaseComponent):
    """
    Excel script parser for conversation scripts.
    
    Parses Excel files containing conversation scripts and converts them
    to internal data structures with validation and error checking.
    """
    
    def __init__(self, config: ScriptParserConfig, config_manager, logger=None):
        """
        Initialize script parser.
        
        Args:
            config: Parser configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("script_parser", config_manager, logger)
        
        self.config = config
        self.parsed_scripts: Dict[str, ConversationScript] = {}
        self.parsing_errors: List[Dict[str, Any]] = []
        
        # Statistics
        self._total_files_processed = 0
        self._total_scripts_parsed = 0
        self._total_errors = 0   
 
    async def _initialize_impl(self) -> None:
        """Initialize script parser."""
        self._log.info("Initializing script parser...")
        
        # Validate script directory
        if not os.path.exists(self.config.script_directory):
            raise ConversationError(f"Script directory not found: {self.config.script_directory}")
        
        # Reset statistics
        self._total_files_processed = 0
        self._total_scripts_parsed = 0
        self._total_errors = 0
        self.parsing_errors.clear()
        
        self._log.info("Script parser initialized")
    
    async def _start_impl(self) -> None:
        """Start script parser."""
        self._log.info("Starting script parser...")
        self._log.info("Script parser started")
    
    async def _stop_impl(self) -> None:
        """Stop script parser."""
        self._log.info("Stopping script parser...")
        
        # Log final statistics
        self._log.info(f"Parser Statistics:")
        self._log.info(f"  Files processed: {self._total_files_processed}")
        self._log.info(f"  Scripts parsed: {self._total_scripts_parsed}")
        self._log.info(f"  Errors encountered: {self._total_errors}")
        
        self._log.info("Script parser stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup script parser resources."""
        self._log.info("Cleaning up script parser...")
        
        # Clear parsed scripts
        self.parsed_scripts.clear()
        self.parsing_errors.clear()
        
        self._log.info("Script parser cleanup completed")
    
    def parse_excel_file(self, file_path: str) -> List[ConversationScript]:
        """
        Parse Excel file and extract conversation scripts.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            List of parsed conversation scripts
        """
        scripts = []
        
        try:
            self._log.info(f"Parsing Excel file: {file_path}")
            
            # Read Excel file
            df = pd.read_excel(file_path)
            
            # Validate required columns
            self._validate_excel_structure(df, file_path)
            
            # Process each row
            for index, row in df.iterrows():
                try:
                    script = self._parse_script_row(row, file_path, index + 2)  # +2 for header and 0-based index
                    if script:
                        scripts.append(script)
                        self.parsed_scripts[script.script_id] = script
                
                except Exception as e:
                    error = {
                        "file": file_path,
                        "row": index + 2,
                        "error": str(e),
                        "timestamp": datetime.now()
                    }
                    self.parsing_errors.append(error)
                    self._total_errors += 1
                    self._log.warning(f"Error parsing row {index + 2} in {file_path}: {e}")
            
            self._total_files_processed += 1
            self._total_scripts_parsed += len(scripts)
            
            self._log.info(f"Successfully parsed {len(scripts)} scripts from {file_path}")
            
        except Exception as e:
            error = {
                "file": file_path,
                "row": None,
                "error": str(e),
                "timestamp": datetime.now()
            }
            self.parsing_errors.append(error)
            self._total_errors += 1
            self._log.error(f"Error parsing Excel file {file_path}: {e}")
            raise ConversationError(f"Failed to parse Excel file: {e}")
        
        return scripts
    
    def _validate_excel_structure(self, df: pd.DataFrame, file_path: str) -> None:
        """Validate Excel file structure."""
        required_columns = [self.config.query_column, self.config.response_column]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ConversationError(
                f"Missing required columns in {file_path}: {missing_columns}. "
                f"Available columns: {list(df.columns)}"
            )
        
        if df.empty:
            raise ConversationError(f"Excel file {file_path} is empty")
    
    def _parse_script_row(self, row: pd.Series, file_path: str, row_number: int) -> Optional[ConversationScript]:
        """Parse individual script row."""
        # Extract basic fields
        query = self._extract_text_field(row, self.config.query_column)
        response = self._extract_text_field(row, self.config.response_column)
        
        # Skip empty rows
        if not query or not response:
            return None
        
        # Validate content
        if self.config.enable_validation:
            self._validate_script_content(query, response, file_path, row_number)
        
        # Extract optional fields
        intent = self._extract_text_field(row, self.config.intent_column)
        priority = self._extract_numeric_field(row, self.config.priority_column, default=1)
        conditions = self._extract_list_field(row, self.config.conditions_column)
        actions = self._extract_list_field(row, self.config.actions_column)
        
        # Normalize text if enabled
        if self.config.normalize_text:
            query = self._normalize_text(query)
            response = self._normalize_text(response)
        
        # Generate script ID
        script_id = self._generate_script_id(file_path, row_number, query)
        
        return ConversationScript(
            script_id=script_id,
            query=query,
            response=response,
            intent=intent,
            priority=priority,
            conditions=conditions,
            actions=actions,
            source_file=file_path,
            row_number=row_number
        )
    
    def _extract_text_field(self, row: pd.Series, column_name: str) -> str:
        """Extract text field from row."""
        if column_name not in row or pd.isna(row[column_name]):
            return ""
        
        value = str(row[column_name]).strip()
        return value
    
    def _extract_numeric_field(self, row: pd.Series, column_name: str, default: int = 0) -> int:
        """Extract numeric field from row."""
        if column_name not in row or pd.isna(row[column_name]):
            return default
        
        try:
            return int(float(row[column_name]))
        except (ValueError, TypeError):
            return default
    
    def _extract_list_field(self, row: pd.Series, column_name: str) -> List[str]:
        """Extract list field from row (comma-separated values)."""
        if column_name not in row or pd.isna(row[column_name]):
            return []
        
        value = str(row[column_name]).strip()
        if not value:
            return []
        
        # Split by comma and clean up
        items = [item.strip() for item in value.split(',')]
        return [item for item in items if item]
    
    def _validate_script_content(self, query: str, response: str, file_path: str, row_number: int) -> None:
        """Validate script content."""
        # Check response length
        if len(response) < self.config.min_response_length:
            raise ConversationError(
                f"Response too short in {file_path}:{row_number}: {len(response)} < {self.config.min_response_length}"
            )
        
        if len(response) > self.config.max_response_length:
            raise ConversationError(
                f"Response too long in {file_path}:{row_number}: {len(response)} > {self.config.max_response_length}"
            )
        
        # Check for potentially problematic content
        if response.strip() == query.strip():
            self._log.warning(f"Query and response are identical in {file_path}:{row_number}")
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text content."""
        if not text:
            return text
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Convert to lowercase if not case sensitive
        if not self.config.case_sensitive:
            text = text.lower()
        
        return text
    
    def _generate_script_id(self, file_path: str, row_number: int, query: str) -> str:
        """Generate unique script ID."""
        file_name = os.path.basename(file_path).split('.')[0]
        query_hash = hash(query) % 10000  # Simple hash for uniqueness
        return f"{file_name}_{row_number}_{query_hash}"    

    def parse_directory(self, directory_path: Optional[str] = None) -> Dict[str, List[ConversationScript]]:
        """
        Parse all Excel files in directory.
        
        Args:
            directory_path: Directory path (uses config default if None)
            
        Returns:
            Dictionary mapping file paths to parsed scripts
        """
        if directory_path is None:
            directory_path = self.config.script_directory
        
        results = {}
        
        try:
            # Find all Excel files
            excel_files = []
            for ext in self.config.supported_formats:
                pattern = os.path.join(directory_path, f"*{ext}")
                import glob
                excel_files.extend(glob.glob(pattern))
            
            self._log.info(f"Found {len(excel_files)} Excel files in {directory_path}")
            
            # Parse each file
            for file_path in excel_files:
                try:
                    scripts = self.parse_excel_file(file_path)
                    results[file_path] = scripts
                except Exception as e:
                    self._log.error(f"Failed to parse {file_path}: {e}")
                    results[file_path] = []
            
            # Remove duplicates if enabled
            if self.config.remove_duplicates:
                self._remove_duplicate_scripts()
            
        except Exception as e:
            self._log.error(f"Error parsing directory {directory_path}: {e}")
            raise ConversationError(f"Failed to parse directory: {e}")
        
        return results
    
    def _remove_duplicate_scripts(self) -> None:
        """Remove duplicate scripts based on query content."""
        seen_queries = set()
        duplicates = []
        
        for script_id, script in self.parsed_scripts.items():
            query_key = script.query.lower() if not self.config.case_sensitive else script.query
            
            if query_key in seen_queries:
                duplicates.append(script_id)
                self._log.debug(f"Found duplicate script: {script_id}")
            else:
                seen_queries.add(query_key)
        
        # Remove duplicates
        for script_id in duplicates:
            del self.parsed_scripts[script_id]
        
        if duplicates:
            self._log.info(f"Removed {len(duplicates)} duplicate scripts")
    
    def get_parsed_scripts(self) -> Dict[str, ConversationScript]:
        """Get all parsed scripts."""
        return self.parsed_scripts.copy()
    
    def get_scripts_by_intent(self, intent: str) -> List[ConversationScript]:
        """Get scripts filtered by intent."""
        return [
            script for script in self.parsed_scripts.values()
            if script.intent and script.intent.lower() == intent.lower()
        ]
    
    def get_scripts_by_priority(self, min_priority: int = 1) -> List[ConversationScript]:
        """Get scripts filtered by minimum priority."""
        return [
            script for script in self.parsed_scripts.values()
            if script.priority >= min_priority
        ]
    
    def search_scripts(self, query: str, limit: int = 10) -> List[ConversationScript]:
        """
        Search scripts by query content.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching scripts
        """
        query_lower = query.lower()
        matches = []
        
        for script in self.parsed_scripts.values():
            # Simple text matching (can be enhanced with fuzzy matching)
            if query_lower in script.query.lower() or query_lower in script.response.lower():
                matches.append(script)
        
        # Sort by priority (higher first)
        matches.sort(key=lambda s: s.priority, reverse=True)
        
        return matches[:limit]
    
    def validate_all_scripts(self) -> Dict[str, Any]:
        """
        Validate all parsed scripts and return validation report.
        
        Returns:
            Validation report with statistics and errors
        """
        report = {
            "total_scripts": len(self.parsed_scripts),
            "valid_scripts": 0,
            "invalid_scripts": 0,
            "validation_errors": [],
            "warnings": []
        }
        
        for script in self.parsed_scripts.values():
            try:
                # Validate script content
                if not script.query.strip():
                    report["validation_errors"].append({
                        "script_id": script.script_id,
                        "error": "Empty query"
                    })
                    continue
                
                if not script.response.strip():
                    report["validation_errors"].append({
                        "script_id": script.script_id,
                        "error": "Empty response"
                    })
                    continue
                
                # Check response length
                if len(script.response) < self.config.min_response_length:
                    report["validation_errors"].append({
                        "script_id": script.script_id,
                        "error": f"Response too short: {len(script.response)}"
                    })
                    continue
                
                if len(script.response) > self.config.max_response_length:
                    report["validation_errors"].append({
                        "script_id": script.script_id,
                        "error": f"Response too long: {len(script.response)}"
                    })
                    continue
                
                # Check for missing intent if required
                if self.config.require_intent and not script.intent:
                    report["warnings"].append({
                        "script_id": script.script_id,
                        "warning": "Missing intent classification"
                    })
                
                report["valid_scripts"] += 1
                
            except Exception as e:
                report["validation_errors"].append({
                    "script_id": script.script_id,
                    "error": str(e)
                })
        
        report["invalid_scripts"] = len(report["validation_errors"])
        
        return report
    
    def export_scripts_to_dict(self) -> List[Dict[str, Any]]:
        """Export parsed scripts to dictionary format."""
        return [
            {
                "script_id": script.script_id,
                "query": script.query,
                "response": script.response,
                "intent": script.intent,
                "priority": script.priority,
                "conditions": script.conditions,
                "actions": script.actions,
                "source_file": script.source_file,
                "row_number": script.row_number,
                "created_time": script.created_time.isoformat()
            }
            for script in self.parsed_scripts.values()
        ]
    
    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get parsing statistics."""
        return {
            "total_files_processed": self._total_files_processed,
            "total_scripts_parsed": self._total_scripts_parsed,
            "total_errors": self._total_errors,
            "current_scripts_count": len(self.parsed_scripts),
            "error_rate": self._total_errors / max(1, self._total_scripts_parsed + self._total_errors),
            "parsing_errors": self.parsing_errors.copy()
        }
    
    def clear_scripts(self) -> None:
        """Clear all parsed scripts."""
        self.parsed_scripts.clear()
        self.parsing_errors.clear()
        self._log.info("Cleared all parsed scripts")
    
    def reload_scripts(self) -> Dict[str, List[ConversationScript]]:
        """Reload all scripts from directory."""
        self._log.info("Reloading scripts from directory...")
        
        # Clear existing scripts
        self.clear_scripts()
        
        # Parse directory again
        results = self.parse_directory()
        
        self._log.info(f"Reloaded {len(self.parsed_scripts)} scripts")
        
        return results


# Utility functions for script parsing

def create_script_parser(
    script_directory: str = "docs",
    query_column: str = "用户问题",
    response_column: str = "回复内容",
    config_manager=None,
    **kwargs
) -> ScriptParser:
    """
    Create script parser with configuration.
    
    Args:
        script_directory: Directory containing Excel files
        query_column: Column name for user queries
        response_column: Column name for responses
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured script parser
    """
    config = ScriptParserConfig(
        script_directory=script_directory,
        query_column=query_column,
        response_column=response_column,
        **kwargs
    )
    
    return ScriptParser(config, config_manager)


def validate_excel_file_structure(file_path: str, required_columns: List[str]) -> Dict[str, Any]:
    """
    Validate Excel file structure without full parsing.
    
    Args:
        file_path: Path to Excel file
        required_columns: List of required column names
        
    Returns:
        Validation result dictionary
    """
    result = {
        "valid": False,
        "errors": [],
        "warnings": [],
        "columns": [],
        "row_count": 0
    }
    
    try:
        # Read just the header
        df = pd.read_excel(file_path, nrows=0)
        result["columns"] = list(df.columns)
        
        # Check for required columns
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            result["errors"].append(f"Missing required columns: {missing_columns}")
        
        # Read full file to get row count
        df_full = pd.read_excel(file_path)
        result["row_count"] = len(df_full)
        
        if result["row_count"] == 0:
            result["errors"].append("File is empty")
        
        # Check for empty required columns
        for col in required_columns:
            if col in df_full.columns:
                empty_count = df_full[col].isna().sum()
                if empty_count > 0:
                    result["warnings"].append(f"Column '{col}' has {empty_count} empty cells")
        
        result["valid"] = len(result["errors"]) == 0
        
    except Exception as e:
        result["errors"].append(f"Error reading file: {str(e)}")
    
    return result