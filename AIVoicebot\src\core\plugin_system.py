"""
Plugin System for AI Voice Customer Service

This module provides a flexible plugin system that allows for dynamic
extension of functionality without modifying core code.
"""

import asyncio
import importlib
import inspect
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Type, Callable
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import json
import yaml
from enum import Enum

from .base_component import BaseComponent


class PluginType(Enum):
    """Plugin types."""
    AUDIO_PROCESSOR = "audio_processor"
    CONVERSATION_HANDLER = "conversation_handler"
    TELEPHONY_ADAPTER = "telephony_adapter"
    ANALYTICS_PROVIDER = "analytics_provider"
    STORAGE_PROVIDER = "storage_provider"
    NOTIFICATION_PROVIDER = "notification_provider"
    MIDDLEWARE = "middleware"
    CUSTOM = "custom"


class PluginStatus(Enum):
    """Plugin status."""
    LOADED = "loaded"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginMetadata:
    """Plugin metadata."""
    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str] = field(default_factory=list)
    config_schema: Dict[str, Any] = field(default_factory=dict)
    entry_point: str = ""
    min_system_version: str = "1.0.0"
    max_system_version: str = ""
    enabled: bool = True
    priority: int = 100
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PluginMetadata':
        """Create metadata from dictionary."""
        plugin_type = PluginType(data.get("plugin_type", "custom"))
        return cls(
            name=data["name"],
            version=data["version"],
            description=data["description"],
            author=data["author"],
            plugin_type=plugin_type,
            dependencies=data.get("dependencies", []),
            config_schema=data.get("config_schema", {}),
            entry_point=data.get("entry_point", ""),
            min_system_version=data.get("min_system_version", "1.0.0"),
            max_system_version=data.get("max_system_version", ""),
            enabled=data.get("enabled", True),
            priority=data.get("priority", 100)
        )


@dataclass
class PluginInfo:
    """Plugin information."""
    metadata: PluginMetadata
    status: PluginStatus
    instance: Optional[Any] = None
    load_time: Optional[datetime] = None
    error_message: Optional[str] = None
    config: Dict[str, Any] = field(default_factory=dict)


class PluginInterface(ABC):
    """Base interface for all plugins."""
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin with configuration."""
        pass
    
    @abstractmethod
    async def start(self) -> bool:
        """Start the plugin."""
        pass
    
    @abstractmethod
    async def stop(self) -> bool:
        """Stop the plugin."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        pass
    
    @property
    @abstractmethod
    def metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        pass


class AudioProcessorPlugin(PluginInterface):
    """Base class for audio processor plugins."""
    
    @abstractmethod
    async def process_audio(self, audio_data: bytes, context: Dict[str, Any]) -> bytes:
        """Process audio data."""
        pass


class ConversationHandlerPlugin(PluginInterface):
    """Base class for conversation handler plugins."""
    
    @abstractmethod
    async def handle_message(self, message: str, context: Dict[str, Any]) -> str:
        """Handle conversation message."""
        pass


class TelephonyAdapterPlugin(PluginInterface):
    """Base class for telephony adapter plugins."""
    
    @abstractmethod
    async def make_call(self, destination: str, caller_id: str = None) -> Optional[str]:
        """Make an outbound call."""
        pass
    
    @abstractmethod
    async def answer_call(self, call_id: str) -> bool:
        """Answer an incoming call."""
        pass
    
    @abstractmethod
    async def hangup_call(self, call_id: str) -> bool:
        """Hang up a call."""
        pass


class PluginManager(BaseComponent):
    """Plugin manager for dynamic functionality extension."""
    
    def __init__(self, config_manager, logger=None):
        super().__init__("plugin_manager", config_manager, logger)
        
        # Plugin storage
        self.plugins: Dict[str, PluginInfo] = {}
        self.plugin_types: Dict[PluginType, List[str]] = {
            plugin_type: [] for plugin_type in PluginType
        }
        
        # Plugin directories
        self.plugin_dirs = [
            Path("plugins"),
            Path("src/plugins"),
            Path("/app/plugins")
        ]
        
        # Event hooks
        self.hooks: Dict[str, List[Callable]] = {}
        
        # Plugin configuration
        self.auto_load = self.config_manager.get_config("plugins.auto_load", True)
        self.plugin_timeout = self.config_manager.get_config("plugins.timeout", 30)
        
    async def _initialize_impl(self) -> None:
        """Initialize the plugin manager."""
        self._log.info("Plugin Manager initialized")
        
        # Create plugin directories if they don't exist
        for plugin_dir in self.plugin_dirs:
            plugin_dir.mkdir(parents=True, exist_ok=True)
        
        # Auto-load plugins if enabled
        if self.auto_load:
            await self.discover_and_load_plugins()
    
    async def _start_impl(self) -> None:
        """Start the plugin manager."""
        self._log.info("Starting Plugin Manager...")
        
        # Start all loaded plugins
        for plugin_name, plugin_info in self.plugins.items():
            if plugin_info.status == PluginStatus.LOADED:
                await self.start_plugin(plugin_name)
        
        self._log.info("Plugin Manager started")
    
    async def _stop_impl(self) -> None:
        """Stop the plugin manager."""
        self._log.info("Stopping Plugin Manager...")
        
        # Stop all active plugins
        for plugin_name, plugin_info in self.plugins.items():
            if plugin_info.status == PluginStatus.ACTIVE:
                await self.stop_plugin(plugin_name)
        
        self._log.info("Plugin Manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Clean up the plugin manager."""
        # Cleanup all plugins
        for plugin_name, plugin_info in self.plugins.items():
            if plugin_info.instance:
                try:
                    await plugin_info.instance.cleanup()
                except Exception as e:
                    self._log.error(f"Error cleaning up plugin {plugin_name}: {e}")
        
        self.plugins.clear()
        self.plugin_types = {plugin_type: [] for plugin_type in PluginType}
        self.hooks.clear()
        
        self._log.info("Plugin Manager cleanup completed")
    
    async def discover_and_load_plugins(self) -> List[str]:
        """Discover and load all available plugins."""
        discovered_plugins = []
        
        for plugin_dir in self.plugin_dirs:
            if plugin_dir.exists():
                discovered_plugins.extend(await self._discover_plugins_in_directory(plugin_dir))
        
        # Load discovered plugins
        loaded_plugins = []
        for plugin_path in discovered_plugins:
            try:
                plugin_name = await self.load_plugin(plugin_path)
                if plugin_name:
                    loaded_plugins.append(plugin_name)
            except Exception as e:
                self._log.error(f"Failed to load plugin from {plugin_path}: {e}")
        
        self._log.info(f"Discovered and loaded {len(loaded_plugins)} plugins")
        return loaded_plugins
    
    async def _discover_plugins_in_directory(self, directory: Path) -> List[Path]:
        """Discover plugins in a directory."""
        plugins = []
        
        for item in directory.iterdir():
            if item.is_dir():
                # Look for plugin.yml or plugin.yaml
                for config_file in ["plugin.yml", "plugin.yaml", "plugin.json"]:
                    config_path = item / config_file
                    if config_path.exists():
                        plugins.append(item)
                        break
        
        return plugins
    
    async def load_plugin(self, plugin_path: Path) -> Optional[str]:
        """Load a plugin from path."""
        try:
            # Load plugin metadata
            metadata = await self._load_plugin_metadata(plugin_path)
            if not metadata:
                return None
            
            # Check if plugin is already loaded
            if metadata.name in self.plugins:
                self._log.warning(f"Plugin {metadata.name} is already loaded")
                return metadata.name
            
            # Validate dependencies
            if not await self._validate_plugin_dependencies(metadata):
                self._log.error(f"Plugin {metadata.name} has unmet dependencies")
                return None
            
            # Load plugin module
            plugin_module = await self._load_plugin_module(plugin_path, metadata)
            if not plugin_module:
                return None
            
            # Create plugin instance
            plugin_class = getattr(plugin_module, metadata.entry_point)
            plugin_instance = plugin_class()
            
            # Validate plugin interface
            if not isinstance(plugin_instance, PluginInterface):
                self._log.error(f"Plugin {metadata.name} does not implement PluginInterface")
                return None
            
            # Create plugin info
            plugin_info = PluginInfo(
                metadata=metadata,
                status=PluginStatus.LOADED,
                instance=plugin_instance,
                load_time=datetime.now()
            )
            
            # Register plugin
            self.plugins[metadata.name] = plugin_info
            self.plugin_types[metadata.plugin_type].append(metadata.name)
            
            self._log.info(f"Loaded plugin: {metadata.name} v{metadata.version}")
            return metadata.name
            
        except Exception as e:
            self._log.error(f"Failed to load plugin from {plugin_path}: {e}")
            return None
    
    async def _load_plugin_metadata(self, plugin_path: Path) -> Optional[PluginMetadata]:
        """Load plugin metadata from configuration file."""
        for config_file in ["plugin.yml", "plugin.yaml", "plugin.json"]:
            config_path = plugin_path / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        if config_file.endswith('.json'):
                            data = json.load(f)
                        else:
                            data = yaml.safe_load(f)
                    
                    return PluginMetadata.from_dict(data)
                    
                except Exception as e:
                    self._log.error(f"Failed to load plugin metadata from {config_path}: {e}")
                    return None
        
        self._log.error(f"No plugin configuration found in {plugin_path}")
        return None
    
    async def _validate_plugin_dependencies(self, metadata: PluginMetadata) -> bool:
        """Validate plugin dependencies."""
        for dependency in metadata.dependencies:
            if dependency not in self.plugins:
                self._log.error(f"Plugin {metadata.name} requires dependency: {dependency}")
                return False
            
            dep_plugin = self.plugins[dependency]
            if dep_plugin.status not in [PluginStatus.LOADED, PluginStatus.ACTIVE]:
                self._log.error(f"Plugin {metadata.name} dependency {dependency} is not available")
                return False
        
        return True
    
    async def _load_plugin_module(self, plugin_path: Path, metadata: PluginMetadata):
        """Load plugin module."""
        try:
            # Add plugin path to Python path
            import sys
            if str(plugin_path) not in sys.path:
                sys.path.insert(0, str(plugin_path))
            
            # Import plugin module
            module_name = metadata.entry_point.split('.')[0] if '.' in metadata.entry_point else 'main'
            module = importlib.import_module(module_name)
            
            return module
            
        except Exception as e:
            self._log.error(f"Failed to load plugin module: {e}")
            return None
    
    async def start_plugin(self, plugin_name: str) -> bool:
        """Start a loaded plugin."""
        if plugin_name not in self.plugins:
            self._log.error(f"Plugin {plugin_name} not found")
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        if plugin_info.status == PluginStatus.ACTIVE:
            self._log.warning(f"Plugin {plugin_name} is already active")
            return True
        
        if plugin_info.status != PluginStatus.LOADED:
            self._log.error(f"Plugin {plugin_name} is not in loaded state")
            return False
        
        try:
            # Initialize plugin
            config = plugin_info.config or {}
            await plugin_info.instance.initialize(config)
            
            # Start plugin
            await plugin_info.instance.start()
            
            # Update status
            plugin_info.status = PluginStatus.ACTIVE
            
            self._log.info(f"Started plugin: {plugin_name}")
            return True
            
        except Exception as e:
            plugin_info.status = PluginStatus.ERROR
            plugin_info.error_message = str(e)
            self._log.error(f"Failed to start plugin {plugin_name}: {e}")
            return False
    
    async def stop_plugin(self, plugin_name: str) -> bool:
        """Stop an active plugin."""
        if plugin_name not in self.plugins:
            self._log.error(f"Plugin {plugin_name} not found")
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        if plugin_info.status != PluginStatus.ACTIVE:
            self._log.warning(f"Plugin {plugin_name} is not active")
            return True
        
        try:
            # Stop plugin
            await plugin_info.instance.stop()
            
            # Update status
            plugin_info.status = PluginStatus.INACTIVE
            
            self._log.info(f"Stopped plugin: {plugin_name}")
            return True
            
        except Exception as e:
            plugin_info.status = PluginStatus.ERROR
            plugin_info.error_message = str(e)
            self._log.error(f"Failed to stop plugin {plugin_name}: {e}")
            return False
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a plugin."""
        if plugin_name not in self.plugins:
            self._log.error(f"Plugin {plugin_name} not found")
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        # Stop plugin if active
        if plugin_info.status == PluginStatus.ACTIVE:
            await self.stop_plugin(plugin_name)
        
        try:
            # Cleanup plugin
            if plugin_info.instance:
                await plugin_info.instance.cleanup()
            
            # Remove from registry
            self.plugins.pop(plugin_name)
            
            # Remove from type registry
            for plugin_type, plugin_list in self.plugin_types.items():
                if plugin_name in plugin_list:
                    plugin_list.remove(plugin_name)
            
            self._log.info(f"Unloaded plugin: {plugin_name}")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to unload plugin {plugin_name}: {e}")
            return False
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[str]:
        """Get plugins by type."""
        return self.plugin_types.get(plugin_type, [])
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """Get plugin information."""
        return self.plugins.get(plugin_name)
    
    def get_all_plugins(self) -> Dict[str, PluginInfo]:
        """Get all plugins."""
        return self.plugins.copy()
    
    # Hook system for plugin events
    def register_hook(self, event_name: str, callback: Callable) -> None:
        """Register a hook for plugin events."""
        if event_name not in self.hooks:
            self.hooks[event_name] = []
        self.hooks[event_name].append(callback)
    
    async def trigger_hook(self, event_name: str, *args, **kwargs) -> None:
        """Trigger hooks for an event."""
        if event_name in self.hooks:
            for callback in self.hooks[event_name]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(*args, **kwargs)
                    else:
                        callback(*args, **kwargs)
                except Exception as e:
                    self._log.error(f"Error in hook {event_name}: {e}")
    
    def get_plugin_statistics(self) -> Dict[str, Any]:
        """Get plugin statistics."""
        stats = {
            "total_plugins": len(self.plugins),
            "active_plugins": len([p for p in self.plugins.values() if p.status == PluginStatus.ACTIVE]),
            "loaded_plugins": len([p for p in self.plugins.values() if p.status == PluginStatus.LOADED]),
            "error_plugins": len([p for p in self.plugins.values() if p.status == PluginStatus.ERROR]),
            "plugins_by_type": {
                plugin_type.value: len(plugin_list)
                for plugin_type, plugin_list in self.plugin_types.items()
            },
            "plugin_details": {
                name: {
                    "status": info.status.value,
                    "type": info.metadata.plugin_type.value,
                    "version": info.metadata.version,
                    "load_time": info.load_time.isoformat() if info.load_time else None,
                    "error": info.error_message
                }
                for name, info in self.plugins.items()
            }
        }
        
        return stats
