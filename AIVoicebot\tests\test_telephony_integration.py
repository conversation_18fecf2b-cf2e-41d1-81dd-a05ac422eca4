"""
Tests for telephony system integration.
"""

import asyncio
import pytest
from unittest.mock import <PERSON><PERSON>, Async<PERSON>ock, patch
import json
from datetime import datetime
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.integrations.telephony_integration import (
    ESP32TelephonyAdapter, AudioFrame, AudioCodec, CallInfo,
    CallDirection, TelephonyProtocol, TelephonyEndpoint, WEBSOCKETS_AVAILABLE
)
from src.integrations.telephony_manager import (
    TelephonyManager, CallRoutingStrategy, TelephonyStats
)


class MockWebSocket:
    """Mock WebSocket for testing."""
    
    def __init__(self):
        self.messages_sent = []
        self.messages_to_receive = []
        self.closed = False
        self.remote_address = ("127.0.0.1", 12345)
    
    async def send(self, message):
        """Mock send method."""
        self.messages_sent.append(message)
    
    async def close(self):
        """Mock close method."""
        self.closed = True
    
    def __aiter__(self):
        """Make it async iterable."""
        return self
    
    async def __anext__(self):
        """Async iterator."""
        if self.messages_to_receive:
            return self.messages_to_receive.pop(0)
        else:
            raise StopAsyncIteration


class TestESP32TelephonyAdapter:
    """Test ESP32 telephony adapter."""
    
    @pytest.fixture
    def esp32_config(self):
        """Create ESP32 configuration."""
        return {
            "esp32_host": "localhost",
            "esp32_port": 8765,
            "max_connections": 5
        }
    
    @pytest.fixture
    def esp32_adapter(self, esp32_config):
        """Create ESP32 adapter instance."""
        return ESP32TelephonyAdapter(esp32_config)
    
    @pytest.mark.asyncio
    async def test_adapter_initialization(self, esp32_adapter):
        """Test adapter initialization."""
        success = await esp32_adapter.initialize()
        if WEBSOCKETS_AVAILABLE:
            assert success
            assert not esp32_adapter.is_listening
        else:
            assert not success  # Should fail when websockets not available
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(not WEBSOCKETS_AVAILABLE, reason="websockets module not available")
    async def test_adapter_lifecycle(self, esp32_adapter):
        """Test adapter start/stop lifecycle."""
        await esp32_adapter.initialize()

        # Mock websockets.serve
        with patch('src.integrations.telephony_integration.websockets.serve') as mock_serve:
            mock_server = Mock()
            mock_serve.return_value = mock_server
            
            # Start listening
            success = await esp32_adapter.start_listening()
            assert success
            assert esp32_adapter.is_listening
            
            # Stop listening
            mock_server.close = AsyncMock()
            mock_server.wait_closed = AsyncMock()
            
            success = await esp32_adapter.stop_listening()
            assert success
            assert not esp32_adapter.is_listening
    
    def test_audio_callback_setup(self, esp32_adapter):
        """Test audio callback setup."""
        callback_called = []
        
        def test_callback(call_id, audio_frame):
            callback_called.append((call_id, audio_frame))
        
        esp32_adapter.set_audio_callback(test_callback)
        assert esp32_adapter.audio_callback == test_callback
    
    def test_call_event_callback_setup(self, esp32_adapter):
        """Test call event callback setup."""
        callback_called = []
        
        def test_callback(call_id, event_type, event_data):
            callback_called.append((call_id, event_type, event_data))
        
        esp32_adapter.set_call_event_callback(test_callback)
        assert esp32_adapter.call_event_callback == test_callback
    
    def test_supported_codecs(self, esp32_adapter):
        """Test supported codecs."""
        codecs = esp32_adapter.get_supported_codecs()
        assert AudioCodec.PCM_16KHZ in codecs
        assert AudioCodec.OPUS in codecs
    
    @pytest.mark.asyncio
    async def test_call_request_handling(self, esp32_adapter):
        """Test handling call request from ESP32."""
        await esp32_adapter.initialize()
        
        # Set up callback
        call_events = []
        def call_event_callback(call_id, event_type, event_data):
            call_events.append((call_id, event_type, event_data))
        
        esp32_adapter.set_call_event_callback(call_event_callback)
        
        # Simulate call request
        connection_id = "test_connection"
        call_request_data = {
            "type": "call_request",
            "caller_number": "esp32_device_001",
            "callee_number": "ai_assistant"
        }
        
        await esp32_adapter._handle_call_request(connection_id, call_request_data)
        
        # Check that call was created
        assert len(esp32_adapter.call_sessions) == 1
        assert len(call_events) == 1
        
        call_id, event_type, event_data = call_events[0]
        assert event_type == "incoming_call"
        assert event_data["caller_number"] == "esp32_device_001"
        assert event_data["protocol"] == TelephonyProtocol.ESP32_CUSTOM.value
    
    @pytest.mark.asyncio
    async def test_audio_data_handling(self, esp32_adapter):
        """Test handling audio data from ESP32."""
        await esp32_adapter.initialize()
        
        # Set up call session
        call_id = "test_call"
        connection_id = "test_connection"
        
        call_info = CallInfo(
            call_id=call_id,
            session_id=connection_id,
            direction=CallDirection.INBOUND,
            caller_number="esp32_device",
            callee_number="ai_assistant",
            protocol=TelephonyProtocol.ESP32_CUSTOM,
            codec=AudioCodec.PCM_16KHZ
        )
        
        esp32_adapter.call_sessions[call_id] = call_info
        
        # Set up audio callback
        audio_frames = []
        def audio_callback(call_id, audio_frame):
            audio_frames.append((call_id, audio_frame))
        
        esp32_adapter.set_audio_callback(audio_callback)
        
        # Simulate audio data
        test_audio_data = b"test_audio_data"
        audio_data = {
            "type": "audio_data",
            "call_id": call_id,
            "codec": "pcm_16khz",
            "sample_rate": 16000,
            "channels": 1,
            "sequence": 1,
            "data": test_audio_data.hex()
        }
        
        await esp32_adapter._handle_audio_data(connection_id, audio_data)
        
        # Check that audio was processed
        assert len(audio_frames) == 1
        received_call_id, audio_frame = audio_frames[0]
        assert received_call_id == call_id
        assert audio_frame.codec == AudioCodec.PCM_16KHZ
        assert audio_frame.data == test_audio_data
        assert audio_frame.sample_rate == 16000
    
    @pytest.mark.asyncio
    async def test_send_audio(self, esp32_adapter):
        """Test sending audio to ESP32."""
        await esp32_adapter.initialize()
        
        # Set up call session and connection
        call_id = "test_call"
        connection_id = "test_connection"
        
        call_info = CallInfo(
            call_id=call_id,
            session_id=connection_id,
            direction=CallDirection.INBOUND,
            caller_number="esp32_device",
            callee_number="ai_assistant",
            protocol=TelephonyProtocol.ESP32_CUSTOM,
            codec=AudioCodec.PCM_16KHZ
        )
        
        esp32_adapter.call_sessions[call_id] = call_info
        
        # Mock WebSocket connection
        mock_websocket = MockWebSocket()
        esp32_adapter.active_connections[connection_id] = mock_websocket
        
        # Create audio frame
        audio_frame = AudioFrame(
            data=b"test_audio",
            codec=AudioCodec.PCM_16KHZ,
            sample_rate=16000,
            channels=1,
            sequence_number=1
        )
        
        # Send audio
        success = await esp32_adapter.send_audio(call_id, audio_frame)
        assert success
        
        # Check that message was sent
        assert len(mock_websocket.messages_sent) == 1
        sent_message = json.loads(mock_websocket.messages_sent[0])
        assert sent_message["type"] == "audio_data"
        assert sent_message["call_id"] == call_id
        assert sent_message["codec"] == "pcm_16khz"


class TestTelephonyManager:
    """Test telephony manager."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        config_manager = Mock()
        config_manager.get_config.side_effect = lambda key, default: {
            "telephony.routing_strategy": "least_busy",
            "telephony.max_concurrent_calls": 10,
            "telephony.call_timeout_seconds": 300,
            "telephony.esp32": {"enabled": False},
            "telephony.sip": {"enabled": False}
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def telephony_manager(self, mock_config_manager):
        """Create telephony manager instance."""
        return TelephonyManager(mock_config_manager)
    
    @pytest.mark.asyncio
    async def test_manager_lifecycle(self, telephony_manager):
        """Test manager lifecycle."""
        await telephony_manager.initialize()
        await telephony_manager.start()
        
        assert telephony_manager.is_initialized
        assert telephony_manager.is_running
        
        await telephony_manager.stop()
        await telephony_manager.cleanup()
    
    def test_adapter_registration(self, telephony_manager):
        """Test adapter registration."""
        # Create mock adapter
        mock_adapter = Mock()
        mock_adapter.set_audio_callback = Mock()
        mock_adapter.set_call_event_callback = Mock()
        mock_adapter.get_supported_codecs.return_value = [AudioCodec.PCM_16KHZ]
        
        # Register adapter
        telephony_manager.register_adapter("test_adapter", mock_adapter)
        
        assert "test_adapter" in telephony_manager.adapters
        assert telephony_manager.get_adapter("test_adapter") == mock_adapter
        
        # Check callbacks were set
        mock_adapter.set_audio_callback.assert_called_once()
        mock_adapter.set_call_event_callback.assert_called_once()
    
    def test_endpoint_registration(self, telephony_manager):
        """Test endpoint registration."""
        endpoint = TelephonyEndpoint(
            endpoint_id="test_endpoint",
            protocol=TelephonyProtocol.ESP32_CUSTOM,
            address="localhost",
            port=8765,
            supported_codecs=[AudioCodec.PCM_16KHZ]
        )
        
        telephony_manager.register_endpoint(endpoint)
        
        assert "test_endpoint" in telephony_manager.endpoints
        assert telephony_manager.endpoints["test_endpoint"] == endpoint
    
    @pytest.mark.asyncio
    async def test_make_call(self, telephony_manager):
        """Test making outbound call."""
        # Register mock adapter
        mock_adapter = Mock()
        mock_adapter.set_audio_callback = Mock()
        mock_adapter.set_call_event_callback = Mock()
        mock_adapter.make_call = AsyncMock(return_value="test_call_id")
        mock_adapter.get_supported_codecs.return_value = [AudioCodec.PCM_16KHZ]
        
        telephony_manager.register_adapter("test_adapter", mock_adapter)
        
        # Make call
        call_id = await telephony_manager.make_call("1234567890", "0987654321")
        
        assert call_id == "test_call_id"
        assert call_id in telephony_manager.call_adapter_mapping
        assert telephony_manager.call_adapter_mapping[call_id] == "test_adapter"
        
        # Check adapter was called
        mock_adapter.make_call.assert_called_once_with("1234567890", "0987654321")
    
    def test_call_event_handling(self, telephony_manager):
        """Test call event handling."""
        # Set up callback
        call_events = []
        def call_event_callback(call_id, event_type, event_data):
            call_events.append((call_id, event_type, event_data))
        
        telephony_manager.add_call_event_callback(call_event_callback)
        
        # Simulate incoming call event
        call_id = "test_call"
        event_data = {
            "call_id": call_id,
            "session_id": "session_123",
            "direction": "inbound",
            "caller_number": "1234567890",
            "callee_number": "ai_assistant",
            "protocol": "esp32_custom",
            "codec": "pcm_16khz",
            "status": "ringing"
        }
        
        telephony_manager._handle_call_event(call_id, "incoming_call", event_data)
        
        # Check event was processed
        assert len(call_events) == 1
        assert call_events[0][0] == call_id
        assert call_events[0][1] == "incoming_call"
        
        # Check call was tracked
        assert call_id in telephony_manager.active_calls
        assert telephony_manager.stats.active_calls == 1
    
    def test_statistics(self, telephony_manager):
        """Test statistics collection."""
        # Add some test data
        telephony_manager.stats.total_calls = 10
        telephony_manager.stats.completed_calls = 8
        telephony_manager.stats.failed_calls = 2
        
        stats = telephony_manager.get_telephony_statistics()
        
        assert stats["system_stats"]["total_calls"] == 10
        assert stats["system_stats"]["completed_calls"] == 8
        assert stats["system_stats"]["failed_calls"] == 2
        assert "timestamp" in stats
        assert "adapter_stats" in stats
        assert "active_calls" in stats


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestESP32TelephonyAdapter().test_adapter_initialization(ESP32TelephonyAdapter({})))
