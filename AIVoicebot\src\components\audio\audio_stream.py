"""
Real-time audio streaming system with low-latency buffering.

This module provides real-time audio streaming capabilities with
circular buffering, adaptive latency control, and stream synchronization.
"""

import asyncio
import threading
import time
import queue
from typing import Optional, AsyncGenerator, Callable, Dict, Any, List
from dataclasses import dataclass, field
from enum import Enum
import logging

from ...core.interfaces import AudioChunk, AudioFormat, AudioProcessingError
from ...core.base_component import BaseComponent
from .audio_buffer import CircularAudioBuffer, BufferConfig


class StreamState(Enum):
    """Audio stream states."""
    IDLE = "idle"
    STARTING = "starting"
    ACTIVE = "active"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class StreamConfig:
    """Configuration for audio streams."""
    stream_id: str
    sample_rate: int = 16000
    channels: int = 1
    format: AudioFormat = AudioFormat.PCM_16KHZ_MONO
    
    # Buffer configuration
    buffer_size: int = 4096
    chunk_size: int = 1024
    
    # Latency control
    target_latency_ms: int = 100
    max_latency_ms: int = 500
    min_buffer_ms: int = 50
    
    # Quality settings
    enable_adaptive_buffering: bool = True
    enable_jitter_compensation: bool = True
    enable_dropout_detection: bool = True
    
    # Callback settings
    chunk_callback: Optional[Callable] = None
    error_callback: Optional[Callable] = None
    state_callback: Optional[Callable] = None


@dataclass
class StreamStats:
    """Statistics for audio stream performance."""
    bytes_processed: int = 0
    chunks_processed: int = 0
    buffer_overruns: int = 0
    buffer_underruns: int = 0
    dropouts: int = 0
    average_latency_ms: float = 0.0
    jitter_ms: float = 0.0
    start_time: float = field(default_factory=time.time)
    
    def get_throughput_bps(self) -> float:
        """Calculate throughput in bytes per second."""
        duration = time.time() - self.start_time
        return self.bytes_processed / duration if duration > 0 else 0
    
    def get_chunk_rate(self) -> float:
        """Calculate chunk processing rate."""
        duration = time.time() - self.start_time
        return self.chunks_processed / duration if duration > 0 else 0


class RealTimeAudioStream(BaseComponent):
    """
    Real-time audio stream with adaptive buffering and latency control.
    
    Provides low-latency audio streaming with automatic buffer management,
    jitter compensation, and dropout detection.
    """
    
    def __init__(self, config: StreamConfig, config_manager, logger=None):
        """
        Initialize real-time audio stream.
        
        Args:
            config: Stream configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__(f"audio_stream_{config.stream_id}", config_manager, logger)
        
        self.config = config
        self.state = StreamState.IDLE
        self.stats = StreamStats()
        
        # Create audio buffers
        buffer_config = BufferConfig(
            max_size=config.buffer_size,
            chunk_size=config.chunk_size,
            sample_rate=config.sample_rate,
            channels=config.channels
        )
        
        self.input_buffer = CircularAudioBuffer(buffer_config)
        self.output_buffer = CircularAudioBuffer(buffer_config)
        
        # Streaming control
        self._stop_event = asyncio.Event()
        self._pause_event = asyncio.Event()
        self._streaming_task: Optional[asyncio.Task] = None
        
        # Latency control
        self._latency_samples: List[float] = []
        self._target_buffer_level = self._calculate_target_buffer_level()
        
        # Synchronization
        self._stream_lock = asyncio.Lock()
        self._stats_lock = threading.Lock()
        
        # Callbacks
        self._chunk_handlers: List[Callable] = []
        self._error_handlers: List[Callable] = []
        self._state_handlers: List[Callable] = []
        
        if config.chunk_callback:
            self._chunk_handlers.append(config.chunk_callback)
        if config.error_callback:
            self._error_handlers.append(config.error_callback)
        if config.state_callback:
            self._state_handlers.append(config.state_callback)
    
    async def _initialize_impl(self) -> None:
        """Initialize audio stream."""
        self._log.info(f"Initializing audio stream: {self.config.stream_id}")
        
        # Validate configuration
        self._validate_config()
        
        # Initialize adaptive buffering
        if self.config.enable_adaptive_buffering:
            self._setup_adaptive_buffering()
        
        self._log.info(f"Audio stream initialized: {self.config.stream_id}")
    
    async def _start_impl(self) -> None:
        """Start audio stream."""
        self._log.info(f"Starting audio stream: {self.config.stream_id}")
        
        await self._set_state(StreamState.STARTING)
        
        # Start streaming task
        self._streaming_task = asyncio.create_task(self._streaming_loop())
        
        await self._set_state(StreamState.ACTIVE)
        self._log.info(f"Audio stream started: {self.config.stream_id}")
    
    async def _stop_impl(self) -> None:
        """Stop audio stream."""
        self._log.info(f"Stopping audio stream: {self.config.stream_id}")
        
        await self._set_state(StreamState.STOPPING)
        
        # Signal stop and wait for streaming task
        self._stop_event.set()
        
        if self._streaming_task and not self._streaming_task.done():
            try:
                await asyncio.wait_for(self._streaming_task, timeout=5.0)
            except asyncio.TimeoutError:
                self._log.warning("Streaming task did not stop gracefully, cancelling")
                self._streaming_task.cancel()
        
        await self._set_state(StreamState.IDLE)
        self._log.info(f"Audio stream stopped: {self.config.stream_id}")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup audio stream resources."""
        self._log.info(f"Cleaning up audio stream: {self.config.stream_id}")
        
        # Clear buffers
        self.input_buffer.clear()
        self.output_buffer.clear()
        
        # Clear handlers
        self._chunk_handlers.clear()
        self._error_handlers.clear()
        self._state_handlers.clear()
        
        self._log.info(f"Audio stream cleanup completed: {self.config.stream_id}")
    
    def _validate_config(self) -> None:
        """Validate stream configuration."""
        if self.config.sample_rate <= 0:
            raise AudioProcessingError(f"Invalid sample rate: {self.config.sample_rate}")
        
        if self.config.channels not in [1, 2]:
            raise AudioProcessingError(f"Unsupported channels: {self.config.channels}")
        
        if self.config.chunk_size <= 0 or self.config.chunk_size > 8192:
            raise AudioProcessingError(f"Invalid chunk size: {self.config.chunk_size}")
        
        if self.config.target_latency_ms <= 0:
            raise AudioProcessingError(f"Invalid target latency: {self.config.target_latency_ms}")
    
    def _calculate_target_buffer_level(self) -> int:
        """Calculate target buffer level based on latency requirements."""
        # Convert target latency to number of chunks
        chunk_duration_ms = (self.config.chunk_size / self.config.sample_rate) * 1000
        target_chunks = max(1, int(self.config.target_latency_ms / chunk_duration_ms))
        return min(target_chunks, self.config.buffer_size // 2)
    
    def _setup_adaptive_buffering(self) -> None:
        """Setup adaptive buffering parameters."""
        self._log.debug(f"Setting up adaptive buffering - target level: {self._target_buffer_level} chunks")
    
    async def _set_state(self, new_state: StreamState) -> None:
        """Set stream state and notify handlers."""
        if self.state != new_state:
            old_state = self.state
            self.state = new_state
            
            self._log.debug(f"Stream state changed: {old_state.value} -> {new_state.value}")
            
            # Notify state handlers
            for handler in self._state_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(self.config.stream_id, old_state, new_state)
                    else:
                        handler(self.config.stream_id, old_state, new_state)
                except Exception as e:
                    self._log.error(f"Error in state handler: {e}")
    
    async def _streaming_loop(self) -> None:
        """Main streaming loop."""
        self._log.debug("Starting streaming loop")
        
        try:
            while not self._stop_event.is_set():
                # Check if paused
                if self.state == StreamState.PAUSED:
                    await asyncio.sleep(0.01)
                    continue
                
                # Process audio chunks
                await self._process_audio_chunks()
                
                # Adaptive buffering adjustment
                if self.config.enable_adaptive_buffering:
                    await self._adjust_buffering()
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.001)
                
        except Exception as e:
            self._log.error(f"Error in streaming loop: {e}")
            await self._set_state(StreamState.ERROR)
            await self._notify_error(e)
    
    async def _process_audio_chunks(self) -> None:
        """Process audio chunks from input to output buffer."""
        # Read from input buffer
        chunk = self.input_buffer.read()
        if chunk is None:
            return
        
        start_time = time.time()
        
        try:
            # Process chunk (placeholder for actual processing)
            processed_chunk = await self._process_chunk(chunk)
            
            # Write to output buffer
            if not self.output_buffer.write(processed_chunk):
                self.stats.buffer_overruns += 1
                self._log.warning("Output buffer overrun")
            
            # Update statistics
            processing_time = time.time() - start_time
            await self._update_stats(chunk, processing_time)
            
            # Notify chunk handlers
            await self._notify_chunk_processed(processed_chunk)
            
        except Exception as e:
            self._log.error(f"Error processing audio chunk: {e}")
            await self._notify_error(e)
    
    async def _process_chunk(self, chunk: AudioChunk) -> AudioChunk:
        """Process individual audio chunk."""
        # Placeholder for actual audio processing
        # In real implementation, this would apply effects, filtering, etc.
        return chunk
    
    async def _adjust_buffering(self) -> None:
        """Adjust buffering based on current conditions."""
        input_level = self.input_buffer.size()
        output_level = self.output_buffer.size()
        
        # Check for buffer imbalance
        if input_level > self._target_buffer_level * 1.5:
            # Too much input buffering, might cause latency
            self._log.debug("High input buffer level, adjusting")
        elif input_level < self._target_buffer_level * 0.5:
            # Too little input buffering, might cause underruns
            self._log.debug("Low input buffer level, adjusting")
        
        # Detect dropouts
        if self.config.enable_dropout_detection:
            await self._detect_dropouts()
    
    async def _detect_dropouts(self) -> None:
        """Detect audio dropouts and handle them."""
        # Simple dropout detection based on buffer underruns
        if self.input_buffer.get_stats()["underruns"] > 0:
            self.stats.dropouts += 1
            self._log.warning("Audio dropout detected")
    
    async def _update_stats(self, chunk: AudioChunk, processing_time: float) -> None:
        """Update stream statistics."""
        with self._stats_lock:
            self.stats.bytes_processed += len(chunk.data)
            self.stats.chunks_processed += 1
            
            # Update latency tracking
            latency_ms = processing_time * 1000
            self._latency_samples.append(latency_ms)
            
            # Keep only recent samples for average calculation
            if len(self._latency_samples) > 100:
                self._latency_samples = self._latency_samples[-100:]
            
            # Calculate average latency
            if self._latency_samples:
                self.stats.average_latency_ms = sum(self._latency_samples) / len(self._latency_samples)
                
                # Calculate jitter (standard deviation)
                if len(self._latency_samples) > 1:
                    mean = self.stats.average_latency_ms
                    variance = sum((x - mean) ** 2 for x in self._latency_samples) / len(self._latency_samples)
                    self.stats.jitter_ms = variance ** 0.5
    
    async def _notify_chunk_processed(self, chunk: AudioChunk) -> None:
        """Notify chunk handlers."""
        for handler in self._chunk_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(chunk)
                else:
                    handler(chunk)
            except Exception as e:
                self._log.error(f"Error in chunk handler: {e}")
    
    async def _notify_error(self, error: Exception) -> None:
        """Notify error handlers."""
        for handler in self._error_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(error)
                else:
                    handler(error)
            except Exception as e:
                self._log.error(f"Error in error handler: {e}")
    
    # Public API methods
    
    async def write_audio(self, chunk: AudioChunk) -> bool:
        """
        Write audio chunk to input buffer.
        
        Args:
            chunk: Audio chunk to write
            
        Returns:
            True if written successfully, False if buffer full
        """
        if self.state not in [StreamState.ACTIVE, StreamState.STARTING]:
            return False
        
        success = self.input_buffer.write(chunk)
        if not success:
            self.stats.buffer_overruns += 1
        
        return success
    
    async def read_audio(self) -> Optional[AudioChunk]:
        """
        Read processed audio chunk from output buffer.
        
        Returns:
            Audio chunk or None if buffer empty
        """
        if self.state not in [StreamState.ACTIVE]:
            return None
        
        chunk = self.output_buffer.read()
        if chunk is None:
            self.stats.buffer_underruns += 1
        
        return chunk
    
    async def get_audio_generator(self) -> AsyncGenerator[AudioChunk, None]:
        """
        Get async generator for processed audio chunks.
        
        Yields:
            Processed audio chunks
        """
        while self.state == StreamState.ACTIVE:
            chunk = await self.read_audio()
            if chunk:
                yield chunk
            else:
                # No audio available, wait a bit
                await asyncio.sleep(0.01)
    
    async def pause(self) -> None:
        """Pause audio stream."""
        if self.state == StreamState.ACTIVE:
            await self._set_state(StreamState.PAUSED)
            self._pause_event.set()
    
    async def resume(self) -> None:
        """Resume audio stream."""
        if self.state == StreamState.PAUSED:
            self._pause_event.clear()
            await self._set_state(StreamState.ACTIVE)
    
    def add_chunk_handler(self, handler: Callable) -> None:
        """Add chunk processing handler."""
        self._chunk_handlers.append(handler)
    
    def remove_chunk_handler(self, handler: Callable) -> None:
        """Remove chunk processing handler."""
        if handler in self._chunk_handlers:
            self._chunk_handlers.remove(handler)
    
    def add_error_handler(self, handler: Callable) -> None:
        """Add error handler."""
        self._error_handlers.append(handler)
    
    def remove_error_handler(self, handler: Callable) -> None:
        """Remove error handler."""
        if handler in self._error_handlers:
            self._error_handlers.remove(handler)
    
    def add_state_handler(self, handler: Callable) -> None:
        """Add state change handler."""
        self._state_handlers.append(handler)
    
    def remove_state_handler(self, handler: Callable) -> None:
        """Remove state change handler."""
        if handler in self._state_handlers:
            self._state_handlers.remove(handler)
    
    def get_stream_stats(self) -> StreamStats:
        """Get current stream statistics."""
        with self._stats_lock:
            return StreamStats(
                bytes_processed=self.stats.bytes_processed,
                chunks_processed=self.stats.chunks_processed,
                buffer_overruns=self.stats.buffer_overruns,
                buffer_underruns=self.stats.buffer_underruns,
                dropouts=self.stats.dropouts,
                average_latency_ms=self.stats.average_latency_ms,
                jitter_ms=self.stats.jitter_ms,
                start_time=self.stats.start_time
            )
    
    def get_buffer_stats(self) -> Dict[str, Any]:
        """Get buffer statistics."""
        return {
            "input_buffer": self.input_buffer.get_stats(),
            "output_buffer": self.output_buffer.get_stats()
        }
    
    def reset_stats(self) -> None:
        """Reset stream statistics."""
        with self._stats_lock:
            self.stats = StreamStats()
            self._latency_samples.clear()
            self.input_buffer.reset_stats()
            self.output_buffer.reset_stats()
    
    @property
    def stream_id(self) -> str:
        """Get stream ID."""
        return self.config.stream_id
    
    @property
    def is_active(self) -> bool:
        """Check if stream is active."""
        return self.state == StreamState.ACTIVE
    
    @property
    def is_paused(self) -> bool:
        """Check if stream is paused."""
        return self.state == StreamState.PAUSED
    
    @property
    def current_latency_ms(self) -> float:
        """Get current average latency in milliseconds."""
        return self.stats.average_latency_ms
    
    @property
    def buffer_health(self) -> Dict[str, str]:
        """Get buffer health status."""
        input_level = self.input_buffer.fill_level()
        output_level = self.output_buffer.fill_level()
        
        input_health = "healthy"
        output_health = "healthy"
        
        if input_level > 0.8:
            input_health = "high"
        elif input_level < 0.2:
            input_health = "low"
        
        if output_level > 0.8:
            output_health = "high"
        elif output_level < 0.2:
            output_health = "low"
        
        return {
            "input": input_health,
            "output": output_health,
            "overall": "healthy" if input_health == "healthy" and output_health == "healthy" else "warning"
        }