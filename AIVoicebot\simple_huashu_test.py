#!/usr/bin/env python3
"""
简单话术文件测试

直接测试话术Excel文件的解析，不依赖复杂的导入
"""

import pandas as pd
import os
from pathlib import Path


def test_excel_files():
    """测试Excel文件是否存在并可读"""
    print("📋 测试Excel文件...")
    
    files = [
        "docs/言犀复贷话术.xlsx",
        "docs/零犀复贷AI话术调优240914.xlsx"
    ]
    
    results = {}
    
    for file_path in files:
        print(f"\n📄 测试文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在")
            results[file_path] = False
            continue
        
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            print(f"  ✅ 文件可读")
            print(f"  📋 工作表: {excel_file.sheet_names}")
            
            # 分析每个工作表
            for sheet_name in excel_file.sheet_names:
                print(f"\n    📊 工作表: {sheet_name}")
                
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    print(f"      📈 行数: {len(df)}")
                    print(f"      📋 列数: {len(df.columns)}")
                    print(f"      🏷️  列名: {list(df.columns)}")
                    
                    # 显示前几行数据
                    if len(df) > 0:
                        print(f"      📝 前3行数据:")
                        for i, (idx, row) in enumerate(df.head(3).iterrows()):
                            print(f"        行 {i+1}:")
                            for col in df.columns:
                                value = row[col]
                                if pd.notna(value) and str(value).strip():
                                    print(f"          {col}: {str(value)[:50]}...")
                    
                    # 检测可能的话术列
                    potential_script_columns = []
                    for col in df.columns:
                        col_lower = str(col).lower()
                        if any(keyword in col_lower for keyword in ['话术', '内容', '回复', '脚本']):
                            potential_script_columns.append(col)
                    
                    if potential_script_columns:
                        print(f"      🎯 可能的话术列: {potential_script_columns}")
                    
                    # 检测可能的流程列
                    potential_flow_columns = []
                    for col in df.columns:
                        col_lower = str(col).lower()
                        if any(keyword in col_lower for keyword in ['流程', '节点', '步骤', '阶段']):
                            potential_flow_columns.append(col)
                    
                    if potential_flow_columns:
                        print(f"      🔄 可能的流程列: {potential_flow_columns}")
                    
                except Exception as e:
                    print(f"      ❌ 读取工作表失败: {e}")
            
            results[file_path] = True
            
        except Exception as e:
            print(f"  ❌ 读取文件失败: {e}")
            results[file_path] = False
    
    return results


def analyze_huashu_structure():
    """分析话术文件结构"""
    print("\n🔍 分析话术文件结构...")
    
    files = [
        "docs/言犀复贷话术.xlsx",
        "docs/零犀复贷AI话术调优240914.xlsx"
    ]
    
    for file_path in files:
        if not os.path.exists(file_path):
            continue
            
        print(f"\n📄 分析文件: {file_path}")
        
        try:
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_name in excel_file.sheet_names:
                print(f"\n  📊 工作表: {sheet_name}")
                
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # 统计非空数据
                non_empty_counts = {}
                for col in df.columns:
                    non_empty = df[col].notna().sum()
                    non_empty_counts[col] = non_empty
                
                print(f"    📈 非空数据统计:")
                for col, count in sorted(non_empty_counts.items(), key=lambda x: x[1], reverse=True):
                    if count > 0:
                        print(f"      {col}: {count}/{len(df)} ({count/len(df)*100:.1f}%)")
                
                # 查找最有价值的列
                valuable_columns = [col for col, count in non_empty_counts.items() if count > len(df) * 0.1]
                print(f"    🎯 有价值的列 (>10%非空): {valuable_columns}")
                
                # 分析数据类型
                print(f"    📋 数据类型:")
                for col in valuable_columns:
                    sample_values = df[col].dropna().head(3).tolist()
                    print(f"      {col}: {[str(v)[:30] for v in sample_values]}")
        
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")


def generate_parser_config():
    """生成解析器配置建议"""
    print("\n⚙️  生成解析器配置建议...")
    
    # 基于分析结果生成配置
    config_suggestions = {
        "flow_node_columns": ["流程节点", "节点", "流程", "步骤", "阶段"],
        "script_columns": ["新话术", "话术", "原话术", "内容", "回复", "脚本"],
        "next_node_columns": ["下一步节点", "下一步", "下个节点", "跳转", "下一阶段"],
        "skip_empty_rows": True,
        "normalize_text": True,
        "default_confidence": 0.8
    }
    
    print("📋 建议的解析器配置:")
    for key, value in config_suggestions.items():
        print(f"  {key}: {value}")
    
    return config_suggestions


def main():
    """主函数"""
    print("🚀 开始话术文件测试...")
    print("=" * 60)
    
    # 测试Excel文件
    file_results = test_excel_files()
    
    # 分析文件结构
    analyze_huashu_structure()
    
    # 生成配置建议
    config_suggestions = generate_parser_config()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for success in file_results.values() if success)
    total_count = len(file_results)
    
    print(f"📄 文件测试结果: {success_count}/{total_count} 成功")
    
    for file_path, success in file_results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {file_path}")
    
    if success_count == total_count:
        print("\n🎉 所有话术文件都可以正常读取！")
        print("文件结构分析完成，可以进行解析器集成。")
        
        print("\n📝 下一步建议:")
        print("1. 话术文件已存在且可读")
        print("2. 文件结构已分析完成")
        print("3. 解析器配置建议已生成")
        print("4. 可以集成到脚本管理器中")
        
        return 0
    else:
        print(f"\n⚠️  {total_count - success_count} 个文件存在问题")
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
