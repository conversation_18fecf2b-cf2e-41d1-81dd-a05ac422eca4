"""
Configuration management package for AI Voice Customer Service System

This package provides centralized configuration management with support for:
- Environment-specific settings
- Configuration validation and defaults
- Hot-reloading capabilities
- Environment variable overrides
"""

from .config_manager import (
    ConfigManager,
    AudioConfig,
    LLMConfig,
    TTSConfig,
    ScriptConfig,
    TelephonyConfig,
    LoggingConfig,
    PerformanceConfig,
    Environment,
    get_config,
    initialize_config
)

# Import model configuration
from .model_config_simple import (
    ModelConfigManager,
    ModelPath,
    SenseVoiceConfig,
    SileroVADConfig,
    EdgeTTSConfig,
    QwenConfig,
    ModelType,
    ModelFormat,
    get_model_config,
    initialize_model_config
)

__all__ = [
    # Configuration Manager
    "ConfigManager",
    "AudioConfig",
    "LLMConfig", 
    "TTSConfig",
    "ScriptConfig",
    "TelephonyConfig",
    "LoggingConfig",
    "PerformanceConfig",
    "Environment",
    "get_config",
    "initialize_config",
    
    # Model Configuration
    "ModelConfigManager",
    "ModelPath",
    "SenseVoiceConfig",
    "SileroVADConfig",
    "EdgeTTSConfig",
    "QwenConfig",
    "ModelType",
    "ModelFormat",
    "get_model_config",
    "initialize_model_config"
]