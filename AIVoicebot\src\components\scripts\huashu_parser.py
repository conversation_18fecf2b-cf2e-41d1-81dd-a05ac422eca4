"""
话术文件解析器 (<PERSON><PERSON><PERSON>)

专门用于解析言犀复贷话术Excel文件的解析器，支持特定的话术文件格式。
"""

import pandas as pd
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass

from ..conversation.conversation_context import ConversationScript, ConversationExchange
from ...core.base_component import BaseComponent


@dataclass
class HuashuParserConfig:
    """话术解析器配置"""
    # 支持的列名映射
    flow_node_columns: List[str] = None  # 流程节点列名
    script_columns: List[str] = None     # 话术内容列名
    next_node_columns: List[str] = None  # 下一步节点列名
    
    # 解析选项
    skip_empty_rows: bool = True
    normalize_text: bool = True
    default_confidence: float = 0.8
    
    def __post_init__(self):
        if self.flow_node_columns is None:
            self.flow_node_columns = ['流程节点', '节点', '流程', '步骤']
        if self.script_columns is None:
            self.script_columns = ['新话术', '话术', '原话术', '内容', '回复']
        if self.next_node_columns is None:
            self.next_node_columns = ['下一步节点', '下一步', '下个节点', '跳转']


class HuashuParser(BaseComponent):
    """话术文件解析器"""
    
    def __init__(self, config: HuashuParserConfig, config_manager, logger=None):
        super().__init__("huashu_parser", config_manager, logger)
        self.config = config
        self.parsed_scripts: Dict[str, List[ConversationScript]] = {}
        self.parsing_errors: List[Dict[str, Any]] = []
        
    async def _initialize_impl(self) -> None:
        """初始化解析器"""
        self._log.info("话术解析器初始化完成")
        
    async def _start_impl(self) -> None:
        """启动解析器"""
        self._log.info("话术解析器启动完成")
        
    async def _stop_impl(self) -> None:
        """停止解析器"""
        self._log.info("话术解析器停止完成")
        
    async def _cleanup_impl(self) -> None:
        """清理解析器"""
        self.parsed_scripts.clear()
        self.parsing_errors.clear()
        self._log.info("话术解析器清理完成")
    
    def parse_huashu_file(self, file_path: str) -> List[ConversationScript]:
        """
        解析话术Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            解析出的对话脚本列表
        """
        try:
            self._log.info(f"开始解析话术文件: {file_path}")
            
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            all_scripts = []
            
            # 处理每个工作表
            for sheet_name in excel_file.sheet_names:
                self._log.info(f"解析工作表: {sheet_name}")
                
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    scripts = self._parse_sheet(df, file_path, sheet_name)
                    all_scripts.extend(scripts)
                    
                    self._log.info(f"工作表 {sheet_name} 解析完成，获得 {len(scripts)} 个脚本")
                    
                except Exception as e:
                    self._log.error(f"解析工作表 {sheet_name} 时出错: {e}")
                    self._record_error(file_path, sheet_name, str(e))
            
            self._log.info(f"文件 {file_path} 解析完成，总共获得 {len(all_scripts)} 个脚本")
            return all_scripts
            
        except Exception as e:
            self._log.error(f"解析话术文件 {file_path} 时出错: {e}")
            self._record_error(file_path, "文件级别", str(e))
            return []
    
    def _parse_sheet(self, df: pd.DataFrame, file_path: str, sheet_name: str) -> List[ConversationScript]:
        """解析单个工作表"""
        scripts = []
        
        # 检测列名映射
        column_mapping = self._detect_column_mapping(df.columns)
        
        if not column_mapping:
            self._log.warning(f"工作表 {sheet_name} 中未找到有效的列映射")
            return scripts
        
        self._log.info(f"检测到列映射: {column_mapping}")
        
        # 解析每一行
        for index, row in df.iterrows():
            try:
                script = self._parse_row(row, column_mapping, file_path, sheet_name, index)
                if script:
                    scripts.append(script)
            except Exception as e:
                self._log.error(f"解析第 {index + 1} 行时出错: {e}")
                self._record_error(file_path, f"{sheet_name}:第{index + 1}行", str(e))
        
        return scripts
    
    def _detect_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """检测列名映射"""
        mapping = {}
        
        # 检测流程节点列
        for col in columns:
            if any(node_col in col for node_col in self.config.flow_node_columns):
                mapping['flow_node'] = col
                break
        
        # 检测话术内容列
        for col in columns:
            if any(script_col in col for script_col in self.config.script_columns):
                mapping['script_content'] = col
                break
        
        # 检测下一步节点列
        for col in columns:
            if any(next_col in col for next_col in self.config.next_node_columns):
                mapping['next_node'] = col
                break
        
        return mapping
    
    def _parse_row(self, row: pd.Series, column_mapping: Dict[str, str], 
                   file_path: str, sheet_name: str, row_index: int) -> Optional[ConversationScript]:
        """解析单行数据"""
        
        # 提取流程节点
        flow_node = ""
        if 'flow_node' in column_mapping:
            flow_node = self._extract_text(row, column_mapping['flow_node'])
        
        # 提取话术内容
        script_content = ""
        if 'script_content' in column_mapping:
            script_content = self._extract_text(row, column_mapping['script_content'])
        
        # 提取下一步节点
        next_node = ""
        if 'next_node' in column_mapping:
            next_node = self._extract_text(row, column_mapping['next_node'])
        
        # 跳过空行
        if self.config.skip_empty_rows and not script_content:
            return None
        
        # 创建脚本ID
        script_id = self._generate_script_id(file_path, sheet_name, row_index, flow_node)
        
        # 创建对话交换
        exchanges = []
        if script_content:
            # 将话术内容作为系统回复，流程节点作为触发条件
            exchange = ConversationExchange(
                user_input=flow_node or f"触发节点_{row_index}",
                system_response=script_content,
                intent=flow_node,
                confidence=self.config.default_confidence,
                metadata={
                    'flow_node': flow_node,
                    'next_node': next_node,
                    'sheet_name': sheet_name,
                    'row_index': row_index
                }
            )
            exchanges.append(exchange)
        
        # 创建对话脚本
        script = ConversationScript(
            script_id=script_id,
            scenario=flow_node or f"场景_{row_index}",
            exchanges=exchanges,
            metadata={
                'file_path': file_path,
                'sheet_name': sheet_name,
                'flow_node': flow_node,
                'next_node': next_node,
                'source': 'huashu_parser'
            }
        )
        
        return script
    
    def _extract_text(self, row: pd.Series, column: str) -> str:
        """提取文本字段"""
        if column not in row or pd.isna(row[column]):
            return ""
        
        text = str(row[column]).strip()
        
        if self.config.normalize_text:
            text = self._normalize_text(text)
        
        return text
    
    def _normalize_text(self, text: str) -> str:
        """标准化文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # 移除特殊字符（可根据需要调整）
        # text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        
        return text
    
    def _generate_script_id(self, file_path: str, sheet_name: str, row_index: int, flow_node: str) -> str:
        """生成脚本ID"""
        file_name = Path(file_path).stem
        node_part = flow_node.replace(' ', '_') if flow_node else f"row_{row_index}"
        return f"{file_name}_{sheet_name}_{node_part}_{row_index}"
    
    def _record_error(self, file_path: str, location: str, error_message: str) -> None:
        """记录解析错误"""
        error = {
            'file_path': file_path,
            'location': location,
            'error': error_message,
            'timestamp': datetime.now().isoformat()
        }
        self.parsing_errors.append(error)
    
    def parse_directory(self, directory_path: str) -> Dict[str, List[ConversationScript]]:
        """
        解析目录中的所有话术文件
        
        Args:
            directory_path: 目录路径
            
        Returns:
            文件路径到脚本列表的映射
        """
        results = {}
        directory = Path(directory_path)
        
        if not directory.exists():
            self._log.error(f"目录不存在: {directory_path}")
            return results
        
        # 查找Excel文件
        excel_files = list(directory.glob("*.xlsx")) + list(directory.glob("*.xls"))
        
        # 过滤话术相关文件
        huashu_files = [f for f in excel_files if any(keyword in f.name for keyword in ['话术', '复贷', '言犀', '零犀'])]
        
        self._log.info(f"在目录 {directory_path} 中找到 {len(huashu_files)} 个话术文件")
        
        for file_path in huashu_files:
            try:
                scripts = self.parse_huashu_file(str(file_path))
                results[str(file_path)] = scripts
            except Exception as e:
                self._log.error(f"解析文件 {file_path} 失败: {e}")
                results[str(file_path)] = []
        
        return results
    
    def get_parsing_errors(self) -> List[Dict[str, Any]]:
        """获取解析错误列表"""
        return self.parsing_errors.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取解析统计信息"""
        total_files = len(self.parsed_scripts)
        total_scripts = sum(len(scripts) for scripts in self.parsed_scripts.values())
        total_errors = len(self.parsing_errors)
        
        return {
            'total_files': total_files,
            'total_scripts': total_scripts,
            'total_errors': total_errors,
            'files_parsed': list(self.parsed_scripts.keys()),
            'last_update': datetime.now().isoformat()
        }
