"""
Demo script for conversation engine.

This script demonstrates how to use the conversation engine for orchestrating
AI voice customer service interactions.
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.conversation.conversation_engine import (
    ConversationEngine, ConversationEngineConfig, ConversationState,
    ConversationFlow, IntentType, create_conversation_engine,
    create_banking_conversation_config
)
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_basic_conversation():
    """Demonstrate basic conversation functionality."""
    logger.info("=== Basic Conversation Demo ===")
    
    try:
        # Create conversation engine with mock components
        engine = await create_conversation_engine(
            max_session_duration_minutes=10,
            enable_intent_classification=True,
            config_manager=Mock()
        )
        
        # Start a conversation session
        session_id = await engine.start_session(
            customer_id="demo_customer",
            customer_info={"name": "张先生", "level": "VIP"}
        )
        
        logger.info(f"Started conversation session: {session_id}")
        
        # Simulate conversation turns
        test_inputs = [
            "你好",
            "我想了解贷款产品",
            "利率是多少？",
            "谢谢，再见"
        ]
        
        for user_input in test_inputs:
            logger.info(f"User: {user_input}")
            
            # Process user input
            result = await engine.process_user_input(session_id, user_input)
            
            logger.info(f"Assistant: {result['response_text']}")
            logger.info(f"Intent: {result['intent']}")
            logger.info(f"Confidence: {result['confidence']:.3f}")
            logger.info(f"Processing time: {result['processing_time_ms']:.2f}ms")
            logger.info("---")
        
        # Get session statistics
        session_stats = engine.get_session_statistics(session_id)
        if session_stats:
            logger.info(f"Session Statistics:")
            logger.info(f"  Duration: {session_stats['duration_seconds']:.1f}s")
            logger.info(f"  Turn count: {session_stats['turn_count']}")
            logger.info(f"  Average confidence: {session_stats['average_confidence']:.3f}")
        
        # End session
        await engine.end_session(session_id, "demo_complete")
        logger.info("Session ended")
        
    except Exception as e:
        logger.error(f"Basic conversation demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def main():
    """Run all demos."""
    logger.info("Starting Conversation Engine Demo")
    
    try:
        await demo_basic_conversation()
        
        logger.info("All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())


async def demo_conversation_flows():
    """Demonstrate conversation flow management."""
    logger.info("=== Conversation Flow Demo ===")
    
    try:
        # Create engine with banking configuration
        config = create_banking_conversation_config()
        engine = ConversationEngine(config, config_manager=Mock())
        
        await engine.initialize()
        await engine.start()
        
        # Start session
        session_id = await engine.start_session(
            customer_info={"name": "李女士", "type": "个人客户"}
        )
        
        # Test different conversation flows
        flow_scenarios = [
            {
                "input": "你好，我是李女士",
                "expected_flow": ConversationFlow.GREETING,
                "description": "Initial greeting"
            },
            {
                "input": "我想了解个人贷款产品",
                "expected_flow": ConversationFlow.INFORMATION_GATHERING,
                "description": "Information gathering phase"
            },
            {
                "input": "我要申请50万的贷款",
                "expected_flow": ConversationFlow.SERVICE_DELIVERY,
                "description": "Service delivery phase"
            },
            {
                "input": "好的，谢谢你的帮助",
                "expected_flow": ConversationFlow.CLOSING,
                "description": "Closing phase"
            }
        ]
        
        for scenario in flow_scenarios:
            logger.info(f"\nScenario: {scenario['description']}")
            logger.info(f"Input: {scenario['input']}")
            
            # Process input
            result = await engine.process_user_input(session_id, scenario["input"])
            
            # Check session state
            session = engine.get_session(session_id)
            logger.info(f"Current flow: {session.current_flow.value}")
            logger.info(f"Current state: {session.current_state.value}")
            logger.info(f"Response: {result['response_text']}")
        
        # End session
        await engine.end_session(session_id, "flow_demo_complete")
        
    except Exception as e:
        logger.error(f"Conversation flows demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def demo_intent_classification():
    """Demonstrate intent classification."""
    logger.info("=== Intent Classification Demo ===")
    
    try:
        engine = await create_conversation_engine(
            enable_intent_classification=True,
            config_manager=Mock()
        )
        
        # Test different intent types
        intent_tests = [
            ("你好，早上好", IntentType.GREETING),
            ("我想申请贷款", IntentType.REQUEST),
            ("利率是多少？", IntentType.INQUIRY),
            ("我要投诉你们的服务", IntentType.COMPLAINT),
            ("是的，我确认", IntentType.CONFIRMATION),
            ("谢谢，再见", IntentType.GOODBYE),
            ("随机的文本内容", IntentType.UNKNOWN)
        ]
        
        session_id = await engine.start_session()
        session = engine.get_session(session_id)
        
        logger.info("Testing intent classification:")
        
        for text, expected_intent in intent_tests:
            classified_intent = await engine._classify_intent(text, session)
            
            logger.info(f"  '{text}' -> {classified_intent.value}")
            
            # Note: In a real scenario, we might not get exact matches
            # due to the simplicity of the rule-based classifier
        
        await engine.end_session(session_id, "intent_demo_complete")
        
    except Exception as e:
        logger.error(f"Intent classification demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def demo_session_management():
    """Demonstrate session management features."""
    logger.info("=== Session Management Demo ===")
    
    try:
        engine = await create_conversation_engine(
            max_session_duration_minutes=1,  # Short for demo
            idle_timeout_seconds=30,
            config_manager=Mock()
        )
        
        # Start multiple sessions
        sessions = []
        for i in range(3):
            session_id = await engine.start_session(
                customer_id=f"customer_{i}",
                customer_info={"name": f"客户{i}", "type": "测试"}
            )
            sessions.append(session_id)
            logger.info(f"Started session {i+1}: {session_id}")
        
        logger.info(f"Active sessions: {engine.get_session_count()}")
        
        # Process some inputs
        for i, session_id in enumerate(sessions):
            await engine.process_user_input(session_id, f"你好，我是客户{i}")
        
        # Test session transfer
        transfer_success = await engine.transfer_session(
            sessions[0],
            target_agent="human_agent_001",
            reason="complex_inquiry"
        )
        
        logger.info(f"Session transfer success: {transfer_success}")
        
        # Get overall statistics
        stats = engine.get_conversation_statistics()
        logger.info(f"Engine Statistics:")
        logger.info(f"  Total sessions: {stats['total_sessions']}")
        logger.info(f"  Active sessions: {stats['active_sessions']}")
        logger.info(f"  Total turns: {stats['total_turns']}")
        
        # End all sessions
        for session_id in sessions:
            await engine.end_session(session_id, "demo_complete")
        
        logger.info(f"Remaining active sessions: {engine.get_session_count()}")
        
    except Exception as e:
        logger.error(f"Session management demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def demo_event_handling():
    """Demonstrate event handling capabilities."""
    logger.info("=== Event Handling Demo ===")
    
    try:
        engine = await create_conversation_engine(config_manager=Mock())
        
        # Event tracking
        state_changes = []
        turn_completions = []
        
        # Define event handlers
        def state_change_handler(session, new_state):
            state_changes.append({
                "session_id": session.session_id,
                "state": new_state.value,
                "timestamp": datetime.now()
            })
            logger.info(f"State changed: {session.session_id} -> {new_state.value}")
        
        def turn_complete_handler(session, turn):
            turn_completions.append({
                "session_id": session.session_id,
                "turn_id": turn.turn_id,
                "confidence": turn.confidence_score,
                "timestamp": datetime.now()
            })
            logger.info(f"Turn completed: {turn.turn_id} (confidence: {turn.confidence_score:.3f})")
        
        # Register event handlers
        engine.add_state_change_handler(state_change_handler)
        engine.add_turn_complete_handler(turn_complete_handler)
        
        # Start session and process inputs
        session_id = await engine.start_session()
        
        await engine.process_user_input(session_id, "你好")
        await engine.process_user_input(session_id, "我想了解贷款")
        
        await engine.end_session(session_id, "event_demo_complete")
        
        # Show collected events
        logger.info(f"Collected {len(state_changes)} state changes")
        logger.info(f"Collected {len(turn_completions)} turn completions")
        
    except Exception as e:
        logger.error(f"Event handling demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def demo_error_handling():
    """Demonstrate error handling and recovery."""
    logger.info("=== Error Handling Demo ===")
    
    try:
        engine = await create_conversation_engine(config_manager=Mock())
        
        # Test invalid session operations
        try:
            await engine.process_user_input("invalid_session", "test input")
        except Exception as e:
            logger.info(f"Expected error for invalid session: {e}")
        
        # Test session with empty input
        session_id = await engine.start_session()
        
        result = await engine.process_user_input(session_id, "")
        logger.info(f"Empty input result: {result['response_text']}")
        
        # Test very long input
        long_input = "很长的输入" * 100
        result = await engine.process_user_input(session_id, long_input)
        logger.info(f"Long input processed: {len(result['response_text'])} chars response")
        
        await engine.end_session(session_id, "error_demo_complete")
        
    except Exception as e:
        logger.error(f"Error handling demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def demo_performance_monitoring():
    """Demonstrate performance monitoring."""
    logger.info("=== Performance Monitoring Demo ===")
    
    try:
        engine = await create_conversation_engine(
            max_processing_time_seconds=2.0,
            config_manager=Mock()
        )
        
        # Start session
        session_id = await engine.start_session()
        
        # Process multiple inputs to generate statistics
        test_inputs = [
            "你好",
            "我想了解个人贷款",
            "需要什么条件？",
            "利率是多少？",
            "如何申请？",
            "谢谢"
        ]
        
        processing_times = []
        confidences = []
        
        for user_input in test_inputs:
            result = await engine.process_user_input(session_id, user_input)
            
            processing_times.append(result['processing_time_ms'])
            confidences.append(result['confidence'])
            
            logger.info(f"Processed: '{user_input}' in {result['processing_time_ms']:.2f}ms")
        
        # Calculate performance metrics
        avg_processing_time = sum(processing_times) / len(processing_times)
        avg_confidence = sum(confidences) / len(confidences)
        max_processing_time = max(processing_times)
        min_processing_time = min(processing_times)
        
        logger.info(f"\nPerformance Metrics:")
        logger.info(f"  Average processing time: {avg_processing_time:.2f}ms")
        logger.info(f"  Max processing time: {max_processing_time:.2f}ms")
        logger.info(f"  Min processing time: {min_processing_time:.2f}ms")
        logger.info(f"  Average confidence: {avg_confidence:.3f}")
        
        # Get engine statistics
        engine_stats = engine.get_conversation_statistics()
        logger.info(f"\nEngine Statistics:")
        logger.info(f"  Total sessions: {engine_stats['total_sessions']}")
        logger.info(f"  Total turns: {engine_stats['total_turns']}")
        logger.info(f"  Average turns per session: {engine_stats['average_turns_per_session']:.1f}")
        
        await engine.end_session(session_id, "performance_demo_complete")
        
    except Exception as e:
        logger.error(f"Performance monitoring demo failed: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.stop()
            await engine.cleanup()


async def main():
    """Run all demos."""
    logger.info("Starting Conversation Engine Demo")
    
    try:
        await demo_basic_conversation()
        await demo_conversation_flows()
        await demo_intent_classification()
        await demo_session_management()
        await demo_event_handling()
        await demo_error_handling()
        await demo_performance_monitoring()
        
        logger.info("All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())