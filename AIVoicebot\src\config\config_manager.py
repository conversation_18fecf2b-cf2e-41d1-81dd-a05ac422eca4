"""
Configuration Manager for AI Voice Customer Service System

This module provides centralized configuration management with support for:
- Environment-specific settings
- Configuration loading from files and environment variables
- Configuration validation and default values
- Hot-reloading of configuration changes
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field
import logging
from enum import Enum

logger = logging.getLogger(__name__)


class Environment(Enum):
    """Supported deployment environments"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class AudioConfig:
    """Audio processing configuration"""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    buffer_duration: float = 5.0
    format: str = "wav"
    
    # VAD settings
    vad_threshold: float = 0.5
    vad_min_speech_duration: float = 0.1
    vad_max_silence_duration: float = 2.0
    
    # Audio quality settings
    noise_reduction: bool = True
    auto_gain_control: bool = True


@dataclass
class LLMConfig:
    """Language model configuration"""
    api_key: str = ""
    model_name: str = "qwen-turbo"
    base_url: str = "https://dashscope.aliyuncs.com/api/v1"
    max_tokens: int = 1000
    temperature: float = 0.7
    timeout: int = 30
    
    # Retry settings
    max_retries: int = 3
    retry_delay: float = 1.0
    backoff_factor: float = 2.0


@dataclass
class TTSConfig:
    """Text-to-speech configuration"""
    voice: str = "zh-CN-XiaoxiaoNeural"
    rate: str = "+0%"
    volume: str = "+0%"
    pitch: str = "+0Hz"
    
    # Audio output settings
    output_format: str = "audio-16khz-32kbitrate-mono-mp3"
    quality: str = "high"
    
    # Performance settings
    cache_enabled: bool = True
    cache_size: int = 100


@dataclass
class ScriptConfig:
    """Conversation script configuration"""
    excel_path: str = "data/scripts/conversation_scripts.xlsx"
    hot_reload: bool = True
    reload_interval: float = 5.0
    
    # Matching settings
    fuzzy_threshold: float = 0.8
    max_suggestions: int = 5
    fallback_enabled: bool = True


@dataclass
class TelephonyConfig:
    """Telephony system configuration"""
    provider: str = "asterisk"
    server_host: str = "localhost"
    server_port: int = 5060
    
    # Call settings
    max_call_duration: int = 1800  # 30 minutes
    call_timeout: int = 30
    dtmf_enabled: bool = True
    
    # Audio settings
    codec: str = "ulaw"
    rtp_port_range: tuple = (10000, 20000)


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/aivoicebot.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    
    # Conversation logging
    conversation_log_enabled: bool = True
    conversation_log_path: str = "logs/conversations"
    include_audio: bool = False


@dataclass
class PerformanceConfig:
    """Performance and monitoring configuration"""
    max_concurrent_calls: int = 10
    memory_limit_mb: int = 2048
    cpu_limit_percent: float = 80.0
    
    # Monitoring settings
    metrics_enabled: bool = True
    metrics_port: int = 8080
    health_check_interval: float = 30.0
    
    # Optimization settings
    model_cache_size: int = 3
    audio_buffer_optimization: bool = True


class ConfigManager:
    """
    Centralized configuration manager with environment support
    
    Features:
    - Environment-specific configuration loading
    - Configuration validation and defaults
    - Hot-reloading capability
    - Environment variable override support
    """
    
    def __init__(self, 
                 config_dir: str = "config",
                 environment: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_dir: Directory containing configuration files
            environment: Target environment (development, testing, staging, production)
        """
        self.config_dir = Path(config_dir)
        self.environment = Environment(environment or os.getenv("AIVOICE_ENV", "development"))
        
        # Configuration storage
        self._config: Dict[str, Any] = {}
        self._file_timestamps: Dict[str, float] = {}
        
        # Configuration objects
        self.audio: Optional[AudioConfig] = None
        self.llm: Optional[LLMConfig] = None
        self.tts: Optional[TTSConfig] = None
        self.script: Optional[ScriptConfig] = None
        self.telephony: Optional[TelephonyConfig] = None
        self.logging: Optional[LoggingConfig] = None
        self.performance: Optional[PerformanceConfig] = None
        
        # Load initial configuration
        self.load_configuration()
        
        logger.info(f"ConfigManager initialized for environment: {self.environment.value}")
    
    def load_configuration(self) -> None:
        """Load configuration from files and environment variables"""
        try:
            # Load base configuration
            base_config = self._load_config_file("base.yaml")
            
            # Load environment-specific configuration
            env_config = self._load_config_file(f"{self.environment.value}.yaml")
            
            # Merge configurations (environment overrides base)
            self._config = self._merge_configs(base_config, env_config)
            
            # Apply environment variable overrides
            self._apply_env_overrides()
            
            # Create configuration objects
            self._create_config_objects()
            
            # Validate configuration
            self._validate_configuration()
            
            logger.info("Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Load default configuration as fallback
            self._load_default_configuration()
    
    def _load_config_file(self, filename: str) -> Dict[str, Any]:
        """Load configuration from YAML or JSON file"""
        config_path = self.config_dir / filename
        
        if not config_path.exists():
            logger.warning(f"Configuration file not found: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    config = yaml.safe_load(f) or {}
                elif filename.endswith('.json'):
                    config = json.load(f) or {}
                else:
                    logger.warning(f"Unsupported config file format: {filename}")
                    return {}
            
            # Store file timestamp for hot-reloading
            self._file_timestamps[str(config_path)] = config_path.stat().st_mtime
            
            logger.debug(f"Loaded configuration from: {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to load config file {config_path}: {e}")
            return {}
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge configuration dictionaries"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides to configuration"""
        env_mappings = {
            # LLM configuration
            "QWEN_API_KEY": ("llm", "api_key"),
            "QWEN_MODEL": ("llm", "model_name"),
            "QWEN_BASE_URL": ("llm", "base_url"),
            
            # Audio configuration
            "AUDIO_SAMPLE_RATE": ("audio", "sample_rate"),
            "VAD_THRESHOLD": ("audio", "vad_threshold"),
            
            # TTS configuration
            "TTS_VOICE": ("tts", "voice"),
            "TTS_RATE": ("tts", "rate"),
            
            # Script configuration
            "SCRIPT_PATH": ("script", "excel_path"),
            "SCRIPT_HOT_RELOAD": ("script", "hot_reload"),
            
            # Telephony configuration
            "TELEPHONY_HOST": ("telephony", "server_host"),
            "TELEPHONY_PORT": ("telephony", "server_port"),
            
            # Logging configuration
            "LOG_LEVEL": ("logging", "level"),
            "LOG_PATH": ("logging", "file_path"),
            
            # Performance configuration
            "MAX_CONCURRENT_CALLS": ("performance", "max_concurrent_calls"),
            "MEMORY_LIMIT_MB": ("performance", "memory_limit_mb"),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Ensure section exists
                if section not in self._config:
                    self._config[section] = {}
                
                # Convert value to appropriate type
                self._config[section][key] = self._convert_env_value(value)
                logger.debug(f"Applied environment override: {env_var} -> {section}.{key}")  
  
    def _convert_env_value(self, value: str) -> Union[str, int, float, bool]:
        """Convert environment variable string to appropriate type"""
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Integer conversion
        try:
            if '.' not in value:
                return int(value)
        except ValueError:
            pass
        
        # Float conversion
        try:
            return float(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    def _create_config_objects(self) -> None:
        """Create typed configuration objects from loaded configuration"""
        try:
            # Audio configuration
            audio_config = self._config.get("audio", {})
            self.audio = AudioConfig(**audio_config)
            
            # LLM configuration
            llm_config = self._config.get("llm", {})
            self.llm = LLMConfig(**llm_config)
            
            # TTS configuration
            tts_config = self._config.get("tts", {})
            self.tts = TTSConfig(**tts_config)
            
            # Script configuration
            script_config = self._config.get("script", {})
            self.script = ScriptConfig(**script_config)
            
            # Telephony configuration
            telephony_config = self._config.get("telephony", {})
            self.telephony = TelephonyConfig(**telephony_config)
            
            # Logging configuration
            logging_config = self._config.get("logging", {})
            self.logging = LoggingConfig(**logging_config)
            
            # Performance configuration
            performance_config = self._config.get("performance", {})
            self.performance = PerformanceConfig(**performance_config)
            
        except Exception as e:
            logger.error(f"Failed to create configuration objects: {e}")
            raise
    
    def _validate_configuration(self) -> None:
        """Validate configuration values and requirements"""
        errors = []
        
        # Validate LLM configuration
        if not self.llm.api_key:
            errors.append("LLM API key is required")
        
        # Validate audio configuration
        if self.audio.sample_rate not in [8000, 16000, 22050, 44100, 48000]:
            errors.append(f"Invalid audio sample rate: {self.audio.sample_rate}")
        
        if self.audio.channels not in [1, 2]:
            errors.append(f"Invalid audio channels: {self.audio.channels}")
        
        # Validate TTS configuration
        if not self.tts.voice:
            errors.append("TTS voice is required")
        
        # Validate script configuration
        script_path = Path(self.script.excel_path)
        if not script_path.exists() and self.environment != Environment.TESTING:
            logger.warning(f"Script file not found: {script_path}")
        
        # Validate telephony configuration
        if self.telephony.server_port < 1 or self.telephony.server_port > 65535:
            errors.append(f"Invalid telephony port: {self.telephony.server_port}")
        
        # Validate performance limits
        if self.performance.max_concurrent_calls < 1:
            errors.append("Max concurrent calls must be at least 1")
        
        if self.performance.memory_limit_mb < 512:
            errors.append("Memory limit must be at least 512MB")
        
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("Configuration validation passed")
    
    def _load_default_configuration(self) -> None:
        """Load default configuration as fallback"""
        logger.info("Loading default configuration")
        
        self.audio = AudioConfig()
        self.llm = LLMConfig()
        self.tts = TTSConfig()
        self.script = ScriptConfig()
        self.telephony = TelephonyConfig()
        self.logging = LoggingConfig()
        self.performance = PerformanceConfig()
    
    def reload_configuration(self) -> bool:
        """
        Reload configuration from files
        
        Returns:
            bool: True if configuration was reloaded, False if no changes detected
        """
        try:
            # Check if any config files have been modified
            files_changed = False
            
            for file_path, last_timestamp in self._file_timestamps.items():
                current_timestamp = Path(file_path).stat().st_mtime
                if current_timestamp > last_timestamp:
                    files_changed = True
                    break
            
            if not files_changed:
                return False
            
            logger.info("Configuration files changed, reloading...")
            self.load_configuration()
            return True
            
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
            return False

    def get_config(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key (implements IConfigManager interface)

        Args:
            key: Configuration key (can be dot-separated path)
            default: Default value if key not found

        Returns:
            Configuration value or default
        """
        return self.get_config_value(key, default)

    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value by dot-separated key path
        
        Args:
            key_path: Dot-separated path (e.g., "audio.sample_rate")
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        try:
            keys = key_path.split('.')
            value = self._config
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set_config_value(self, key_path: str, value: Any) -> None:
        """
        Set configuration value by dot-separated key path
        
        Args:
            key_path: Dot-separated path (e.g., "audio.sample_rate")
            value: Value to set
        """
        keys = key_path.split('.')
        config = self._config
        
        # Navigate to parent dictionary
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        
        # Recreate configuration objects
        self._create_config_objects()
        
        logger.debug(f"Configuration updated: {key_path} = {value}")
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get current environment information"""
        return {
            "environment": self.environment.value,
            "config_dir": str(self.config_dir),
            "loaded_files": list(self._file_timestamps.keys()),
            "python_version": os.sys.version,
            "platform": os.name,
        }
    
    def export_config(self, format: str = "yaml") -> str:
        """
        Export current configuration to string
        
        Args:
            format: Export format ("yaml" or "json")
            
        Returns:
            Configuration as formatted string
        """
        if format.lower() == "json":
            return json.dumps(self._config, indent=2, ensure_ascii=False)
        elif format.lower() == "yaml":
            return yaml.dump(self._config, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def create_sample_config_files(self) -> None:
        """Create sample configuration files for different environments"""
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Base configuration
        base_config = {
            "audio": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_size": 1024,
                "buffer_duration": 5.0,
                "format": "wav",
                "vad_threshold": 0.5,
                "vad_min_speech_duration": 0.1,
                "vad_max_silence_duration": 2.0,
                "noise_reduction": True,
                "auto_gain_control": True
            },
            "llm": {
                "api_key": "${QWEN_API_KEY}",
                "model_name": "qwen-turbo",
                "base_url": "https://dashscope.aliyuncs.com/api/v1",
                "max_tokens": 1000,
                "temperature": 0.7,
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 1.0,
                "backoff_factor": 2.0
            },
            "tts": {
                "voice": "zh-CN-XiaoxiaoNeural",
                "rate": "+0%",
                "volume": "+0%",
                "pitch": "+0Hz",
                "output_format": "audio-16khz-32kbitrate-mono-mp3",
                "quality": "high",
                "cache_enabled": True,
                "cache_size": 100
            },
            "script": {
                "excel_path": "data/scripts/conversation_scripts.xlsx",
                "hot_reload": True,
                "reload_interval": 5.0,
                "fuzzy_threshold": 0.8,
                "max_suggestions": 5,
                "fallback_enabled": True
            },
            "telephony": {
                "provider": "asterisk",
                "server_host": "localhost",
                "server_port": 5060,
                "max_call_duration": 1800,
                "call_timeout": 30,
                "dtmf_enabled": True,
                "codec": "ulaw",
                "rtp_port_range": [10000, 20000]
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_path": "logs/aivoicebot.log",
                "max_file_size": 10485760,
                "backup_count": 5,
                "conversation_log_enabled": True,
                "conversation_log_path": "logs/conversations",
                "include_audio": False
            },
            "performance": {
                "max_concurrent_calls": 10,
                "memory_limit_mb": 2048,
                "cpu_limit_percent": 80.0,
                "metrics_enabled": True,
                "metrics_port": 8080,
                "health_check_interval": 30.0,
                "model_cache_size": 3,
                "audio_buffer_optimization": True
            }
        }
        
        # Write base configuration
        with open(self.config_dir / "base.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(base_config, f, default_flow_style=False, allow_unicode=True)
        
        # Development environment
        dev_config = {
            "logging": {
                "level": "DEBUG"
            },
            "performance": {
                "max_concurrent_calls": 2,
                "memory_limit_mb": 1024
            }
        }
        
        with open(self.config_dir / "development.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(dev_config, f, default_flow_style=False, allow_unicode=True)
        
        # Production environment
        prod_config = {
            "logging": {
                "level": "WARNING",
                "include_audio": False
            },
            "performance": {
                "max_concurrent_calls": 50,
                "memory_limit_mb": 4096,
                "metrics_enabled": True
            },
            "audio": {
                "vad_threshold": 0.6
            }
        }
        
        with open(self.config_dir / "production.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(prod_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"Sample configuration files created in: {self.config_dir}")


# Global configuration manager instance
config_manager: Optional[ConfigManager] = None


def get_config() -> ConfigManager:
    """Get global configuration manager instance"""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager


def initialize_config(config_dir: str = "config", environment: Optional[str] = None) -> ConfigManager:
    """
    Initialize global configuration manager
    
    Args:
        config_dir: Configuration directory path
        environment: Target environment
        
    Returns:
        ConfigManager instance
    """
    global config_manager
    config_manager = ConfigManager(config_dir, environment)
    return config_manager