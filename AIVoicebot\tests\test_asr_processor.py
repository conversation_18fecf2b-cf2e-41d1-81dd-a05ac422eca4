"""
Tests for ASR Processing Pipeline

This module tests the ASR processing functionality including:
- ASRProcessor workflow management
- TranscriptionBuffer result handling
- Error handling and retry logic
"""

import pytest
import numpy as np
import asyncio
from unittest.mock import patch, MagicMock

from src.components.asr.asr_processor import (
    ASRProcessor,
    TranscriptionBuffer,
    TranscriptionResult,
    ASRProcessorConfig,
    TranscriptionStatus,
    ASRError,
    create_asr_processor,
    process_audio_batch
)


class TestTranscriptionResult:
    """Test cases for TranscriptionResult"""
    
    def test_transcription_result_creation(self):
        """Test TranscriptionResult creation and properties"""
        result = TranscriptionResult(
            text="Hello world",
            confidence=0.8,
            status=TranscriptionStatus.FINAL,
            timestamp=1234567890.0,
            duration=2.5
        )
        
        assert result.text == "Hello world"
        assert result.confidence == 0.8
        assert result.status == TranscriptionStatus.FINAL
        assert result.timestamp == 1234567890.0
        assert result.duration == 2.5
        assert result.is_final is True
        assert result.is_partial is False
        assert result.has_error is False
    
    def test_transcription_result_status_properties(self):
        """Test status property methods"""
        # Test partial result
        partial_result = TranscriptionResult(
            text="Partial text",
            confidence=0.6,
            status=TranscriptionStatus.PARTIAL,
            timestamp=0.0
        )
        
        assert partial_result.is_partial is True
        assert partial_result.is_final is False
        assert partial_result.has_error is False
        
        # Test error result
        error_result = TranscriptionResult(
            text="",
            confidence=0.0,
            status=TranscriptionStatus.ERROR,
            timestamp=0.0
        )
        
        assert error_result.has_error is True
        assert error_result.is_final is False
        assert error_result.is_partial is False


class TestASRProcessorConfig:
    """Test cases for ASRProcessorConfig"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = ASRProcessorConfig()
        
        assert config.sample_rate == 16000
        assert config.chunk_size == 1024
        assert config.min_audio_length == 0.1
        assert config.max_audio_length == 30.0
        assert config.min_confidence == 0.3
        assert config.processing_timeout == 10.0
        assert config.max_retries == 3
        assert config.enable_partial_results is True
    
    def test_custom_config(self):
        """Test custom configuration values"""
        config = ASRProcessorConfig(
            sample_rate=8000,
            min_confidence=0.5,
            max_retries=5,
            enable_partial_results=False
        )
        
        assert config.sample_rate == 8000
        assert config.min_confidence == 0.5
        assert config.max_retries == 5
        assert config.enable_partial_results is False


class TestTranscriptionBuffer:
    """Test cases for TranscriptionBuffer"""
    
    def setup_method(self):
        """Set up test environment"""
        self.config = ASRProcessorConfig(max_buffer_size=5)
        self.buffer = TranscriptionBuffer(self.config)
    
    def test_buffer_initialization(self):
        """Test TranscriptionBuffer initialization"""
        assert len(self.buffer.results) == 0
        assert self.buffer.current_partial is None
        assert self.buffer.config == self.config
    
    def test_add_final_result(self):
        """Test adding final transcription result"""
        result = TranscriptionResult(
            text="Final result",
            confidence=0.8,
            status=TranscriptionStatus.FINAL,
            timestamp=0.0
        )
        
        success = self.buffer.add_result(result)
        assert success is True
        assert len(self.buffer.results) == 1
        assert self.buffer.results[0] == result
        assert self.buffer.current_partial is None
    
    def test_add_partial_result(self):
        """Test adding partial transcription result"""
        result = TranscriptionResult(
            text="Partial result",
            confidence=0.6,
            status=TranscriptionStatus.PARTIAL,
            timestamp=0.0
        )
        
        success = self.buffer.add_result(result)
        assert success is True
        assert len(self.buffer.results) == 0  # Partial results don't go to main buffer
        assert self.buffer.current_partial == result
    
    def test_partial_to_final_transition(self):
        """Test transition from partial to final result"""
        # Add partial result
        partial_result = TranscriptionResult(
            text="Partial",
            confidence=0.6,
            status=TranscriptionStatus.PARTIAL,
            timestamp=0.0
        )
        self.buffer.add_result(partial_result)
        assert self.buffer.current_partial == partial_result
        
        # Add final result
        final_result = TranscriptionResult(
            text="Final complete text",
            confidence=0.8,
            status=TranscriptionStatus.FINAL,
            timestamp=0.0
        )
        self.buffer.add_result(final_result)
        
        assert len(self.buffer.results) == 1
        assert self.buffer.results[0] == final_result
        assert self.buffer.current_partial is None  # Should be cleared
    
    def test_buffer_size_limit(self):
        """Test buffer size limiting"""
        # Add more results than buffer size
        for i in range(10):
            result = TranscriptionResult(
                text=f"Result {i}",
                confidence=0.8,
                status=TranscriptionStatus.FINAL,
                timestamp=float(i)
            )
            self.buffer.add_result(result)
        
        # Should only keep last 5 results (max_buffer_size)
        assert len(self.buffer.results) == 5
        assert self.buffer.results[0].text == "Result 5"
        assert self.buffer.results[-1].text == "Result 9"
    
    def test_get_latest_result(self):
        """Test getting latest result"""
        # No results initially
        assert self.buffer.get_latest_result() is None
        
        # Add final result
        final_result = TranscriptionResult(
            text="Final",
            confidence=0.8,
            status=TranscriptionStatus.FINAL,
            timestamp=0.0
        )
        self.buffer.add_result(final_result)
        assert self.buffer.get_latest_result() == final_result
        
        # Add partial result (should become latest)
        partial_result = TranscriptionResult(
            text="Partial",
            confidence=0.6,
            status=TranscriptionStatus.PARTIAL,
            timestamp=1.0
        )
        self.buffer.add_result(partial_result)
        assert self.buffer.get_latest_result() == partial_result
    
    def test_get_final_results(self):
        """Test getting final results only"""
        # Add mix of final and error results
        results = [
            TranscriptionResult("Final 1", 0.8, TranscriptionStatus.FINAL, 0.0),
            TranscriptionResult("", 0.0, TranscriptionStatus.ERROR, 1.0),
            TranscriptionResult("Final 2", 0.7, TranscriptionStatus.FINAL, 2.0),
        ]
        
        for result in results:
            self.buffer.add_result(result)
        
        final_results = self.buffer.get_final_results()
        assert len(final_results) == 2
        assert final_results[0].text == "Final 1"
        assert final_results[1].text == "Final 2"
    
    def test_clear_buffer(self):
        """Test clearing buffer"""
        # Add some results
        self.buffer.add_result(TranscriptionResult("Final", 0.8, TranscriptionStatus.FINAL, 0.0))
        self.buffer.add_result(TranscriptionResult("Partial", 0.6, TranscriptionStatus.PARTIAL, 1.0))
        
        assert len(self.buffer.results) == 1
        assert self.buffer.current_partial is not None
        
        # Clear buffer
        self.buffer.clear_buffer()
        
        assert len(self.buffer.results) == 0
        assert self.buffer.current_partial is None
    
    def test_get_buffer_stats(self):
        """Test buffer statistics"""
        # Add various types of results
        results = [
            TranscriptionResult("Final 1", 0.8, TranscriptionStatus.FINAL, 0.0),
            TranscriptionResult("Final 2", 0.6, TranscriptionStatus.FINAL, 1.0),
            TranscriptionResult("", 0.0, TranscriptionStatus.ERROR, 2.0),
        ]
        
        for result in results:
            self.buffer.add_result(result)
        
        # Add partial result
        self.buffer.add_result(TranscriptionResult("Partial", 0.5, TranscriptionStatus.PARTIAL, 3.0))
        
        stats = self.buffer.get_buffer_stats()
        
        assert stats["total_results"] == 3
        assert stats["final_results"] == 2
        assert stats["error_results"] == 1
        assert stats["has_partial"] is True
        assert stats["average_confidence"] == 0.7  # (0.8 + 0.6) / 2
        assert stats["success_rate"] == 2/3  # 2 final out of 3 total


class TestASRProcessor:
    """Test cases for ASRProcessor"""
    
    def setup_method(self):
        """Set up test environment"""
        self.config = ASRProcessorConfig(
            min_audio_length=0.1,
            max_audio_length=5.0,
            min_confidence=0.5
        )
        self.processor = ASRProcessor(self.config)
    
    def test_processor_initialization(self):
        """Test ASRProcessor initialization"""
        assert self.processor.config == self.config
        assert self.processor.buffer is not None
        assert self.processor.is_processing is False
        assert self.processor.total_processed == 0
        assert self.processor.total_errors == 0
    
    def test_preprocess_audio_valid(self):
        """Test audio preprocessing with valid audio"""
        # Create valid audio (1 second at 16kHz)
        audio_data = np.random.randn(16000).astype(np.float32) * 0.5
        
        processed = self.processor.preprocess_audio(audio_data)
        
        assert processed is not None
        assert processed.dtype == np.float32
        assert len(processed) == len(audio_data)
        assert np.max(np.abs(processed)) <= 1.0  # Should be normalized
    
    def test_preprocess_audio_too_short(self):
        """Test audio preprocessing with too short audio"""
        # Create very short audio (0.05 seconds)
        short_audio = np.random.randn(800).astype(np.float32)
        
        processed = self.processor.preprocess_audio(short_audio)
        
        assert processed is None  # Should reject short audio
    
    def test_preprocess_audio_too_long(self):
        """Test audio preprocessing with too long audio"""
        # Create very long audio (10 seconds, longer than max_audio_length=5s)
        long_audio = np.random.randn(160000).astype(np.float32)
        
        processed = self.processor.preprocess_audio(long_audio)
        
        assert processed is not None
        # Should be truncated to max length
        expected_length = int(self.config.max_audio_length * self.config.sample_rate)
        assert len(processed) == expected_length
    
    def test_preprocess_audio_silent(self):
        """Test audio preprocessing with silent audio"""
        # Create silent audio
        silent_audio = np.zeros(16000, dtype=np.float32)
        
        processed = self.processor.preprocess_audio(silent_audio)
        
        assert processed is None  # Should reject silent audio
    
    def test_process_audio_sync(self):
        """Test synchronous audio processing"""
        # Create valid test audio
        audio_data = np.random.randn(16000).astype(np.float32) * 0.5
        
        result = self.processor.process_audio(audio_data)
        
        assert isinstance(result, TranscriptionResult)
        assert result.text != ""  # Should have some text (mock result)
        assert result.confidence > 0
        assert result.status in [TranscriptionStatus.FINAL, TranscriptionStatus.ERROR]
    
    def test_process_audio_with_retry_success(self):
        """Test audio processing with retry logic - success case"""
        audio_data = np.random.randn(16000).astype(np.float32) * 0.5
        
        result = self.processor.process_audio_with_retry(audio_data)
        
        assert isinstance(result, TranscriptionResult)
        # Mock should return successful result
        assert result.confidence >= 0.8  # Mock returns high confidence
    
    def test_process_and_buffer(self):
        """Test processing audio and adding to buffer"""
        audio_data = np.random.randn(16000).astype(np.float32) * 0.5
        
        # Add callback to track results
        callback_results = []
        self.processor.add_result_callback(lambda r: callback_results.append(r))
        
        result = self.processor.process_and_buffer(audio_data)
        
        assert isinstance(result, TranscriptionResult)
        
        # Check that result was added to buffer
        latest = self.processor.buffer.get_latest_result()
        assert latest == result
        
        # Check that callback was called
        assert len(callback_results) == 1
        assert callback_results[0] == result
    
    def test_get_processing_stats(self):
        """Test processing statistics"""
        # Process some audio to generate stats
        audio_data = np.random.randn(16000).astype(np.float32) * 0.5
        
        self.processor.process_and_buffer(audio_data)
        self.processor.process_and_buffer(audio_data)
        
        stats = self.processor.get_processing_stats()
        
        assert "total_processed" in stats
        assert "total_errors" in stats
        assert "success_rate" in stats
        assert "average_processing_time" in stats
        assert "is_processing" in stats
        
        assert stats["total_processed"] >= 2
        assert stats["success_rate"] >= 0.0
        assert stats["success_rate"] <= 1.0
    
    def test_clear_stats(self):
        """Test clearing processing statistics"""
        # Process some audio
        audio_data = np.random.randn(16000).astype(np.float32) * 0.5
        self.processor.process_and_buffer(audio_data)
        
        assert self.processor.total_processed > 0
        
        # Clear stats
        self.processor.clear_stats()
        
        assert self.processor.total_processed == 0
        assert self.processor.total_errors == 0
        assert len(self.processor.processing_times) == 0
        assert len(self.processor.buffer.results) == 0


class TestConvenienceFunctions:
    """Test cases for convenience functions"""
    
    def test_create_asr_processor(self):
        """Test create_asr_processor function"""
        processor = create_asr_processor(
            sample_rate=8000,
            min_confidence=0.7,
            enable_partial_results=False,
            max_retries=5
        )
        
        assert processor.config.sample_rate == 8000
        assert processor.config.min_confidence == 0.7
        assert processor.config.enable_partial_results is False
        assert processor.config.max_retries == 5
    
    def test_process_audio_batch(self):
        """Test batch audio processing"""
        processor = create_asr_processor()
        
        # Create multiple audio segments
        audio_segments = [
            np.random.randn(16000).astype(np.float32) * 0.5,
            np.random.randn(8000).astype(np.float32) * 0.3,
            np.random.randn(24000).astype(np.float32) * 0.4,
        ]
        
        results = process_audio_batch(audio_segments, processor)
        
        assert len(results) == len(audio_segments)
        
        for result in results:
            assert isinstance(result, TranscriptionResult)
        
        # Check that results were added to processor buffer
        buffer_results = processor.buffer.get_all_results()
        assert len(buffer_results) == len(audio_segments)


class TestErrorHandling:
    """Test cases for error handling"""
    
    def test_invalid_audio_handling(self):
        """Test handling of invalid audio data"""
        processor = create_asr_processor()
        
        # Test with None
        result = processor.process_audio(None)
        assert result.has_error
        
        # Test with empty array
        empty_audio = np.array([])
        result = processor.process_audio(empty_audio)
        assert result.has_error
        
        # Test with wrong dtype
        int_audio = np.random.randint(-1000, 1000, 16000, dtype=np.int16)
        result = processor.process_audio(int_audio)
        # Should handle dtype conversion gracefully
        assert isinstance(result, TranscriptionResult)


if __name__ == "__main__":
    pytest.main([__file__])