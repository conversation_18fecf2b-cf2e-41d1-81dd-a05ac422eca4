"""
Configuration Manager Demo

This demo shows how to use the ConfigManager for:
- Loading environment-specific configurations
- Accessing configuration values
- Hot-reloading configuration changes
- Environment variable overrides
"""

import os
import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from config.config_manager import ConfigManager, initialize_config, get_config


def demo_basic_usage():
    """Demonstrate basic configuration manager usage"""
    print("=== Basic Configuration Manager Usage ===")
    
    # Initialize configuration manager
    config = initialize_config(
        config_dir="config",
        environment="development"
    )
    
    print(f"Environment: {config.environment.value}")
    print(f"Config directory: {config.config_dir}")
    
    # Access configuration values
    print(f"\nAudio Configuration:")
    print(f"  Sample Rate: {config.audio.sample_rate}")
    print(f"  Channels: {config.audio.channels}")
    print(f"  VAD Threshold: {config.audio.vad_threshold}")
    
    print(f"\nLLM Configuration:")
    print(f"  Model: {config.llm.model_name}")
    print(f"  Max Tokens: {config.llm.max_tokens}")
    print(f"  Temperature: {config.llm.temperature}")
    print(f"  API Key: {'***' if config.llm.api_key else 'Not set'}")
    
    print(f"\nTTS Configuration:")
    print(f"  Voice: {config.tts.voice}")
    print(f"  Rate: {config.tts.rate}")
    print(f"  Cache Enabled: {config.tts.cache_enabled}")


def demo_environment_variables():
    """Demonstrate environment variable overrides"""
    print("\n=== Environment Variable Overrides ===")
    
    # Set environment variables
    os.environ["QWEN_API_KEY"] = "demo_api_key_12345"
    os.environ["AUDIO_SAMPLE_RATE"] = "22050"
    os.environ["VAD_THRESHOLD"] = "0.8"
    os.environ["TTS_VOICE"] = "zh-CN-YunxiNeural"
    
    # Create new config manager to pick up env vars
    config = ConfigManager(
        config_dir="config",
        environment="development"
    )
    
    print("Environment variables set:")
    print(f"  QWEN_API_KEY: {os.environ.get('QWEN_API_KEY')}")
    print(f"  AUDIO_SAMPLE_RATE: {os.environ.get('AUDIO_SAMPLE_RATE')}")
    print(f"  VAD_THRESHOLD: {os.environ.get('VAD_THRESHOLD')}")
    print(f"  TTS_VOICE: {os.environ.get('TTS_VOICE')}")
    
    print("\nConfiguration values (with env overrides):")
    print(f"  API Key: {config.llm.api_key}")
    print(f"  Sample Rate: {config.audio.sample_rate}")
    print(f"  VAD Threshold: {config.audio.vad_threshold}")
    print(f"  TTS Voice: {config.tts.voice}")
    
    # Clean up environment variables
    for key in ["QWEN_API_KEY", "AUDIO_SAMPLE_RATE", "VAD_THRESHOLD", "TTS_VOICE"]:
        os.environ.pop(key, None)


def demo_config_access_methods():
    """Demonstrate different ways to access configuration"""
    print("\n=== Configuration Access Methods ===")
    
    config = get_config()
    
    # Direct object access
    print("Direct object access:")
    print(f"  config.audio.sample_rate = {config.audio.sample_rate}")
    print(f"  config.llm.model_name = {config.llm.model_name}")
    
    # Key path access
    print("\nKey path access:")
    print(f"  get_config_value('audio.sample_rate') = {config.get_config_value('audio.sample_rate')}")
    print(f"  get_config_value('llm.model_name') = {config.get_config_value('llm.model_name')}")
    print(f"  get_config_value('nonexistent.key', 'default') = {config.get_config_value('nonexistent.key', 'default')}")
    
    # Setting values
    print("\nSetting configuration values:")
    original_rate = config.audio.sample_rate
    config.set_config_value("audio.sample_rate", 48000)
    print(f"  Original sample rate: {original_rate}")
    print(f"  New sample rate: {config.audio.sample_rate}")
    
    # Reset to original
    config.set_config_value("audio.sample_rate", original_rate)
    print(f"  Reset sample rate: {config.audio.sample_rate}")


def demo_environment_comparison():
    """Demonstrate different environment configurations"""
    print("\n=== Environment Configuration Comparison ===")
    
    environments = ["development", "production"]
    
    for env in environments:
        print(f"\n{env.upper()} Environment:")
        config = ConfigManager(
            config_dir="config",
            environment=env
        )
        
        print(f"  Logging Level: {config.logging.level}")
        print(f"  Max Concurrent Calls: {config.performance.max_concurrent_calls}")
        print(f"  Memory Limit: {config.performance.memory_limit_mb}MB")
        print(f"  VAD Threshold: {config.audio.vad_threshold}")
        print(f"  Include Audio Logs: {config.logging.include_audio}")


def demo_config_export():
    """Demonstrate configuration export functionality"""
    print("\n=== Configuration Export ===")
    
    config = get_config()
    
    # Export as YAML
    print("YAML Export (first 10 lines):")
    yaml_export = config.export_config("yaml")
    yaml_lines = yaml_export.split('\n')[:10]
    for line in yaml_lines:
        print(f"  {line}")
    print("  ...")
    
    # Export as JSON
    print("\nJSON Export (first 5 lines):")
    json_export = config.export_config("json")
    json_lines = json_export.split('\n')[:5]
    for line in json_lines:
        print(f"  {line}")
    print("  ...")


def demo_environment_info():
    """Demonstrate environment information retrieval"""
    print("\n=== Environment Information ===")
    
    config = get_config()
    env_info = config.get_environment_info()
    
    print("Environment Information:")
    for key, value in env_info.items():
        if key == "loaded_files":
            print(f"  {key}:")
            for file_path in value:
                print(f"    - {file_path}")
        else:
            print(f"  {key}: {value}")


def demo_configuration_validation():
    """Demonstrate configuration validation"""
    print("\n=== Configuration Validation ===")
    
    config = get_config()
    
    print("Current configuration is valid")
    
    # Try to set invalid values
    print("\nTesting validation with invalid values:")
    
    try:
        # This should work
        config.set_config_value("audio.sample_rate", 44100)
        print("  ✓ Valid sample rate (44100) accepted")
    except Exception as e:
        print(f"  ✗ Error: {e}")
    
    try:
        # This should fail validation if we had stricter validation
        config.set_config_value("performance.max_concurrent_calls", -1)
        print("  ⚠ Invalid max_concurrent_calls (-1) was accepted (validation needed)")
    except Exception as e:
        print(f"  ✓ Invalid max_concurrent_calls rejected: {e}")


def main():
    """Run all configuration manager demos"""
    print("AI Voice Customer Service - Configuration Manager Demo")
    print("=" * 60)
    
    try:
        # Create sample config files if they don't exist
        config_dir = Path("config")
        if not config_dir.exists():
            print("Creating sample configuration files...")
            temp_config = ConfigManager()
            temp_config.create_sample_config_files()
            print("Sample configuration files created!")
        
        # Run demos
        demo_basic_usage()
        demo_environment_variables()
        demo_config_access_methods()
        demo_environment_comparison()
        demo_config_export()
        demo_environment_info()
        demo_configuration_validation()
        
        print("\n" + "=" * 60)
        print("Configuration Manager Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()