"""
Extension Manager for AI Voice Customer Service

This module provides unified management of plugins, resources, and auto-scaling
for the AI voice system, acting as a central coordinator for all extensions.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from .base_component import BaseComponent
from .plugin_system import Plugin<PERSON>anager, PluginType, PluginStatus
from .resource_manager import ResourceManager, ResourceType
from .auto_scaler import AutoScaler, ScalingAction


@dataclass
class ExtensionStatus:
    """Overall extension system status."""
    plugins_loaded: int
    plugins_active: int
    plugins_error: int
    resource_pools: int
    total_resources: int
    resources_in_use: int
    auto_scaling_enabled: bool
    recent_scaling_actions: int
    system_health: str
    last_updated: datetime


class ExtensionManager(BaseComponent):
    """Unified extension manager for plugins, resources, and scaling."""
    
    def __init__(self, config_manager, logger=None):
        super().__init__("extension_manager", config_manager, logger)
        
        # Sub-managers
        self.plugin_manager: Optional[PluginManager] = None
        self.resource_manager: Optional[ResourceManager] = None
        self.auto_scaler: Optional[AutoScaler] = None
        
        # Extension configuration
        self.plugins_enabled = self.config_manager.get_config("extensions.plugins.enabled", True)
        self.resources_enabled = self.config_manager.get_config("extensions.resources.enabled", True)
        self.auto_scaling_enabled = self.config_manager.get_config("extensions.auto_scaling.enabled", True)
        
        # Extension registry
        self.registered_extensions: Dict[str, Any] = {}
        
        # Health monitoring
        self.health_check_interval = self.config_manager.get_config("extensions.health_check_interval", 60)
        self.health_task: Optional[asyncio.Task] = None
        
    async def _initialize_impl(self) -> None:
        """Initialize the extension manager."""
        self._log.info("Extension Manager initialized")
        
        # Initialize sub-managers
        if self.plugins_enabled:
            self.plugin_manager = PluginManager(self.config_manager, self._log)
            await self.plugin_manager.initialize()
        
        if self.resources_enabled:
            self.resource_manager = ResourceManager(self.config_manager, self._log)
            await self.resource_manager.initialize()
        
        if self.auto_scaling_enabled:
            self.auto_scaler = AutoScaler(self.config_manager, self._log)
            await self.auto_scaler.initialize()
        
        # Register built-in extensions
        await self._register_builtin_extensions()
        
    async def _start_impl(self) -> None:
        """Start the extension manager."""
        self._log.info("Starting Extension Manager...")
        
        # Start sub-managers
        if self.plugin_manager:
            await self.plugin_manager.start()
        
        if self.resource_manager:
            await self.resource_manager.start()
        
        if self.auto_scaler:
            await self.auto_scaler.start()
        
        # Start health monitoring
        self.health_task = asyncio.create_task(self._health_monitoring_loop())
        
        self._log.info("Extension Manager started")
    
    async def _stop_impl(self) -> None:
        """Stop the extension manager."""
        self._log.info("Stopping Extension Manager...")
        
        # Cancel health monitoring
        if self.health_task:
            self.health_task.cancel()
        
        # Stop sub-managers
        if self.auto_scaler:
            await self.auto_scaler.stop()
        
        if self.resource_manager:
            await self.resource_manager.stop()
        
        if self.plugin_manager:
            await self.plugin_manager.stop()
        
        self._log.info("Extension Manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Clean up the extension manager."""
        # Cleanup sub-managers
        if self.auto_scaler:
            await self.auto_scaler.cleanup()
        
        if self.resource_manager:
            await self.resource_manager.cleanup()
        
        if self.plugin_manager:
            await self.plugin_manager.cleanup()
        
        self.registered_extensions.clear()
        self._log.info("Extension Manager cleanup completed")
    
    async def _register_builtin_extensions(self) -> None:
        """Register built-in extensions."""
        # Register core resource types
        if self.resource_manager:
            self.registered_extensions["http_clients"] = {
                "type": "resource_pool",
                "manager": self.resource_manager,
                "description": "HTTP client connection pool"
            }
            
            self.registered_extensions["thread_pools"] = {
                "type": "resource_pool",
                "manager": self.resource_manager,
                "description": "Thread pool for CPU-intensive tasks"
            }
            
            self.registered_extensions["memory_buffers"] = {
                "type": "resource_pool",
                "manager": self.resource_manager,
                "description": "Memory buffer pool for audio processing"
            }
        
        # Register auto-scaling components
        if self.auto_scaler:
            self.registered_extensions["auto_scaling"] = {
                "type": "auto_scaler",
                "manager": self.auto_scaler,
                "description": "Automatic scaling system"
            }
    
    async def _health_monitoring_loop(self) -> None:
        """Health monitoring loop for extensions."""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._check_extension_health()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in extension health monitoring: {e}")
    
    async def _check_extension_health(self) -> None:
        """Check health of all extensions."""
        unhealthy_extensions = []
        
        # Check plugin health
        if self.plugin_manager:
            plugin_stats = self.plugin_manager.get_plugin_statistics()
            error_plugins = plugin_stats.get("error_plugins", 0)
            
            if error_plugins > 0:
                unhealthy_extensions.append(f"plugins ({error_plugins} errors)")
        
        # Check resource health
        if self.resource_manager:
            resource_stats = self.resource_manager.get_resource_statistics()
            
            for pool_name, pool_stats in resource_stats.get("pools", {}).items():
                error_count = pool_stats.get("error_count", 0)
                if error_count > 10:  # Threshold for unhealthy pool
                    unhealthy_extensions.append(f"resource_pool:{pool_name}")
        
        # Log health issues
        if unhealthy_extensions:
            self._log.warning(f"Unhealthy extensions detected: {', '.join(unhealthy_extensions)}")
        else:
            self._log.debug("All extensions are healthy")
    
    # Plugin Management Interface
    
    async def load_plugin(self, plugin_path: str) -> Optional[str]:
        """Load a plugin."""
        if not self.plugin_manager:
            self._log.error("Plugin manager not available")
            return None
        
        return await self.plugin_manager.load_plugin(plugin_path)
    
    async def start_plugin(self, plugin_name: str) -> bool:
        """Start a plugin."""
        if not self.plugin_manager:
            return False
        
        return await self.plugin_manager.start_plugin(plugin_name)
    
    async def stop_plugin(self, plugin_name: str) -> bool:
        """Stop a plugin."""
        if not self.plugin_manager:
            return False
        
        return await self.plugin_manager.stop_plugin(plugin_name)
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[str]:
        """Get plugins by type."""
        if not self.plugin_manager:
            return []
        
        return self.plugin_manager.get_plugins_by_type(plugin_type)
    
    # Resource Management Interface
    
    async def get_resource(self, pool_name: str, timeout: float = 30.0, user_id: str = None):
        """Get a resource from a pool."""
        if not self.resource_manager:
            return None
        
        return await self.resource_manager.get_resource(pool_name, timeout, user_id)
    
    async def release_resource(self, pool_name: str, resource, user_id: str = None) -> None:
        """Release a resource back to a pool."""
        if not self.resource_manager:
            return
        
        await self.resource_manager.release_resource(pool_name, resource, user_id)
    
    # Auto-Scaling Interface
    
    def get_scaling_status(self) -> Dict[str, Any]:
        """Get auto-scaling status."""
        if not self.auto_scaler:
            return {"enabled": False}
        
        return self.auto_scaler.get_scaling_status()
    
    # Unified Status and Statistics
    
    def get_extension_status(self) -> ExtensionStatus:
        """Get overall extension system status."""
        plugins_loaded = 0
        plugins_active = 0
        plugins_error = 0
        
        if self.plugin_manager:
            plugin_stats = self.plugin_manager.get_plugin_statistics()
            plugins_loaded = plugin_stats.get("total_plugins", 0)
            plugins_active = plugin_stats.get("active_plugins", 0)
            plugins_error = plugin_stats.get("error_plugins", 0)
        
        resource_pools = 0
        total_resources = 0
        resources_in_use = 0
        
        if self.resource_manager:
            resource_stats = self.resource_manager.get_resource_statistics()
            resource_pools = resource_stats.get("pool_count", 0)
            total_resources = resource_stats.get("total_resources", 0)
            resources_in_use = resource_stats.get("total_in_use", 0)
        
        recent_scaling_actions = 0
        if self.auto_scaler:
            scaling_status = self.auto_scaler.get_scaling_status()
            recent_scaling_actions = len(scaling_status.get("recent_actions", []))
        
        # Determine overall health
        system_health = "healthy"
        if plugins_error > 0 or (total_resources > 0 and resources_in_use / total_resources > 0.9):
            system_health = "warning"
        if plugins_error > plugins_active / 2:
            system_health = "unhealthy"
        
        return ExtensionStatus(
            plugins_loaded=plugins_loaded,
            plugins_active=plugins_active,
            plugins_error=plugins_error,
            resource_pools=resource_pools,
            total_resources=total_resources,
            resources_in_use=resources_in_use,
            auto_scaling_enabled=self.auto_scaling_enabled,
            recent_scaling_actions=recent_scaling_actions,
            system_health=system_health,
            last_updated=datetime.now()
        )
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """Get comprehensive extension statistics."""
        stats = {
            "extension_manager": {
                "plugins_enabled": self.plugins_enabled,
                "resources_enabled": self.resources_enabled,
                "auto_scaling_enabled": self.auto_scaling_enabled,
                "registered_extensions": len(self.registered_extensions)
            }
        }
        
        # Plugin statistics
        if self.plugin_manager:
            stats["plugins"] = self.plugin_manager.get_plugin_statistics()
        
        # Resource statistics
        if self.resource_manager:
            stats["resources"] = self.resource_manager.get_resource_statistics()
        
        # Auto-scaling statistics
        if self.auto_scaler:
            stats["auto_scaling"] = self.auto_scaler.get_scaling_status()
        
        # Overall status
        status = self.get_extension_status()
        stats["overall_status"] = {
            "system_health": status.system_health,
            "plugins_loaded": status.plugins_loaded,
            "plugins_active": status.plugins_active,
            "resource_utilization": (
                status.resources_in_use / status.total_resources
                if status.total_resources > 0 else 0.0
            ),
            "last_updated": status.last_updated.isoformat()
        }
        
        return stats
    
    # Extension Registry
    
    def register_extension(self, name: str, extension_info: Dict[str, Any]) -> bool:
        """Register a custom extension."""
        if name in self.registered_extensions:
            self._log.warning(f"Extension {name} is already registered")
            return False
        
        self.registered_extensions[name] = extension_info
        self._log.info(f"Registered extension: {name}")
        return True
    
    def unregister_extension(self, name: str) -> bool:
        """Unregister an extension."""
        if name not in self.registered_extensions:
            self._log.warning(f"Extension {name} is not registered")
            return False
        
        del self.registered_extensions[name]
        self._log.info(f"Unregistered extension: {name}")
        return True
    
    def get_registered_extensions(self) -> Dict[str, Any]:
        """Get all registered extensions."""
        return self.registered_extensions.copy()
    
    # Health and Diagnostics
    
    async def run_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive diagnostics on all extensions."""
        diagnostics = {
            "timestamp": datetime.now().isoformat(),
            "extension_manager": {
                "status": "healthy" if self.is_running else "stopped",
                "plugins_enabled": self.plugins_enabled,
                "resources_enabled": self.resources_enabled,
                "auto_scaling_enabled": self.auto_scaling_enabled
            }
        }
        
        # Plugin diagnostics
        if self.plugin_manager:
            diagnostics["plugins"] = {
                "manager_status": "running" if self.plugin_manager.is_running else "stopped",
                "statistics": self.plugin_manager.get_plugin_statistics(),
                "plugin_details": {
                    name: {
                        "status": info.status.value,
                        "error": info.error_message
                    }
                    for name, info in self.plugin_manager.get_all_plugins().items()
                }
            }
        
        # Resource diagnostics
        if self.resource_manager:
            diagnostics["resources"] = {
                "manager_status": "running" if self.resource_manager.is_running else "stopped",
                "statistics": self.resource_manager.get_resource_statistics(),
                "system_metrics": self.resource_manager.get_system_metrics()
            }
        
        # Auto-scaling diagnostics
        if self.auto_scaler:
            diagnostics["auto_scaling"] = {
                "manager_status": "running" if self.auto_scaler.is_running else "stopped",
                "scaling_status": self.auto_scaler.get_scaling_status()
            }
        
        return diagnostics
