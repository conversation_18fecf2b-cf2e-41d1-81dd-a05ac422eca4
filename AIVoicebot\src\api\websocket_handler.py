"""
WebSocket Handler for AI Voice Customer Service System

This module provides WebSocket handling for real-time communication
with clients, including call status updates and audio streaming.
"""

import asyncio
import json
import logging
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from dataclasses import dataclass
from enum import Enum


class MessageType(Enum):
    """WebSocket message types."""
    PING = "ping"
    PONG = "pong"
    CALL_STATUS = "call_status"
    AUDIO_DATA = "audio_data"
    SYSTEM_STATUS = "system_status"
    ERROR = "error"
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"


@dataclass
class WebSocketConnection:
    """WebSocket connection information."""
    websocket: WebSocket
    client_id: str
    connected_at: datetime
    subscriptions: Set[str]
    last_ping: Optional[datetime] = None


class WebSocketManager:
    """WebSocket connection manager."""
    
    def __init__(self):
        self.connections: Dict[str, WebSocketConnection] = {}
        self.logger = logging.getLogger(__name__)
        
        # Subscription topics
        self.subscribers: Dict[str, Set[str]] = {
            "call_events": set(),
            "system_events": set(),
            "audio_stream": set(),
            "performance_metrics": set()
        }
        
        # Background tasks
        self.ping_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
    async def connect(self, websocket: WebSocket, client_id: str = None) -> str:
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        # Generate client ID if not provided
        if not client_id:
            client_id = f"client_{len(self.connections)}_{int(datetime.now().timestamp())}"
        
        # Create connection record
        connection = WebSocketConnection(
            websocket=websocket,
            client_id=client_id,
            connected_at=datetime.now(),
            subscriptions=set()
        )
        
        self.connections[client_id] = connection
        
        self.logger.info(f"WebSocket client connected: {client_id}")
        
        # Start background tasks if this is the first connection
        if len(self.connections) == 1:
            await self._start_background_tasks()
        
        # Send welcome message
        await self._send_message(client_id, {
            "type": "welcome",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        })
        
        return client_id
    
    async def disconnect(self, websocket: WebSocket) -> None:
        """Handle WebSocket disconnection."""
        # Find connection by websocket
        client_id = None
        for cid, conn in self.connections.items():
            if conn.websocket == websocket:
                client_id = cid
                break
        
        if client_id:
            # Remove from subscriptions
            connection = self.connections[client_id]
            for topic in connection.subscriptions:
                if topic in self.subscribers:
                    self.subscribers[topic].discard(client_id)
            
            # Remove connection
            del self.connections[client_id]
            
            self.logger.info(f"WebSocket client disconnected: {client_id}")
            
            # Stop background tasks if no connections remain
            if len(self.connections) == 0:
                await self._stop_background_tasks()
    
    async def handle_message(self, websocket: WebSocket, message: str) -> None:
        """Handle incoming WebSocket message."""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            # Find client ID
            client_id = None
            for cid, conn in self.connections.items():
                if conn.websocket == websocket:
                    client_id = cid
                    break
            
            if not client_id:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Connection not found"
                }))
                return
            
            # Handle different message types
            if message_type == MessageType.PING.value:
                await self._handle_ping(client_id, data)
            elif message_type == MessageType.SUBSCRIBE.value:
                await self._handle_subscribe(client_id, data)
            elif message_type == MessageType.UNSUBSCRIBE.value:
                await self._handle_unsubscribe(client_id, data)
            elif message_type == MessageType.AUDIO_DATA.value:
                await self._handle_audio_data(client_id, data)
            else:
                await self._send_error(client_id, f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Invalid JSON format"
            }))
        except Exception as e:
            self.logger.error(f"Error handling WebSocket message: {e}")
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Internal server error"
            }))
    
    async def _handle_ping(self, client_id: str, data: Dict[str, Any]) -> None:
        """Handle ping message."""
        connection = self.connections.get(client_id)
        if connection:
            connection.last_ping = datetime.now()
            
            await self._send_message(client_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_subscribe(self, client_id: str, data: Dict[str, Any]) -> None:
        """Handle subscription request."""
        topic = data.get("topic")
        
        if not topic:
            await self._send_error(client_id, "Missing topic in subscription request")
            return
        
        if topic not in self.subscribers:
            await self._send_error(client_id, f"Unknown topic: {topic}")
            return
        
        # Add to subscription
        self.subscribers[topic].add(client_id)
        
        # Update connection record
        connection = self.connections.get(client_id)
        if connection:
            connection.subscriptions.add(topic)
        
        await self._send_message(client_id, {
            "type": "subscribed",
            "topic": topic,
            "timestamp": datetime.now().isoformat()
        })
        
        self.logger.info(f"Client {client_id} subscribed to {topic}")
    
    async def _handle_unsubscribe(self, client_id: str, data: Dict[str, Any]) -> None:
        """Handle unsubscription request."""
        topic = data.get("topic")
        
        if not topic:
            await self._send_error(client_id, "Missing topic in unsubscription request")
            return
        
        # Remove from subscription
        if topic in self.subscribers:
            self.subscribers[topic].discard(client_id)
        
        # Update connection record
        connection = self.connections.get(client_id)
        if connection:
            connection.subscriptions.discard(topic)
        
        await self._send_message(client_id, {
            "type": "unsubscribed",
            "topic": topic,
            "timestamp": datetime.now().isoformat()
        })
        
        self.logger.info(f"Client {client_id} unsubscribed from {topic}")
    
    async def _handle_audio_data(self, client_id: str, data: Dict[str, Any]) -> None:
        """Handle audio data from client."""
        # This would integrate with the audio pipeline
        # For now, just acknowledge receipt
        await self._send_message(client_id, {
            "type": "audio_received",
            "timestamp": datetime.now().isoformat()
        })
    
    async def _send_message(self, client_id: str, message: Dict[str, Any]) -> bool:
        """Send message to a specific client."""
        connection = self.connections.get(client_id)
        if not connection:
            return False
        
        try:
            await connection.websocket.send_text(json.dumps(message))
            return True
        except Exception as e:
            self.logger.error(f"Error sending message to {client_id}: {e}")
            # Remove broken connection
            await self._remove_connection(client_id)
            return False
    
    async def _send_error(self, client_id: str, error_message: str) -> None:
        """Send error message to client."""
        await self._send_message(client_id, {
            "type": "error",
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        })
    
    async def broadcast_to_topic(self, topic: str, message: Dict[str, Any]) -> int:
        """Broadcast message to all subscribers of a topic."""
        if topic not in self.subscribers:
            return 0
        
        subscribers = self.subscribers[topic].copy()
        sent_count = 0
        
        for client_id in subscribers:
            if await self._send_message(client_id, message):
                sent_count += 1
        
        return sent_count
    
    async def broadcast_call_event(self, call_id: str, event_type: str, event_data: Dict[str, Any]) -> None:
        """Broadcast call event to subscribers."""
        message = {
            "type": "call_event",
            "call_id": call_id,
            "event_type": event_type,
            "data": event_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_to_topic("call_events", message)
    
    async def broadcast_system_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Broadcast system event to subscribers."""
        message = {
            "type": "system_event",
            "event_type": event_type,
            "data": event_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_to_topic("system_events", message)
    
    async def _start_background_tasks(self) -> None:
        """Start background tasks."""
        self.ping_task = asyncio.create_task(self._ping_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def _stop_background_tasks(self) -> None:
        """Stop background tasks."""
        if self.ping_task:
            self.ping_task.cancel()
            self.ping_task = None
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            self.cleanup_task = None
    
    async def _ping_loop(self) -> None:
        """Background task to ping clients."""
        while True:
            try:
                await asyncio.sleep(30)  # Ping every 30 seconds
                
                current_time = datetime.now()
                for client_id in list(self.connections.keys()):
                    try:
                        await self._send_message(client_id, {
                            "type": "ping",
                            "timestamp": current_time.isoformat()
                        })
                    except Exception as e:
                        self.logger.error(f"Error pinging client {client_id}: {e}")
                        await self._remove_connection(client_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in ping loop: {e}")
    
    async def _cleanup_loop(self) -> None:
        """Background task to clean up stale connections."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = datetime.now()
                stale_connections = []
                
                for client_id, connection in self.connections.items():
                    # Check if connection is stale (no ping for 2 minutes)
                    if connection.last_ping:
                        time_since_ping = (current_time - connection.last_ping).total_seconds()
                        if time_since_ping > 120:  # 2 minutes
                            stale_connections.append(client_id)
                
                # Remove stale connections
                for client_id in stale_connections:
                    self.logger.info(f"Removing stale connection: {client_id}")
                    await self._remove_connection(client_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in cleanup loop: {e}")
    
    async def _remove_connection(self, client_id: str) -> None:
        """Remove a connection and clean up subscriptions."""
        connection = self.connections.get(client_id)
        if connection:
            # Remove from subscriptions
            for topic in connection.subscriptions:
                if topic in self.subscribers:
                    self.subscribers[topic].discard(client_id)
            
            # Close WebSocket if still open
            try:
                await connection.websocket.close()
            except Exception:
                pass
            
            # Remove from connections
            del self.connections[client_id]
    
    async def cleanup(self) -> None:
        """Clean up all connections and stop background tasks."""
        await self._stop_background_tasks()
        
        # Close all connections
        for client_id in list(self.connections.keys()):
            await self._remove_connection(client_id)
        
        self.logger.info("WebSocket manager cleanup completed")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.connections),
            "subscriptions": {
                topic: len(subscribers)
                for topic, subscribers in self.subscribers.items()
            },
            "connections": [
                {
                    "client_id": conn.client_id,
                    "connected_at": conn.connected_at.isoformat(),
                    "subscriptions": list(conn.subscriptions),
                    "last_ping": conn.last_ping.isoformat() if conn.last_ping else None
                }
                for conn in self.connections.values()
            ]
        }
