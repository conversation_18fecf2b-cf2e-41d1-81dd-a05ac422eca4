<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI语音客服系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .subtitle {
            color: #666;
            font-size: 1.1em;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }

        .status-label {
            font-weight: 500;
            color: #333;
        }

        .status-value {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status-running {
            background: #d4edda;
            color: #155724;
        }

        .status-ready {
            background: #cce5ff;
            color: #004085;
        }

        .voice-controls {
            margin: 30px 0;
        }

        .mic-button {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 2em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(238, 90, 36, 0.3);
            margin: 20px;
        }

        .mic-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(238, 90, 36, 0.4);
        }

        .mic-button:active {
            transform: translateY(-2px);
        }

        .mic-button.recording {
            background: linear-gradient(135deg, #ff4757, #c44569);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .conversation-area {
            margin: 30px 0;
            text-align: left;
        }

        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
        }

        .user-message {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #f1f8e9;
            margin-right: auto;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎤 AI语音客服系统</h1>
            <p class="subtitle">基于SenseVoice + 通义千问 + Edge-TTS</p>
        </div>

        <div class="status-panel">
            <div class="status-item">
                <span class="status-label">🎯 语音识别</span>
                <span class="status-value status-ready" id="asr-status">SenseVoice</span>
            </div>
            <div class="status-item">
                <span class="status-label">🤖 AI对话</span>
                <span class="status-value status-ready" id="ai-status">通义千问</span>
            </div>
            <div class="status-item">
                <span class="status-label">🔊 语音合成</span>
                <span class="status-value status-ready" id="tts-status">Edge-TTS</span>
            </div>
            <div class="status-item">
                <span class="status-label">📡 系统状态</span>
                <span class="status-value status-running" id="system-status">运行中</span>
            </div>
        </div>

        <div class="voice-controls">
            <button class="mic-button" id="micButton" onclick="startRealVoiceInput()">
                🎤
            </button>
            <p id="micStatus">点击麦克风开始真实语音识别</p>
            <p style="color: #666; font-size: 0.9em; margin-top: 10px;">
                系统将使用您的麦克风进行真实语音识别 (SenseVoice + 通义千问 + Edge-TTS)
            </p>
        </div>

        <div class="conversation-area" id="conversationArea">
            <div class="message ai-message">
                <strong>AI客服：</strong> 您好！我是AI语音客服，请问有什么可以帮助您的吗？
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>AI正在思考中...</p>
        </div>

        <div class="controls">
            <button class="btn btn-secondary" onclick="clearConversation()">清空对话</button>
            <button class="btn btn-primary" onclick="testSystem()">系统测试</button>
        </div>

        <div class="footer">
            <p>🚀 技术栈：SenseVoiceSmall + 通义千问 + Edge-TTS</p>
            <p>💡 支持中文语音识别和智能对话</p>
        </div>
    </div>

    <script>
        let isRecording = false;
        let mediaRecorder = null;
        let audioChunks = [];

        // 更新系统状态
        async function updateSystemStatus() {
            try {
                const response = await fetch('/voice/info');
                const data = await response.json();
                
                document.getElementById('asr-status').textContent = data.asr_provider || 'SenseVoice';
                document.getElementById('ai-status').textContent = data.ai_provider || '通义千问';
                document.getElementById('tts-status').textContent = data.tts_provider || 'Edge-TTS';
                
                const healthResponse = await fetch('/health');
                const healthData = await healthResponse.json();
                document.getElementById('system-status').textContent = healthData.status === 'healthy' ? '运行中' : '异常';
            } catch (error) {
                console.error('Failed to update status:', error);
            }
        }

        // 开始真实语音输入
        async function startRealVoiceInput() {
            try {
                document.getElementById('micButton').classList.add('recording');
                document.getElementById('micButton').innerHTML = '🎧';
                document.getElementById('micStatus').textContent = '正在监听麦克风... (智能检测)';
                document.getElementById('loading').style.display = 'block';

                // 调用后端的麦克风监听功能
                const response = await fetch('/voice/listen', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    // 显示真实的语音识别结果
                    if (result.transcription && result.transcription.trim()) {
                        addMessage('user', result.transcription);

                        // 显示AI回复
                        if (result.response && result.response.trim()) {
                            addMessage('ai', result.response);

                            // 播放TTS音频
                            if (result.audio_base64) {
                                playTTSAudio(result.audio_base64);
                            }
                        } else {
                            addMessage('ai', '我听到了您的话，但暂时无法生成回复。');
                        }

                        // 显示置信度
                        if (result.confidence) {
                            console.log(`语音识别置信度: ${result.confidence}`);
                        }
                    } else {
                        addMessage('ai', '没有检测到语音，请重试。');
                    }
                } else {
                    addMessage('ai', `错误: ${result.error || '语音处理失败'}`);
                }

            } catch (error) {
                console.error('Error with voice input:', error);
                addMessage('ai', '抱歉，语音处理时出现网络错误。');
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('micButton').classList.remove('recording');
                document.getElementById('micButton').innerHTML = '🎤';
                document.getElementById('micStatus').textContent = '点击麦克风开始真实语音识别';
            }
        }



        // 添加消息到对话区域
        function addMessage(sender, text) {
            const conversationArea = document.getElementById('conversationArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;

            const senderLabel = sender === 'user' ? '用户' : 'AI客服';
            messageDiv.innerHTML = `<strong>${senderLabel}：</strong> ${text}`;

            conversationArea.appendChild(messageDiv);
            conversationArea.scrollTop = conversationArea.scrollHeight;
        }

        // 播放TTS音频
        function playTTSAudio(audioBase64) {
            try {
                // 创建音频对象
                const audio = new Audio();
                audio.src = `data:audio/wav;base64,${audioBase64}`;

                // 设置音频属性
                audio.volume = 0.8;
                audio.preload = 'auto';

                // 播放音频
                audio.play().then(() => {
                    console.log('🔊 TTS音频播放成功');
                    addMessage('system', '🔊 AI语音回复播放中...');
                }).catch(error => {
                    console.error('TTS音频播放失败:', error);
                    addMessage('system', '⚠️ 音频播放失败，请检查浏览器音频权限');
                });

                // 音频播放结束事件
                audio.addEventListener('ended', () => {
                    console.log('TTS音频播放完成');
                });

            } catch (error) {
                console.error('创建TTS音频失败:', error);
                addMessage('system', '⚠️ 音频创建失败');
            }
        }

        // 清空对话
        function clearConversation() {
            const conversationArea = document.getElementById('conversationArea');
            conversationArea.innerHTML = `
                <div class="message ai-message">
                    <strong>AI客服：</strong> 您好！我是AI语音客服，请问有什么可以帮助您的吗？
                </div>
            `;
        }

        // 系统测试
        async function testSystem() {
            try {
                document.getElementById('loading').style.display = 'block';
                const response = await fetch('/voice/test-conversation', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    addMessage('ai', '系统测试完成！所有组件运行正常。');
                } else {
                    addMessage('ai', '系统测试失败，请检查日志。');
                }
            } catch (error) {
                console.error('Test failed:', error);
                addMessage('ai', '系统测试出现错误。');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // 页面加载时更新状态
        window.onload = function() {
            updateSystemStatus();
            setInterval(updateSystemStatus, 30000); // 每30秒更新一次状态
        };
    </script>
</body>
</html>
