"""
Model Configuration and Path Management

This module provides centralized management for AI model configurations including:
- Model file paths and parameters
- SenseVoiceSmall ASR model configuration
- SileroVAD model configuration
- EdgeTTS configuration
- Qwen-turbo API configuration
- Model loading and validation
"""

import os
import json
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Supported AI model types"""
    ASR = "asr"  # Automatic Speech Recognition
    VAD = "vad"  # Voice Activity Detection
    TTS = "tts"  # Text-to-Speech
    LLM = "llm"  # Large Language Model


class ModelFormat(Enum):
    """Supported model file formats"""
    PYTORCH = "pytorch"  # .pt, .pth files
    ONNX = "onnx"       # .onnx files
    TENSORRT = "tensorrt"  # .trt files
    API = "api"         # API-based models


@dataclass
class ModelPath:
    """Model file path configuration"""
    model_file: str
    config_file: Optional[str] = None
    vocab_file: Optional[str] = None
    tokenizer_file: Optional[str] = None
    
    def __post_init__(self):
        """Validate model paths after initialization"""
        self.model_file = str(Path(self.model_file).resolve())
        if self.config_file:
            self.config_file = str(Path(self.config_file).resolve())
        if self.vocab_file:
            self.vocab_file = str(Path(self.vocab_file).resolve())
        if self.tokenizer_file:
            self.tokenizer_file = str(Path(self.tokenizer_file).resolve())
    
    def exists(self) -> bool:
        """Check if all required model files exist"""
        if not Path(self.model_file).exists():
            return False
        
        for file_path in [self.config_file, self.vocab_file, self.tokenizer_file]:
            if file_path and not Path(file_path).exists():
                return False
        
        return True
    
    def get_size_mb(self) -> float:
        """Get total size of model files in MB"""
        total_size = 0
        
        for file_path in [self.model_file, self.config_file, self.vocab_file, self.tokenizer_file]:
            if file_path and Path(file_path).exists():
                total_size += Path(file_path).stat().st_size
        
        return total_size / (1024 * 1024)


@dataclass
class ModelPath:
    """Model file path configuration"""
    model_file: str
    config_file: Optional[str] = None
    vocab_file: Optional[str] = None
    tokenizer_file: Optional[str] = None
    
    def __post_init__(self):
        """Validate model paths after initialization"""
        self.model_file = str(Path(self.model_file).resolve())
        if self.config_file:
            self.config_file = str(Path(self.config_file).resolve())
        if self.vocab_file:
            self.vocab_file = str(Path(self.vocab_file).resolve())
        if self.tokenizer_file:
            self.tokenizer_file = str(Path(self.tokenizer_file).resolve())
    
    def exists(self) -> bool:
        """Check if all required model files exist"""
        if not Path(self.model_file).exists():
            return False
        
        for file_path in [self.config_file, self.vocab_file, self.tokenizer_file]:
            if file_path and not Path(file_path).exists():
                return False
        
        return True
    
    def get_size_mb(self) -> float:
        """Get total size of model files in MB"""
        total_size = 0
        
        for file_path in [self.model_file, self.config_file, self.vocab_file, self.tokenizer_file]:
            if file_path and Path(file_path).exists():
                total_size += Path(file_path).stat().st_size
        
        return total_size / (1024 * 1024)


@dataclass
class SenseVoiceConfig:
    """SenseVoiceSmall ASR model configuration"""
    model_path: ModelPath
    model_type: str = "SenseVoiceSmall"
    language: str = "zh"  # Chinese
    sample_rate: int = 16000
    chunk_size: int = 1024
    
    # Model parameters
    beam_size: int = 1
    temperature: float = 0.0
    max_length: int = 512
    
    # Processing parameters
    vad_enabled: bool = True
    normalize_audio: bool = True
    remove_silence: bool = True
    
    # Performance settings
    use_gpu: bool = True
    gpu_memory_fraction: float = 0.3
    batch_size: int = 1
    
    def __post_init__(self):
        """Validate SenseVoice configuration"""
        if not self.model_path.exists():
            logger.warning(f"SenseVoice model files not found: {self.model_path.model_file}")
        
        if self.sample_rate not in [8000, 16000, 22050, 44100]:
            raise ValueError(f"Invalid sample rate for SenseVoice: {self.sample_rate}")


@dataclass
class SileroVADConfig:
    """SileroVAD model configuration"""
    model_path: ModelPath
    model_type: str = "SileroVAD"
    sample_rate: int = 16000
    
    # VAD parameters
    threshold: float = 0.5
    min_speech_duration_ms: int = 100
    max_speech_duration_s: int = 30
    min_silence_duration_ms: int = 100
    window_size_samples: int = 1536  # 96ms at 16kHz
    
    # Processing parameters
    speech_pad_ms: int = 30
    return_seconds: bool = False
    
    # Performance settings
    use_gpu: bool = False  # SileroVAD typically runs on CPU
    batch_size: int = 1
    
    def __post_init__(self):
        """Validate SileroVAD configuration"""
        if not self.model_path.exists():
            logger.warning(f"SileroVAD model files not found: {self.model_path.model_file}")
        
        if self.sample_rate != 16000:
            logger.warning(f"SileroVAD optimized for 16kHz, got {self.sample_rate}Hz")
        
        if not 0.0 <= self.threshold <= 1.0:
            raise ValueError(f"VAD threshold must be between 0.0 and 1.0, got {self.threshold}")


@dataclass
class EdgeTTSConfig:
    """EdgeTTS configuration"""
    model_type: str = "EdgeTTS"
    
    # Voice settings
    voice: str = "zh-CN-XiaoxiaoNeural"
    language: str = "zh-CN"
    gender: str = "Female"
    
    # Speech parameters
    rate: str = "+0%"  # -100% to +200%
    volume: str = "+0%"  # -100% to +100%
    pitch: str = "+0Hz"  # -50Hz to +50Hz
    
    # Audio output settings
    output_format: str = "audio-16khz-32kbitrate-mono-mp3"
    quality: str = "high"  # low, medium, high
    
    # Performance settings
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Caching settings
    cache_enabled: bool = True
    cache_dir: str = "cache/tts"
    cache_size_mb: int = 100
    cache_ttl_hours: int = 24
    
    def __post_init__(self):
        """Validate EdgeTTS configuration"""
        # Validate voice format
        if not self.voice.startswith("zh-CN-"):
            logger.warning(f"Voice {self.voice} may not be Chinese")
        
        # Create cache directory
        if self.cache_enabled:
            Path(self.cache_dir).mkdir(parents=True, exist_ok=True)


@dataclass
class QwenConfig:
    """Qwen-turbo LLM configuration"""
    model_type: str = "Qwen-turbo"
    
    # API settings
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/api/v1"
    model_name: str = "qwen-turbo"
    
    # Generation parameters
    max_tokens: int = 1000
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    repetition_penalty: float = 1.1
    
    # Request settings
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    backoff_factor: float = 2.0
    
    # Rate limiting
    requests_per_minute: int = 60
    tokens_per_minute: int = 100000
    
    # Context management
    max_context_length: int = 8000
    context_window_overlap: int = 200
    
    def __post_init__(self):
        """Validate Qwen configuration"""
        if not self.api_key:
            logger.warning("Qwen API key not set")
        
        if not 0.0 <= self.temperature <= 2.0:
            raise ValueError(f"Temperature must be between 0.0 and 2.0, got {self.temperature}")
        
        if not 0.0 <= self.top_p <= 1.0:
            raise ValueError(f"Top-p must be between 0.0 and 1.0, got {self.top_p}")


class ModelConfigManager:
    """
    Centralized manager for AI model configurations
    
    Features:
    - Model path validation and management
    - Configuration loading and validation
    - Model availability checking
    - Performance optimization settings
    """
    
    def __init__(self, models_dir: str = "models", cache_dir: str = "cache"):
        """
        Initialize model configuration manager
        
        Args:
            models_dir: Directory containing model files
            cache_dir: Directory for model caches
        """
        self.models_dir = Path(models_dir)
        self.cache_dir = Path(cache_dir)
        
        # Ensure directories exist
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Model configurations
        self.sense_voice: Optional[SenseVoiceConfig] = None
        self.silero_vad: Optional[SileroVADConfig] = None
        self.edge_tts: Optional[EdgeTTSConfig] = None
        self.qwen: Optional[QwenConfig] = None
        
        # Model registry
        self._model_registry: Dict[str, Dict[str, Any]] = {}
        
        # Load configurations
        self._load_model_configurations()
        
        logger.info(f"ModelConfigManager initialized with models_dir: {self.models_dir}")
    
    def _load_model_configurations(self) -> None:
        """Load model configurations from files and environment"""
        try:
            # Load SenseVoice configuration
            self._load_sense_voice_config()
            
            # Load SileroVAD configuration
            self._load_silero_vad_config()
            
            # Load EdgeTTS configuration
            self._load_edge_tts_config()
            
            # Load Qwen configuration
            self._load_qwen_config()
            
            # Build model registry
            self._build_model_registry()
            
            logger.info("Model configurations loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model configurations: {e}")
            self._load_default_configurations()
    
    def _load_sense_voice_config(self) -> None:
        """Load SenseVoice model configuration"""
        model_file = self.models_dir / "sensevoice" / "model.pt"
        config_file = self.models_dir / "sensevoice" / "config.json"
        
        # Check for alternative paths
        if not model_file.exists():
            # Try common alternative paths
            alternatives = [
                self.models_dir / "SenseVoiceSmall" / "model.pt",
                self.models_dir / "sense_voice_small.pt",
                Path("models/sensevoice/model.pt")
            ]
            
            for alt_path in alternatives:
                if alt_path.exists():
                    model_file = alt_path
                    break
        
        model_path = ModelPath(
            model_file=str(model_file),
            config_file=str(config_file) if config_file.exists() else None
        )
        
        # Override with environment variables
        env_model_path = os.getenv("SENSEVOICE_MODEL_PATH")
        if env_model_path:
            model_path.model_file = env_model_path
        
        self.sense_voice = SenseVoiceConfig(
            model_path=model_path,
            sample_rate=int(os.getenv("SENSEVOICE_SAMPLE_RATE", "16000")),
            use_gpu=os.getenv("SENSEVOICE_USE_GPU", "true").lower() == "true",
            beam_size=int(os.getenv("SENSEVOICE_BEAM_SIZE", "1"))
        )
    
    def _load_silero_vad_config(self) -> None:
        """Load SileroVAD model configuration"""
        model_file = self.models_dir / "silero_vad" / "model.pt"
        
        # Check for alternative paths
        if not model_file.exists():
            alternatives = [
                self.models_dir / "silero_vad.pt",
                Path("models/silero_vad/model.pt")
            ]
            
            for alt_path in alternatives:
                if alt_path.exists():
                    model_file = alt_path
                    break
        
        model_path = ModelPath(model_file=str(model_file))
        
        # Override with environment variables
        env_model_path = os.getenv("SILERO_VAD_MODEL_PATH")
        if env_model_path:
            model_path.model_file = env_model_path
        
        self.silero_vad = SileroVADConfig(
            model_path=model_path,
            threshold=float(os.getenv("VAD_THRESHOLD", "0.5")),
            min_speech_duration_ms=int(os.getenv("VAD_MIN_SPEECH_MS", "100")),
            use_gpu=os.getenv("SILERO_VAD_USE_GPU", "false").lower() == "true"
        )
    
    def _load_edge_tts_config(self) -> None:
        """Load EdgeTTS configuration"""
        self.edge_tts = EdgeTTSConfig(
            voice=os.getenv("TTS_VOICE", "zh-CN-XiaoxiaoNeural"),
            rate=os.getenv("TTS_RATE", "+0%"),
            volume=os.getenv("TTS_VOLUME", "+0%"),
            pitch=os.getenv("TTS_PITCH", "+0Hz"),
            cache_enabled=os.getenv("TTS_CACHE_ENABLED", "true").lower() == "true",
            cache_dir=os.getenv("TTS_CACHE_DIR", str(self.cache_dir / "tts"))
        )
    
    def _load_qwen_config(self) -> None:
        """Load Qwen LLM configuration"""
        self.qwen = QwenConfig(
            api_key=os.getenv("QWEN_API_KEY", ""),
            base_url=os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/api/v1"),
            model_name=os.getenv("QWEN_MODEL", "qwen-turbo"),
            max_tokens=int(os.getenv("QWEN_MAX_TOKENS", "1000")),
            temperature=float(os.getenv("QWEN_TEMPERATURE", "0.7")),
            timeout=int(os.getenv("QWEN_TIMEOUT", "30"))
        )   
 
    def _load_default_configurations(self) -> None:
        """Load default model configurations as fallback"""
        logger.info("Loading default model configurations")
        
        # Default SenseVoice configuration
        default_sense_voice_path = ModelPath(
            model_file=str(self.models_dir / "sensevoice" / "model.pt")
        )
        self.sense_voice = SenseVoiceConfig(model_path=default_sense_voice_path)
        
        # Default SileroVAD configuration
        default_vad_path = ModelPath(
            model_file=str(self.models_dir / "silero_vad" / "model.pt")
        )
        self.silero_vad = SileroVADConfig(model_path=default_vad_path)
        
        # Default EdgeTTS configuration
        self.edge_tts = EdgeTTSConfig()
        
        # Default Qwen configuration
        self.qwen = QwenConfig()
                "available": self.sense_voice.model_path.exists() if self.sense_voice else False,
                "size_mb": self.sense_voice.model_path.get_size_mb() if self.sense_voice and self.sense_voice.model_path.exists() else 0
            },
            "silero_vad": {
                "type": ModelType.VAD,
                "format": ModelFormat.PYTORCH,
                "config": self.silero_vad,
                "available": self.silero_vad.model_path.exists() if self.silero_vad else False,
                "size_mb": self.silero_vad.model_path.get_size_mb() if self.silero_vad and self.silero_vad.model_path.exists() else 0
            },
            "edge_tts": {
                "type": ModelType.TTS,
                "format": ModelFormat.API,
                "config": self.edge_tts,
                "available": True,  # API-based, always available
                "size_mb": 0
            },
            "qwen": {
                "type": ModelType.LLM,
                "format": ModelFormat.API,
                "config": self.qwen,
                "available": bool(self.qwen.api_key) if self.qwen else False,
                "size_mb": 0
            }
        }
    
    def get_model_config(self, model_name: str) -> Optional[Union[SenseVoiceConfig, SileroVADConfig, EdgeTTSConfig, QwenConfig]]:
        """
        Get configuration for a specific model
        
        Args:
            model_name: Name of the model (sensevoice, silero_vad, edge_tts, qwen)
            
        Returns:
            Model configuration object or None if not found
        """
        model_configs = {
            "sensevoice": self.sense_voice,
            "silero_vad": self.silero_vad,
            "edge_tts": self.edge_tts,
            "qwen": self.qwen
        }
        
        return model_configs.get(model_name.lower())
    
    def is_model_available(self, model_name: str) -> bool:
        """
        Check if a model is available for use
        
        Args:
            model_name: Name of the model
            
        Returns:
            True if model is available, False otherwise
        """
        if model_name.lower() not in self._model_registry:
            return False
        
        return self._model_registry[model_name.lower()]["available"]
    
    def get_available_models(self) -> List[str]:
        """
        Get list of available models
        
        Returns:
            List of available model names
        """
        return [
            name for name, info in self._model_registry.items()
            if info["available"]
        ]
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a model
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dictionary with model information or None if not found
        """
        return self._model_registry.get(model_name.lower())
    
    def get_all_models_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all configured models
        
        Returns:
            Dictionary with all model information
        """
        return self._model_registry.copy()
    
    def validate_model_files(self) -> Dict[str, bool]:
        """
        Validate that all model files exist
        
        Returns:
            Dictionary mapping model names to validation status
        """
        validation_results = {}
        
        for model_name, info in self._model_registry.items():
            if info["format"] == ModelFormat.API:
                # API-based models don't have files to validate
                validation_results[model_name] = True
            else:
                config = info["config"]
                if hasattr(config, "model_path"):
                    validation_results[model_name] = config.model_path.exists()
                else:
                    validation_results[model_name] = False
        
        return validation_results
    
    def get_total_model_size(self) -> float:
        """
        Get total size of all model files in MB
        
        Returns:
            Total size in MB
        """
        total_size = 0.0
        
        for info in self._model_registry.values():
            total_size += info["size_mb"]
        
        return total_size
    
    def create_model_directories(self) -> None:
        """Create necessary directories for model storage"""
        directories = [
            self.models_dir / "sensevoice",
            self.models_dir / "silero_vad",
            self.cache_dir / "tts",
            self.cache_dir / "asr",
            self.cache_dir / "vad"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {directory}")
    
    def download_model_info(self) -> Dict[str, Dict[str, str]]:
        """
        Get download information for models
        
        Returns:
            Dictionary with download URLs and instructions
        """
        return {
            "sensevoice": {
                "name": "SenseVoiceSmall",
                "description": "Chinese ASR model by Alibaba",
                "url": "https://www.modelscope.cn/models/iic/SenseVoiceSmall",
                "size": "~1.2GB",
                "format": "PyTorch (.pt)",
                "instructions": "Download model.pt and place in models/sensevoice/"
            },
            "silero_vad": {
                "name": "Silero VAD",
                "description": "Voice Activity Detection model",
                "url": "https://github.com/snakers4/silero-vad",
                "size": "~1MB",
                "format": "PyTorch (.pt)",
                "instructions": "Download silero_vad.pt and place in models/silero_vad/"
            },
            "edge_tts": {
                "name": "Microsoft Edge TTS",
                "description": "Text-to-Speech API service",
                "url": "https://azure.microsoft.com/en-us/services/cognitive-services/text-to-speech/",
                "size": "API-based",
                "format": "REST API",
                "instructions": "No download required, uses Microsoft Edge TTS API"
            },
            "qwen": {
                "name": "Qwen-turbo",
                "description": "Large Language Model by Alibaba",
                "url": "https://dashscope.aliyuncs.com/",
                "size": "API-based",
                "format": "REST API",
                "instructions": "Requires API key from Alibaba Cloud DashScope"
            }
        }
    
    def update_model_config(self, model_name: str, **kwargs) -> bool:
        """
        Update configuration for a specific model
        
        Args:
            model_name: Name of the model to update
            **kwargs: Configuration parameters to update
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            config = self.get_model_config(model_name)
            if not config:
                logger.error(f"Model not found: {model_name}")
                return False
            
            # Update configuration attributes
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                    logger.debug(f"Updated {model_name}.{key} = {value}")
                else:
                    logger.warning(f"Unknown configuration parameter: {key}")
            
            # Rebuild registry
            self._build_model_registry()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update model config for {model_name}: {e}")
            return False
    
    def export_model_configs(self) -> Dict[str, Any]:
        """
        Export all model configurations to dictionary
        
        Returns:
            Dictionary with all model configurations
        """
        configs = {}
        
        if self.sense_voice:
            configs["sensevoice"] = {
                "model_path": self.sense_voice.model_path.model_file,
                "sample_rate": self.sense_voice.sample_rate,
                "beam_size": self.sense_voice.beam_size,
                "use_gpu": self.sense_voice.use_gpu,
                "batch_size": self.sense_voice.batch_size
            }
        
        if self.silero_vad:
            configs["silero_vad"] = {
                "model_path": self.silero_vad.model_path.model_file,
                "threshold": self.silero_vad.threshold,
                "min_speech_duration_ms": self.silero_vad.min_speech_duration_ms,
                "use_gpu": self.silero_vad.use_gpu
            }
        
        if self.edge_tts:
            configs["edge_tts"] = {
                "voice": self.edge_tts.voice,
                "rate": self.edge_tts.rate,
                "volume": self.edge_tts.volume,
                "pitch": self.edge_tts.pitch,
                "cache_enabled": self.edge_tts.cache_enabled
            }
        
        if self.qwen:
            configs["qwen"] = {
                "model_name": self.qwen.model_name,
                "max_tokens": self.qwen.max_tokens,
                "temperature": self.qwen.temperature,
                "timeout": self.qwen.timeout,
                "api_key_set": bool(self.qwen.api_key)
            }
        
        return configs
    
    def get_model_status_report(self) -> str:
        """
        Generate a comprehensive status report for all models
        
        Returns:
            Formatted status report string
        """
        report_lines = ["Model Configuration Status Report", "=" * 40]
        
        total_size = 0.0
        available_count = 0
        
        for model_name, info in self._model_registry.items():
            status = "✓ Available" if info["available"] else "✗ Not Available"
            size_info = f"{info['size_mb']:.1f}MB" if info['size_mb'] > 0 else "API-based"
            
            report_lines.append(f"{model_name.upper()}:")
            report_lines.append(f"  Status: {status}")
            report_lines.append(f"  Type: {info['type'].value}")
            report_lines.append(f"  Format: {info['format'].value}")
            report_lines.append(f"  Size: {size_info}")
            
            if info["available"]:
                available_count += 1
            total_size += info["size_mb"]
            
            report_lines.append("")
        
        report_lines.extend([
            f"Summary:",
            f"  Available Models: {available_count}/{len(self._model_registry)}",
            f"  Total Size: {total_size:.1f}MB",
            f"  Models Directory: {self.models_dir}",
            f"  Cache Directory: {self.cache_dir}"
        ])
        
        return "\n".join(report_lines)


# Global model configuration manager instance
model_config_manager: Optional[ModelConfigManager] = None


def get_model_config() -> ModelConfigManager:
    """Get global model configuration manager instance"""
    global model_config_manager
    if model_config_manager is None:
        model_config_manager = ModelConfigManager()
    return model_config_manager


def initialize_model_config(models_dir: str = "models", cache_dir: str = "cache") -> ModelConfigManager:
    """
    Initialize global model configuration manager
    
    Args:
        models_dir: Directory containing model files
        cache_dir: Directory for model caches
        
    Returns:
        ModelConfigManager instance
    """
    global model_config_manager
    model_config_manager = ModelConfigManager(models_dir, cache_dir)
    return model_config_manager


def setup_model_environment() -> None:
    """Set up model environment with necessary directories and configurations"""
    manager = get_model_config()
    manager.create_model_directories()
    
    # Log model status
    logger.info("Model environment setup completed")
    logger.info(f"Available models: {manager.get_available_models()}")
    
    # Validate model files
    validation_results = manager.validate_model_files()
    for model_name, is_valid in validation_results.items():
        if not is_valid and model_name not in ["edge_tts", "qwen"]:  # Skip API-based models
            logger.warning(f"Model files not found for {model_name}")


def get_model_download_instructions() -> str:
    """
    Get formatted instructions for downloading required models
    
    Returns:
        Formatted download instructions
    """
    manager = get_model_config()
    download_info = manager.download_model_info()
    
    instructions = ["Model Download Instructions", "=" * 30, ""]
    
    for model_name, info in download_info.items():
        instructions.extend([
            f"{info['name']}:",
            f"  Description: {info['description']}",
            f"  Size: {info['size']}",
            f"  URL: {info['url']}",
            f"  Instructions: {info['instructions']}",
            ""
        ])
    
    return "\n".join(instructions)  
  
    def _build_model_registry(self) -> None:
        """Build registry of all configured models"""
        self._model_registry = {
            "sensevoice": {
                "type": ModelType.ASR,
                "format": ModelFormat.PYTORCH,
                "config": self.sense_voice,
                "available": self.sense_voice.model_path.exists() if self.sense_voice else False,
                "size_mb": self.sense_voice.model_path.get_size_mb() if self.sense_voice and self.sense_voice.model_path.exists() else 0
            },
            "silero_vad": {
                "type": ModelType.VAD,
                "format": ModelFormat.PYTORCH,
                "config": self.silero_vad,
                "available": self.silero_vad.model_path.exists() if self.silero_vad else False,
                "size_mb": self.silero_vad.model_path.get_size_mb() if self.silero_vad and self.silero_vad.model_path.exists() else 0
            },
            "edge_tts": {
                "type": ModelType.TTS,
                "format": ModelFormat.API,
                "config": self.edge_tts,
                "available": True,  # API-based, always available
                "size_mb": 0
            },
            "qwen": {
                "type": ModelType.LLM,
                "format": ModelFormat.API,
                "config": self.qwen,
                "available": bool(self.qwen.api_key) if self.qwen else False,
                "size_mb": 0
            }
        }
    
    def get_model_config(self, model_name: str) -> Optional[Union[SenseVoiceConfig, SileroVADConfig, EdgeTTSConfig, QwenConfig]]:
        """
        Get configuration for a specific model
        
        Args:
            model_name: Name of the model (sensevoice, silero_vad, edge_tts, qwen)
            
        Returns:
            Model configuration object or None if not found
        """
        model_configs = {
            "sensevoice": self.sense_voice,
            "silero_vad": self.silero_vad,
            "edge_tts": self.edge_tts,
            "qwen": self.qwen
        }
        
        return model_configs.get(model_name.lower())
    
    def is_model_available(self, model_name: str) -> bool:
        """
        Check if a model is available for use
        
        Args:
            model_name: Name of the model
            
        Returns:
            True if model is available, False otherwise
        """
        if model_name.lower() not in self._model_registry:
            return False
        
        return self._model_registry[model_name.lower()]["available"]
    
    def get_available_models(self) -> List[str]:
        """
        Get list of available models
        
        Returns:
            List of available model names
        """
        return [
            name for name, info in self._model_registry.items()
            if info["available"]
        ]
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a model
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dictionary with model information or None if not found
        """
        return self._model_registry.get(model_name.lower())
    
    def get_all_models_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all configured models
        
        Returns:
            Dictionary with all model information
        """
        return self._model_registry.copy()
    
    def validate_model_files(self) -> Dict[str, bool]:
        """
        Validate that all model files exist
        
        Returns:
            Dictionary mapping model names to validation status
        """
        validation_results = {}
        
        for model_name, info in self._model_registry.items():
            if info["format"] == ModelFormat.API:
                # API-based models don't have files to validate
                validation_results[model_name] = True
            else:
                config = info["config"]
                if hasattr(config, "model_path"):
                    validation_results[model_name] = config.model_path.exists()
                else:
                    validation_results[model_name] = False
        
        return validation_results
    
    def get_total_model_size(self) -> float:
        """
        Get total size of all model files in MB
        
        Returns:
            Total size in MB
        """
        total_size = 0.0
        
        for info in self._model_registry.values():
            total_size += info["size_mb"]
        
        return total_size
    
    def create_model_directories(self) -> None:
        """Create necessary directories for model storage"""
        directories = [
            self.models_dir / "sensevoice",
            self.models_dir / "silero_vad",
            self.cache_dir / "tts",
            self.cache_dir / "asr",
            self.cache_dir / "vad"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {directory}")
    
    def get_model_status_report(self) -> str:
        """
        Generate a comprehensive status report for all models
        
        Returns:
            Formatted status report string
        """
        report_lines = ["Model Configuration Status Report", "=" * 40]
        
        total_size = 0.0
        available_count = 0
        
        for model_name, info in self._model_registry.items():
            status = "✓ Available" if info["available"] else "✗ Not Available"
            size_info = f"{info['size_mb']:.1f}MB" if info['size_mb'] > 0 else "API-based"
            
            report_lines.append(f"{model_name.upper()}:")
            report_lines.append(f"  Status: {status}")
            report_lines.append(f"  Type: {info['type'].value}")
            report_lines.append(f"  Format: {info['format'].value}")
            report_lines.append(f"  Size: {size_info}")
            
            if info["available"]:
                available_count += 1
            total_size += info["size_mb"]
            
            report_lines.append("")
        
        report_lines.extend([
            f"Summary:",
            f"  Available Models: {available_count}/{len(self._model_registry)}",
            f"  Total Size: {total_size:.1f}MB",
            f"  Models Directory: {self.models_dir}",
            f"  Cache Directory: {self.cache_dir}"
        ])
        
        return "\n".join(report_lines)


# Global model configuration manager instance
model_config_manager: Optional[ModelConfigManager] = None


def get_model_config() -> ModelConfigManager:
    """Get global model configuration manager instance"""
    global model_config_manager
    if model_config_manager is None:
        model_config_manager = ModelConfigManager()
    return model_config_manager


def initialize_model_config(models_dir: str = "models", cache_dir: str = "cache") -> ModelConfigManager:
    """
    Initialize global model configuration manager
    
    Args:
        models_dir: Directory containing model files
        cache_dir: Directory for model caches
        
    Returns:
        ModelConfigManager instance
    """
    global model_config_manager
    model_config_manager = ModelConfigManager(models_dir, cache_dir)
    return model_config_manager


def setup_model_environment() -> None:
    """Set up model environment with necessary directories and configurations"""
    manager = get_model_config()
    manager.create_model_directories()
    
    # Log model status
    logger.info("Model environment setup completed")
    logger.info(f"Available models: {manager.get_available_models()}")
    
    # Validate model files
    validation_results = manager.validate_model_files()
    for model_name, is_valid in validation_results.items():
        if not is_valid and model_name not in ["edge_tts", "qwen"]:  # Skip API-based models
            logger.warning(f"Model files not found for {model_name}")


def get_model_download_instructions() -> str:
    """
    Get formatted instructions for downloading required models
    
    Returns:
        Formatted download instructions
    """
    manager = get_model_config()
    download_info = {
        "sensevoice": {
            "name": "SenseVoiceSmall",
            "description": "Chinese ASR model by Alibaba",
            "url": "https://www.modelscope.cn/models/iic/SenseVoiceSmall",
            "size": "~1.2GB",
            "format": "PyTorch (.pt)",
            "instructions": "Download model.pt and place in models/sensevoice/"
        },
        "silero_vad": {
            "name": "Silero VAD",
            "description": "Voice Activity Detection model",
            "url": "https://github.com/snakers4/silero-vad",
            "size": "~1MB",
            "format": "PyTorch (.pt)",
            "instructions": "Download silero_vad.pt and place in models/silero_vad/"
        },
        "edge_tts": {
            "name": "Microsoft Edge TTS",
            "description": "Text-to-Speech API service",
            "url": "https://azure.microsoft.com/en-us/services/cognitive-services/text-to-speech/",
            "size": "API-based",
            "format": "REST API",
            "instructions": "No download required, uses Microsoft Edge TTS API"
        },
        "qwen": {
            "name": "Qwen-turbo",
            "description": "Large Language Model by Alibaba",
            "url": "https://dashscope.aliyuncs.com/",
            "size": "API-based",
            "format": "REST API",
            "instructions": "Requires API key from Alibaba Cloud DashScope"
        }
    }
    
    instructions = ["Model Download Instructions", "=" * 30, ""]
    
    for model_name, info in download_info.items():
        instructions.extend([
            f"{info['name']}:",
            f"  Description: {info['description']}",
            f"  Size: {info['size']}",
            f"  URL: {info['url']}",
            f"  Instructions: {info['instructions']}",
            ""
        ])
    
    return "\n".join(instructions)