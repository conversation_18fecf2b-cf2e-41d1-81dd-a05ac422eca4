"""
Load Testing Framework

This module provides comprehensive load testing capabilities for the
AI voice customer service system, including concurrent call simulation,
performance benchmarking, and stress testing.
"""

import asyncio
import pytest
import time
import statistics
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import uuid
import sys
import os
from concurrent.futures import ThreadPoolExecutor
import threading

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from tests.integration.test_e2e_conversation_flow import E2ETestFramework, E2ETestScenario
from tests.test_utils import <PERSON><PERSON><PERSON>onfigManager, MockLogger, AudioTestData


@dataclass
class LoadTestMetrics:
    """Load test performance metrics."""
    test_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    response_times: List[float] = field(default_factory=list)
    error_rates: List[float] = field(default_factory=list)
    throughput_per_second: List[float] = field(default_factory=list)
    resource_usage: Dict[str, List[float]] = field(default_factory=dict)
    
    @property
    def duration_seconds(self) -> float:
        """Get test duration in seconds."""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0
    
    @property
    def success_rate(self) -> float:
        """Get success rate percentage."""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def average_response_time(self) -> float:
        """Get average response time."""
        return statistics.mean(self.response_times) if self.response_times else 0.0
    
    @property
    def p95_response_time(self) -> float:
        """Get 95th percentile response time."""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(0.95 * len(sorted_times))
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]
    
    @property
    def average_throughput(self) -> float:
        """Get average throughput per second."""
        return statistics.mean(self.throughput_per_second) if self.throughput_per_second else 0.0


class LoadTestRunner:
    """Load test runner for performance testing."""
    
    def __init__(self, config_manager=None, logger=None):
        self.config_manager = config_manager or MockConfigManager()
        self.logger = logger or MockLogger()
        
        # Test configuration
        self.max_concurrent_calls = self.config_manager.get_config("load_test.max_concurrent_calls", 50)
        self.test_duration_seconds = self.config_manager.get_config("load_test.duration_seconds", 60)
        self.ramp_up_seconds = self.config_manager.get_config("load_test.ramp_up_seconds", 10)
        self.ramp_down_seconds = self.config_manager.get_config("load_test.ramp_down_seconds", 10)
        
        # Test state
        self.active_tests: Dict[str, LoadTestMetrics] = {}
        self.completed_tests: List[LoadTestMetrics] = []
        self.test_framework: Optional[E2ETestFramework] = None
        
        # Performance monitoring
        self.metrics_collection_interval = 1.0  # seconds
        self._stop_metrics_collection = threading.Event()
    
    async def initialize(self):
        """Initialize the load test runner."""
        self.logger.info("Initializing load test runner")
        
        # Initialize E2E test framework
        self.test_framework = E2ETestFramework(self.config_manager, self.logger)
        await self.test_framework.initialize()
        
        self.logger.info("Load test runner initialized")
    
    async def cleanup(self):
        """Clean up the load test runner."""
        self.logger.info("Cleaning up load test runner")
        
        if self.test_framework:
            await self.test_framework.cleanup()
        
        self._stop_metrics_collection.set()
        self.logger.info("Load test runner cleanup completed")
    
    async def run_concurrent_call_test(
        self,
        concurrent_calls: int,
        duration_seconds: float,
        call_scenario: Dict[str, Any] = None
    ) -> LoadTestMetrics:
        """Run concurrent call load test."""
        test_name = f"concurrent_calls_{concurrent_calls}_{duration_seconds}s"
        self.logger.info(f"Starting concurrent call test: {test_name}")
        
        metrics = LoadTestMetrics(
            test_name=test_name,
            start_time=datetime.now()
        )
        
        self.active_tests[test_name] = metrics
        
        try:
            # Start metrics collection
            metrics_task = asyncio.create_task(self._collect_metrics(metrics))
            
            # Create call tasks
            call_tasks = []
            for i in range(concurrent_calls):
                task = asyncio.create_task(
                    self._simulate_call(f"call_{i}", call_scenario or {}, metrics)
                )
                call_tasks.append(task)
                
                # Ramp up gradually
                if i < concurrent_calls - 1:
                    await asyncio.sleep(self.ramp_up_seconds / concurrent_calls)
            
            # Wait for test duration
            await asyncio.sleep(duration_seconds)
            
            # Cancel all call tasks
            for task in call_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*call_tasks, return_exceptions=True)
            
            # Stop metrics collection
            metrics_task.cancel()
            try:
                await metrics_task
            except asyncio.CancelledError:
                pass
            
            metrics.end_time = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Load test failed: {e}")
            metrics.end_time = datetime.now()
        finally:
            self.active_tests.pop(test_name, None)
            self.completed_tests.append(metrics)
        
        self.logger.info(f"Concurrent call test completed: {test_name}")
        return metrics
    
    async def run_stress_test(
        self,
        max_calls: int,
        increment_step: int = 5,
        step_duration: float = 30.0
    ) -> List[LoadTestMetrics]:
        """Run stress test with gradually increasing load."""
        self.logger.info(f"Starting stress test: 0 to {max_calls} calls")
        
        stress_test_results = []
        current_calls = increment_step
        
        while current_calls <= max_calls:
            self.logger.info(f"Stress test step: {current_calls} concurrent calls")
            
            metrics = await self.run_concurrent_call_test(
                concurrent_calls=current_calls,
                duration_seconds=step_duration
            )
            
            stress_test_results.append(metrics)
            
            # Check if system is still stable
            if metrics.success_rate < 90:  # Less than 90% success rate
                self.logger.warning(f"System stability degraded at {current_calls} calls")
                break
            
            current_calls += increment_step
        
        self.logger.info("Stress test completed")
        return stress_test_results
    
    async def run_endurance_test(
        self,
        concurrent_calls: int,
        duration_hours: float
    ) -> LoadTestMetrics:
        """Run endurance test for extended duration."""
        duration_seconds = duration_hours * 3600
        self.logger.info(f"Starting endurance test: {concurrent_calls} calls for {duration_hours} hours")
        
        return await self.run_concurrent_call_test(
            concurrent_calls=concurrent_calls,
            duration_seconds=duration_seconds
        )
    
    async def run_spike_test(
        self,
        baseline_calls: int,
        spike_calls: int,
        spike_duration: float = 60.0,
        baseline_duration: float = 120.0
    ) -> List[LoadTestMetrics]:
        """Run spike test with sudden load increases."""
        self.logger.info(f"Starting spike test: {baseline_calls} -> {spike_calls} calls")
        
        spike_test_results = []
        
        # Baseline load
        baseline_metrics = await self.run_concurrent_call_test(
            concurrent_calls=baseline_calls,
            duration_seconds=baseline_duration
        )
        spike_test_results.append(baseline_metrics)
        
        # Spike load
        spike_metrics = await self.run_concurrent_call_test(
            concurrent_calls=spike_calls,
            duration_seconds=spike_duration
        )
        spike_test_results.append(spike_metrics)
        
        # Return to baseline
        recovery_metrics = await self.run_concurrent_call_test(
            concurrent_calls=baseline_calls,
            duration_seconds=baseline_duration
        )
        spike_test_results.append(recovery_metrics)
        
        self.logger.info("Spike test completed")
        return spike_test_results
    
    async def _simulate_call(
        self,
        call_id: str,
        scenario: Dict[str, Any],
        metrics: LoadTestMetrics
    ):
        """Simulate a single call for load testing."""
        start_time = time.time()
        
        try:
            # Create test scenario
            test_scenario = E2ETestScenario(
                scenario_id=call_id,
                name=f"Load test call {call_id}",
                description="Simulated call for load testing",
                steps=[
                    {
                        "type": "initiate_call",
                        "data": {
                            "caller_number": f"+123456{call_id[-4:]}",
                            "callee_number": "+0987654321"
                        }
                    },
                    {
                        "type": "send_audio",
                        "data": {
                            "audio_type": "speech",
                            "text_content": "你好，我想查询账户余额"
                        }
                    },
                    {
                        "type": "expect_response",
                        "data": {
                            "expected_keywords": ["账户", "余额"],
                            "timeout_seconds": 5.0
                        }
                    },
                    {
                        "type": "end_call",
                        "data": {}
                    }
                ],
                expected_outcomes={
                    "max_duration_seconds": 30.0,
                    "min_step_success_rate": 0.8
                },
                timeout_seconds=30.0
            )
            
            # Run scenario
            success = await self.test_framework.run_scenario(test_scenario)
            
            # Record metrics
            response_time = time.time() - start_time
            metrics.response_times.append(response_time)
            metrics.total_requests += 1
            
            if success:
                metrics.successful_requests += 1
            else:
                metrics.failed_requests += 1
            
        except Exception as e:
            self.logger.error(f"Call simulation failed: {e}")
            metrics.total_requests += 1
            metrics.failed_requests += 1
            
            response_time = time.time() - start_time
            metrics.response_times.append(response_time)
    
    async def _collect_metrics(self, metrics: LoadTestMetrics):
        """Collect performance metrics during test execution."""
        while True:
            try:
                await asyncio.sleep(self.metrics_collection_interval)
                
                # Calculate current throughput
                current_time = datetime.now()
                if metrics.start_time:
                    elapsed = (current_time - metrics.start_time).total_seconds()
                    if elapsed > 0:
                        throughput = metrics.total_requests / elapsed
                        metrics.throughput_per_second.append(throughput)
                
                # Calculate current error rate
                if metrics.total_requests > 0:
                    error_rate = (metrics.failed_requests / metrics.total_requests) * 100
                    metrics.error_rates.append(error_rate)
                
                # Collect resource usage (simulated)
                if "cpu_usage" not in metrics.resource_usage:
                    metrics.resource_usage["cpu_usage"] = []
                    metrics.resource_usage["memory_usage"] = []
                    metrics.resource_usage["network_usage"] = []
                
                # Simulate resource usage based on load
                load_factor = min(metrics.total_requests / 100, 1.0)
                metrics.resource_usage["cpu_usage"].append(30 + load_factor * 50)
                metrics.resource_usage["memory_usage"].append(40 + load_factor * 40)
                metrics.resource_usage["network_usage"].append(20 + load_factor * 60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error collecting metrics: {e}")
    
    def generate_performance_report(self, metrics: LoadTestMetrics) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        return {
            "test_name": metrics.test_name,
            "duration_seconds": metrics.duration_seconds,
            "total_requests": metrics.total_requests,
            "successful_requests": metrics.successful_requests,
            "failed_requests": metrics.failed_requests,
            "success_rate_percent": metrics.success_rate,
            "average_response_time_ms": metrics.average_response_time * 1000,
            "p95_response_time_ms": metrics.p95_response_time * 1000,
            "average_throughput_per_second": metrics.average_throughput,
            "peak_throughput_per_second": max(metrics.throughput_per_second) if metrics.throughput_per_second else 0,
            "average_error_rate_percent": statistics.mean(metrics.error_rates) if metrics.error_rates else 0,
            "peak_error_rate_percent": max(metrics.error_rates) if metrics.error_rates else 0,
            "resource_usage": {
                resource: {
                    "average": statistics.mean(values),
                    "peak": max(values),
                    "minimum": min(values)
                } for resource, values in metrics.resource_usage.items()
            },
            "start_time": metrics.start_time.isoformat(),
            "end_time": metrics.end_time.isoformat() if metrics.end_time else None
        }
    
    def compare_performance(
        self,
        baseline_metrics: LoadTestMetrics,
        current_metrics: LoadTestMetrics
    ) -> Dict[str, Any]:
        """Compare performance between two test runs."""
        return {
            "response_time_change_percent": (
                (current_metrics.average_response_time - baseline_metrics.average_response_time) /
                baseline_metrics.average_response_time * 100
            ) if baseline_metrics.average_response_time > 0 else 0,
            "throughput_change_percent": (
                (current_metrics.average_throughput - baseline_metrics.average_throughput) /
                baseline_metrics.average_throughput * 100
            ) if baseline_metrics.average_throughput > 0 else 0,
            "success_rate_change_percent": current_metrics.success_rate - baseline_metrics.success_rate,
            "performance_regression": (
                current_metrics.average_response_time > baseline_metrics.average_response_time * 1.1 or
                current_metrics.success_rate < baseline_metrics.success_rate - 5
            )
        }
