"""
Simplified Model Configuration and Path Management

This module provides basic model configuration management for AI models.
"""

import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Supported AI model types"""
    ASR = "asr"
    VAD = "vad"
    TTS = "tts"
    LLM = "llm"


class ModelFormat(Enum):
    """Supported model file formats"""
    PYTORCH = "pytorch"
    API = "api"


@dataclass
class ModelPath:
    """Model file path configuration"""
    model_file: str
    config_file: Optional[str] = None
    
    def exists(self) -> bool:
        """Check if model file exists"""
        return Path(self.model_file).exists()
    
    def get_size_mb(self) -> float:
        """Get model file size in MB"""
        if self.exists():
            return Path(self.model_file).stat().st_size / (1024 * 1024)
        return 0.0


@dataclass
class SenseVoiceConfig:
    """SenseVoice ASR model configuration"""
    model_path: ModelPath
    sample_rate: int = 16000
    use_gpu: bool = True
    beam_size: int = 1


@dataclass
class SileroVADConfig:
    """SileroVAD model configuration"""
    model_path: ModelPath
    threshold: float = 0.5
    use_gpu: bool = False


@dataclass
class EdgeTTSConfig:
    """EdgeTTS configuration"""
    voice: str = "zh-CN-XiaoxiaoNeural"
    rate: str = "+0%"
    cache_enabled: bool = True


@dataclass
class QwenConfig:
    """Qwen LLM configuration"""
    api_key: str = ""
    model_name: str = "qwen-turbo"
    max_tokens: int = 1000
    temperature: float = 0.7


class ModelConfigManager:
    """Simple model configuration manager"""
    
    def __init__(self, models_dir: str = "models", cache_dir: str = "cache"):
        self.models_dir = Path(models_dir)
        self.cache_dir = Path(cache_dir)
        
        # Create directories
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize configurations
        self._load_configurations()
    
    def _load_configurations(self):
        """Load model configurations"""
        # SenseVoice
        sense_voice_path = ModelPath(
            model_file=str(self.models_dir / "sensevoice" / "model.pt")
        )
        self.sense_voice = SenseVoiceConfig(
            model_path=sense_voice_path,
            sample_rate=int(os.getenv("SENSEVOICE_SAMPLE_RATE", "16000")),
            use_gpu=os.getenv("SENSEVOICE_USE_GPU", "true").lower() == "true"
        )
        
        # SileroVAD
        vad_path = ModelPath(
            model_file=str(self.models_dir / "silero_vad" / "model.pt")
        )
        self.silero_vad = SileroVADConfig(
            model_path=vad_path,
            threshold=float(os.getenv("VAD_THRESHOLD", "0.5"))
        )
        
        # EdgeTTS
        self.edge_tts = EdgeTTSConfig(
            voice=os.getenv("TTS_VOICE", "zh-CN-XiaoxiaoNeural"),
            rate=os.getenv("TTS_RATE", "+0%")
        )
        
        # Qwen
        self.qwen = QwenConfig(
            api_key=os.getenv("QWEN_API_KEY", ""),
            model_name=os.getenv("QWEN_MODEL", "qwen-turbo"),
            temperature=float(os.getenv("QWEN_TEMPERATURE", "0.7"))
        )
    
    def get_model_config(self, model_name: str):
        """Get model configuration"""
        configs = {
            "sensevoice": self.sense_voice,
            "silero_vad": self.silero_vad,
            "edge_tts": self.edge_tts,
            "qwen": self.qwen
        }
        return configs.get(model_name.lower())
    
    def is_model_available(self, model_name: str) -> bool:
        """Check if model is available"""
        config = self.get_model_config(model_name)
        if not config:
            return False
        
        if model_name.lower() in ["edge_tts", "qwen"]:
            return True  # API-based models
        
        return hasattr(config, "model_path") and config.model_path.exists()
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        models = ["sensevoice", "silero_vad", "edge_tts", "qwen"]
        return [m for m in models if self.is_model_available(m)]
    
    def get_model_status_report(self) -> str:
        """Get model status report"""
        lines = ["Model Status Report", "=" * 20]
        
        for model_name in ["sensevoice", "silero_vad", "edge_tts", "qwen"]:
            available = self.is_model_available(model_name)
            status = "✓ Available" if available else "✗ Not Available"
            lines.append(f"{model_name}: {status}")
        
        return "\n".join(lines)


# Global instance
_model_config_manager: Optional[ModelConfigManager] = None


def get_model_config() -> ModelConfigManager:
    """Get global model configuration manager"""
    global _model_config_manager
    if _model_config_manager is None:
        _model_config_manager = ModelConfigManager()
    return _model_config_manager


def initialize_model_config(models_dir: str = "models", cache_dir: str = "cache") -> ModelConfigManager:
    """Initialize model configuration manager"""
    global _model_config_manager
    _model_config_manager = ModelConfigManager(models_dir, cache_dir)
    return _model_config_manager