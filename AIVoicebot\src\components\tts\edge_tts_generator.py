"""
EdgeTTS integration for text-to-speech conversion.

This module provides a wrapper for EdgeTTS with Chinese voice selection,
parameter configuration, and audio format optimization for telephony.
"""

import asyncio
import logging
import io
import tempfile
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
import json

try:
    import edge_tts
    import pydub
    from pydub import AudioSegment
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

from ...core.base_component import BaseComponent
from ...core.interfaces import AudioProcessingError


class VoiceGender(Enum):
    """Voice gender options."""
    MALE = "male"
    FEMALE = "female"
    NEUTRAL = "neutral"


class AudioFormat(Enum):
    """Supported audio formats."""
    WAV = "wav"
    MP3 = "mp3"
    PCM = "pcm"
    OPUS = "opus"


@dataclass
class TTSVoice:
    """TTS voice configuration."""
    name: str
    short_name: str
    gender: VoiceGender
    locale: str
    language: str
    
    # Voice characteristics
    description: str = ""
    sample_rate: int = 24000
    is_neural: bool = True
    
    # Quality ratings (1-5)
    naturalness: int = 4
    clarity: int = 4
    expressiveness: int = 3
    
    def __str__(self) -> str:
        return f"{self.name} ({self.short_name}) - {self.gender.value}"


@dataclass
class EdgeTTSConfig:
    """Configuration for EdgeTTS generator."""
    # Voice settings
    default_voice: str = "zh-CN-XiaoxiaoNeural"
    voice_speed: str = "+0%"  # Speed adjustment (-50% to +100%)
    voice_pitch: str = "+0Hz"  # Pitch adjustment
    voice_volume: str = "+0%"  # Volume adjustment
    
    # Audio format settings
    output_format: AudioFormat = AudioFormat.WAV
    sample_rate: int = 16000  # Telephony standard
    channels: int = 1  # Mono for telephony
    bit_depth: int = 16
    
    # Quality settings
    enable_audio_optimization: bool = True
    enable_noise_reduction: bool = True
    enable_volume_normalization: bool = True
    
    # Performance settings
    connection_timeout: float = 10.0
    read_timeout: float = 30.0
    max_retries: int = 3
    
    # Caching settings
    enable_caching: bool = True
    cache_directory: str = "tts_cache"
    cache_size_mb: int = 100
    cache_ttl_hours: int = 24
    
    # Telephony optimization
    telephony_optimization: bool = True
    compress_for_telephony: bool = True
    target_bitrate_kbps: int = 64


@dataclass
class TTSResult:
    """Result from TTS generation."""
    audio_data: bytes
    format: AudioFormat
    sample_rate: int
    channels: int
    duration_ms: float
    
    # Generation metadata
    text: str
    voice_used: str
    generation_time_ms: float
    file_size_bytes: int
    
    # Quality metrics
    audio_quality_score: float = 0.0
    compression_ratio: float = 1.0
    
    # Processing info
    cached: bool = False
    optimized: bool = False
    compressed: bool = False
    
    # Timestamps
    generated_at: datetime = field(default_factory=datetime.now)
    
    def save_to_file(self, file_path: str) -> bool:
        """Save audio data to file."""
        try:
            with open(file_path, 'wb') as f:
                f.write(self.audio_data)
            return True
        except Exception:
            return False


class EdgeTTSGenerator(BaseComponent):
    """
    EdgeTTS integration for text-to-speech conversion.
    
    Provides Chinese voice selection, parameter configuration,
    and audio format optimization for telephony compatibility.
    """
    
    def __init__(self, config: EdgeTTSConfig, config_manager, logger=None):
        """
        Initialize EdgeTTS generator.
        
        Args:
            config: TTS configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("edge_tts_generator", config_manager, logger)
        
        if not EDGE_TTS_AVAILABLE:
            raise AudioProcessingError("EdgeTTS dependencies not available. Install with: pip install edge-tts pydub")
        
        self.config = config
        
        # Available voices
        self.available_voices: Dict[str, TTSVoice] = {}
        self.chinese_voices: Dict[str, TTSVoice] = {}
        
        # Audio cache
        self._audio_cache: Dict[str, TTSResult] = {}
        self._cache_directory: Optional[Path] = None
        
        # Statistics
        self._total_generations = 0
        self._cache_hits = 0
        self._total_audio_duration = 0.0
        self._total_generation_time = 0.0
        
        # Load default Chinese voices
        self._load_default_chinese_voices()
    
    async def _initialize_impl(self) -> None:
        """Initialize EdgeTTS generator."""
        self._log.info("Initializing EdgeTTS generator...")
        
        # Setup cache directory
        if self.config.enable_caching:
            self._cache_directory = Path(self.config.cache_directory)
            self._cache_directory.mkdir(parents=True, exist_ok=True)
        
        # Load available voices from EdgeTTS
        try:
            await self._load_available_voices()
        except Exception as e:
            self._log.warning(f"Failed to load EdgeTTS voices: {e}")
        
        # Validate default voice
        if self.config.default_voice not in self.available_voices:
            self._log.warning(f"Default voice {self.config.default_voice} not available")
            # Use first available Chinese voice as fallback
            if self.chinese_voices:
                self.config.default_voice = next(iter(self.chinese_voices.keys()))
                self._log.info(f"Using fallback voice: {self.config.default_voice}")
        
        self._log.info(f"EdgeTTS generator initialized with {len(self.available_voices)} voices")
    
    async def _start_impl(self) -> None:
        """Start EdgeTTS generator."""
        self._log.info("Starting EdgeTTS generator...")
        
        # Reset statistics
        self._total_generations = 0
        self._cache_hits = 0
        self._total_audio_duration = 0.0
        self._total_generation_time = 0.0
        
        self._log.info("EdgeTTS generator started")
    
    async def _stop_impl(self) -> None:
        """Stop EdgeTTS generator."""
        self._log.info("Stopping EdgeTTS generator...")
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("EdgeTTS generator stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup EdgeTTS generator resources."""
        self._log.info("Cleaning up EdgeTTS generator...")
        
        # Clear cache
        self._audio_cache.clear()
        
        # Clean up cache directory if needed
        if self._cache_directory and self.config.enable_caching:
            await self._cleanup_cache_directory()
        
        self._log.info("EdgeTTS generator cleanup completed")
    
    def _load_default_chinese_voices(self) -> None:
        """Load default Chinese voice configurations."""
        # Define popular Chinese voices with their characteristics
        default_voices = [
            TTSVoice(
                name="Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)",
                short_name="zh-CN-XiaoxiaoNeural",
                gender=VoiceGender.FEMALE,
                locale="zh-CN",
                language="Chinese (Mainland)",
                description="Natural female voice, suitable for customer service",
                naturalness=5,
                clarity=5,
                expressiveness=4
            ),
            TTSVoice(
                name="Microsoft Yunxi Online (Natural) - Chinese (Mainland)",
                short_name="zh-CN-YunxiNeural",
                gender=VoiceGender.MALE,
                locale="zh-CN",
                language="Chinese (Mainland)",
                description="Natural male voice, professional tone",
                naturalness=5,
                clarity=5,
                expressiveness=4
            ),
            TTSVoice(
                name="Microsoft Xiaoyi Online (Natural) - Chinese (Mainland)",
                short_name="zh-CN-XiaoyiNeural",
                gender=VoiceGender.FEMALE,
                locale="zh-CN",
                language="Chinese (Mainland)",
                description="Gentle female voice, warm and friendly",
                naturalness=4,
                clarity=5,
                expressiveness=5
            ),
            TTSVoice(
                name="Microsoft Yunjian Online (Natural) - Chinese (Mainland)",
                short_name="zh-CN-YunjianNeural",
                gender=VoiceGender.MALE,
                locale="zh-CN",
                language="Chinese (Mainland)",
                description="Mature male voice, authoritative",
                naturalness=4,
                clarity=5,
                expressiveness=3
            ),
            TTSVoice(
                name="Microsoft Xiaomo Online (Natural) - Chinese (Mainland)",
                short_name="zh-CN-XiaomoNeural",
                gender=VoiceGender.FEMALE,
                locale="zh-CN",
                language="Chinese (Mainland)",
                description="Young female voice, energetic",
                naturalness=4,
                clarity=4,
                expressiveness=5
            )
        ]
        
        # Store voices
        for voice in default_voices:
            self.chinese_voices[voice.short_name] = voice
            self.available_voices[voice.short_name] = voice
    
    async def _load_available_voices(self) -> None:
        """Load available voices from EdgeTTS."""
        try:
            voices = await edge_tts.list_voices()
            
            for voice_info in voices:
                # Extract voice information
                short_name = voice_info.get("ShortName", "")
                name = voice_info.get("FriendlyName", "")
                locale = voice_info.get("Locale", "")
                gender_str = voice_info.get("Gender", "").lower()
                
                # Map gender
                gender = VoiceGender.NEUTRAL
                if gender_str == "male":
                    gender = VoiceGender.MALE
                elif gender_str == "female":
                    gender = VoiceGender.FEMALE
                
                # Create voice object
                voice = TTSVoice(
                    name=name,
                    short_name=short_name,
                    gender=gender,
                    locale=locale,
                    language=voice_info.get("Language", ""),
                    description=f"EdgeTTS voice: {name}",
                    is_neural="Neural" in name
                )
                
                self.available_voices[short_name] = voice
                
                # Add to Chinese voices if applicable
                if locale.startswith("zh-"):
                    self.chinese_voices[short_name] = voice
            
            self._log.info(f"Loaded {len(self.available_voices)} voices from EdgeTTS")
            self._log.info(f"Found {len(self.chinese_voices)} Chinese voices")
            
        except Exception as e:
            self._log.error(f"Failed to load EdgeTTS voices: {e}")
    
    async def generate_speech(
        self,
        text: str,
        voice: Optional[str] = None,
        **kwargs
    ) -> TTSResult:
        """
        Generate speech from text.
        
        Args:
            text: Text to convert to speech
            voice: Voice to use (uses default if None)
            **kwargs: Additional parameters (speed, pitch, volume)
            
        Returns:
            TTS result with audio data
        """
        start_time = datetime.now()
        
        try:
            # Use default voice if not specified
            if voice is None:
                voice = self.config.default_voice
            
            # Validate voice
            if voice not in self.available_voices:
                raise AudioProcessingError(f"Voice not available: {voice}")
            
            # Check cache first
            cache_key = self._generate_cache_key(text, voice, kwargs)
            if self.config.enable_caching and cache_key in self._audio_cache:
                self._cache_hits += 1
                cached_result = self._audio_cache[cache_key]
                cached_result.cached = True
                return cached_result
            
            # Generate speech
            audio_data = await self._generate_audio(text, voice, **kwargs)
            
            # Optimize audio if enabled
            if self.config.enable_audio_optimization:
                audio_data = await self._optimize_audio(audio_data)
            
            # Calculate metrics
            generation_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Create result
            result = TTSResult(
                audio_data=audio_data,
                format=self.config.output_format,
                sample_rate=self.config.sample_rate,
                channels=self.config.channels,
                duration_ms=self._calculate_audio_duration(audio_data),
                text=text,
                voice_used=voice,
                generation_time_ms=generation_time,
                file_size_bytes=len(audio_data),
                optimized=self.config.enable_audio_optimization,
                compressed=self.config.compress_for_telephony
            )
            
            # Cache result
            if self.config.enable_caching:
                await self._cache_result(cache_key, result)
            
            # Update statistics
            self._update_statistics(result)
            
            return result
            
        except Exception as e:
            self._log.error(f"Error generating speech: {e}")
            raise AudioProcessingError(f"TTS generation failed: {e}")
    
    async def _generate_audio(
        self,
        text: str,
        voice: str,
        **kwargs
    ) -> bytes:
        """Generate audio using EdgeTTS."""
        # Extract parameters
        speed = kwargs.get("speed", self.config.voice_speed)
        pitch = kwargs.get("pitch", self.config.voice_pitch)
        volume = kwargs.get("volume", self.config.voice_volume)
        
        # Create SSML if parameters are specified
        if speed != "+0%" or pitch != "+0Hz" or volume != "+0%":
            ssml_text = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
                <voice name="{voice}">
                    <prosody rate="{speed}" pitch="{pitch}" volume="{volume}">
                        {text}
                    </prosody>
                </voice>
            </speak>
            """
        else:
            ssml_text = text
        
        # Generate audio
        communicate = edge_tts.Communicate(ssml_text, voice)
        
        # Collect audio data
        audio_data = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        return audio_data
    
    async def _optimize_audio(self, audio_data: bytes) -> bytes:
        """Optimize audio for telephony."""
        try:
            # Load audio with pydub
            audio = AudioSegment.from_file(io.BytesIO(audio_data), format="mp3")
            
            # Convert to target format
            audio = audio.set_frame_rate(self.config.sample_rate)
            audio = audio.set_channels(self.config.channels)
            audio = audio.set_sample_width(self.config.bit_depth // 8)
            
            # Apply telephony optimizations
            if self.config.telephony_optimization:
                # Apply high-pass filter to remove low frequencies
                audio = audio.high_pass_filter(300)
                
                # Apply low-pass filter to remove high frequencies
                audio = audio.low_pass_filter(3400)
                
                # Normalize volume
                if self.config.enable_volume_normalization:
                    audio = audio.normalize()
                
                # Apply compression
                if self.config.compress_for_telephony:
                    audio = audio.compress_dynamic_range(threshold=-20.0, ratio=4.0)
            
            # Export optimized audio
            output_buffer = io.BytesIO()
            audio.export(
                output_buffer,
                format=self.config.output_format.value,
                bitrate=f"{self.config.target_bitrate_kbps}k" if self.config.output_format == AudioFormat.MP3 else None
            )
            
            return output_buffer.getvalue()
            
        except Exception as e:
            self._log.warning(f"Audio optimization failed: {e}")
            return audio_data  # Return original if optimization fails
    
    def _calculate_audio_duration(self, audio_data: bytes) -> float:
        """Calculate audio duration in milliseconds."""
        try:
            audio = AudioSegment.from_file(io.BytesIO(audio_data))
            return len(audio)  # pydub returns duration in milliseconds
        except Exception:
            # Rough estimation based on file size and sample rate
            bytes_per_second = self.config.sample_rate * self.config.channels * (self.config.bit_depth // 8)
            duration_seconds = len(audio_data) / bytes_per_second
            return duration_seconds * 1000
    
    def _generate_cache_key(self, text: str, voice: str, params: Dict[str, Any]) -> str:
        """Generate cache key for audio."""
        # Create a hash-like key
        key_parts = [
            text,
            voice,
            str(params.get("speed", self.config.voice_speed)),
            str(params.get("pitch", self.config.voice_pitch)),
            str(params.get("volume", self.config.voice_volume)),
            str(self.config.sample_rate),
            str(self.config.channels)
        ]
        
        return "|".join(key_parts)
    
    async def _cache_result(self, cache_key: str, result: TTSResult) -> None:
        """Cache TTS result."""
        # Memory cache
        if len(self._audio_cache) >= 100:  # Simple size limit
            # Remove oldest entry
            oldest_key = next(iter(self._audio_cache))
            del self._audio_cache[oldest_key]
        
        self._audio_cache[cache_key] = result
        
        # File cache if enabled
        if self._cache_directory:
            try:
                cache_file = self._cache_directory / f"{hash(cache_key) % 100000}.{result.format.value}"
                with open(cache_file, 'wb') as f:
                    f.write(result.audio_data)
            except Exception as e:
                self._log.warning(f"Failed to cache to file: {e}")
    
    async def _cleanup_cache_directory(self) -> None:
        """Clean up cache directory."""
        if not self._cache_directory or not self._cache_directory.exists():
            return
        
        try:
            # Calculate cache size
            total_size = sum(f.stat().st_size for f in self._cache_directory.glob("*") if f.is_file())
            max_size = self.config.cache_size_mb * 1024 * 1024
            
            if total_size > max_size:
                # Remove oldest files
                cache_files = list(self._cache_directory.glob("*"))
                cache_files.sort(key=lambda f: f.stat().st_mtime)
                
                for cache_file in cache_files:
                    if total_size <= max_size:
                        break
                    
                    file_size = cache_file.stat().st_size
                    cache_file.unlink()
                    total_size -= file_size
                
                self._log.info(f"Cleaned up cache directory, removed {len(cache_files)} files")
        
        except Exception as e:
            self._log.error(f"Error cleaning cache directory: {e}")
    
    def _update_statistics(self, result: TTSResult) -> None:
        """Update generation statistics."""
        self._total_generations += 1
        self._total_audio_duration += result.duration_ms
        self._total_generation_time += result.generation_time_ms
    
    def get_available_voices(self, language: Optional[str] = None) -> List[TTSVoice]:
        """Get list of available voices."""
        if language:
            return [voice for voice in self.available_voices.values() 
                   if voice.locale.startswith(language)]
        return list(self.available_voices.values())
    
    def get_chinese_voices(self, gender: Optional[VoiceGender] = None) -> List[TTSVoice]:
        """Get Chinese voices, optionally filtered by gender."""
        voices = list(self.chinese_voices.values())
        if gender:
            voices = [voice for voice in voices if voice.gender == gender]
        return voices
    
    def get_recommended_voice(self, gender: Optional[VoiceGender] = None) -> Optional[str]:
        """Get recommended voice for customer service."""
        chinese_voices = self.get_chinese_voices(gender)
        
        if not chinese_voices:
            return None
        
        # Sort by quality metrics
        chinese_voices.sort(
            key=lambda v: (v.naturalness + v.clarity + v.expressiveness),
            reverse=True
        )
        
        return chinese_voices[0].short_name
    
    def get_generator_stats(self) -> Dict[str, Any]:
        """Get generator statistics."""
        avg_generation_time = (
            self._total_generation_time / max(1, self._total_generations)
        )
        
        cache_hit_rate = self._cache_hits / max(1, self._total_generations)
        
        return {
            "total_generations": self._total_generations,
            "cache_hits": self._cache_hits,
            "cache_hit_rate": cache_hit_rate,
            "total_audio_duration_ms": self._total_audio_duration,
            "average_generation_time_ms": avg_generation_time,
            "available_voices": len(self.available_voices),
            "chinese_voices": len(self.chinese_voices),
            "cache_size": len(self._audio_cache),
            "default_voice": self.config.default_voice
        }
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_generator_stats()
        
        self._log.info(f"EdgeTTS generator statistics:")
        self._log.info(f"  Total generations: {stats['total_generations']}")
        self._log.info(f"  Cache hit rate: {stats['cache_hit_rate']:.2%}")
        self._log.info(f"  Average generation time: {stats['average_generation_time_ms']:.2f}ms")
        self._log.info(f"  Total audio duration: {stats['total_audio_duration_ms']:.2f}ms")
    
    async def test_voice(self, voice: str, test_text: str = "您好，这是语音测试。") -> TTSResult:
        """Test a voice with sample text."""
        return await self.generate_speech(test_text, voice)
    
    def validate_voice_parameters(self, **kwargs) -> Dict[str, str]:
        """Validate voice parameters and return normalized values."""
        validated = {}
        
        # Validate speed
        speed = kwargs.get("speed", self.config.voice_speed)
        if isinstance(speed, (int, float)):
            speed = f"{speed:+d}%" if speed >= 0 else f"{speed}%"
        validated["speed"] = speed
        
        # Validate pitch
        pitch = kwargs.get("pitch", self.config.voice_pitch)
        if isinstance(pitch, (int, float)):
            pitch = f"{pitch:+d}Hz" if pitch >= 0 else f"{pitch}Hz"
        validated["pitch"] = pitch
        
        # Validate volume
        volume = kwargs.get("volume", self.config.voice_volume)
        if isinstance(volume, (int, float)):
            volume = f"{volume:+d}%" if volume >= 0 else f"{volume}%"
        validated["volume"] = volume
        
        return validated

# Utility functions for EdgeTTS

async def create_edge_tts_generator(
    default_voice: str = "zh-CN-XiaoxiaoNeural",
    sample_rate: int = 16000,
    enable_optimization: bool = True,
    enable_caching: bool = True,
    config_manager=None,
    **kwargs
) -> EdgeTTSGenerator:
    """
    Create and initialize EdgeTTS generator.
    
    Args:
        default_voice: Default voice to use
        sample_rate: Audio sample rate
        enable_optimization: Enable audio optimization
        enable_caching: Enable result caching
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized EdgeTTS generator
    """
    config = EdgeTTSConfig(
        default_voice=default_voice,
        sample_rate=sample_rate,
        enable_audio_optimization=enable_optimization,
        enable_caching=enable_caching,
        **kwargs
    )
    
    generator = EdgeTTSGenerator(config, config_manager)
    await generator.initialize()
    await generator.start()
    
    return generator


def get_telephony_optimized_config() -> EdgeTTSConfig:
    """Get configuration optimized for telephony."""
    return EdgeTTSConfig(
        default_voice="zh-CN-XiaoxiaoNeural",
        sample_rate=8000,  # Telephony standard
        channels=1,
        bit_depth=16,
        output_format=AudioFormat.WAV,
        telephony_optimization=True,
        compress_for_telephony=True,
        target_bitrate_kbps=32,  # Low bitrate for telephony
        enable_audio_optimization=True,
        enable_noise_reduction=True,
        enable_volume_normalization=True
    )


def get_high_quality_config() -> EdgeTTSConfig:
    """Get configuration for high-quality audio."""
    return EdgeTTSConfig(
        default_voice="zh-CN-XiaoxiaoNeural",
        sample_rate=24000,  # High quality
        channels=1,
        bit_depth=16,
        output_format=AudioFormat.WAV,
        telephony_optimization=False,
        compress_for_telephony=False,
        enable_audio_optimization=True,
        enable_volume_normalization=True
    )


def create_customer_service_voice_config(
    gender: VoiceGender = VoiceGender.FEMALE,
    tone: str = "professional"
) -> Dict[str, Any]:
    """
    Create voice configuration for customer service.
    
    Args:
        gender: Preferred voice gender
        tone: Voice tone (professional, friendly, warm)
        
    Returns:
        Voice configuration parameters
    """
    # Base configuration
    config = {
        "speed": "+0%",
        "pitch": "+0Hz",
        "volume": "+0%"
    }
    
    # Adjust based on tone
    if tone == "professional":
        config["speed"] = "-5%"  # Slightly slower for clarity
        config["pitch"] = "-10Hz"  # Slightly lower for authority
    elif tone == "friendly":
        config["speed"] = "+5%"  # Slightly faster for energy
        config["pitch"] = "+20Hz"  # Slightly higher for friendliness
    elif tone == "warm":
        config["speed"] = "-10%"  # Slower for warmth
        config["volume"] = "-5%"  # Slightly softer
    
    return config


def select_best_chinese_voice(
    available_voices: List[TTSVoice],
    gender: Optional[VoiceGender] = None,
    prioritize_naturalness: bool = True
) -> Optional[TTSVoice]:
    """
    Select the best Chinese voice from available options.
    
    Args:
        available_voices: List of available voices
        gender: Preferred gender
        prioritize_naturalness: Whether to prioritize naturalness over other factors
        
    Returns:
        Best matching voice or None
    """
    # Filter Chinese voices
    chinese_voices = [v for v in available_voices if v.locale.startswith("zh-")]
    
    if not chinese_voices:
        return None
    
    # Filter by gender if specified
    if gender:
        gender_filtered = [v for v in chinese_voices if v.gender == gender]
        if gender_filtered:
            chinese_voices = gender_filtered
    
    # Score voices based on quality metrics
    def score_voice(voice: TTSVoice) -> float:
        if prioritize_naturalness:
            return voice.naturalness * 0.5 + voice.clarity * 0.3 + voice.expressiveness * 0.2
        else:
            return voice.clarity * 0.4 + voice.naturalness * 0.3 + voice.expressiveness * 0.3
    
    # Sort by score and return best
    chinese_voices.sort(key=score_voice, reverse=True)
    return chinese_voices[0]


async def test_tts_performance(
    generator: EdgeTTSGenerator,
    test_texts: List[str],
    voices: List[str]
) -> Dict[str, Any]:
    """
    Test TTS performance with different texts and voices.
    
    Args:
        generator: EdgeTTS generator instance
        test_texts: List of test texts
        voices: List of voices to test
        
    Returns:
        Performance test results
    """
    results = {
        "total_tests": len(test_texts) * len(voices),
        "successful_generations": 0,
        "failed_generations": 0,
        "average_generation_time": 0.0,
        "total_audio_duration": 0.0,
        "voice_performance": {},
        "text_performance": {}
    }
    
    total_generation_time = 0.0
    total_audio_duration = 0.0
    
    for voice in voices:
        voice_results = {
            "successful": 0,
            "failed": 0,
            "avg_generation_time": 0.0,
            "total_duration": 0.0
        }
        
        voice_generation_time = 0.0
        
        for text in test_texts:
            try:
                result = await generator.generate_speech(text, voice)
                
                voice_results["successful"] += 1
                results["successful_generations"] += 1
                
                voice_generation_time += result.generation_time_ms
                voice_results["total_duration"] += result.duration_ms
                
                total_generation_time += result.generation_time_ms
                total_audio_duration += result.duration_ms
                
            except Exception as e:
                voice_results["failed"] += 1
                results["failed_generations"] += 1
        
        if voice_results["successful"] > 0:
            voice_results["avg_generation_time"] = voice_generation_time / voice_results["successful"]
        
        results["voice_performance"][voice] = voice_results
    
    # Calculate overall averages
    if results["successful_generations"] > 0:
        results["average_generation_time"] = total_generation_time / results["successful_generations"]
        results["total_audio_duration"] = total_audio_duration
    
    return results


def format_audio_duration(duration_ms: float) -> str:
    """Format audio duration for display."""
    if duration_ms < 1000:
        return f"{duration_ms:.0f}ms"
    elif duration_ms < 60000:
        return f"{duration_ms/1000:.1f}s"
    else:
        minutes = int(duration_ms // 60000)
        seconds = (duration_ms % 60000) / 1000
        return f"{minutes}m {seconds:.1f}s"


def calculate_audio_quality_score(
    result: TTSResult,
    target_sample_rate: int = 16000,
    target_bitrate: int = 64
) -> float:
    """
    Calculate audio quality score based on various factors.
    
    Args:
        result: TTS result to evaluate
        target_sample_rate: Target sample rate
        target_bitrate: Target bitrate in kbps
        
    Returns:
        Quality score (0.0 to 1.0)
    """
    score = 1.0
    
    # Sample rate factor
    if result.sample_rate < target_sample_rate:
        score *= 0.8
    elif result.sample_rate > target_sample_rate * 1.5:
        score *= 0.9  # Higher sample rate is good but not always necessary
    
    # Duration factor (very short or very long audio might have issues)
    if result.duration_ms < 500:  # Very short
        score *= 0.9
    elif result.duration_ms > 30000:  # Very long
        score *= 0.95
    
    # Compression factor
    if result.compressed and result.compression_ratio > 0.5:
        score *= 0.95  # Good compression
    elif result.compression_ratio < 0.3:
        score *= 0.8  # Over-compressed
    
    # Optimization factor
    if result.optimized:
        score *= 1.05  # Bonus for optimization
    
    return min(1.0, max(0.0, score))


# Voice recommendation functions

def recommend_voice_for_banking(
    available_voices: List[TTSVoice],
    customer_type: str = "general"
) -> Optional[str]:
    """
    Recommend voice for banking customer service.
    
    Args:
        available_voices: Available voices
        customer_type: Type of customer (general, vip, elderly)
        
    Returns:
        Recommended voice short name
    """
    chinese_voices = [v for v in available_voices if v.locale.startswith("zh-")]
    
    if not chinese_voices:
        return None
    
    # Different recommendations based on customer type
    if customer_type == "vip":
        # Prefer professional, clear voices
        candidates = [v for v in chinese_voices if v.clarity >= 4 and v.naturalness >= 4]
        if candidates:
            # Prefer female voices for VIP service
            female_candidates = [v for v in candidates if v.gender == VoiceGender.FEMALE]
            if female_candidates:
                return female_candidates[0].short_name
            return candidates[0].short_name
    
    elif customer_type == "elderly":
        # Prefer slower, clearer voices
        candidates = [v for v in chinese_voices if v.clarity >= 4]
        if candidates:
            # Prefer slightly lower pitch voices
            return candidates[0].short_name
    
    # General recommendation - balanced naturalness and clarity
    candidates = [v for v in chinese_voices if v.naturalness >= 4 and v.clarity >= 4]
    if candidates:
        return candidates[0].short_name
    
    # Fallback to first available Chinese voice
    return chinese_voices[0].short_name if chinese_voices else None


def get_voice_parameters_for_context(
    context: str,
    base_speed: str = "+0%",
    base_pitch: str = "+0Hz",
    base_volume: str = "+0%"
) -> Dict[str, str]:
    """
    Get voice parameters adjusted for specific context.
    
    Args:
        context: Context (greeting, explanation, closing, urgent, etc.)
        base_speed: Base speed setting
        base_pitch: Base pitch setting
        base_volume: Base volume setting
        
    Returns:
        Adjusted voice parameters
    """
    params = {
        "speed": base_speed,
        "pitch": base_pitch,
        "volume": base_volume
    }
    
    # Adjust based on context
    if context == "greeting":
        params["speed"] = "+5%"  # Slightly faster for energy
        params["pitch"] = "+10Hz"  # Slightly higher for friendliness
    
    elif context == "explanation":
        params["speed"] = "-5%"  # Slower for clarity
        params["volume"] = "+5%"  # Slightly louder for emphasis
    
    elif context == "closing":
        params["speed"] = "-10%"  # Slower for warmth
        params["pitch"] = "-5Hz"  # Slightly lower for finality
    
    elif context == "urgent":
        params["speed"] = "+10%"  # Faster for urgency
        params["volume"] = "+10%"  # Louder for attention
    
    elif context == "apologetic":
        params["speed"] = "-15%"  # Much slower for sincerity
        params["pitch"] = "-10Hz"  # Lower for seriousness
        params["volume"] = "-5%"  # Softer for humility
    
    return params