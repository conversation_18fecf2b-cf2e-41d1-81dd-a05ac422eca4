# Base configuration for AI Voice Customer Service System
# This file contains default settings that apply to all environments

audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  buffer_duration: 5.0
  format: "wav"
  
  # Voice Activity Detection settings
  vad_threshold: 0.5
  vad_min_speech_duration: 0.1
  vad_max_silence_duration: 2.0
  
  # Audio processing settings
  noise_reduction: true
  auto_gain_control: true

llm:
  api_key: "${QWEN_API_KEY}"  # Set via environment variable
  model_name: "qwen-turbo"
  base_url: "https://dashscope.aliyuncs.com/api/v1"
  max_tokens: 1000
  temperature: 0.7
  timeout: 30
  
  # Retry configuration
  max_retries: 3
  retry_delay: 1.0
  backoff_factor: 2.0

tts:
  voice: "zh-CN-XiaoxiaoNeural"
  rate: "+0%"
  volume: "+0%"
  pitch: "+0Hz"
  
  # Audio output settings
  output_format: "audio-16khz-32kbitrate-mono-mp3"
  quality: "high"
  
  # Performance settings
  cache_enabled: true
  cache_size: 100

script:
  excel_path: "data/scripts/conversation_scripts.xlsx"
  hot_reload: true
  reload_interval: 5.0
  
  # Matching settings
  fuzzy_threshold: 0.8
  max_suggestions: 5
  fallback_enabled: true

telephony:
  provider: "asterisk"
  server_host: "localhost"
  server_port: 5060
  
  # Call settings
  max_call_duration: 1800  # 30 minutes
  call_timeout: 30
  dtmf_enabled: true
  
  # Audio settings
  codec: "ulaw"
  rtp_port_range: [10000, 20000]

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/aivoicebot.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
  
  # Conversation logging
  conversation_log_enabled: true
  conversation_log_path: "logs/conversations"
  include_audio: false

performance:
  max_concurrent_calls: 10
  memory_limit_mb: 2048
  cpu_limit_percent: 80.0
  
  # Monitoring settings
  metrics_enabled: true
  metrics_port: 8080
  health_check_interval: 30.0
  
  # Optimization settings
  model_cache_size: 3
  audio_buffer_optimization: true