"""
Example configuration for Qwen API client.

This file shows how to configure the Qwen client for different environments
and use cases.
"""

from src.components.llm.qwen_client import QwenClientConfig, QwenModelType


# Development configuration
DEVELOPMENT_CONFIG = QwenClientConfig(
    # API settings
    api_key="your_development_api_key_here",
    model=QwenModelType.QWEN_TURBO,
    
    # Request parameters
    max_tokens=1500,
    temperature=0.7,
    top_p=0.8,
    
    # Retry settings (more aggressive for development)
    max_retries=2,
    retry_delay=1.0,
    retry_backoff=1.5,
    
    # Timeout settings (shorter for development)
    request_timeout=15.0,
    connect_timeout=5.0,
    
    # Rate limiting (more lenient for development)
    requests_per_minute=30,
    enable_rate_limiting=True,
    
    # Safety settings
    enable_content_filter=True,
    max_input_length=6000
)


# Production configuration
PRODUCTION_CONFIG = QwenClientConfig(
    # API settings
    api_key="your_production_api_key_here",
    model=QwenModelType.QWEN_TURBO,
    
    # Request parameters (optimized for production)
    max_tokens=2000,
    temperature=0.6,  # Slightly more deterministic
    top_p=0.9,
    
    # Retry settings (more robust for production)
    max_retries=3,
    retry_delay=2.0,
    retry_backoff=2.0,
    
    # Timeout settings (longer for production stability)
    request_timeout=30.0,
    connect_timeout=10.0,
    
    # Rate limiting (conservative for production)
    requests_per_minute=60,
    enable_rate_limiting=True,
    
    # Safety settings
    enable_content_filter=True,
    max_input_length=8000
)


# High-performance configuration (for high-volume scenarios)
HIGH_PERFORMANCE_CONFIG = QwenClientConfig(
    # API settings
    api_key="your_api_key_here",
    model=QwenModelType.QWEN_PLUS,  # More powerful model
    
    # Request parameters
    max_tokens=2500,
    temperature=0.5,  # More deterministic for consistency
    top_p=0.95,
    
    # Retry settings (minimal retries for speed)
    max_retries=1,
    retry_delay=0.5,
    retry_backoff=1.0,
    
    # Timeout settings (optimized for speed)
    request_timeout=20.0,
    connect_timeout=5.0,
    
    # Rate limiting (higher limits)
    requests_per_minute=120,
    enable_rate_limiting=True,
    
    # Safety settings
    enable_content_filter=True,
    max_input_length=10000
)


# Testing configuration (for unit tests and integration tests)
TESTING_CONFIG = QwenClientConfig(
    # API settings
    api_key="test_api_key",
    model=QwenModelType.QWEN_TURBO,
    
    # Request parameters (minimal for testing)
    max_tokens=500,
    temperature=0.1,  # Very deterministic for testing
    top_p=0.5,
    
    # Retry settings (fast failure for testing)
    max_retries=1,
    retry_delay=0.1,
    retry_backoff=1.0,
    
    # Timeout settings (short for testing)
    request_timeout=5.0,
    connect_timeout=2.0,
    
    # Rate limiting (disabled for testing)
    requests_per_minute=1000,
    enable_rate_limiting=False,
    
    # Safety settings
    enable_content_filter=False,  # Disabled for testing
    max_input_length=2000
)


# Customer service specific configuration
CUSTOMER_SERVICE_CONFIG = QwenClientConfig(
    # API settings
    api_key="your_customer_service_api_key_here",
    model=QwenModelType.QWEN_TURBO,
    
    # Request parameters (optimized for customer service)
    max_tokens=1000,  # Concise responses
    temperature=0.3,  # More consistent and professional
    top_p=0.7,
    
    # Retry settings (reliable for customer interactions)
    max_retries=3,
    retry_delay=1.5,
    retry_backoff=2.0,
    
    # Timeout settings (reasonable for real-time chat)
    request_timeout=25.0,
    connect_timeout=8.0,
    
    # Rate limiting (moderate for customer service)
    requests_per_minute=45,
    enable_rate_limiting=True,
    
    # Safety settings (important for customer service)
    enable_content_filter=True,
    max_input_length=5000
)


def get_config_by_environment(environment: str = "development") -> QwenClientConfig:
    """
    Get configuration by environment name.
    
    Args:
        environment: Environment name ("development", "production", "testing", etc.)
        
    Returns:
        Appropriate configuration
    """
    configs = {
        "development": DEVELOPMENT_CONFIG,
        "production": PRODUCTION_CONFIG,
        "testing": TESTING_CONFIG,
        "high_performance": HIGH_PERFORMANCE_CONFIG,
        "customer_service": CUSTOMER_SERVICE_CONFIG
    }
    
    return configs.get(environment, DEVELOPMENT_CONFIG)


def create_custom_config(**kwargs) -> QwenClientConfig:
    """
    Create custom configuration with overrides.
    
    Args:
        **kwargs: Configuration parameters to override
        
    Returns:
        Custom configuration
    """
    # Start with development config as base
    base_config = DEVELOPMENT_CONFIG
    
    # Create new config with overrides
    config_dict = {
        "api_key": kwargs.get("api_key", base_config.api_key),
        "model": kwargs.get("model", base_config.model),
        "max_tokens": kwargs.get("max_tokens", base_config.max_tokens),
        "temperature": kwargs.get("temperature", base_config.temperature),
        "top_p": kwargs.get("top_p", base_config.top_p),
        "top_k": kwargs.get("top_k", base_config.top_k),
        "max_retries": kwargs.get("max_retries", base_config.max_retries),
        "retry_delay": kwargs.get("retry_delay", base_config.retry_delay),
        "retry_backoff": kwargs.get("retry_backoff", base_config.retry_backoff),
        "request_timeout": kwargs.get("request_timeout", base_config.request_timeout),
        "connect_timeout": kwargs.get("connect_timeout", base_config.connect_timeout),
        "requests_per_minute": kwargs.get("requests_per_minute", base_config.requests_per_minute),
        "enable_rate_limiting": kwargs.get("enable_rate_limiting", base_config.enable_rate_limiting),
        "enable_content_filter": kwargs.get("enable_content_filter", base_config.enable_content_filter),
        "max_input_length": kwargs.get("max_input_length", base_config.max_input_length)
    }
    
    return QwenClientConfig(**config_dict)


# Example usage:
if __name__ == "__main__":
    # Get production config
    prod_config = get_config_by_environment("production")
    print(f"Production model: {prod_config.model.value}")
    
    # Create custom config
    custom_config = create_custom_config(
        api_key="my_custom_key",
        temperature=0.9,
        max_tokens=3000
    )
    print(f"Custom temperature: {custom_config.temperature}")