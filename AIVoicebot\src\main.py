"""
Main Application Entry Point for AI Voice Customer Service System

This module provides the main entry point for the complete AI voice customer
service system, including web API, WebSocket endpoints, and system management.
"""

import asyncio
import signal
import sys
import logging
from pathlib import Path
from typing import Optional
import uvicorn
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.core.system_integrator import AIVoiceCustomerServiceSystem, SystemState
from src.config.config_manager import ConfigManager


# Global system instance
ai_voice_system: Optional[AIVoiceCustomerServiceSystem] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global ai_voice_system

    # Startup
    logging.info("Starting AI Voice Customer Service System...")

    try:
        # Initialize configuration
        config_manager = ConfigManager()

        # Initialize and start the AI voice system
        ai_voice_system = AIVoiceCustomerServiceSystem(config_manager)
        await ai_voice_system.initialize()
        await ai_voice_system.start()

        logging.info("AI Voice Customer Service System started successfully")

        yield

    except Exception as e:
        logging.error(f"Failed to start system: {e}")
        raise

    finally:
        # Shutdown
        logging.info("Shutting down AI Voice Customer Service System...")

        if ai_voice_system:
            try:
                await ai_voice_system.stop()
                await ai_voice_system.cleanup()
            except Exception as e:
                logging.error(f"Error during system shutdown: {e}")

        logging.info("AI Voice Customer Service System shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="AI Voice Customer Service System",
    description="Complete AI-powered voice customer service system",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
static_path = Path(__file__).parent.parent / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")

# Root endpoint - serve HTML interface
@app.get("/")
async def root():
    """Serve the main HTML interface."""
    html_file = Path(__file__).parent.parent / "static" / "index.html"
    if html_file.exists():
        return FileResponse(str(html_file))
    else:
        # Fallback to JSON if HTML not found
        return {
            "message": "AI Voice Customer Service System",
            "version": "1.0.0",
            "status": "running",
            "endpoints": {
                "health": "/health",
                "status": "/status",
                "docs": "/docs",
                "redoc": "/redoc"
            }
        }

# API endpoint for system information
@app.get("/api/info")
async def api_info():
    """API endpoint with system information."""
    return {
        "message": "AI Voice Customer Service System",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "status": "/status",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    }


# Health check endpoints
@app.get("/health")
async def health_check():
    """Basic health check endpoint."""
    if not ai_voice_system:
        raise HTTPException(status_code=503, detail="System not initialized")

    status = ai_voice_system.get_system_status()

    if status.state == SystemState.RUNNING:
        return {
            "status": "healthy",
            "state": status.state.value,
            "uptime": status.system_uptime,
            "active_calls": status.active_calls,
            "components_running": status.components_running,
            "timestamp": status.last_updated.isoformat()
        }
    else:
        raise HTTPException(
            status_code=503,
            detail=f"System is in {status.state.value} state"
        )


@app.get("/status")
async def system_status():
    """Get comprehensive system status."""
    if not ai_voice_system:
        raise HTTPException(status_code=503, detail="System not initialized")

    status = ai_voice_system.get_system_status()
    components = ai_voice_system.get_component_status()

    return {
        "system_status": {
            "state": status.state.value,
            "uptime": status.system_uptime,
            "total_calls_handled": status.total_calls_handled,
            "active_calls": status.active_calls,
            "components_initialized": status.components_initialized,
            "components_running": status.components_running,
            "components_error": status.components_error,
            "last_updated": status.last_updated.isoformat()
        },
        "components": components
    }


# Call handling endpoints
@app.post("/calls/incoming")
async def handle_incoming_call(call_data: dict):
    """Handle incoming call."""
    if not ai_voice_system:
        raise HTTPException(status_code=503, detail="System not initialized")

    caller_number = call_data.get("caller_number")
    callee_number = call_data.get("callee_number")

    if not caller_number or not callee_number:
        raise HTTPException(status_code=400, detail="Missing caller_number or callee_number")

    try:
        call_id = await ai_voice_system.process_incoming_call(caller_number, callee_number)

        if call_id:
            return {
                "status": "success",
                "call_id": call_id,
                "message": "Call session created successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to create call session")

    except Exception as e:
        logging.error(f"Error handling incoming call: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Voice AI endpoints
@app.post("/voice/test-conversation")
async def test_voice_conversation():
    """Test voice AI conversation flow."""
    if not ai_voice_system or not hasattr(ai_voice_system, 'voice_ai_system') or not ai_voice_system.voice_ai_system:
        raise HTTPException(status_code=503, detail="Voice AI system not available")

    try:
        result = await ai_voice_system.voice_ai_system.test_voice_ai_flow()
        return result
    except Exception as e:
        logging.error(f"Voice conversation test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/voice/info")
async def get_voice_info():
    """Get voice AI system information."""
    if not ai_voice_system or not hasattr(ai_voice_system, 'voice_ai_system') or not ai_voice_system.voice_ai_system:
        raise HTTPException(status_code=503, detail="Voice AI system not available")

    try:
        return ai_voice_system.voice_ai_system.get_system_info()
    except Exception as e:
        logging.error(f"Failed to get voice info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/voice/listen")
async def listen_from_microphone():
    """Listen from microphone and return transcription with AI response."""
    if not ai_voice_system or not hasattr(ai_voice_system, 'voice_ai_system') or not ai_voice_system.voice_ai_system:
        raise HTTPException(status_code=503, detail="Voice AI system not available")

    try:
        voice_system = ai_voice_system.voice_ai_system

        logging.info("Starting microphone listening for 5 seconds...")

        # Step 1: Listen from microphone and get transcription (using VAD for intelligent detection)
        asr_result = await voice_system.voice_processor.asr_service.recognize_from_microphone()

        if not asr_result.text.strip():
            return {
                "success": True,
                "transcription": "",
                "response": "没有检测到语音，请重试。",
                "confidence": 0.0,
                "message": "未检测到语音输入"
            }

        logging.info(f"ASR result: '{asr_result.text}' (confidence: {asr_result.confidence:.2f})")

        # Step 2: Get AI response using conversation engine
        try:
            # Create a conversation session
            session_id = await voice_system.conversation_engine.create_session(
                user_id=f"web_user_{int(asyncio.get_event_loop().time())}"
            )

            # Process the message through conversation engine
            result = await voice_system.conversation_engine.process_message(session_id, asr_result.text)

            # End the conversation session
            await voice_system.conversation_engine.end_session(session_id)

            response_text = result.text if hasattr(result, 'text') else "AI处理完成"

            # Limit response length for TTS (avoid too long audio)
            if len(response_text) > 200:
                response_text = response_text[:200] + "..."
                logging.info(f"Response truncated to 200 chars for TTS")

            # Step 3: Generate TTS audio for the response
            try:
                tts_result = await voice_system.voice_processor.tts_service.synthesize_speech(response_text)
                # Convert audio to base64 for web playback
                import base64
                audio_base64 = base64.b64encode(tts_result.audio_data).decode('utf-8')

                logging.info(f"TTS generated successfully, audio length: {len(tts_result.audio_data)} bytes")

            except Exception as tts_error:
                logging.warning(f"TTS generation failed: {tts_error}")
                audio_base64 = None

        except Exception as conv_error:
            logging.warning(f"Conversation engine failed: {conv_error}")
            response_text = f"我听到您说：{asr_result.text}。但AI回复系统暂时不可用。"
            audio_base64 = None

        return {
            "success": True,
            "transcription": asr_result.text,
            "response": response_text,
            "confidence": asr_result.confidence,
            "audio_base64": audio_base64,
            "message": "语音识别和AI回复完成"
        }

    except Exception as e:
        logging.error(f"Error listening from microphone: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "transcription": "",
            "response": "抱歉，无法访问麦克风或处理语音。请检查麦克风权限。"
        }


@app.post("/voice/process")
async def process_voice(audio: UploadFile = File(...)):
    """Process uploaded audio file and return AI response."""
    if not ai_voice_system or not hasattr(ai_voice_system, 'voice_ai_system') or not ai_voice_system.voice_ai_system:
        raise HTTPException(status_code=503, detail="Voice AI system not available")

    try:
        # Read audio data
        audio_data = await audio.read()
        logging.info(f"Received audio file: {audio.filename}, size: {len(audio_data)} bytes")

        # Get the voice AI system
        voice_system = ai_voice_system.voice_ai_system

        # Save audio to temporary file for processing
        import tempfile
        import wave

        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name

        try:
            # Try to process the audio file using the existing demo logic
            # Let's use the voice AI system's process_voice_input method
            call_id = f"web_call_{int(asyncio.get_event_loop().time())}"

            # Process the audio through the voice AI system
            response_audio = await voice_system.process_voice_input(call_id, audio_data, 16000)

            # For now, return a success response
            # In a complete implementation, we would extract the transcription and response text
            return {
                "success": True,
                "transcription": "语音识别处理中...",
                "response": "AI正在处理您的请求...",
                "audio_url": None,
                "call_id": call_id
            }

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        logging.error(f"Error processing voice: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "transcription": "",
            "response": "抱歉，处理您的语音时出现了问题。"
        }


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Main entry point."""
    # Setup logging
    setup_logging()

    # Get configuration
    try:
        config_manager = ConfigManager()
        server_config = config_manager.get_config("server", {})

        host = server_config.get("host", "0.0.0.0")
        port = server_config.get("port", 8000)

        logging.info(f"Starting AI Voice Customer Service System on {host}:{port}")

        # Run the application
        uvicorn.run(
            "src.main:app",
            host=host,
            port=port,
            log_level="info",
            access_log=True,
            reload=False  # Set to True for development
        )

    except Exception as e:
        logging.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
