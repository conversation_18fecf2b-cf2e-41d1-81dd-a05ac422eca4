"""
Telephony System Integration

This module provides comprehensive integration with various telephony systems including:
- xiaozhi-esp32-server project integration
- SIP protocol support for traditional telephony
- WebRTC integration for modern web-based calls
- Audio stream processing and codec support
- Compatibility layer for existing phone infrastructure
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod
import uuid
import socket
import struct
from collections import deque

# Optional websockets import
try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    websockets = None

from ..core.base_component import BaseComponent
from ..core.interfaces import ITelephonyInterface, CallSession


class TelephonyProtocol(Enum):
    """Supported telephony protocols."""
    SIP = "sip"
    WEBSOCKET = "websocket"
    ESP32_CUSTOM = "esp32_custom"
    WEBRTC = "webrtc"
    HTTP_API = "http_api"


class AudioCodec(Enum):
    """Supported audio codecs."""
    PCM_16KHZ = "pcm_16khz"
    PCM_8KHZ = "pcm_8khz"
    OPUS = "opus"
    G711_ULAW = "g711_ulaw"
    G711_ALAW = "g711_alaw"
    G722 = "g722"


class CallDirection(Enum):
    """Call direction types."""
    INBOUND = "inbound"
    OUTBOUND = "outbound"


@dataclass
class AudioFrame:
    """Audio frame data structure."""
    data: bytes
    codec: AudioCodec
    sample_rate: int
    channels: int = 1
    timestamp: datetime = field(default_factory=datetime.now)
    sequence_number: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "codec": self.codec.value,
            "sample_rate": self.sample_rate,
            "channels": self.channels,
            "timestamp": self.timestamp.isoformat(),
            "sequence_number": self.sequence_number,
            "data_length": len(self.data)
        }


@dataclass
class TelephonyEndpoint:
    """Telephony endpoint configuration."""
    endpoint_id: str
    protocol: TelephonyProtocol
    address: str
    port: int
    credentials: Dict[str, str] = field(default_factory=dict)
    supported_codecs: List[AudioCodec] = field(default_factory=list)
    max_concurrent_calls: int = 10
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "endpoint_id": self.endpoint_id,
            "protocol": self.protocol.value,
            "address": self.address,
            "port": self.port,
            "supported_codecs": [codec.value for codec in self.supported_codecs],
            "max_concurrent_calls": self.max_concurrent_calls,
            "enabled": self.enabled
        }


@dataclass
class CallInfo:
    """Call information structure."""
    call_id: str
    session_id: str
    direction: CallDirection
    caller_number: str
    callee_number: str
    protocol: TelephonyProtocol
    codec: AudioCodec
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    status: str = "connecting"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "call_id": self.call_id,
            "session_id": self.session_id,
            "direction": self.direction.value,
            "caller_number": self.caller_number,
            "callee_number": self.callee_number,
            "protocol": self.protocol.value,
            "codec": self.codec.value,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "status": self.status
        }


class TelephonyAdapter(ABC):
    """Abstract base class for telephony adapters."""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the telephony adapter."""
        pass
    
    @abstractmethod
    async def start_listening(self) -> bool:
        """Start listening for incoming calls."""
        pass
    
    @abstractmethod
    async def stop_listening(self) -> bool:
        """Stop listening for incoming calls."""
        pass
    
    @abstractmethod
    async def make_call(self, destination: str, caller_id: str = None) -> Optional[str]:
        """Make an outbound call. Returns call_id if successful."""
        pass
    
    @abstractmethod
    async def answer_call(self, call_id: str) -> bool:
        """Answer an incoming call."""
        pass
    
    @abstractmethod
    async def hangup_call(self, call_id: str) -> bool:
        """Hang up a call."""
        pass
    
    @abstractmethod
    async def send_audio(self, call_id: str, audio_frame: AudioFrame) -> bool:
        """Send audio data to a call."""
        pass
    
    @abstractmethod
    def set_audio_callback(self, callback: Callable[[str, AudioFrame], None]) -> None:
        """Set callback for received audio data."""
        pass
    
    @abstractmethod
    def set_call_event_callback(self, callback: Callable[[str, str, Dict[str, Any]], None]) -> None:
        """Set callback for call events (connected, disconnected, etc.)."""
        pass
    
    @abstractmethod
    def get_supported_codecs(self) -> List[AudioCodec]:
        """Get list of supported audio codecs."""
        pass


class ESP32TelephonyAdapter(TelephonyAdapter):
    """
    Telephony adapter for xiaozhi-esp32-server integration.
    
    Provides WebSocket-based communication with ESP32 devices
    for voice interaction.
    """
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # Configuration
        self.server_host = config.get("esp32_host", "0.0.0.0")
        self.server_port = config.get("esp32_port", 8765)
        self.max_connections = config.get("max_connections", 10)
        
        # Connection management
        self.active_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.call_sessions: Dict[str, CallInfo] = {}
        
        # Callbacks
        self.audio_callback: Optional[Callable[[str, AudioFrame], None]] = None
        self.call_event_callback: Optional[Callable[[str, str, Dict[str, Any]], None]] = None
        
        # Server state
        self.server: Optional[websockets.WebSocketServer] = None
        self.is_listening = False
        
        # Audio processing
        self.supported_codecs = [AudioCodec.PCM_16KHZ, AudioCodec.OPUS]
        self.audio_buffer: Dict[str, deque] = {}
    
    async def initialize(self) -> bool:
        """Initialize the ESP32 telephony adapter."""
        try:
            if not WEBSOCKETS_AVAILABLE:
                self.logger.warning("websockets module not available, ESP32 adapter will not function")
                return False

            self.logger.info(f"Initializing ESP32 telephony adapter on {self.server_host}:{self.server_port}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize ESP32 adapter: {e}")
            return False
    
    async def start_listening(self) -> bool:
        """Start WebSocket server for ESP32 connections."""
        try:
            if not WEBSOCKETS_AVAILABLE:
                self.logger.error("websockets module not available")
                return False

            self.server = await websockets.serve(
                self._handle_connection,
                self.server_host,
                self.server_port,
                max_size=1024*1024,  # 1MB max message size
                ping_interval=30,
                ping_timeout=10
            )

            self.is_listening = True
            self.logger.info(f"ESP32 telephony server started on {self.server_host}:{self.server_port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start ESP32 server: {e}")
            return False
    
    async def stop_listening(self) -> bool:
        """Stop WebSocket server."""
        try:
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None
            
            # Close all active connections
            for connection_id, websocket in list(self.active_connections.items()):
                await websocket.close()
            
            self.active_connections.clear()
            self.call_sessions.clear()
            self.is_listening = False
            
            self.logger.info("ESP32 telephony server stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop ESP32 server: {e}")
            return False
    
    async def make_call(self, destination: str, caller_id: str = None) -> Optional[str]:
        """Make an outbound call (not supported for ESP32 - devices connect to us)."""
        self.logger.warning("Outbound calls not supported for ESP32 adapter")
        return None
    
    async def answer_call(self, call_id: str) -> bool:
        """Answer an incoming call."""
        if call_id not in self.call_sessions:
            return False
        
        call_info = self.call_sessions[call_id]
        call_info.status = "connected"
        
        # Send answer message to ESP32
        connection_id = call_info.session_id
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            message = {
                "type": "call_answered",
                "call_id": call_id,
                "timestamp": datetime.now().isoformat()
            }
            
            try:
                await websocket.send(json.dumps(message))
                self.logger.info(f"Call answered: {call_id}")
                return True
            except Exception as e:
                self.logger.error(f"Failed to send answer message: {e}")
        
        return False
    
    async def hangup_call(self, call_id: str) -> bool:
        """Hang up a call."""
        if call_id not in self.call_sessions:
            return False
        
        call_info = self.call_sessions[call_id]
        call_info.status = "disconnected"
        call_info.end_time = datetime.now()
        
        # Send hangup message to ESP32
        connection_id = call_info.session_id
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            message = {
                "type": "call_hangup",
                "call_id": call_id,
                "timestamp": datetime.now().isoformat()
            }
            
            try:
                await websocket.send(json.dumps(message))
                self.logger.info(f"Call hung up: {call_id}")
                
                # Clean up
                del self.call_sessions[call_id]
                if connection_id in self.audio_buffer:
                    del self.audio_buffer[connection_id]
                
                return True
            except Exception as e:
                self.logger.error(f"Failed to send hangup message: {e}")
        
        return False

    async def send_audio(self, call_id: str, audio_frame: AudioFrame) -> bool:
        """Send audio data to ESP32."""
        if call_id not in self.call_sessions:
            return False

        call_info = self.call_sessions[call_id]
        connection_id = call_info.session_id

        if connection_id not in self.active_connections:
            return False

        websocket = self.active_connections[connection_id]

        # Convert audio frame to ESP32 format
        message = {
            "type": "audio_data",
            "call_id": call_id,
            "codec": audio_frame.codec.value,
            "sample_rate": audio_frame.sample_rate,
            "channels": audio_frame.channels,
            "sequence": audio_frame.sequence_number,
            "timestamp": audio_frame.timestamp.isoformat(),
            "data": audio_frame.data.hex()  # Send as hex string
        }

        try:
            await websocket.send(json.dumps(message))
            return True
        except Exception as e:
            self.logger.error(f"Failed to send audio data: {e}")
            return False

    def set_audio_callback(self, callback: Callable[[str, AudioFrame], None]) -> None:
        """Set callback for received audio data."""
        self.audio_callback = callback

    def set_call_event_callback(self, callback: Callable[[str, str, Dict[str, Any]], None]) -> None:
        """Set callback for call events."""
        self.call_event_callback = callback

    def get_supported_codecs(self) -> List[AudioCodec]:
        """Get supported audio codecs."""
        return self.supported_codecs

    async def _handle_connection(self, websocket, path):
        """Handle new WebSocket connection from ESP32."""
        connection_id = str(uuid.uuid4())
        self.active_connections[connection_id] = websocket

        self.logger.info(f"New ESP32 connection: {connection_id} from {websocket.remote_address}")

        try:
            # Send welcome message
            welcome_message = {
                "type": "welcome",
                "connection_id": connection_id,
                "supported_codecs": [codec.value for codec in self.supported_codecs],
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(welcome_message))

            # Handle messages
            async for message in websocket:
                await self._process_message(connection_id, message)

        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"ESP32 connection closed: {connection_id}")
        except Exception as e:
            self.logger.error(f"Error handling ESP32 connection {connection_id}: {e}")
        finally:
            # Cleanup
            self.active_connections.pop(connection_id, None)

            # End any active calls for this connection
            calls_to_end = [
                call_id for call_id, call_info in self.call_sessions.items()
                if call_info.session_id == connection_id
            ]

            for call_id in calls_to_end:
                await self.hangup_call(call_id)

    async def _process_message(self, connection_id: str, message: str) -> None:
        """Process message from ESP32."""
        try:
            data = json.loads(message)
            message_type = data.get("type")

            if message_type == "call_request":
                await self._handle_call_request(connection_id, data)
            elif message_type == "audio_data":
                await self._handle_audio_data(connection_id, data)
            elif message_type == "call_end":
                await self._handle_call_end(connection_id, data)
            elif message_type == "heartbeat":
                await self._handle_heartbeat(connection_id, data)
            else:
                self.logger.warning(f"Unknown message type from ESP32: {message_type}")

        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON from ESP32 connection {connection_id}")
        except Exception as e:
            self.logger.error(f"Error processing ESP32 message: {e}")

    async def _handle_call_request(self, connection_id: str, data: Dict[str, Any]) -> None:
        """Handle call request from ESP32."""
        call_id = str(uuid.uuid4())
        caller_number = data.get("caller_number", "esp32_device")
        callee_number = data.get("callee_number", "ai_assistant")

        # Create call info
        call_info = CallInfo(
            call_id=call_id,
            session_id=connection_id,
            direction=CallDirection.INBOUND,
            caller_number=caller_number,
            callee_number=callee_number,
            protocol=TelephonyProtocol.ESP32_CUSTOM,
            codec=AudioCodec.PCM_16KHZ,  # Default codec
            status="ringing"
        )

        self.call_sessions[call_id] = call_info
        self.audio_buffer[connection_id] = deque(maxlen=1000)

        # Notify about incoming call
        if self.call_event_callback:
            self.call_event_callback(call_id, "incoming_call", call_info.to_dict())

        self.logger.info(f"Incoming call from ESP32: {call_id}")

    async def _handle_audio_data(self, connection_id: str, data: Dict[str, Any]) -> None:
        """Handle audio data from ESP32."""
        call_id = data.get("call_id")
        if not call_id or call_id not in self.call_sessions:
            return

        try:
            # Parse audio data
            audio_hex = data.get("data", "")
            audio_bytes = bytes.fromhex(audio_hex)

            codec_str = data.get("codec", "pcm_16khz")
            codec = AudioCodec(codec_str)

            audio_frame = AudioFrame(
                data=audio_bytes,
                codec=codec,
                sample_rate=data.get("sample_rate", 16000),
                channels=data.get("channels", 1),
                sequence_number=data.get("sequence", 0)
            )

            # Buffer audio data
            if connection_id in self.audio_buffer:
                self.audio_buffer[connection_id].append(audio_frame)

            # Notify audio callback
            if self.audio_callback:
                self.audio_callback(call_id, audio_frame)

        except Exception as e:
            self.logger.error(f"Error processing audio data from ESP32: {e}")

    async def _handle_call_end(self, connection_id: str, data: Dict[str, Any]) -> None:
        """Handle call end from ESP32."""
        call_id = data.get("call_id")
        if call_id and call_id in self.call_sessions:
            await self.hangup_call(call_id)

    async def _handle_heartbeat(self, connection_id: str, data: Dict[str, Any]) -> None:
        """Handle heartbeat from ESP32."""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            response = {
                "type": "heartbeat_response",
                "timestamp": datetime.now().isoformat()
            }
            try:
                await websocket.send(json.dumps(response))
            except Exception as e:
                self.logger.error(f"Failed to send heartbeat response: {e}")
