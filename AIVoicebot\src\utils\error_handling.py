"""
Error handling utilities for the AI Voice Customer Service system.

This module provides comprehensive error handling patterns including
circuit breakers, retry logic, and graceful degradation strategies.
"""

import asyncio
import logging
from typing import Any, Callable, Optional, Dict, List, Type
from functools import wraps
from datetime import datetime, timedelta
from enum import Enum
import traceback

from ..core.interfaces import AIVoiceServiceError


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """
    Circuit breaker pattern implementation for external service calls.
    
    Prevents cascading failures by temporarily stopping calls to failing services.
    """
    
    def __init__(self,
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 expected_exception: Type[Exception] = Exception):
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying again
            expected_exception: Exception type that triggers circuit opening
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = CircuitState.CLOSED
        
        self._logger = logging.getLogger("aivoice.circuit_breaker")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If circuit is open or function fails
        """
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                self._logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise AIVoiceServiceError("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        return datetime.now() - self.last_failure_time >= timedelta(seconds=self.recovery_timeout)
    
    def _on_success(self) -> None:
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
        if self.state == CircuitState.HALF_OPEN:
            self._logger.info("Circuit breaker reset to CLOSED state")
    
    def _on_failure(self) -> None:
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            self._logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class RetryConfig:
    """Configuration for retry logic."""
    
    def __init__(self,
                 max_attempts: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 exponential_base: float = 2.0,
                 jitter: bool = True):
        """
        Initialize retry configuration.
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay between retries in seconds
            max_delay: Maximum delay between retries in seconds
            exponential_base: Base for exponential backoff
            jitter: Add random jitter to delays
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


async def retry_async(config: RetryConfig,
                     exceptions: tuple = (Exception,),
                     logger: Optional[logging.Logger] = None):
    """
    Decorator for async functions with retry logic.
    
    Args:
        config: Retry configuration
        exceptions: Tuple of exceptions that trigger retry
        logger: Optional logger for retry attempts
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        if logger:
                            logger.error(f"Function {func.__name__} failed after {config.max_attempts} attempts: {e}")
                        break
                    
                    # Calculate delay with exponential backoff
                    delay = min(
                        config.base_delay * (config.exponential_base ** attempt),
                        config.max_delay
                    )
                    
                    # Add jitter if enabled
                    if config.jitter:
                        import random
                        delay *= (0.5 + random.random() * 0.5)
                    
                    if logger:
                        logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{config.max_attempts}), retrying in {delay:.2f}s: {e}")
                    
                    await asyncio.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator


def retry_sync(config: RetryConfig,
              exceptions: tuple = (Exception,),
              logger: Optional[logging.Logger] = None):
    """
    Decorator for sync functions with retry logic.
    
    Args:
        config: Retry configuration
        exceptions: Tuple of exceptions that trigger retry
        logger: Optional logger for retry attempts
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            import time
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        if logger:
                            logger.error(f"Function {func.__name__} failed after {config.max_attempts} attempts: {e}")
                        break
                    
                    # Calculate delay with exponential backoff
                    delay = min(
                        config.base_delay * (config.exponential_base ** attempt),
                        config.max_delay
                    )
                    
                    # Add jitter if enabled
                    if config.jitter:
                        import random
                        delay *= (0.5 + random.random() * 0.5)
                    
                    if logger:
                        logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{config.max_attempts}), retrying in {delay:.2f}s: {e}")
                    
                    time.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator


class ErrorHandler:
    """Centralized error handling and reporting."""
    
    def __init__(self):
        self.error_counts: Dict[str, int] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._logger = logging.getLogger("aivoice.error_handler")
    
    def get_circuit_breaker(self, name: str, **kwargs) -> CircuitBreaker:
        """Get or create circuit breaker for a service."""
        if name not in self.circuit_breakers:
            self.circuit_breakers[name] = CircuitBreaker(**kwargs)
        return self.circuit_breakers[name]
    
    async def handle_error(self,
                          error: Exception,
                          context: Dict[str, Any],
                          recovery_action: Optional[Callable] = None) -> None:
        """
        Handle and log error with context.
        
        Args:
            error: Exception that occurred
            context: Context information about the error
            recovery_action: Optional recovery action to attempt
        """
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Log error with full context
        self._logger.error(
            f"Error occurred: {error}",
            extra={
                "error_type": error_type,
                "error_count": self.error_counts[error_type],
                "context": context,
                "traceback": traceback.format_exc()
            }
        )
        
        # Attempt recovery if provided
        if recovery_action:
            try:
                await recovery_action() if asyncio.iscoroutinefunction(recovery_action) else recovery_action()
                self._logger.info("Recovery action completed successfully")
            except Exception as recovery_error:
                self._logger.error(f"Recovery action failed: {recovery_error}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """Get error statistics."""
        return self.error_counts.copy()
    
    def reset_error_stats(self) -> None:
        """Reset error statistics."""
        self.error_counts.clear()


class GracefulDegradation:
    """Implements graceful degradation strategies."""
    
    def __init__(self):
        self.fallback_handlers: Dict[str, Callable] = {}
        self._logger = logging.getLogger("aivoice.graceful_degradation")
    
    def register_fallback(self, service_name: str, fallback_handler: Callable) -> None:
        """Register fallback handler for a service."""
        self.fallback_handlers[service_name] = fallback_handler
        self._logger.info(f"Registered fallback handler for service: {service_name}")
    
    async def execute_with_fallback(self,
                                   service_name: str,
                                   primary_action: Callable,
                                   *args,
                                   **kwargs) -> Any:
        """
        Execute primary action with fallback on failure.
        
        Args:
            service_name: Name of the service
            primary_action: Primary action to execute
            *args: Arguments for the action
            **kwargs: Keyword arguments for the action
            
        Returns:
            Result from primary action or fallback
        """
        try:
            if asyncio.iscoroutinefunction(primary_action):
                return await primary_action(*args, **kwargs)
            else:
                return primary_action(*args, **kwargs)
        except Exception as e:
            self._logger.warning(f"Primary action failed for {service_name}: {e}")
            
            if service_name in self.fallback_handlers:
                self._logger.info(f"Executing fallback for {service_name}")
                fallback = self.fallback_handlers[service_name]
                
                try:
                    if asyncio.iscoroutinefunction(fallback):
                        return await fallback(*args, **kwargs)
                    else:
                        return fallback(*args, **kwargs)
                except Exception as fallback_error:
                    self._logger.error(f"Fallback also failed for {service_name}: {fallback_error}")
                    raise
            else:
                self._logger.error(f"No fallback registered for {service_name}")
                raise


# Global instances
error_handler = ErrorHandler()
graceful_degradation = GracefulDegradation()


# Convenience decorators
def with_circuit_breaker(name: str, **circuit_kwargs):
    """Decorator to add circuit breaker protection to a function."""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            circuit_breaker = error_handler.get_circuit_breaker(name, **circuit_kwargs)
            return await circuit_breaker.call(func, *args, **kwargs)
        return wrapper
    return decorator


def with_error_handling(context: Dict[str, Any], recovery_action: Optional[Callable] = None):
    """Decorator to add error handling to a function."""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                await error_handler.handle_error(e, context, recovery_action)
                raise
        return wrapper
    return decorator