"""
Test Suite Runner

This module provides a comprehensive test runner for all components
of the AI voice customer service system.
"""

import asyncio
import pytest
import sys
import os
import time
from typing import Dict, List, Any
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from tests.test_utils import MockConfigManager, MockLogger, AudioTestData, TextTestData


class TestSuiteRunner:
    """Comprehensive test suite runner."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # Test categories
        self.test_categories = {
            "unit_tests": [
                "tests/test_prompt_manager.py",
                "tests/test_conversation_context.py",
                "tests/test_call_manager.py",
                "tests/test_performance_monitor.py",
                "tests/test_error_handling.py",
                "tests/test_health_monitor.py",
                "tests/test_component_coordinator.py",
                "tests/test_telephony_integration.py"
            ],
            "comprehensive_tests": [
                "tests/test_prompt_manager_comprehensive.py",
                "tests/test_conversation_context_comprehensive.py",
                "tests/test_audio_processing_comprehensive.py"
            ],
            "integration_tests": [
                "tests/integration/test_e2e_conversation_flow.py",
                "tests/integration/test_integration_scenarios.py"
            ],
            "performance_tests": [
                "tests/performance/test_load_testing.py",
                "tests/performance/test_benchmarks.py"
            ]
        }
    
    def run_all_tests(self, verbose: bool = True) -> Dict[str, Any]:
        """Run all test categories."""
        self.start_time = datetime.now()
        
        print("🚀 Starting AI Voice Customer Service Test Suite")
        print("=" * 60)
        
        # Run each test category
        for category, test_files in self.test_categories.items():
            print(f"\n📋 Running {category.replace('_', ' ').title()}")
            print("-" * 40)
            
            category_results = self._run_test_category(category, test_files, verbose)
            self.test_results[category] = category_results
        
        self.end_time = datetime.now()
        
        # Generate summary report
        self._generate_summary_report()
        
        return self.test_results
    
    def run_category(self, category: str, verbose: bool = True) -> Dict[str, Any]:
        """Run tests for a specific category."""
        if category not in self.test_categories:
            raise ValueError(f"Unknown test category: {category}")
        
        test_files = self.test_categories[category]
        return self._run_test_category(category, test_files, verbose)
    
    def _run_test_category(self, category: str, test_files: List[str], verbose: bool) -> Dict[str, Any]:
        """Run tests for a category."""
        category_results = {
            "total_files": len(test_files),
            "passed_files": 0,
            "failed_files": 0,
            "skipped_files": 0,
            "file_results": {},
            "start_time": datetime.now(),
            "end_time": None
        }
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"  🧪 Running {os.path.basename(test_file)}")
                
                # Run pytest for this file
                result = self._run_pytest_file(test_file, verbose)
                category_results["file_results"][test_file] = result
                
                if result["exit_code"] == 0:
                    category_results["passed_files"] += 1
                    print(f"    ✅ PASSED ({result['passed']} tests)")
                else:
                    category_results["failed_files"] += 1
                    print(f"    ❌ FAILED ({result['failed']} failures, {result['errors']} errors)")
                
                if result["skipped"] > 0:
                    print(f"    ⏭️  SKIPPED ({result['skipped']} tests)")
            else:
                print(f"  ⚠️  Test file not found: {test_file}")
                category_results["skipped_files"] += 1
        
        category_results["end_time"] = datetime.now()
        return category_results
    
    def _run_pytest_file(self, test_file: str, verbose: bool) -> Dict[str, Any]:
        """Run pytest on a single file."""
        import subprocess
        
        # Prepare pytest command
        cmd = [sys.executable, "-m", "pytest", test_file, "--tb=short", "-q"]
        if verbose:
            cmd.extend(["-v", "--tb=long"])
        
        try:
            # Run pytest
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per file
            )
            
            # Parse output to extract test counts
            output = result.stdout + result.stderr
            
            # Extract test statistics
            passed = output.count(" PASSED")
            failed = output.count(" FAILED")
            errors = output.count(" ERROR")
            skipped = output.count(" SKIPPED")
            
            return {
                "exit_code": result.returncode,
                "passed": passed,
                "failed": failed,
                "errors": errors,
                "skipped": skipped,
                "output": output,
                "duration": 0  # Could be extracted from output
            }
            
        except subprocess.TimeoutExpired:
            return {
                "exit_code": -1,
                "passed": 0,
                "failed": 0,
                "errors": 1,
                "skipped": 0,
                "output": "Test timed out after 5 minutes",
                "duration": 300
            }
        except Exception as e:
            return {
                "exit_code": -1,
                "passed": 0,
                "failed": 0,
                "errors": 1,
                "skipped": 0,
                "output": f"Error running test: {str(e)}",
                "duration": 0
            }
    
    def _generate_summary_report(self):
        """Generate and display summary report."""
        print("\n" + "=" * 60)
        print("📊 TEST SUITE SUMMARY REPORT")
        print("=" * 60)
        
        total_duration = (self.end_time - self.start_time).total_seconds()
        
        # Overall statistics
        total_passed = 0
        total_failed = 0
        total_errors = 0
        total_skipped = 0
        total_files = 0
        
        for category, results in self.test_results.items():
            total_files += results["total_files"]
            
            for file_result in results["file_results"].values():
                total_passed += file_result["passed"]
                total_failed += file_result["failed"]
                total_errors += file_result["errors"]
                total_skipped += file_result["skipped"]
        
        print(f"⏱️  Total Duration: {total_duration:.2f} seconds")
        print(f"📁 Total Test Files: {total_files}")
        print(f"✅ Total Passed: {total_passed}")
        print(f"❌ Total Failed: {total_failed}")
        print(f"🚨 Total Errors: {total_errors}")
        print(f"⏭️  Total Skipped: {total_skipped}")
        
        # Category breakdown
        print(f"\n📋 CATEGORY BREAKDOWN:")
        for category, results in self.test_results.items():
            category_name = category.replace('_', ' ').title()
            print(f"  {category_name}:")
            print(f"    Files: {results['passed_files']}/{results['total_files']} passed")
            
            category_passed = sum(r["passed"] for r in results["file_results"].values())
            category_failed = sum(r["failed"] for r in results["file_results"].values())
            category_errors = sum(r["errors"] for r in results["file_results"].values())
            
            print(f"    Tests: {category_passed} passed, {category_failed} failed, {category_errors} errors")
        
        # Success rate
        total_tests = total_passed + total_failed + total_errors
        if total_tests > 0:
            success_rate = (total_passed / total_tests) * 100
            print(f"\n🎯 Overall Success Rate: {success_rate:.1f}%")
        
        # Status
        if total_failed == 0 and total_errors == 0:
            print("\n🎉 ALL TESTS PASSED! 🎉")
        else:
            print(f"\n⚠️  {total_failed + total_errors} tests need attention")
        
        print("=" * 60)
    
    def save_report(self, filename: str = None):
        """Save test results to JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.json"
        
        # Prepare serializable data
        report_data = {
            "timestamp": self.start_time.isoformat() if self.start_time else None,
            "duration": (self.end_time - self.start_time).total_seconds() if self.end_time and self.start_time else 0,
            "results": {}
        }
        
        for category, results in self.test_results.items():
            # Convert datetime objects to strings
            category_data = dict(results)
            if "start_time" in category_data:
                category_data["start_time"] = category_data["start_time"].isoformat()
            if "end_time" in category_data and category_data["end_time"]:
                category_data["end_time"] = category_data["end_time"].isoformat()
            
            report_data["results"][category] = category_data
        
        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Test report saved to: {filename}")
    
    def run_smoke_tests(self) -> bool:
        """Run a quick smoke test to verify basic functionality."""
        print("🔥 Running Smoke Tests")
        print("-" * 30)
        
        smoke_tests = [
            "tests/test_prompt_manager.py::TestPromptManager::test_manager_lifecycle",
            "tests/test_conversation_context.py::TestConversationContext::test_context_lifecycle",
            "tests/test_component_coordinator.py::TestComponentCoordinator::test_coordinator_lifecycle"
        ]
        
        all_passed = True
        
        for test in smoke_tests:
            if "::" in test:
                test_file, test_name = test.split("::", 1)
                if os.path.exists(test_file):
                    print(f"  🧪 {test_name}")
                    result = self._run_pytest_file(test, False)
                    
                    if result["exit_code"] == 0:
                        print(f"    ✅ PASSED")
                    else:
                        print(f"    ❌ FAILED")
                        all_passed = False
                else:
                    print(f"  ⚠️  Test file not found: {test_file}")
                    all_passed = False
        
        if all_passed:
            print("\n🎉 All smoke tests passed!")
        else:
            print("\n⚠️  Some smoke tests failed")
        
        return all_passed


def main():
    """Main entry point for test suite runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI Voice Customer Service Test Suite Runner")
    parser.add_argument("--category", choices=["unit_tests", "comprehensive_tests", "integration_tests", "performance_tests"], 
                       help="Run tests for specific category only")
    parser.add_argument("--smoke", action="store_true", help="Run smoke tests only")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--save-report", help="Save test report to specified file")
    
    args = parser.parse_args()
    
    runner = TestSuiteRunner()
    
    if args.smoke:
        success = runner.run_smoke_tests()
        sys.exit(0 if success else 1)
    elif args.category:
        runner.run_category(args.category, args.verbose)
    else:
        runner.run_all_tests(args.verbose)
    
    if args.save_report:
        runner.save_report(args.save_report)


if __name__ == "__main__":
    main()
