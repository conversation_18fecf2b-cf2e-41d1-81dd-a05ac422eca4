#!/usr/bin/env python3
"""
AI Voice Customer Service System - Basic Usage Examples
======================================================

This file demonstrates how to interact with the AI Voice Customer Service System
through its REST API endpoints.

Prerequisites:
- System running on http://localhost:8000
- Python with requests library installed
"""

import requests
import json
import time
from typing import Dict, Any

# Base URL for the API
BASE_URL = "http://localhost:8000"

class AIVoiceSystemClient:
    """Simple client for interacting with the AI Voice System."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get basic system information."""
        response = self.session.get(f"{self.base_url}/")
        response.raise_for_status()
        return response.json()
    
    def check_health(self) -> Dict[str, Any]:
        """Check system health status."""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def get_status(self) -> Dict[str, Any]:
        """Get detailed system status."""
        response = self.session.get(f"{self.base_url}/status")
        response.raise_for_status()
        return response.json()
    
    def simulate_incoming_call(self, caller_number: str, callee_number: str) -> Dict[str, Any]:
        """Simulate an incoming call."""
        data = {
            "caller_number": caller_number,
            "callee_number": callee_number
        }
        response = self.session.post(f"{self.base_url}/calls/incoming", json=data)
        response.raise_for_status()
        return response.json()

def main():
    """Demonstrate basic usage of the AI Voice System."""
    print("🤖 AI Voice Customer Service System - Basic Usage Demo")
    print("=" * 60)
    
    # Initialize client
    client = AIVoiceSystemClient()
    
    try:
        # 1. Get system information
        print("\n1. 📋 System Information:")
        system_info = client.get_system_info()
        print(f"   System: {system_info['message']}")
        print(f"   Version: {system_info['version']}")
        print(f"   Status: {system_info['status']}")
        print(f"   Available endpoints: {', '.join(system_info['endpoints'].keys())}")
        
        # 2. Check system health
        print("\n2. 🏥 Health Check:")
        health = client.check_health()
        print(f"   Status: {health['status']}")
        print(f"   State: {health['state']}")
        print(f"   Uptime: {health['uptime']:.2f} seconds")
        print(f"   Active calls: {health['active_calls']}")
        print(f"   Running components: {health['components_running']}")
        
        # 3. Get detailed status
        print("\n3. 📊 Detailed Status:")
        status = client.get_status()
        sys_status = status['system_status']
        print(f"   State: {sys_status['state']}")
        print(f"   Uptime: {sys_status['uptime']:.2f} seconds")
        print(f"   Total calls handled: {sys_status['total_calls_handled']}")
        print(f"   Components initialized: {sys_status['components_initialized']}")
        print(f"   Components running: {sys_status['components_running']}")
        print(f"   Components with errors: {sys_status['components_error']}")
        
        # 4. Test call simulation (if endpoint exists)
        print("\n4. 📞 Testing Call Simulation:")
        try:
            call_result = client.simulate_incoming_call("+**********", "+**********")
            print(f"   Call simulation result: {call_result}")
        except requests.exceptions.HTTPError as e:
            print(f"   Call simulation not available yet: {e}")
        
        print("\n✅ All basic tests completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Cannot connect to the AI Voice System.")
        print("   Make sure the system is running on http://localhost:8000")
    except requests.exceptions.HTTPError as e:
        print(f"❌ HTTP Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
