"""
Tests for Call Management System.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock

from src.services.call_manager import <PERSON><PERSON><PERSON><PERSON>, CallManagerConfig
from src.services.mock_telephony import MockTelephony
from src.core.interfaces import (
    IConversationEngine, CallState, CallSession, CallManagementError, TelephonyError
)


@pytest.fixture
def mock_config_manager():
    """Create mock configuration manager."""
    return Mock()

@pytest.fixture
def call_manager_config():
    """Create test CallManager configuration."""
    return CallManagerConfig(max_concurrent_calls=2)

@pytest.fixture
def mock_conversation_engine():
    """Create mock conversation engine."""
    engine = Mock(spec=IConversationEngine)
    engine.is_initialized = True
    engine.is_running = True
    engine.start_session = AsyncMock()
    engine.initialize = AsyncMock()
    engine.start = AsyncMock()
    engine.stop = AsyncMock()
    engine.cleanup = AsyncMock()
    return engine

@pytest.fixture
def mock_telephony_interface(mock_config_manager):
    """Create mock telephony interface."""
    telephony = MockTelephony(mock_config_manager)
    telephony.initialize = AsyncMock()
    telephony.start = AsyncMock()
    telephony.stop = AsyncMock()
    telephony.cleanup = AsyncMock()
    return telephony

@pytest.fixture
def mock_conversation_logger():
    """Create mock conversation logger."""
    logger = Mock()
    logger.log_session_start = AsyncMock()
    logger.log_session_end = AsyncMock()
    return logger

@pytest.fixture
def call_manager(call_manager_config, mock_conversation_engine, mock_telephony_interface, mock_conversation_logger, mock_config_manager):
    """Create CallManager instance for testing."""
    return CallManager(
        config=call_manager_config,
        conversation_engine=mock_conversation_engine,
        telephony_interface=mock_telephony_interface,
        conversation_logger=mock_conversation_logger,
        config_manager=mock_config_manager
    )

@pytest.mark.asyncio
async def test_call_manager_lifecycle(call_manager):
    """Test CallManager lifecycle."""
    await call_manager.initialize()
    await call_manager.start()
    assert call_manager.is_initialized
    assert call_manager.is_running
    await call_manager.stop()
    await call_manager.cleanup()
    assert not call_manager.is_running

@pytest.mark.asyncio
async def test_initiate_call_answered(call_manager):
    """Test initiating a call that is answered."""
    await call_manager.initialize()
    await call_manager.start()

    phone_number = "+1234567890"
    call_manager.telephony_interface.set_next_call_outcome(phone_number, "answered")
    session = await call_manager.initiate_call(phone_number)

    assert isinstance(session, CallSession)
    assert session.phone_number == phone_number
    assert session.state == CallState.INITIALIZING
    assert session.session_id in call_manager.active_calls

    await asyncio.sleep(0.1)
    assert session.state == CallState.ACTIVE

    await call_manager.stop()

@pytest.mark.asyncio
async def test_initiate_call_failed(call_manager):
    """Test initiating a call that fails to connect."""
    await call_manager.initialize()
    await call_manager.start()

    phone_number = "+1FAILFAIL"
    call_manager.telephony_interface.set_next_call_outcome(phone_number, "failed")
    session = await call_manager.initiate_call(phone_number)
    
    await asyncio.sleep(0.1)
    assert session.state == CallState.FAILED
    assert session.session_id not in call_manager.active_calls

    await call_manager.stop()

@pytest.mark.asyncio
async def test_end_call(call_manager):
    """Test ending a call."""
    await call_manager.initialize()
    await call_manager.start()

    phone_number = "+1234567890"
    call_manager.telephony_interface.set_next_call_outcome(phone_number, "answered")
    session = await call_manager.initiate_call(phone_number)
    assert len(call_manager.active_calls) == 1

    summary = await call_manager.end_call(session.session_id)

    assert len(call_manager.active_calls) == 0
    assert summary.session_id == session.session_id
    assert summary.success is True

    await call_manager.stop()

@pytest.mark.asyncio
async def test_max_concurrent_calls(call_manager):
    """Test reaching the maximum concurrent call limit."""
    await call_manager.initialize()
    await call_manager.start()

    # Set outcomes for the calls
    call_manager.telephony_interface.set_next_call_outcome("+1000000001", "answered")
    call_manager.telephony_interface.set_next_call_outcome("+1000000002", "answered")

    # Initiate calls up to the limit
    await call_manager.initiate_call("+1000000001")
    await call_manager.initiate_call("+1000000002")

    assert len(call_manager.active_calls) == 2

    # Try to initiate one more call
    with pytest.raises(CallManagementError):
        await call_manager.initiate_call("+1000000003")

    await call_manager.stop()

@pytest.mark.asyncio
async def test_get_active_sessions(call_manager):
    """Test retrieving active sessions."""
    await call_manager.initialize()
    await call_manager.start()

    call_manager.telephony_interface.set_next_call_outcome("111", "answered")
    call_manager.telephony_interface.set_next_call_outcome("222", "answered")

    session1 = await call_manager.initiate_call("111")
    session2 = await call_manager.initiate_call("222")

    active_sessions = await call_manager.get_active_sessions()
    assert len(active_sessions) == 2
    session_ids = [s.session_id for s in active_sessions]
    assert session1.session_id in session_ids
    assert session2.session_id in session_ids

    await call_manager.stop()