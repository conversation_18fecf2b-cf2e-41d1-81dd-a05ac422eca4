# Telephony system configuration
# This file contains configuration for telephony integration

# Telephony provider configuration
provider:
  name: custom
  type: sip  # sip, twilio, asterisk, custom
  
# SIP configuration (example for SIP-based telephony)
sip:
  server: "sip.example.com"
  port: 5060
  username: ""  # Set via environment variable
  password: ""  # Set via environment variable
  domain: "example.com"
  transport: udp  # udp, tcp, tls
  
  # Audio codec preferences
  codecs:
    - "PCMU"
    - "PCMA"
    - "G722"
  
  # Registration settings
  registration:
    expires: 3600
    retry_interval: 30
    max_retries: 5

# Twilio configuration (example for Twilio integration)
twilio:
  account_sid: ""  # Set via environment variable
  auth_token: ""   # Set via environment variable
  phone_number: "" # Your Twilio phone number
  
  # Voice settings
  voice:
    url: "https://your-webhook-url.com/voice"
    method: "POST"
    timeout: 60
  
  # Recording settings
  recording:
    enabled: false
    channels: "mono"
    format: "wav"

# Asterisk configuration (example for Asterisk integration)
asterisk:
  host: "localhost"
  port: 5038
  username: ""  # Set via environment variable
  password: ""  # Set via environment variable
  
  # AMI (Asterisk Manager Interface) settings
  ami:
    timeout: 30
    keepalive: true
  
  # Dialplan settings
  dialplan:
    context: "aivoice"
    extension_prefix: "8"

# Custom telephony configuration
custom:
  # WebRTC configuration
  webrtc:
    enabled: false
    stun_servers:
      - "stun:stun.l.google.com:19302"
    turn_servers: []
  
  # Audio streaming configuration
  audio:
    sample_rate: 16000
    channels: 1
    format: "pcm"
    buffer_size: 1024
    
  # Connection settings
  connection:
    timeout: 30
    keepalive_interval: 60
    max_reconnect_attempts: 5
    reconnect_delay: 5

# Call routing configuration
routing:
  # Default routing rules
  default_route: "aivoice_handler"
  
  # Route patterns (regex patterns for phone numbers)
  patterns:
    - pattern: "^\\+1\\d{10}$"  # US numbers
      route: "us_handler"
    - pattern: "^\\+86\\d{11}$" # China numbers
      route: "china_handler"
  
  # Fallback configuration
  fallback:
    enabled: true
    route: "human_operator"
    timeout: 300  # 5 minutes

# Quality of Service (QoS) settings
qos:
  # Bandwidth management
  bandwidth:
    max_bitrate: 64000  # 64 kbps
    min_bitrate: 8000   # 8 kbps
  
  # Jitter buffer settings
  jitter_buffer:
    min_delay: 20   # ms
    max_delay: 200  # ms
    adaptive: true
  
  # Packet loss handling
  packet_loss:
    threshold: 0.05  # 5%
    action: "reduce_quality"

# Security settings
security:
  # Encryption
  encryption:
    enabled: true
    method: "SRTP"  # SRTP, DTLS-SRTP
  
  # Authentication
  authentication:
    method: "digest"  # digest, basic, certificate
    realm: "aivoice.local"
  
  # Access control
  access_control:
    whitelist: []  # IP addresses or ranges
    blacklist: []  # IP addresses or ranges
    rate_limiting:
      calls_per_minute: 10
      calls_per_hour: 100

# Monitoring and logging
monitoring:
  # Call detail records (CDR)
  cdr:
    enabled: true
    format: "json"
    storage: "file"  # file, database, syslog
    retention_days: 90
  
  # Real-time monitoring
  realtime:
    enabled: true
    metrics:
      - "call_count"
      - "call_duration"
      - "audio_quality"
      - "error_rate"
  
  # Alerting
  alerting:
    enabled: true
    thresholds:
      error_rate: 0.1      # 10%
      response_time: 5.0   # 5 seconds
      concurrent_calls: 50

# Development and testing
development:
  # Mock telephony for development
  mock_enabled: false
  
  # Test numbers
  test_numbers:
    - "+1234567890"
    - "+8613800138000"
  
  # Simulation settings
  simulation:
    call_duration_range: [30, 300]  # 30 seconds to 5 minutes
    success_rate: 0.95  # 95% success rate
    latency_range: [100, 500]  # 100-500ms latency