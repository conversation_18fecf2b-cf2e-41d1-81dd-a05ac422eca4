"""
End-to-End System Integration Tests

This module contains comprehensive tests for the complete AI Voice Customer
Service system, testing the full conversation flow from call initiation to completion.
"""

import pytest
import asyncio
import json
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from src.core.system_integrator import AIVoiceCustomerServiceSystem, SystemState
from src.core.config_manager import ConfigManager
from tests.test_utils import MockConfigManager


class TestSystemIntegration:
    """Test complete system integration."""
    
    @pytest.fixture
    async def config_manager(self):
        """Create mock configuration manager."""
        return MockConfigManager()
    
    @pytest.fixture
    async def ai_voice_system(self, config_manager):
        """Create AI voice system instance."""
        system = AIVoiceCustomerServiceSystem(config_manager)
        yield system
        
        # Cleanup
        if system.is_running:
            await system.stop()
        await system.cleanup()
    
    @pytest.mark.asyncio
    async def test_system_initialization(self, ai_voice_system):
        """Test system initialization."""
        # Test initialization
        await ai_voice_system.initialize()
        
        assert ai_voice_system.is_initialized
        assert ai_voice_system.system_state == SystemState.INITIALIZING
        assert len(ai_voice_system.components) > 0
        
        # Check that core components are initialized
        expected_components = [
            "extension_manager",
            "component_coordinator", 
            "conversation_logger",
            "prompt_manager",
            "conversation_manager",
            "audio_pipeline",
            "telephony_manager",
            "call_manager",
            "performance_monitor",
            "health_monitor"
        ]
        
        for component_name in expected_components:
            assert component_name in ai_voice_system.components
            component = ai_voice_system.components[component_name]
            assert component.is_initialized
    
    @pytest.mark.asyncio
    async def test_system_startup_shutdown(self, ai_voice_system):
        """Test system startup and shutdown."""
        # Initialize first
        await ai_voice_system.initialize()
        
        # Test startup
        await ai_voice_system.start()
        
        assert ai_voice_system.is_running
        assert ai_voice_system.system_state == SystemState.RUNNING
        assert ai_voice_system.start_time is not None
        
        # Check that all components are running
        for component in ai_voice_system.components.values():
            assert component.is_running
        
        # Test shutdown
        await ai_voice_system.stop()
        
        assert not ai_voice_system.is_running
        assert ai_voice_system.system_state == SystemState.STOPPED
        
        # Check that all components are stopped
        for component in ai_voice_system.components.values():
            assert not component.is_running
    
    @pytest.mark.asyncio
    async def test_system_status(self, ai_voice_system):
        """Test system status reporting."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Get system status
        status = ai_voice_system.get_system_status()
        
        assert status.state == SystemState.RUNNING
        assert status.components_initialized > 0
        assert status.components_running > 0
        assert status.components_error == 0
        assert status.system_uptime >= 0
        assert status.last_updated is not None
    
    @pytest.mark.asyncio
    async def test_component_status(self, ai_voice_system):
        """Test component status reporting."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Get all component status
        component_status = ai_voice_system.get_component_status()
        
        assert isinstance(component_status, dict)
        assert len(component_status) > 0
        
        # Check individual component status
        for name, status in component_status.items():
            assert "name" in status
            assert "type" in status
            assert "initialized" in status
            assert "running" in status
            assert status["initialized"] is True
            assert status["running"] is True
    
    @pytest.mark.asyncio
    async def test_event_system(self, ai_voice_system):
        """Test event handling system."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Test event handler registration
        event_received = []
        
        def test_handler(event_data):
            event_received.append(event_data)
        
        ai_voice_system.register_event_handler("test_event", test_handler)
        
        # Trigger event
        test_data = {"test": "data", "timestamp": datetime.now()}
        await ai_voice_system._trigger_event("test_event", test_data)
        
        # Verify event was received
        assert len(event_received) == 1
        assert event_received[0] == test_data
    
    @pytest.mark.asyncio
    async def test_call_processing_flow(self, ai_voice_system):
        """Test complete call processing flow."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Mock call manager methods
        with patch.object(ai_voice_system.call_manager, 'create_call_session') as mock_create:
            mock_create.return_value = "test_call_123"
            
            # Process incoming call
            call_id = await ai_voice_system.process_incoming_call(
                caller_number="+1234567890",
                callee_number="+0987654321"
            )
            
            assert call_id == "test_call_123"
            mock_create.assert_called_once_with("+1234567890", "+0987654321")
    
    @pytest.mark.asyncio
    async def test_error_handling(self, ai_voice_system):
        """Test system error handling."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Test component error handling
        error_events = []
        
        async def error_handler(event_data):
            error_events.append(event_data)
        
        ai_voice_system.register_event_handler("component_error", error_handler)
        
        # Trigger component error
        await ai_voice_system._trigger_event("component_error", {
            "component": "test_component",
            "error": "Test error message"
        })
        
        assert len(error_events) == 1
        assert error_events[0]["component"] == "test_component"
        assert error_events[0]["error"] == "Test error message"
    
    @pytest.mark.asyncio
    async def test_dependency_order(self, ai_voice_system):
        """Test component dependency ordering."""
        await ai_voice_system.initialize()
        
        # Get component start order
        start_order = ai_voice_system._get_component_start_order()
        
        # Verify dependencies are respected
        dependencies = ai_voice_system.component_dependencies
        
        for i, component in enumerate(start_order):
            component_deps = dependencies.get(component, [])
            
            # Check that all dependencies come before this component
            for dep in component_deps:
                dep_index = start_order.index(dep)
                assert dep_index < i, f"Dependency {dep} should come before {component}"
    
    @pytest.mark.asyncio
    async def test_extension_integration(self, ai_voice_system):
        """Test extension manager integration."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Check extension manager is available
        assert ai_voice_system.extension_manager is not None
        assert ai_voice_system.extension_manager.is_running
        
        # Get extension statistics
        if hasattr(ai_voice_system.extension_manager, 'get_comprehensive_statistics'):
            stats = ai_voice_system.extension_manager.get_comprehensive_statistics()
            assert isinstance(stats, dict)
    
    @pytest.mark.asyncio
    async def test_monitoring_integration(self, ai_voice_system):
        """Test monitoring system integration."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Check performance monitor
        assert ai_voice_system.performance_monitor is not None
        assert ai_voice_system.performance_monitor.is_running
        
        # Check health monitor
        assert ai_voice_system.health_monitor is not None
        assert ai_voice_system.health_monitor.is_running
    
    @pytest.mark.asyncio
    async def test_conversation_flow_integration(self, ai_voice_system):
        """Test conversation flow integration."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Check conversation components
        assert ai_voice_system.conversation_manager is not None
        assert ai_voice_system.prompt_manager is not None
        assert ai_voice_system.conversation_logger is not None
        
        # All should be running
        assert ai_voice_system.conversation_manager.is_running
        assert ai_voice_system.prompt_manager.is_running
        assert ai_voice_system.conversation_logger.is_running
    
    @pytest.mark.asyncio
    async def test_audio_pipeline_integration(self, ai_voice_system):
        """Test audio pipeline integration."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Check audio pipeline
        assert ai_voice_system.audio_pipeline is not None
        assert ai_voice_system.audio_pipeline.is_running
    
    @pytest.mark.asyncio
    async def test_telephony_integration(self, ai_voice_system):
        """Test telephony system integration."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Check telephony manager
        assert ai_voice_system.telephony_manager is not None
        assert ai_voice_system.telephony_manager.is_running
    
    @pytest.mark.asyncio
    async def test_system_recovery(self, ai_voice_system):
        """Test system recovery capabilities."""
        await ai_voice_system.initialize()
        await ai_voice_system.start()
        
        # Simulate system overload
        overload_events = []
        
        async def overload_handler(event_data):
            overload_events.append(event_data)
        
        ai_voice_system.register_event_handler("system_overload", overload_handler)
        
        # Trigger overload event
        await ai_voice_system._trigger_event("system_overload", {
            "cpu_usage": 95.0,
            "memory_usage": 90.0,
            "timestamp": datetime.now()
        })
        
        assert len(overload_events) == 1


class TestSystemIntegrationWithMocks:
    """Test system integration with mocked external dependencies."""
    
    @pytest.mark.asyncio
    async def test_full_conversation_flow(self):
        """Test complete conversation flow with mocks."""
        config_manager = MockConfigManager()
        
        with patch('src.core.system_integrator.ExtensionManager') as mock_ext_mgr, \
             patch('src.core.system_integrator.ComponentCoordinator') as mock_coordinator, \
             patch('src.core.system_integrator.CallManager') as mock_call_mgr, \
             patch('src.core.system_integrator.ConversationManager') as mock_conv_mgr, \
             patch('src.core.system_integrator.PromptManager') as mock_prompt_mgr, \
             patch('src.core.system_integrator.TelephonyManager') as mock_tel_mgr, \
             patch('src.core.system_integrator.AudioPipeline') as mock_audio, \
             patch('src.core.system_integrator.PerformanceMonitor') as mock_perf, \
             patch('src.core.system_integrator.SystemHealthMonitor') as mock_health, \
             patch('src.core.system_integrator.ConversationLogger') as mock_logger:
            
            # Setup mocks
            for mock_class in [mock_ext_mgr, mock_coordinator, mock_call_mgr, 
                             mock_conv_mgr, mock_prompt_mgr, mock_tel_mgr, 
                             mock_audio, mock_perf, mock_health, mock_logger]:
                mock_instance = AsyncMock()
                mock_instance.is_initialized = True
                mock_instance.is_running = True
                mock_instance.component_name = "mock_component"
                mock_instance.component_type = "mock"
                mock_class.return_value = mock_instance
            
            # Create and test system
            system = AIVoiceCustomerServiceSystem(config_manager)
            
            await system.initialize()
            assert system.is_initialized
            
            await system.start()
            assert system.is_running
            
            # Test call processing
            mock_call_mgr.return_value.create_call_session.return_value = "test_call_456"
            
            call_id = await system.process_incoming_call("+**********", "+**********")
            assert call_id == "test_call_456"
            
            await system.stop()
            await system.cleanup()


if __name__ == "__main__":
    pytest.main([__file__])
