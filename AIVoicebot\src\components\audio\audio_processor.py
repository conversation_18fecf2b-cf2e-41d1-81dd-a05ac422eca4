"""
Audio processor for format conversion and preprocessing.

This module provides audio processing utilities including format conversion,
resampling, noise reduction, and audio preprocessing for AI models.
"""

import numpy as np
import scipy.signal
import librosa
from typing import Optional, Tuple, Dict, Any
import time
import logging

from ...core.interfaces import (
    IAudioProcessor, AudioChunk, AudioFormat, 
    AudioProcessingError
)
from ...core.base_component import BaseComponent
from .audio_buffer import AudioChunkProcessor


class AudioProcessor(BaseComponent, IAudioProcessor):
    """
    Audio processor for format conversion and preprocessing.
    
    Handles audio format conversion, resampling, noise reduction,
    and preprocessing for speech recognition models.
    """
    
    def __init__(self, config_manager, logger=None):
        """
        Initialize audio processor.
        
        Args:
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("audio_processor", config_manager, logger)
        
        # Audio processing configuration
        self.target_sample_rate = self.get_config("audio.sample_rate", 16000)
        self.target_channels = self.get_config("audio.channels", 1)
        self.target_format = AudioFormat.PCM_16KHZ_MONO
        
        # Processing options
        self.enable_noise_reduction = self.get_config("audio.enable_noise_reduction", False)
        self.enable_normalization = self.get_config("audio.enable_normalization", True)
        self.enable_dc_removal = self.get_config("audio.enable_dc_removal", True)
        
        # Noise reduction parameters
        self.noise_reduction_strength = self.get_config("audio.noise_reduction_strength", 0.5)
        
        # Normalization parameters
        self.target_rms = self.get_config("audio.target_rms", 0.1)
        self.max_gain = self.get_config("audio.max_gain", 10.0)
        
        # Processing statistics
        self.processed_chunks = 0
        self.total_processing_time = 0.0
        
        # Audio processing utilities
        self.chunk_processor = AudioChunkProcessor()
    
    async def _initialize_impl(self) -> None:
        """Initialize audio processor."""
        self._log.info("Initializing audio processor...")
        
        # Validate configuration
        self._validate_config()
        
        self._log.info(f"Audio processor initialized - Target: {self.target_sample_rate}Hz, "
                      f"{self.target_channels} channel(s), Format: {self.target_format.value}")
    
    async def _start_impl(self) -> None:
        """Start audio processor."""
        self._log.info("Audio processor started")
    
    async def _stop_impl(self) -> None:
        """Stop audio processor."""
        self._log.info("Audio processor stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup audio processor."""
        self._log.info("Audio processor cleanup completed")
    
    def _validate_config(self) -> None:
        """Validate audio processing configuration."""
        if self.target_sample_rate not in [8000, 16000, 22050, 44100, 48000]:
            raise AudioProcessingError(f"Unsupported target sample rate: {self.target_sample_rate}")
        
        if self.target_channels not in [1, 2]:
            raise AudioProcessingError(f"Unsupported target channels: {self.target_channels}")
        
        if not 0.0 <= self.noise_reduction_strength <= 1.0:
            raise AudioProcessingError(f"Invalid noise reduction strength: {self.noise_reduction_strength}")
    
    async def process_audio_chunk(self, audio_chunk: AudioChunk) -> AudioChunk:
        """
        Process audio chunk with format conversion and preprocessing.
        
        Args:
            audio_chunk: Input audio chunk
            
        Returns:
            Processed audio chunk
        """
        start_time = time.time()
        
        try:
            # Convert to numpy array for processing
            audio_data = self._chunk_to_numpy(audio_chunk)
            
            # Apply preprocessing steps
            processed_data = await self._preprocess_audio(
                audio_data, 
                audio_chunk.sample_rate
            )
            
            # Convert back to audio chunk
            processed_chunk = self._numpy_to_chunk(
                processed_data,
                audio_chunk.timestamp,
                self.target_sample_rate
            )
            
            # Update statistics
            processing_time = time.time() - start_time
            self.processed_chunks += 1
            self.total_processing_time += processing_time
            
            return processed_chunk
            
        except Exception as e:
            raise AudioProcessingError(f"Audio processing failed: {e}")
    
    async def start_processing(self) -> None:
        """Start audio processing."""
        if not self.is_running:
            await self.start()
    
    async def stop_processing(self) -> None:
        """Stop audio processing."""
        if self.is_running:
            await self.stop()
    
    def _chunk_to_numpy(self, chunk: AudioChunk) -> np.ndarray:
        """
        Convert audio chunk to numpy array.
        
        Args:
            chunk: Audio chunk
            
        Returns:
            Numpy array with audio data
        """
        # Assume 16-bit PCM data
        audio_data = np.frombuffer(chunk.data, dtype=np.int16)
        
        # Convert to float32 and normalize to [-1, 1]
        audio_data = audio_data.astype(np.float32) / 32768.0
        
        return audio_data
    
    def _numpy_to_chunk(self, data: np.ndarray, timestamp: float, sample_rate: int) -> AudioChunk:
        """
        Convert numpy array to audio chunk.
        
        Args:
            data: Numpy array with audio data
            timestamp: Timestamp for the chunk
            sample_rate: Sample rate
            
        Returns:
            Audio chunk
        """
        # Convert back to 16-bit PCM
        audio_data = (data * 32767).astype(np.int16)
        audio_bytes = audio_data.tobytes()
        
        # Calculate duration
        duration_ms = int(len(data) / sample_rate * 1000)
        
        return AudioChunk(
            data=audio_bytes,
            format=self.target_format,
            timestamp=timestamp,
            duration_ms=duration_ms,
            sample_rate=sample_rate
        )
    
    async def _preprocess_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Apply audio preprocessing steps.
        
        Args:
            audio_data: Input audio data
            sample_rate: Sample rate of input audio
            
        Returns:
            Preprocessed audio data
        """
        processed_data = audio_data.copy()
        
        # Resample if needed
        if sample_rate != self.target_sample_rate:
            processed_data = self._resample_audio(processed_data, sample_rate, self.target_sample_rate)
        
        # Convert to mono if needed
        if len(processed_data.shape) > 1 and processed_data.shape[1] > 1:
            processed_data = self._convert_to_mono(processed_data)
        
        # Remove DC component
        if self.enable_dc_removal:
            processed_data = self._remove_dc_component(processed_data)
        
        # Apply noise reduction
        if self.enable_noise_reduction:
            processed_data = self._reduce_noise(processed_data)
        
        # Normalize audio
        if self.enable_normalization:
            processed_data = self._normalize_audio(processed_data)
        
        return processed_data
    
    def _resample_audio(self, audio_data: np.ndarray, source_rate: int, target_rate: int) -> np.ndarray:
        """
        Resample audio to target sample rate.
        
        Args:
            audio_data: Input audio data
            source_rate: Source sample rate
            target_rate: Target sample rate
            
        Returns:
            Resampled audio data
        """
        if source_rate == target_rate:
            return audio_data
        
        try:
            # Use librosa for high-quality resampling
            resampled = librosa.resample(
                audio_data, 
                orig_sr=source_rate, 
                target_sr=target_rate,
                res_type='kaiser_best'
            )
            return resampled
        except Exception as e:
            self._log.warning(f"Librosa resampling failed, using scipy: {e}")
            
            # Fallback to scipy resampling
            resample_ratio = target_rate / source_rate
            num_samples = int(len(audio_data) * resample_ratio)
            resampled = scipy.signal.resample(audio_data, num_samples)
            return resampled
    
    def _convert_to_mono(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Convert stereo audio to mono.
        
        Args:
            audio_data: Stereo audio data (shape: [samples, channels])
            
        Returns:
            Mono audio data
        """
        if len(audio_data.shape) == 1:
            return audio_data
        
        # Average all channels
        return np.mean(audio_data, axis=1)
    
    def _remove_dc_component(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Remove DC component from audio.
        
        Args:
            audio_data: Input audio data
            
        Returns:
            Audio data with DC component removed
        """
        # Simple high-pass filter to remove DC
        dc_mean = np.mean(audio_data)
        return audio_data - dc_mean
    
    def _reduce_noise(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Apply noise reduction to audio.
        
        Args:
            audio_data: Input audio data
            
        Returns:
            Noise-reduced audio data
        """
        # Simple spectral subtraction-based noise reduction
        try:
            # Estimate noise from first 0.1 seconds (assuming it's silence)
            noise_samples = min(int(0.1 * self.target_sample_rate), len(audio_data) // 4)
            
            if noise_samples > 0:
                noise_level = np.std(audio_data[:noise_samples])
                
                # Apply simple noise gate
                threshold = noise_level * (1 + self.noise_reduction_strength)
                mask = np.abs(audio_data) > threshold
                
                # Gradually reduce noise instead of hard gating
                reduction_factor = 1.0 - self.noise_reduction_strength * 0.5
                audio_data = np.where(mask, audio_data, audio_data * reduction_factor)
            
            return audio_data
            
        except Exception as e:
            self._log.warning(f"Noise reduction failed: {e}")
            return audio_data
    
    def _normalize_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Normalize audio to target RMS level.
        
        Args:
            audio_data: Input audio data
            
        Returns:
            Normalized audio data
        """
        try:
            # Calculate current RMS
            current_rms = np.sqrt(np.mean(audio_data ** 2))
            
            if current_rms > 0:
                # Calculate gain needed
                gain = self.target_rms / current_rms
                
                # Limit maximum gain to prevent excessive amplification
                gain = min(gain, self.max_gain)
                
                # Apply gain
                normalized = audio_data * gain
                
                # Prevent clipping
                max_val = np.max(np.abs(normalized))
                if max_val > 1.0:
                    normalized = normalized / max_val
                
                return normalized
            
            return audio_data
            
        except Exception as e:
            self._log.warning(f"Audio normalization failed: {e}")
            return audio_data
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get audio processing statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        avg_processing_time = (
            self.total_processing_time / self.processed_chunks 
            if self.processed_chunks > 0 else 0
        )
        
        return {
            "processed_chunks": self.processed_chunks,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time,
            "target_sample_rate": self.target_sample_rate,
            "target_channels": self.target_channels,
            "noise_reduction_enabled": self.enable_noise_reduction,
            "normalization_enabled": self.enable_normalization
        }
    
    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self.processed_chunks = 0
        self.total_processing_time = 0.0
    
    async def validate_audio_chunk(self, chunk: AudioChunk) -> bool:
        """
        Validate audio chunk format and content.
        
        Args:
            chunk: Audio chunk to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check basic properties
            if not chunk.data or len(chunk.data) == 0:
                return False
            
            # Check if data length matches duration
            expected_bytes = (
                chunk.duration_ms * chunk.sample_rate * chunk.channels * 2 // 1000
            )  # 2 bytes per sample for 16-bit
            
            # Allow some tolerance
            if abs(len(chunk.data) - expected_bytes) > chunk.sample_rate * 2:  # 1 second tolerance
                return False
            
            # Check for valid audio data (not all zeros or constant)
            audio_array = self._chunk_to_numpy(chunk)
            if np.all(audio_array == 0) or np.std(audio_array) < 1e-6:
                self._log.warning("Audio chunk appears to be silence or constant")
            
            return True
            
        except Exception as e:
            self._log.error(f"Audio chunk validation failed: {e}")
            return False
    
    async def convert_chunk_format(self, chunk: AudioChunk, target_format: AudioFormat) -> AudioChunk:
        """
        Convert audio chunk to target format.
        
        Args:
            chunk: Source audio chunk
            target_format: Target audio format
            
        Returns:
            Converted audio chunk
        """
        if chunk.format == target_format:
            return chunk
        
        # Use the chunk processor for format conversion
        return self.chunk_processor.convert_format(chunk, target_format)