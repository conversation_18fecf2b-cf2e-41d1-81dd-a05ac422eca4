#!/usr/bin/env python3
"""
Health Check Script for AI Voice Customer Service System

This script performs comprehensive health checks for deployed services
and provides detailed status reports.
"""

import asyncio
import aiohttp
import argparse
import json
import sys
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

try:
    import psycopg2
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False


@dataclass
class HealthCheckResult:
    """Health check result for a single service."""
    service_name: str
    status: str  # "healthy", "unhealthy", "warning"
    response_time_ms: float
    details: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SystemHealthReport:
    """Overall system health report."""
    overall_status: str
    total_services: int
    healthy_services: int
    unhealthy_services: int
    warning_services: int
    check_duration_ms: float
    timestamp: datetime
    service_results: List[HealthCheckResult] = field(default_factory=list)


class HealthChecker:
    """Comprehensive health checker for the AI Voice system."""
    
    def __init__(self, base_url: str = "http://localhost:8000", timeout: int = 30):
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def check_system_health(self) -> SystemHealthReport:
        """Perform comprehensive system health check."""
        start_time = time.time()
        
        self.logger.info("Starting comprehensive health check...")
        
        # Define health checks
        health_checks = [
            ("Application API", self._check_application_api),
            ("Database", self._check_database),
            ("Redis", self._check_redis),
            ("WebSocket", self._check_websocket),
            ("Audio Pipeline", self._check_audio_pipeline),
            ("External APIs", self._check_external_apis),
            ("File System", self._check_file_system),
            ("Memory Usage", self._check_memory_usage),
            ("Disk Space", self._check_disk_space)
        ]
        
        results = []
        
        # Run health checks
        for service_name, check_func in health_checks:
            try:
                result = await check_func()
                result.service_name = service_name
                results.append(result)
                
                status_icon = "✓" if result.status == "healthy" else "⚠" if result.status == "warning" else "✗"
                self.logger.info(f"{status_icon} {service_name}: {result.status} ({result.response_time_ms:.1f}ms)")
                
            except Exception as e:
                error_result = HealthCheckResult(
                    service_name=service_name,
                    status="unhealthy",
                    response_time_ms=0,
                    error_message=str(e)
                )
                results.append(error_result)
                self.logger.error(f"✗ {service_name}: Failed - {e}")
        
        # Calculate overall status
        healthy_count = sum(1 for r in results if r.status == "healthy")
        warning_count = sum(1 for r in results if r.status == "warning")
        unhealthy_count = sum(1 for r in results if r.status == "unhealthy")
        
        if unhealthy_count > 0:
            overall_status = "unhealthy"
        elif warning_count > 0:
            overall_status = "warning"
        else:
            overall_status = "healthy"
        
        check_duration = (time.time() - start_time) * 1000
        
        report = SystemHealthReport(
            overall_status=overall_status,
            total_services=len(results),
            healthy_services=healthy_count,
            unhealthy_services=unhealthy_count,
            warning_services=warning_count,
            check_duration_ms=check_duration,
            timestamp=datetime.now(),
            service_results=results
        )
        
        self.logger.info(f"Health check completed in {check_duration:.1f}ms")
        return report
    
    async def _check_application_api(self) -> HealthCheckResult:
        """Check main application API health."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return HealthCheckResult(
                        service_name="Application API",
                        status="healthy",
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="Application API",
                        status="unhealthy",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="Application API",
                status="unhealthy",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_database(self) -> HealthCheckResult:
        """Check database connectivity."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/database") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return HealthCheckResult(
                        service_name="Database",
                        status="healthy",
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="Database",
                        status="unhealthy",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="Database",
                status="unhealthy",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_redis(self) -> HealthCheckResult:
        """Check Redis connectivity."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/redis") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return HealthCheckResult(
                        service_name="Redis",
                        status="healthy",
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="Redis",
                        status="unhealthy",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="Redis",
                status="unhealthy",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_websocket(self) -> HealthCheckResult:
        """Check WebSocket connectivity."""
        start_time = time.time()
        
        try:
            # Try to connect to WebSocket endpoint
            ws_url = self.base_url.replace("http://", "ws://").replace("https://", "wss://")
            ws_url = f"{ws_url.replace(':8000', ':8765')}/ws/health"
            
            async with self.session.ws_connect(ws_url) as ws:
                # Send ping message
                await ws.send_str(json.dumps({"type": "ping"}))
                
                # Wait for response
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        response_time = (time.time() - start_time) * 1000
                        data = json.loads(msg.data)
                        
                        if data.get("type") == "pong":
                            return HealthCheckResult(
                                service_name="WebSocket",
                                status="healthy",
                                response_time_ms=response_time,
                                details={"message": "WebSocket connection successful"}
                            )
                    break
            
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="WebSocket",
                status="unhealthy",
                response_time_ms=response_time,
                error_message="No valid response received"
            )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="WebSocket",
                status="unhealthy",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_audio_pipeline(self) -> HealthCheckResult:
        """Check audio pipeline health."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/audio") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return HealthCheckResult(
                        service_name="Audio Pipeline",
                        status="healthy",
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="Audio Pipeline",
                        status="unhealthy",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="Audio Pipeline",
                status="warning",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_external_apis(self) -> HealthCheckResult:
        """Check external API connectivity."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/external") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return HealthCheckResult(
                        service_name="External APIs",
                        status="healthy",
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="External APIs",
                        status="warning",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="External APIs",
                status="warning",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_file_system(self) -> HealthCheckResult:
        """Check file system health."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/filesystem") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return HealthCheckResult(
                        service_name="File System",
                        status="healthy",
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="File System",
                        status="warning",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="File System",
                status="warning",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_memory_usage(self) -> HealthCheckResult:
        """Check memory usage."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/memory") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    memory_percent = data.get("memory_percent", 0)
                    
                    if memory_percent > 90:
                        status = "unhealthy"
                    elif memory_percent > 80:
                        status = "warning"
                    else:
                        status = "healthy"
                    
                    return HealthCheckResult(
                        service_name="Memory Usage",
                        status=status,
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="Memory Usage",
                        status="warning",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="Memory Usage",
                status="warning",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_disk_space(self) -> HealthCheckResult:
        """Check disk space."""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/health/disk") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    disk_percent = data.get("disk_percent", 0)
                    
                    if disk_percent > 95:
                        status = "unhealthy"
                    elif disk_percent > 85:
                        status = "warning"
                    else:
                        status = "healthy"
                    
                    return HealthCheckResult(
                        service_name="Disk Space",
                        status=status,
                        response_time_ms=response_time,
                        details=data
                    )
                else:
                    return HealthCheckResult(
                        service_name="Disk Space",
                        status="warning",
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}"
                    )
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="Disk Space",
                status="warning",
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    def print_report(self, report: SystemHealthReport, format: str = "text"):
        """Print health check report."""
        if format == "json":
            # Convert to JSON-serializable format
            report_dict = {
                "overall_status": report.overall_status,
                "total_services": report.total_services,
                "healthy_services": report.healthy_services,
                "unhealthy_services": report.unhealthy_services,
                "warning_services": report.warning_services,
                "check_duration_ms": report.check_duration_ms,
                "timestamp": report.timestamp.isoformat(),
                "services": [
                    {
                        "name": result.service_name,
                        "status": result.status,
                        "response_time_ms": result.response_time_ms,
                        "details": result.details,
                        "error_message": result.error_message,
                        "timestamp": result.timestamp.isoformat()
                    }
                    for result in report.service_results
                ]
            }
            print(json.dumps(report_dict, indent=2))
        
        else:
            # Text format
            status_icon = "✓" if report.overall_status == "healthy" else "⚠" if report.overall_status == "warning" else "✗"
            
            print(f"\n{status_icon} AI Voice Customer Service System Health Report")
            print("=" * 60)
            print(f"Overall Status: {report.overall_status.upper()}")
            print(f"Check Duration: {report.check_duration_ms:.1f}ms")
            print(f"Timestamp: {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Services: {report.healthy_services}✓ {report.warning_services}⚠ {report.unhealthy_services}✗")
            print()
            
            # Service details
            for result in report.service_results:
                status_icon = "✓" if result.status == "healthy" else "⚠" if result.status == "warning" else "✗"
                print(f"{status_icon} {result.service_name:<20} {result.status:<10} ({result.response_time_ms:.1f}ms)")
                
                if result.error_message:
                    print(f"    Error: {result.error_message}")
                
                if result.details and format == "verbose":
                    for key, value in result.details.items():
                        print(f"    {key}: {value}")
            
            print()


async def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="AI Voice Customer Service Health Checker")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the service")
    parser.add_argument("--timeout", type=int, default=30, help="Request timeout in seconds")
    parser.add_argument("--format", choices=["text", "json", "verbose"], default="text", help="Output format")
    parser.add_argument("--continuous", action="store_true", help="Run continuous health checks")
    parser.add_argument("--interval", type=int, default=60, help="Interval for continuous checks (seconds)")
    
    args = parser.parse_args()
    
    async with HealthChecker(args.url, args.timeout) as checker:
        if args.continuous:
            print(f"Starting continuous health checks every {args.interval} seconds...")
            print("Press Ctrl+C to stop")
            
            try:
                while True:
                    report = await checker.check_system_health()
                    checker.print_report(report, args.format)
                    
                    if report.overall_status == "unhealthy":
                        print("⚠ System is unhealthy!")
                    
                    await asyncio.sleep(args.interval)
            
            except KeyboardInterrupt:
                print("\nHealth check stopped by user")
        
        else:
            report = await checker.check_system_health()
            checker.print_report(report, args.format)
            
            # Exit with appropriate code
            if report.overall_status == "unhealthy":
                sys.exit(1)
            elif report.overall_status == "warning":
                sys.exit(2)
            else:
                sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
