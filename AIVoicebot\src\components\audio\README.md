# Audio Processing Components

This module provides real-time audio processing capabilities for the AI Voice Customer Service system, including low-latency streaming, buffering, and multi-stream management.

## Components

### CircularAudioBuffer
- **File**: `audio_buffer.py`
- **Purpose**: Efficient circular buffer for audio data with overflow/underflow handling
- **Features**:
  - Lock-free circular buffer implementation
  - Configurable buffer size and chunk size
  - Statistics tracking (overruns, underruns, fill level)
  - Thread-safe operations

### AudioProcessor
- **File**: `audio_processor.py`
- **Purpose**: Audio format conversion and preprocessing
- **Features**:
  - Sample rate conversion
  - Channel mixing/splitting
  - Audio format standardization (16kHz mono)
  - Noise reduction and filtering

### AudioPipeline
- **File**: `audio_pipeline.py`
- **Purpose**: Coordinated audio processing pipeline
- **Features**:
  - Multi-stage processing pipeline
  - Component chaining and coordination
  - Error handling and recovery
  - Performance monitoring

### RealTimeAudioStream
- **File**: `audio_stream.py`
- **Purpose**: Real-time audio streaming with adaptive buffering
- **Features**:
  - Low-latency audio processing (target: 100ms)
  - Adaptive buffering and jitter compensation
  - Stream state management (idle, active, paused, error)
  - Performance statistics and health monitoring
  - Event-driven architecture with callbacks

### AudioStreamManager
- **File**: `stream_manager.py`
- **Purpose**: Multi-stream management and coordination
- **Features**:
  - Concurrent stream management (up to 10 streams by default)
  - Load balancing and resource allocation
  - Health monitoring and automatic cleanup
  - System-wide statistics and performance tracking

### AudioStreaming
- **File**: `streaming.py`
- **Purpose**: High-level streaming utilities and helpers
- **Features**:
  - Simplified streaming API
  - Common streaming patterns
  - Integration helpers

## Usage Examples

### Basic Buffer Usage

```python
from src.components.audio import CircularAudioBuffer, BufferConfig

# Create buffer configuration
config = BufferConfig(
    max_size=1024,  # Hold up to 1024 chunks
    chunk_size=512,  # 512 bytes per chunk
    sample_rate=16000,
    channels=1
)

# Create and use buffer
buffer = CircularAudioBuffer(config)

# Write audio data
success = buffer.write(audio_chunk)

# Read audio data
chunk = buffer.read()
```

### Real-Time Audio Stream

```python
from src.components.audio import RealTimeAudioStream, StreamConfig

# Create stream configuration
config = StreamConfig(
    stream_id="voice_input",
    sample_rate=16000,
    channels=1,
    target_latency_ms=100,
    enable_adaptive_buffering=True
)

# Create and start stream
stream = RealTimeAudioStream(config, config_manager)
await stream.initialize()
await stream.start()

# Process audio
await stream.write_audio(audio_chunk)
processed_chunk = await stream.read_audio()

# Clean up
await stream.stop()
await stream.cleanup()
```

### Multi-Stream Management

```python
from src.components.audio import AudioStreamManager, StreamManagerConfig

# Create manager
manager_config = StreamManagerConfig(
    max_concurrent_streams=5,
    enable_load_balancing=True
)

manager = AudioStreamManager(manager_config, config_manager)
await manager.initialize()
await manager.start()

# Create multiple streams
for stream_id in ["input", "output", "monitoring"]:
    stream_config = StreamConfig(stream_id=stream_id)
    stream = await manager.create_stream(stream_config)

# Use streams through manager
await manager.write_to_stream("input", audio_chunk)
processed = await manager.read_from_stream("output")

# Clean up
await manager.stop()
await manager.cleanup()
```

### Async Generator Pattern

```python
# Stream audio using async generator
async for audio_chunk in manager.get_stream_generator("voice_input"):
    # Process each chunk as it becomes available
    processed = await process_audio(audio_chunk)
    await manager.write_to_stream("voice_output", processed)
```

## Configuration

### Stream Configuration Options

- **stream_id**: Unique identifier for the stream
- **sample_rate**: Audio sample rate (default: 16000 Hz)
- **channels**: Number of audio channels (1 or 2)
- **buffer_size**: Maximum buffer size in chunks (default: 4096)
- **chunk_size**: Size of each audio chunk in bytes (default: 1024)
- **target_latency_ms**: Target processing latency (default: 100ms)
- **enable_adaptive_buffering**: Enable automatic buffer adjustment
- **enable_jitter_compensation**: Enable jitter compensation
- **enable_dropout_detection**: Enable audio dropout detection

### Manager Configuration Options

- **max_concurrent_streams**: Maximum number of concurrent streams (default: 10)
- **enable_load_balancing**: Enable automatic load balancing
- **enable_resource_monitoring**: Enable system resource monitoring
- **health_check_interval_seconds**: Health check frequency (default: 30s)
- **stream_timeout_seconds**: Stream timeout (default: 300s)

## Performance Monitoring

### Stream Statistics

Each stream provides detailed statistics:

- **bytes_processed**: Total bytes processed
- **chunks_processed**: Total chunks processed
- **buffer_overruns**: Number of buffer overflow events
- **buffer_underruns**: Number of buffer underflow events
- **average_latency_ms**: Average processing latency
- **jitter_ms**: Latency variation (jitter)
- **throughput_bps**: Processing throughput in bytes per second

### System Health

The stream manager provides system-wide health monitoring:

- **active_streams**: Number of currently active streams
- **memory_usage_mb**: Memory usage in megabytes
- **cpu_usage_percent**: CPU usage percentage
- **error_rate**: System error rate
- **overall_health**: Overall system health status

## Error Handling

The audio system provides comprehensive error handling:

- **AudioProcessingError**: Base class for audio processing errors
- **Buffer overflow/underflow**: Automatic detection and handling
- **Stream errors**: Automatic recovery and cleanup
- **Resource exhaustion**: Load balancing and resource management
- **Network issues**: Retry logic and graceful degradation

## Integration

### With Voice Activity Detection (VAD)

```python
# Stream audio to VAD system
async for audio_chunk in stream.get_audio_generator():
    voice_probability = await vad_detector.detect_voice_activity(audio_chunk)
    if voice_probability > 0.5:
        # Process speech
        await speech_recognizer.transcribe(audio_chunk)
```

### With Speech Recognition

```python
# Continuous speech recognition
async def process_speech_stream(stream_manager):
    async for audio_chunk in stream_manager.get_stream_generator("voice_input"):
        transcription = await speech_recognizer.transcribe(audio_chunk)
        if transcription.is_final:
            # Process final transcription
            response = await conversation_engine.process_input(transcription.text)
```

### With Text-to-Speech

```python
# Generate and stream TTS audio
async def stream_tts_response(text, stream_manager):
    tts_audio = await tts_engine.synthesize(text)
    await stream_manager.write_to_stream("voice_output", tts_audio)
```

## Testing

Run the test suite:

```bash
python -m pytest tests/test_audio_streaming.py -v
```

Run the examples:

```bash
python examples/audio_streaming_example.py
```

## Performance Considerations

### Latency Optimization

- Use appropriate buffer sizes (smaller = lower latency, larger = more stable)
- Enable adaptive buffering for dynamic adjustment
- Monitor jitter and adjust accordingly
- Use dedicated threads for audio processing

### Memory Management

- Configure appropriate buffer limits
- Enable resource monitoring
- Use circular buffers to avoid memory allocation
- Clean up inactive streams automatically

### Scalability

- Limit concurrent streams based on system capacity
- Use load balancing for optimal resource utilization
- Monitor system health and adjust accordingly
- Implement graceful degradation under load

## Troubleshooting

### High Latency

- Check buffer sizes and target latency settings
- Monitor system resource usage
- Verify network conditions for remote audio
- Enable adaptive buffering

### Audio Dropouts

- Increase buffer sizes
- Check for system resource constraints
- Monitor buffer health status
- Verify audio input stability

### Memory Issues

- Reduce maximum concurrent streams
- Lower buffer sizes if appropriate
- Enable automatic cleanup of inactive streams
- Monitor memory usage statistics

### Performance Issues

- Enable load balancing
- Monitor CPU usage
- Adjust processing priorities
- Consider hardware acceleration