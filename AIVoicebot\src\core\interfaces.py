"""
Core interfaces and abstract base classes for the AI Voice Customer Service system.

This module defines the contracts that all components must implement to ensure
proper integration and maintainability.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import asyncio


class CallState(Enum):
    """Enumeration of possible call states."""
    INITIALIZING = "initializing"
    CONNECTING = "connecting"
    ACTIVE = "active"
    ENDING = "ending"
    COMPLETED = "completed"
    FAILED = "failed"


class AudioFormat(Enum):
    """Supported audio formats."""
    PCM_16KHZ_MONO = "pcm_16khz_mono"
    WAV_16KHZ_MONO = "wav_16khz_mono"


@dataclass
class AudioChunk:
    """Represents a chunk of audio data."""
    data: bytes
    format: AudioFormat
    timestamp: datetime
    duration_ms: int
    sample_rate: int = 16000


@dataclass
class TranscriptionResult:
    """Result from speech recognition."""
    text: str
    confidence: float
    is_final: bool
    timestamp: datetime
    language: Optional[str] = None


@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation."""
    timestamp: datetime
    speaker: str  # 'customer' or 'ai'
    text: str
    audio_duration: float
    confidence_score: float
    intent: Optional[str] = None


@dataclass
class ConversationContext:
    """Maintains conversation state and history."""
    session_id: str
    conversation_history: List[ConversationTurn]
    current_intent: str
    customer_info: Dict[str, Any]
    script_position: str
    metadata: Dict[str, Any]


@dataclass
class CallSession:
    """Represents an active call session."""
    session_id: str
    phone_number: str
    start_time: datetime
    state: CallState
    conversation_context: ConversationContext
    script_id: str
    end_time: Optional[datetime] = None
    call_duration: Optional[float] = None


@dataclass
class CallSummary:
    """Summary of a completed call."""
    session_id: str
    phone_number: str
    start_time: datetime
    end_time: datetime
    duration: float
    total_turns: int
    final_intent: str
    success: bool
    transcript: List[ConversationTurn]


@dataclass
class ScriptResponse:
    """Represents a response from conversation scripts."""
    response_id: str
    text: str
    conditions: List[str]
    follow_up_actions: List[str]
    priority: int
    metadata: Dict[str, Any]


@dataclass
class ConversationResponse:
    """Response generated by the conversation engine."""
    text: str
    audio_data: Optional[bytes]
    next_actions: List[str]
    confidence: float
    source: str  # 'script' or 'llm'
    metadata: Dict[str, Any]


# Core Component Interfaces

class IAudioProcessor(ABC):
    """Interface for audio processing components."""
    
    @abstractmethod
    async def process_audio_chunk(self, audio_chunk: AudioChunk) -> AudioChunk:
        """Process an audio chunk and return the processed result."""
        pass
    
    @abstractmethod
    async def start_processing(self) -> None:
        """Start the audio processing pipeline."""
        pass
    
    @abstractmethod
    async def stop_processing(self) -> None:
        """Stop the audio processing pipeline."""
        pass


class IVoiceActivityDetector(ABC):
    """Interface for voice activity detection."""
    
    @abstractmethod
    async def detect_voice_activity(self, audio_chunk: AudioChunk) -> float:
        """Detect voice activity and return probability (0.0 to 1.0)."""
        pass
    
    @abstractmethod
    async def segment_speech(self, audio_stream: AsyncGenerator[AudioChunk, None]) -> AsyncGenerator[AudioChunk, None]:
        """Segment continuous speech from audio stream."""
        pass


class ISpeechRecognizer(ABC):
    """Interface for speech recognition."""
    
    @abstractmethod
    async def transcribe(self, audio_chunk: AudioChunk) -> TranscriptionResult:
        """Transcribe audio to text."""
        pass
    
    @abstractmethod
    async def transcribe_stream(self, audio_stream: AsyncGenerator[AudioChunk, None]) -> AsyncGenerator[TranscriptionResult, None]:
        """Transcribe streaming audio to text."""
        pass


class ITextToSpeech(ABC):
    """Interface for text-to-speech synthesis."""
    
    @abstractmethod
    async def synthesize(self, text: str, voice_config: Optional[Dict] = None) -> AudioChunk:
        """Synthesize text to speech audio."""
        pass
    
    @abstractmethod
    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices."""
        pass


class ILanguageModel(ABC):
    """Interface for language model integration."""
    
    @abstractmethod
    async def generate_response(self, prompt: str, context: ConversationContext) -> str:
        """Generate a response based on prompt and context."""
        pass
    
    @abstractmethod
    async def classify_intent(self, text: str, context: ConversationContext) -> str:
        """Classify the intent of user input."""
        pass


class IScriptManager(ABC):
    """Interface for conversation script management."""
    
    @abstractmethod
    async def load_scripts(self, script_files: List[str]) -> None:
        """Load conversation scripts from files."""
        pass
    
    @abstractmethod
    async def find_response(self, query: str, context: ConversationContext) -> Optional[ScriptResponse]:
        """Find appropriate script response for a query."""
        pass
    
    @abstractmethod
    async def reload_scripts(self) -> None:
        """Reload scripts from source files."""
        pass


class IConversationEngine(ABC):
    """Interface for conversation orchestration."""
    
    @abstractmethod
    async def process_user_input(self, text: str, session_id: str) -> ConversationResponse:
        """Process user input and generate appropriate response."""
        pass
    
    @abstractmethod
    async def get_conversation_context(self, session_id: str) -> ConversationContext:
        """Get current conversation context."""
        pass
    
    @abstractmethod
    async def reset_conversation(self, session_id: str) -> None:
        """Reset conversation state."""
        pass


class ICallManager(ABC):
    """Interface for call management."""
    
    @abstractmethod
    async def initiate_call(self, phone_number: str, script_id: str) -> CallSession:
        """Initiate a new outbound call."""
        pass
    
    @abstractmethod
    async def end_call(self, session_id: str) -> CallSummary:
        """End an active call and return summary."""
        pass
    
    @abstractmethod
    async def get_active_sessions(self) -> List[CallSession]:
        """Get list of currently active call sessions."""
        pass
    
    @abstractmethod
    async def get_session(self, session_id: str) -> Optional[CallSession]:
        """Get specific call session by ID."""
        pass


class ITelephonyInterface(ABC):
    """Interface for telephony system integration."""
    
    @abstractmethod
    async def make_call(self, phone_number: str) -> str:
        """Make an outbound call and return call ID."""
        pass
    
    @abstractmethod
    async def end_call(self, call_id: str) -> None:
        """End an active call."""
        pass
    
    @abstractmethod
    async def get_audio_stream(self, call_id: str) -> AsyncGenerator[AudioChunk, None]:
        """Get audio stream from active call."""
        pass
    
    @abstractmethod
    async def send_audio(self, call_id: str, audio_chunk: AudioChunk) -> None:
        """Send audio to active call."""
        pass


class IConfigManager(ABC):
    """Interface for configuration management."""
    
    @abstractmethod
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        pass
    
    @abstractmethod
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value."""
        pass
    
    @abstractmethod
    def load_config(self, config_file: str) -> None:
        """Load configuration from file."""
        pass


class ILogger(ABC):
    """Interface for logging and monitoring."""
    
    @abstractmethod
    async def log_conversation_turn(self, session_id: str, turn: ConversationTurn) -> None:
        """Log a conversation turn."""
        pass
    
    @abstractmethod
    async def log_call_summary(self, summary: CallSummary) -> None:
        """Log call summary."""
        pass
    
    @abstractmethod
    async def log_error(self, error: Exception, context: Dict[str, Any]) -> None:
        """Log error with context."""
        pass
    
    @abstractmethod
    async def get_conversation_history(self, session_id: str) -> List[ConversationTurn]:
        """Get conversation history for a session."""
        pass


# Exception Classes

class AIVoiceServiceError(Exception):
    """Base exception for AI Voice Service errors."""
    pass


class AudioProcessingError(AIVoiceServiceError):
    """Error in audio processing."""
    pass


class SpeechRecognitionError(AIVoiceServiceError):
    """Error in speech recognition."""
    pass


class TextToSpeechError(AIVoiceServiceError):
    """Error in text-to-speech synthesis."""
    pass


class LanguageModelError(AIVoiceServiceError):
    """Error in language model processing."""
    pass


class ConversationError(AIVoiceServiceError):
    """Error in conversation processing."""
    pass


class CallManagementError(AIVoiceServiceError):
    """Error in call management."""
    pass


class TelephonyError(AIVoiceServiceError):
    """Error in telephony operations."""
    pass


class ConfigurationError(AIVoiceServiceError):
    """Error in configuration."""
    pass