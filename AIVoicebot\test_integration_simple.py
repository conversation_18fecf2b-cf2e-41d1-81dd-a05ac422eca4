"""
Simple System Integration Test

This script tests the basic system integration functionality
without requiring all components to be available.
"""

import asyncio
import logging
from src.core.system_integrator import AIVoiceCustomerServiceSystem, SystemState
from tests.test_utils import MockConfigManager


async def test_basic_integration():
    """Test basic system integration."""
    print("Starting basic system integration test...")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create system
    config_manager = MockConfigManager()
    system = AIVoiceCustomerServiceSystem(config_manager)
    
    try:
        # Test initialization
        print("Testing system initialization...")
        await system.initialize()
        
        assert system.is_initialized
        assert system.system_state == SystemState.INITIALIZING
        print(f"✓ System initialized with {len(system.components)} components")
        
        # Test startup
        print("Testing system startup...")
        await system.start()
        
        assert system.is_running
        assert system.system_state == SystemState.RUNNING
        print("✓ System started successfully")
        
        # Test status reporting
        print("Testing status reporting...")
        status = system.get_system_status()
        
        assert status.state == SystemState.RUNNING
        assert status.components_initialized >= 0
        assert status.components_running >= 0
        print(f"✓ Status: {status.components_running} components running")
        
        # Test component status
        print("Testing component status...")
        component_status = system.get_component_status()
        
        assert isinstance(component_status, dict)
        print(f"✓ Component status retrieved for {len(component_status)} components")
        
        # Test event system
        print("Testing event system...")
        event_received = []
        
        def test_handler(event_data):
            event_received.append(event_data)
        
        system.register_event_handler("test_event", test_handler)
        
        await system._trigger_event("test_event", {"test": "data"})
        
        assert len(event_received) == 1
        print("✓ Event system working")
        
        # Test shutdown
        print("Testing system shutdown...")
        await system.stop()
        
        assert not system.is_running
        assert system.system_state == SystemState.STOPPED
        print("✓ System stopped successfully")
        
        print("\n🎉 All basic integration tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
    
    finally:
        # Cleanup
        await system.cleanup()
        print("✓ System cleanup completed")


async def test_extension_manager_integration():
    """Test extension manager integration."""
    print("\nTesting extension manager integration...")
    
    config_manager = MockConfigManager()
    system = AIVoiceCustomerServiceSystem(config_manager)
    
    try:
        await system.initialize()
        await system.start()
        
        # Test extension manager
        assert system.extension_manager is not None
        assert system.extension_manager.is_running
        
        # Get extension statistics
        stats = system.extension_manager.get_comprehensive_statistics()
        assert isinstance(stats, dict)
        
        print("✓ Extension manager integration working")
        
        await system.stop()
        
    finally:
        await system.cleanup()


async def test_error_handling():
    """Test system error handling."""
    print("\nTesting error handling...")
    
    config_manager = MockConfigManager()
    system = AIVoiceCustomerServiceSystem(config_manager)
    
    try:
        await system.initialize()
        await system.start()
        
        # Test error event handling
        error_events = []
        
        async def error_handler(event_data):
            error_events.append(event_data)
        
        system.register_event_handler("component_error", error_handler)
        
        # Trigger error event
        await system._trigger_event("component_error", {
            "component": "test_component",
            "error": "Test error"
        })
        
        assert len(error_events) == 1
        assert error_events[0]["component"] == "test_component"
        
        print("✓ Error handling working")
        
        await system.stop()
        
    finally:
        await system.cleanup()


async def test_dependency_ordering():
    """Test component dependency ordering."""
    print("\nTesting dependency ordering...")
    
    config_manager = MockConfigManager()
    system = AIVoiceCustomerServiceSystem(config_manager)
    
    try:
        await system.initialize()
        
        # Test dependency ordering
        start_order = system._get_component_start_order()
        dependencies = system.component_dependencies
        
        # Verify dependencies are respected
        for i, component in enumerate(start_order):
            component_deps = dependencies.get(component, [])
            
            for dep in component_deps:
                if dep in start_order:
                    dep_index = start_order.index(dep)
                    assert dep_index < i, f"Dependency {dep} should come before {component}"
        
        print("✓ Dependency ordering correct")
        
    finally:
        await system.cleanup()


async def main():
    """Run all integration tests."""
    print("=" * 60)
    print("AI Voice Customer Service System - Integration Tests")
    print("=" * 60)
    
    try:
        await test_basic_integration()
        await test_extension_manager_integration()
        await test_error_handling()
        await test_dependency_ordering()
        
        print("\n" + "=" * 60)
        print("🎉 ALL INTEGRATION TESTS PASSED! 🎉")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Integration tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
