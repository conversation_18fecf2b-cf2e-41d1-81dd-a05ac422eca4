"""
Abstract interface for telephony system integration.

This module defines the abstract base class for telephony services, allowing
the system to integrate with different providers like Asterisk, Twilio, etc.
"""

from abc import ABC, abstractmethod
from typing import AsyncGenerator, Optional, Dict, Any, List
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ..core.interfaces import AudioChunk, AIVoiceServiceError


class TelephonyError(AIVoiceServiceError):
    """Custom exception for telephony-related errors."""
    pass


class CallStatus(Enum):
    """Call status enumeration."""
    INITIALIZING = "initializing"
    DIALING = "dialing"
    RINGING = "ringing"
    ANSWERED = "answered"
    BUSY = "busy"
    NO_ANSWER = "no_answer"
    FAILED = "failed"
    ENDED = "ended"


class ConnectionQuality(Enum):
    """Connection quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class CallQualityMetrics:
    """Call quality metrics."""
    jitter_ms: float = 0.0
    packet_loss_percent: float = 0.0
    latency_ms: float = 0.0
    audio_level_db: float = -60.0
    signal_to_noise_ratio: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)

    @property
    def quality_level(self) -> ConnectionQuality:
        """Determine overall quality level based on metrics."""
        if self.packet_loss_percent > 5.0 or self.jitter_ms > 150 or self.latency_ms > 300:
            return ConnectionQuality.CRITICAL
        elif self.packet_loss_percent > 3.0 or self.jitter_ms > 100 or self.latency_ms > 200:
            return ConnectionQuality.POOR
        elif self.packet_loss_percent > 1.0 or self.jitter_ms > 50 or self.latency_ms > 150:
            return ConnectionQuality.FAIR
        elif self.packet_loss_percent > 0.5 or self.jitter_ms > 30 or self.latency_ms > 100:
            return ConnectionQuality.GOOD
        else:
            return ConnectionQuality.EXCELLENT


@dataclass
class CallInfo:
    """Information about an active call."""
    call_id: str
    phone_number: str
    session_id: str
    status: CallStatus
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    quality_metrics: Optional[CallQualityMetrics] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def duration_seconds(self) -> float:
        """Get call duration in seconds."""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()


@dataclass
class ConnectionProblem:
    """Represents a connection problem."""
    problem_type: str
    severity: str
    description: str
    timestamp: datetime = field(default_factory=datetime.now)
    call_id: Optional[str] = None
    metrics: Optional[CallQualityMetrics] = None


class ITelephonyInterface(ABC):
    """Interface for telephony system integration."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the telephony interface."""
        pass

    @abstractmethod
    async def start(self) -> None:
        """Start the telephony interface."""
        pass

    @abstractmethod
    async def stop(self) -> None:
        """Stop the telephony interface."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources."""
        pass

    @property
    @abstractmethod
    def is_initialized(self) -> bool:
        """Check if the interface is initialized."""
        pass

    @property
    @abstractmethod
    def is_running(self) -> bool:
        """Check if the interface is running."""
        pass

    @abstractmethod
    async def make_call(self, phone_number: str, session_id: str) -> str:
        """
        Make an outbound call.
        
        Args:
            phone_number: The phone number to call.
            session_id: The session ID for this call.
            
        Returns:
            A unique call identifier.
        """
        pass

    @abstractmethod
    async def end_call(self, call_id: str) -> None:
        """
        End an active call.
        
        Args:
            call_id: The unique identifier of the call to end.
        """
        pass

    @abstractmethod
    async def get_audio_stream(self, call_id: str) -> AsyncGenerator[AudioChunk, None]:
        """
        Get the audio stream from an active call.
        
        Args:
            call_id: The unique identifier of the call.
            
        Yields:
            AudioChunk objects from the call.
        """
        # This is an async generator, so it should be implemented with `yield`
        # For the abstract method, we can just have this comment.
        if False:
            yield

    @abstractmethod
    async def send_audio(self, call_id: str, audio_chunk: AudioChunk) -> None:
        """
        Send an audio chunk to an active call.

        Args:
            call_id: The unique identifier of the call.
            audio_chunk: The audio chunk to send.
        """
        pass

    @abstractmethod
    async def get_call_info(self, call_id: str) -> Optional[CallInfo]:
        """
        Get information about an active call.

        Args:
            call_id: The unique identifier of the call.

        Returns:
            CallInfo object or None if call not found.
        """
        pass

    @abstractmethod
    async def get_call_quality(self, call_id: str) -> Optional[CallQualityMetrics]:
        """
        Get current quality metrics for a call.

        Args:
            call_id: The unique identifier of the call.

        Returns:
            CallQualityMetrics object or None if call not found.
        """
        pass

    @abstractmethod
    async def get_active_calls(self) -> List[CallInfo]:
        """
        Get information about all active calls.

        Returns:
            List of CallInfo objects for active calls.
        """
        pass

    @abstractmethod
    async def detect_connection_problems(self, call_id: str) -> List[ConnectionProblem]:
        """
        Detect connection problems for a specific call.

        Args:
            call_id: The unique identifier of the call.

        Returns:
            List of detected connection problems.
        """
        pass

    @abstractmethod
    async def get_system_status(self) -> Dict[str, Any]:
        """
        Get overall telephony system status.

        Returns:
            Dictionary containing system status information.
        """
        pass
