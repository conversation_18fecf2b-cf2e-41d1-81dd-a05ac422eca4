"""
Tests for Automatic Speech Recognition (ASR) components.
"""

import asyncio
import pytest
import numpy as np
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.interfaces import AudioChunk, AudioFormat, TranscriptionResult
from src.components.asr import (
    SenseVoiceRecognizer, SenseVoiceConfig,
    ASRProcessor, ASRProcessorConfig, ASRResult,
    TranscriptionBuffer, BufferConfig
)


class TestSenseVoiceRecognizer:
    """Test SenseVoice recognizer functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def sensevoice_config(self):
        """Create test SenseVoice configuration."""
        return SenseVoiceConfig(
            model_path="models/SenseVoiceSmall/model.pt",
            language="zh",
            confidence_threshold=0.3,
            use_gpu=False,  # Use CPU for testing
            sample_rate=16000
        )
    
    def create_test_audio_chunk(self, duration_ms: int = 1000, sample_rate: int = 16000) -> AudioChunk:
        """Create test audio chunk."""
        samples = int(duration_ms * sample_rate / 1000)
        audio_data = np.random.randint(-32768, 32767, samples, dtype=np.int16).tobytes()
        
        return AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=duration_ms,
            sample_rate=sample_rate
        )
    
    @pytest.mark.asyncio
    async def test_recognizer_creation(self, sensevoice_config, mock_config_manager):
        """Test SenseVoice recognizer creation."""
        recognizer = SenseVoiceRecognizer(sensevoice_config, mock_config_manager)
        
        assert recognizer.config == sensevoice_config
        assert recognizer.model is None  # Not initialized yet
        assert not recognizer.is_initialized
    
    @pytest.mark.asyncio
    @patch('torch.load')
    async def test_recognizer_initialization(self, mock_torch_load, sensevoice_config, mock_config_manager):
        """Test SenseVoice recognizer initialization."""
        # Mock model loading
        mock_model = Mock()
        mock_model.eval = Mock()
        mock_torch_load.return_value = mock_model
        
        recognizer = SenseVoiceRecognizer(sensevoice_config, mock_config_manager)
        
        # Mock file existence
        with patch('os.path.exists', return_value=True):
            await recognizer.initialize()
        
        assert recognizer.is_initialized
        assert recognizer.model is not None
        assert recognizer.device is not None
    
    @pytest.mark.asyncio
    @patch('torch.load')
    async def test_speech_recognition(self, mock_torch_load, sensevoice_config, mock_config_manager):
        """Test speech recognition functionality."""
        # Mock model
        mock_model = Mock()
        mock_model.eval = Mock()
        mock_torch_load.return_value = mock_model
        
        recognizer = SenseVoiceRecognizer(sensevoice_config, mock_config_manager)
        
        with patch('os.path.exists', return_value=True):
            await recognizer.initialize()
            await recognizer.start()
        
        # Create test audio
        audio_chunk = self.create_test_audio_chunk()
        
        # Test transcription (will use placeholder implementation)
        result = await recognizer.transcribe(audio_chunk)
        
        assert isinstance(result, TranscriptionResult)
        assert isinstance(result.text, str)
        assert isinstance(result.confidence, float)
        assert 0.0 <= result.confidence <= 1.0
        assert result.is_final
        
        await recognizer.stop()
        await recognizer.cleanup()
    
    @pytest.mark.asyncio
    @patch('torch.load')
    async def test_batch_transcription(self, mock_torch_load, sensevoice_config, mock_config_manager):
        """Test batch transcription."""
        mock_model = Mock()
        mock_model.eval = Mock()
        mock_torch_load.return_value = mock_model
        
        recognizer = SenseVoiceRecognizer(sensevoice_config, mock_config_manager)
        
        with patch('os.path.exists', return_value=True):
            await recognizer.initialize()
            await recognizer.start()
        
        # Create multiple audio chunks
        audio_chunks = [self.create_test_audio_chunk() for _ in range(3)]
        
        # Test batch transcription
        results = await recognizer.transcribe_batch(audio_chunks)
        
        assert len(results) == 3
        for result in results:
            assert isinstance(result, TranscriptionResult)
        
        await recognizer.stop()
        await recognizer.cleanup()
    
    @pytest.mark.asyncio
    @patch('torch.load')
    async def test_recognition_statistics(self, mock_torch_load, sensevoice_config, mock_config_manager):
        """Test recognition statistics collection."""
        mock_model = Mock()
        mock_model.eval = Mock()
        mock_torch_load.return_value = mock_model
        
        recognizer = SenseVoiceRecognizer(sensevoice_config, mock_config_manager)
        
        with patch('os.path.exists', return_value=True):
            await recognizer.initialize()
            await recognizer.start()
        
        # Process some audio
        for i in range(3):
            audio_chunk = self.create_test_audio_chunk()
            await recognizer.transcribe(audio_chunk)
        
        # Get statistics
        stats = recognizer.get_recognition_stats()
        
        assert stats["total_recognitions"] == 3
        assert "average_processing_time_s" in stats
        assert "real_time_factor" in stats
        assert "model_device" in stats
        
        # Reset statistics
        recognizer.reset_stats()
        stats_after_reset = recognizer.get_recognition_stats()
        assert stats_after_reset["total_recognitions"] == 0
        
        await recognizer.stop()
        await recognizer.cleanup()


class TestTranscriptionBuffer:
    """Test transcription buffer functionality."""
    
    @pytest.fixture
    def buffer_config(self):
        """Create test buffer configuration."""
        return BufferConfig(
            max_size=50,
            timeout_ms=1000,
            enable_result_merging=True,
            merge_threshold_ms=500
        )
    
    def create_test_transcription(self, text: str = "测试文本", 
                                confidence: float = 0.8, 
                                is_final: bool = False) -> TranscriptionResult:
        """Create test transcription result."""
        return TranscriptionResult(
            text=text,
            confidence=confidence,
            is_final=is_final,
            timestamp=datetime.now(),
            language="zh"
        )
    
    @pytest.mark.asyncio
    async def test_buffer_creation(self, buffer_config):
        """Test transcription buffer creation."""
        buffer = TranscriptionBuffer(buffer_config)
        
        assert buffer.config == buffer_config
        assert len(buffer) == 0
        assert not buffer.has_final_result()
        assert not buffer.has_partial_results()
    
    @pytest.mark.asyncio
    async def test_buffer_lifecycle(self, buffer_config):
        """Test buffer lifecycle."""
        buffer = TranscriptionBuffer(buffer_config)
        
        await buffer.initialize()
        await buffer.start()
        
        assert buffer.is_initialized
        assert buffer.is_running
        
        await buffer.stop()
        await buffer.cleanup()
    
    @pytest.mark.asyncio
    async def test_add_and_retrieve_results(self, buffer_config):
        """Test adding and retrieving results."""
        buffer = TranscriptionBuffer(buffer_config)
        
        await buffer.initialize()
        await buffer.start()
        
        # Add partial result
        partial_result = self.create_test_transcription("部分结果", is_final=False)
        await buffer.add_result(partial_result)
        
        assert buffer.has_partial_results()
        assert len(buffer) == 1
        
        # Add final result
        final_result = self.create_test_transcription("最终结果", is_final=True)
        await buffer.add_result(final_result)
        
        assert buffer.has_final_result()
        assert len(buffer) == 2
        
        # Retrieve final result
        retrieved_final = await buffer.get_final_result(timeout_ms=100)
        assert retrieved_final is not None
        assert retrieved_final.text == "最终结果"
        assert retrieved_final.is_final
        
        # Retrieve next result (should be partial)
        retrieved_partial = await buffer.get_next_result(timeout_ms=100)
        assert retrieved_partial is not None
        assert retrieved_partial.text == "部分结果"
        assert not retrieved_partial.is_final
        
        await buffer.stop()
        await buffer.cleanup()
    
    @pytest.mark.asyncio
    async def test_result_merging(self, buffer_config):
        """Test result merging functionality."""
        buffer_config.enable_result_merging = True
        buffer = TranscriptionBuffer(buffer_config)
        
        await buffer.initialize()
        await buffer.start()
        
        # Add first partial result
        result1 = self.create_test_transcription("你好", confidence=0.7, is_final=False)
        await buffer.add_result(result1)
        
        # Add second partial result that should merge
        await asyncio.sleep(0.1)  # Small delay
        result2 = self.create_test_transcription("你好世界", confidence=0.8, is_final=False)
        await buffer.add_result(result2)
        
        # Should have merged results
        partial_results = await buffer.get_partial_results()
        
        # Check that merging occurred (exact behavior depends on implementation)
        assert len(partial_results) >= 1
        
        await buffer.stop()
        await buffer.cleanup()
    
    @pytest.mark.asyncio
    async def test_buffer_timeout(self, buffer_config):
        """Test buffer timeout functionality."""
        buffer = TranscriptionBuffer(buffer_config)
        
        await buffer.initialize()
        await buffer.start()
        
        # Try to get result with timeout (should timeout)
        result = await buffer.get_next_result(timeout_ms=100)
        assert result is None
        
        # Try to get final result with timeout (should timeout)
        final_result = await buffer.get_final_result(timeout_ms=100)
        assert final_result is None
        
        await buffer.stop()
        await buffer.cleanup()
    
    @pytest.mark.asyncio
    async def test_buffer_statistics(self, buffer_config):
        """Test buffer statistics."""
        buffer = TranscriptionBuffer(buffer_config)
        
        await buffer.initialize()
        await buffer.start()
        
        # Add some results
        for i in range(3):
            result = self.create_test_transcription(f"结果{i}", is_final=i == 2)
            await buffer.add_result(result)
        
        # Get statistics
        stats = buffer.get_buffer_stats()
        
        assert stats["total_added"] == 3
        assert stats["total_finalized"] == 1
        assert "merge_rate" in stats
        assert "finalization_rate" in stats
        
        # Reset statistics
        buffer.reset_stats()
        stats_after_reset = buffer.get_buffer_stats()
        assert stats_after_reset["total_added"] == 0
        
        await buffer.stop()
        await buffer.cleanup()


class TestASRProcessor:
    """Test ASR processor functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def asr_config(self):
        """Create test ASR processor configuration."""
        return ASRProcessorConfig(
            chunk_duration_ms=1000,
            min_confidence_threshold=0.3,
            enable_confidence_filtering=True,
            max_retry_attempts=2
        )
    
    @pytest.fixture
    def mock_recognizer(self):
        """Create mock SenseVoice recognizer."""
        recognizer = Mock(spec=SenseVoiceRecognizer)
        recognizer.is_initialized = True
        recognizer.is_running = True
        
        # Mock transcription
        async def mock_transcribe(audio_chunk):
            return TranscriptionResult(
                text="模拟转录结果",
                confidence=0.8,
                is_final=True,
                timestamp=datetime.now(),
                language="zh"
            )
        
        recognizer.transcribe = mock_transcribe
        recognizer.get_recognition_stats = Mock(return_value={
            "total_recognitions": 0,
            "real_time_factor": 1.0
        })
        recognizer.reset_stats = Mock()
        
        return recognizer
    
    def create_test_audio_chunk(self) -> AudioChunk:
        """Create test audio chunk."""
        audio_data = np.random.randint(-32768, 32767, 16000, dtype=np.int16).tobytes()
        
        return AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=1000,
            sample_rate=16000
        )
    
    @pytest.mark.asyncio
    async def test_processor_creation(self, asr_config, mock_recognizer, mock_config_manager):
        """Test ASR processor creation."""
        processor = ASRProcessor(asr_config, mock_recognizer, mock_config_manager)
        
        assert processor.config == asr_config
        assert processor.recognizer == mock_recognizer
        assert not processor.is_initialized
    
    @pytest.mark.asyncio
    async def test_processor_lifecycle(self, asr_config, mock_recognizer, mock_config_manager):
        """Test processor lifecycle."""
        processor = ASRProcessor(asr_config, mock_recognizer, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        assert processor.is_initialized
        assert processor.is_running
        
        await processor.stop()
        await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_audio_chunk_processing(self, asr_config, mock_recognizer, mock_config_manager):
        """Test audio chunk processing."""
        processor = ASRProcessor(asr_config, mock_recognizer, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        # Process audio chunk
        audio_chunk = self.create_test_audio_chunk()
        
        # Add result handler to capture results
        results = []
        async def result_handler(result):
            results.append(result)
        
        processor.add_result_handler(result_handler)
        
        # Process chunk
        await processor.process_audio_chunk(audio_chunk)
        
        # Wait a bit for processing
        await asyncio.sleep(0.2)
        
        # Should have received result
        assert len(results) >= 0  # May be 0 if processing is async
        
        await processor.stop()
        await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_processing_statistics(self, asr_config, mock_recognizer, mock_config_manager):
        """Test processing statistics."""
        processor = ASRProcessor(asr_config, mock_recognizer, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        # Process some chunks
        for i in range(3):
            audio_chunk = self.create_test_audio_chunk()
            await processor.process_audio_chunk(audio_chunk)
        
        # Wait for processing
        await asyncio.sleep(0.3)
        
        # Get statistics
        stats = processor.get_processing_stats()
        
        assert "total_processed" in stats
        assert "successful_recognitions" in stats
        assert "success_rate" in stats
        assert "queue_size" in stats
        
        # Reset statistics
        processor.reset_stats()
        stats_after_reset = processor.get_processing_stats()
        assert stats_after_reset["total_processed"] == 0
        
        await processor.stop()
        await processor.cleanup()


# Integration test
@pytest.mark.asyncio
async def test_asr_integration():
    """Integration test for ASR components."""
    # Mock configuration manager
    config_manager = Mock()
    
    # Create configurations
    sensevoice_config = SenseVoiceConfig(
        model_path="models/SenseVoiceSmall/model.pt",
        use_gpu=False,
        confidence_threshold=0.3
    )
    
    asr_config = ASRProcessorConfig(
        chunk_duration_ms=1000,
        min_confidence_threshold=0.3
    )
    
    # Mock SenseVoice model loading
    with patch('torch.load') as mock_torch_load, \
         patch('os.path.exists', return_value=True):
        
        mock_model = Mock()
        mock_model.eval = Mock()
        mock_torch_load.return_value = mock_model
        
        # Create recognizer
        recognizer = SenseVoiceRecognizer(sensevoice_config, config_manager)
        await recognizer.initialize()
        await recognizer.start()
        
        # Create processor
        processor = ASRProcessor(asr_config, recognizer, config_manager)
        await processor.initialize()
        await processor.start()
        
        # Test integration
        audio_data = np.random.randint(-32768, 32767, 16000, dtype=np.int16).tobytes()
        audio_chunk = AudioChunk(
            data=audio_data,
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=1000,
            sample_rate=16000
        )
        
        # Process audio
        await processor.process_audio_chunk(audio_chunk)
        
        # Wait for processing
        await asyncio.sleep(0.2)
        
        # Get statistics
        recognizer_stats = recognizer.get_recognition_stats()
        processor_stats = processor.get_processing_stats()
        
        assert isinstance(recognizer_stats, dict)
        assert isinstance(processor_stats, dict)
        
        # Cleanup
        await processor.stop()
        await processor.cleanup()
        await recognizer.stop()
        await recognizer.cleanup()


if __name__ == "__main__":
    # Run basic tests
    asyncio.run(test_asr_integration())