"""Simple test runner for response matcher"""

import sys
from pathlib import Path

sys.path.append("src")

from components.scripts.response_matcher_simple import (
    ResponseMatcher,
    ScriptEntry,
    MatchResult,
    MatchType,
    create_sample_scripts,
    create_response_matcher
)


def test_script_entry():
    """Test ScriptEntry creation"""
    print("Testing ScriptEntry...")
    
    entry = ScriptEntry(
        id="test_1",
        category="test",
        keywords=["hello", "hi"],
        patterns=["hello world", "hi there"],
        response="Hello! How can I help you?",
        confidence_threshold=0.7,
        priority=1
    )
    
    assert entry.id == "test_1"
    assert entry.category == "test"
    assert len(entry.keywords) == 2
    assert len(entry.patterns) == 2
    assert entry.response == "Hello! How can I help you?"
    
    print("✓ ScriptEntry test passed")


def test_match_result():
    """Test MatchResult creation"""
    print("Testing MatchResult...")
    
    entry = ScriptEntry("test", "test", ["hi"], ["hello"], "Hi there!")
    
    result = MatchResult(
        script_entry=entry,
        confidence=0.8,
        match_type=MatchType.FUZZY,
        matched_text="hello"
    )
    
    assert result.script_entry == entry
    assert result.confidence == 0.8
    assert result.match_type == MatchType.FUZZY
    assert result.matched_text == "hello"
    
    print("✓ MatchResult test passed")


def test_response_matcher_initialization():
    """Test ResponseMatcher initialization"""
    print("Testing ResponseMatcher initialization...")
    
    matcher = ResponseMatcher(fuzzy_threshold=0.7)
    assert matcher.fuzzy_threshold == 0.7
    assert len(matcher.script_entries) == 0
    assert len(matcher.keyword_index) == 0
    
    print("✓ ResponseMatcher initialization test passed")


def test_add_script_entry():
    """Test adding script entries"""
    print("Testing add_script_entry...")
    
    matcher = ResponseMatcher()
    entry = ScriptEntry("test", "greeting", ["hello", "hi"], ["hello world"], "Hi!")
    
    matcher.add_script_entry(entry)
    
    assert len(matcher.script_entries) == 1
    assert matcher.script_entries[0] == entry
    assert "hello" in matcher.keyword_index
    assert "hi" in matcher.keyword_index
    
    print("✓ add_script_entry test passed")


def test_exact_match():
    """Test exact matching"""
    print("Testing exact_match...")
    
    matcher = create_response_matcher()
    
    # Test exact match
    results = matcher.exact_match("你好")
    assert len(results) > 0
    assert results[0].match_type == MatchType.EXACT
    assert results[0].confidence == 1.0
    
    # Test no match
    no_results = matcher.exact_match("xyz123")
    assert len(no_results) == 0
    
    print("✓ exact_match test passed")


def test_fuzzy_match():
    """Test fuzzy matching"""
    print("Testing fuzzy_match...")
    
    matcher = create_response_matcher()
    
    # Test fuzzy match
    results = matcher.fuzzy_match("你好吗")  # Similar to "你好"
    assert len(results) >= 0  # May or may not match depending on similarity
    
    # Test with exact match (should have high confidence)
    exact_results = matcher.fuzzy_match("你好")
    assert len(exact_results) > 0
    
    print("✓ fuzzy_match test passed")


def test_keyword_match():
    """Test keyword matching"""
    print("Testing keyword_match...")
    
    matcher = create_response_matcher()
    
    # Test keyword match
    results = matcher.keyword_match("我想查询账户余额")
    assert len(results) > 0
    
    # Check that result contains account-related entry
    found_account = False
    for result in results:
        if "account" in result.script_entry.category:
            found_account = True
            break
    
    assert found_account
    
    print("✓ keyword_match test passed")


def test_intent_match():
    """Test intent matching"""
    print("Testing intent_match...")
    
    matcher = create_response_matcher()
    
    # Test greeting intent
    results = matcher.intent_match("你好，我有问题")
    assert len(results) >= 0  # May match greeting or question intents
    
    print("✓ intent_match test passed")


def test_match_query():
    """Test comprehensive query matching"""
    print("Testing match_query...")
    
    matcher = create_response_matcher()
    
    # Test with greeting
    results = matcher.match_query("你好")
    assert len(results) > 0
    assert results[0].confidence > 0
    
    # Test with account query
    results = matcher.match_query("查询余额")
    assert len(results) > 0
    
    # Test with unknown query
    results = matcher.match_query("xyz123unknown")
    # May or may not have results depending on matching algorithms
    
    print("✓ match_query test passed")


def test_get_best_response():
    """Test getting best response"""
    print("Testing get_best_response...")
    
    matcher = create_response_matcher()
    
    # Test with known query
    response = matcher.get_best_response("你好")
    assert response is not None
    assert isinstance(response, str)
    assert len(response) > 0
    
    # Test with unknown query
    no_response = matcher.get_best_response("xyz123unknown")
    # May be None or a low-confidence response
    
    print("✓ get_best_response test passed")


def test_similarity_calculation():
    """Test similarity calculation"""
    print("Testing _calculate_similarity...")
    
    matcher = ResponseMatcher()
    
    # Test identical strings
    sim1 = matcher._calculate_similarity("hello", "hello")
    assert sim1 == 1.0
    
    # Test different strings
    sim2 = matcher._calculate_similarity("hello", "world")
    assert 0.0 <= sim2 <= 1.0
    
    # Test empty strings
    sim3 = matcher._calculate_similarity("", "")
    assert sim3 == 1.0
    
    sim4 = matcher._calculate_similarity("hello", "")
    assert sim4 == 0.0
    
    print("✓ _calculate_similarity test passed")


def test_statistics():
    """Test getting statistics"""
    print("Testing get_statistics...")
    
    matcher = create_response_matcher()
    stats = matcher.get_statistics()
    
    assert "total_entries" in stats
    assert "categories" in stats
    assert "keyword_index_size" in stats
    assert "fuzzy_threshold" in stats
    
    assert stats["total_entries"] > 0
    assert isinstance(stats["categories"], dict)
    assert stats["fuzzy_threshold"] == matcher.fuzzy_threshold
    
    print("✓ get_statistics test passed")


def test_sample_scripts():
    """Test sample scripts creation"""
    print("Testing create_sample_scripts...")
    
    scripts = create_sample_scripts()
    assert len(scripts) > 0
    
    for script in scripts:
        assert isinstance(script, ScriptEntry)
        assert script.id
        assert script.category
        assert len(script.keywords) > 0
        assert len(script.patterns) > 0
        assert script.response
    
    print("✓ create_sample_scripts test passed")


def main():
    """Run all tests"""
    print("Running Response Matcher Tests")
    print("=" * 40)
    
    try:
        test_script_entry()
        test_match_result()
        test_response_matcher_initialization()
        test_add_script_entry()
        test_exact_match()
        test_fuzzy_match()
        test_keyword_match()
        test_intent_match()
        test_match_query()
        test_get_best_response()
        test_similarity_calculation()
        test_statistics()
        test_sample_scripts()
        
        print("\n" + "=" * 40)
        print("All tests passed successfully!")
        
    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)