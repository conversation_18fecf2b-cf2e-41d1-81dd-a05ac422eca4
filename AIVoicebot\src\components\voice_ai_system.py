"""
Voice AI System
===============

This module integrates voice processing with AI conversation capabilities
to provide a complete voice AI customer service experience.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from ..core.base_component import BaseComponent
from .voice.voice_processor import VoiceProcessor, VoiceProcessorConfig
from .ai.conversation_engine import ConversationE<PERSON>ine, ConversationConfig, ConversationResponse

@dataclass
class VoiceAIConfig:
    """Configuration for Voice AI System."""
    voice_config: VoiceProcessorConfig
    conversation_config: ConversationConfig
    auto_session_timeout: float = 300.0  # 5 minutes
    enable_conversation_logging: bool = True

class VoiceAISystem(BaseComponent):
    """Complete Voice AI system that combines voice processing with AI conversation."""
    
    def __init__(self, config: VoiceAIConfig, logger: Optional[logging.Logger] = None):
        super().__init__("voice_ai_system", logger)
        self.config = config
        
        # Initialize components
        self.voice_processor = VoiceProcessor(config.voice_config, logger)
        self.conversation_engine = ConversationEngine(config.conversation_config, logger)
        
        # Session management
        self.active_voice_sessions: Dict[str, str] = {}  # call_id -> session_id mapping
        
        self._log.info("Voice AI System initialized")
    
    async def _initialize_impl(self) -> bool:
        """Implementation of initialization logic."""
        try:
            # Initialize voice processor
            await self.voice_processor.initialize()
            if not self.voice_processor.is_initialized:
                self._log.error("Failed to initialize Voice Processor")
                return False
            
            # Initialize conversation engine
            await self.conversation_engine.initialize()
            if not self.conversation_engine.is_initialized:
                self._log.error("Failed to initialize Conversation Engine")
                return False
            
            # Set up voice processor callback
            self.voice_processor.set_conversation_callback(self._handle_conversation)
            
            self._log.info("Voice AI System initialized successfully")
            return True
            
        except Exception as e:
            self._log.error(f"Failed to initialize Voice AI System: {e}")
            return False
    
    async def _start_impl(self) -> bool:
        """Implementation of start logic."""
        # Start components
        await self.voice_processor.start()
        await self.conversation_engine.start()
        
        self._log.info("Voice AI System started")
        return True
    
    async def _stop_impl(self) -> bool:
        """Implementation of stop logic."""
        # Stop components
        await self.voice_processor.stop()
        await self.conversation_engine.stop()
        
        self._log.info("Voice AI System stopped")
        return True
    
    async def _cleanup_impl(self) -> bool:
        """Implementation of cleanup logic."""
        await self.voice_processor.cleanup()
        await self.conversation_engine.cleanup()
        self.active_voice_sessions.clear()
        self._log.info("Voice AI System cleanup completed")
        return True
    
    async def _handle_conversation(self, user_text: str) -> str:
        """
        Handle conversation processing for voice input.
        
        Args:
            user_text: Text from speech recognition
            
        Returns:
            AI response text for TTS
        """
        try:
            # For now, use a default session
            # In a real implementation, this would be tied to the call session
            session_id = "default_voice_session"
            
            if session_id not in self.conversation_engine.active_sessions:
                await self.conversation_engine.create_session()
            
            # Process the message through AI
            response = await self.conversation_engine.process_message(session_id, user_text)
            
            self._log.info(f"AI conversation: '{user_text}' -> '{response.text}'")
            return response.text
            
        except Exception as e:
            self._log.error(f"Conversation handling failed: {e}")
            return "I'm sorry, I encountered an error. Please try again."
    
    async def start_voice_conversation(self, call_id: str, user_id: Optional[str] = None) -> str:
        """
        Start a voice conversation for a call.
        
        Args:
            call_id: Call identifier
            user_id: Optional user identifier
            
        Returns:
            Session ID for the conversation
        """
        try:
            # Create conversation session
            session_id = await self.conversation_engine.create_session(user_id)
            
            # Map call to session
            self.active_voice_sessions[call_id] = session_id
            
            self._log.info(f"Started voice conversation for call {call_id}, session {session_id}")
            return session_id
            
        except Exception as e:
            self._log.error(f"Failed to start voice conversation: {e}")
            raise
    
    async def process_voice_input(self, call_id: str, audio_data: bytes, sample_rate: int = 16000) -> bytes:
        """
        Process voice input and return voice response.
        
        Args:
            call_id: Call identifier
            audio_data: Raw audio from user
            sample_rate: Audio sample rate
            
        Returns:
            Audio response bytes
        """
        try:
            # Get session for this call
            session_id = self.active_voice_sessions.get(call_id)
            if not session_id:
                session_id = await self.start_voice_conversation(call_id)
            
            # Process through voice pipeline
            tts_result = await self.voice_processor.process_conversation_turn(audio_data, sample_rate)
            
            self._log.info(f"Voice processing completed for call {call_id}")
            return tts_result.audio_data
            
        except Exception as e:
            self._log.error(f"Voice input processing failed: {e}")
            # Generate error response
            error_response = await self.voice_processor.generate_speech_output(
                "I'm sorry, I had trouble processing your request. Please try again."
            )
            return error_response.audio_data
    
    async def end_voice_conversation(self, call_id: str) -> bool:
        """
        End a voice conversation.
        
        Args:
            call_id: Call identifier
            
        Returns:
            True if successful
        """
        try:
            session_id = self.active_voice_sessions.get(call_id)
            if session_id:
                await self.conversation_engine.end_session(session_id)
                del self.active_voice_sessions[call_id]
                self._log.info(f"Ended voice conversation for call {call_id}")
                return True
            return False
            
        except Exception as e:
            self._log.error(f"Failed to end voice conversation: {e}")
            return False
    
    async def test_voice_ai_flow(self) -> Dict[str, Any]:
        """
        Test the complete voice AI flow with timeout protection.

        Returns:
            Test results
        """
        try:
            # Test conversation flow
            test_call_id = "test_call_001"
            session_id = await asyncio.wait_for(
                self.start_voice_conversation(test_call_id),
                timeout=5.0
            )

            # Test messages
            test_messages = [
                "Hello, I need help with my account",
                "Can you check my balance please?",
                "Thank you for your help"
            ]

            results = []
            for i, message in enumerate(test_messages):
                try:
                    self._log.info(f"Processing test message {i+1}/{len(test_messages)}: '{message}'")

                    # Simulate voice processing with timeout
                    start_time = asyncio.get_event_loop().time()

                    # Process message through conversation engine (with timeout)
                    response = await asyncio.wait_for(
                        self.conversation_engine.process_message(session_id, message),
                        timeout=5.0
                    )

                    # Generate TTS response (with timeout)
                    tts_result = await asyncio.wait_for(
                        self.voice_processor.generate_speech_output(response.text),
                        timeout=8.0
                    )

                    end_time = asyncio.get_event_loop().time()

                    result = {
                        "user_input": message,
                        "ai_response": response.text,
                        "intent": response.intent,
                        "processing_time": end_time - start_time,
                        "tts_duration": tts_result.duration,
                        "success": True
                    }

                    results.append(result)
                    self._log.info(f"Test message {i+1} completed successfully")

                except asyncio.TimeoutError:
                    self._log.error(f"Test message {i+1} timed out")
                    results.append({
                        "user_input": message,
                        "error": "timeout",
                        "success": False
                    })
                    break  # Stop testing on timeout
                except Exception as e:
                    self._log.error(f"Test message {i+1} failed: {e}")
                    results.append({
                        "user_input": message,
                        "error": str(e),
                        "success": False
                    })

            # End conversation
            try:
                await asyncio.wait_for(
                    self.end_voice_conversation(test_call_id),
                    timeout=3.0
                )
            except asyncio.TimeoutError:
                self._log.warning("End conversation timed out")

            successful_results = [r for r in results if r.get("success", False)]

            return {
                "test_call_id": test_call_id,
                "session_id": session_id,
                "results": results,
                "total_exchanges": len(results),
                "successful_exchanges": len(successful_results),
                "success": len(successful_results) > 0
            }

        except Exception as e:
            self._log.error(f"Voice AI flow test failed: {e}")
            return {
                "error": str(e),
                "success": False
            }
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get information about the Voice AI system."""
        return {
            "voice_processor": self.voice_processor.get_processor_info(),
            "conversation_engine": self.conversation_engine.get_engine_info(),
            "active_voice_sessions": len(self.active_voice_sessions),
            "config": {
                "auto_session_timeout": self.config.auto_session_timeout,
                "enable_conversation_logging": self.config.enable_conversation_logging
            },
            "status": {
                "is_initialized": self.is_initialized,
                "is_running": self.is_running,
                "voice_processor_running": self.voice_processor.is_running,
                "conversation_engine_running": self.conversation_engine.is_running
            }
        }
