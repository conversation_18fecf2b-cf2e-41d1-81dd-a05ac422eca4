"""
Tests for Audio Pipeline Components

This module tests the audio processing pipeline including:
- AudioBuffer circular buffer functionality
- AudioProcessor format conversion and preprocessing
- AudioPipeline coordination and processing
"""

import pytest
import numpy as np
import time
import threading
from unittest.mock import patch, MagicMock

from src.components.audio.audio_pipeline_simple import (
    AudioPipeline,
    AudioBuffer,
    AudioProcessor,
    AudioConfig,
    AudioFormat,
    create_audio_pipeline,
    create_simple_processor
)


class TestAudioConfig:
    """Test cases for AudioConfig"""
    
    def test_audio_config_defaults(self):
        """Test AudioConfig default values"""
        config = AudioConfig()
        
        assert config.sample_rate == 16000
        assert config.channels == 1
        assert config.chunk_size == 1024
        assert config.format == AudioFormat.PCM_16
        assert config.buffer_duration == 5.0
    
    def test_audio_config_custom(self):
        """Test AudioConfig with custom values"""
        config = AudioConfig(
            sample_rate=44100,
            channels=2,
            chunk_size=2048,
            format=AudioFormat.FLOAT32,
            buffer_duration=10.0
        )
        
        assert config.sample_rate == 44100
        assert config.channels == 2
        assert config.chunk_size == 2048
        assert config.format == AudioFormat.FLOAT32
        assert config.buffer_duration == 10.0


class TestAudioBuffer:
    """Test cases for AudioBuffer"""
    
    def setup_method(self):
        """Set up test environment"""
        self.config = AudioConfig(sample_rate=1000, buffer_duration=1.0)  # 1000 samples
        self.buffer = AudioBuffer(self.config)
    
    def test_buffer_initialization(self):
        """Test AudioBuffer initialization"""
        assert self.buffer.buffer_size == 1000
        assert self.buffer.write_pos == 0
        assert self.buffer.read_pos == 0
        assert self.buffer.get_available_data() == 0
    
    def test_write_and_read(self):
        """Test basic write and read operations"""
        # Write data
        test_data = np.array([1.0, 2.0, 3.0, 4.0], dtype=np.float32)
        success = self.buffer.write(test_data)
        assert success is True
        assert self.buffer.get_available_data() == 4
        
        # Read data
        read_data = self.buffer.read(4)
        assert read_data is not None
        np.testing.assert_array_equal(read_data, test_data)
        assert self.buffer.get_available_data() == 0
    
    def test_partial_read(self):
        """Test partial read operations"""
        # Write data
        test_data = np.array([1.0, 2.0, 3.0, 4.0], dtype=np.float32)
        self.buffer.write(test_data)
        
        # Read partial data
        partial_data = self.buffer.read(2)
        assert partial_data is not None
        np.testing.assert_array_equal(partial_data, np.array([1.0, 2.0]))
        assert self.buffer.get_available_data() == 2
        
        # Read remaining data
        remaining_data = self.buffer.read(2)
        assert remaining_data is not None
        np.testing.assert_array_equal(remaining_data, np.array([3.0, 4.0]))
        assert self.buffer.get_available_data() == 0
    
    def test_read_insufficient_data(self):
        """Test reading when insufficient data available"""
        # Write small amount of data
        test_data = np.array([1.0, 2.0], dtype=np.float32)
        self.buffer.write(test_data)
        
        # Try to read more data than available
        read_data = self.buffer.read(4)
        assert read_data is None
        assert self.buffer.get_available_data() == 2
    
    def test_buffer_wrap_around(self):
        """Test buffer wrap-around functionality"""
        # Fill most of buffer
        large_data = np.ones(900, dtype=np.float32)
        self.buffer.write(large_data)
        
        # Read some data to move read pointer
        self.buffer.read(400)
        
        # Write more data that should wrap around
        wrap_data = np.full(600, 2.0, dtype=np.float32)
        success = self.buffer.write(wrap_data)
        assert success is True
        
        # Verify total available data
        assert self.buffer.get_available_data() == 1100  # 500 + 600
    
    def test_buffer_overflow(self):
        """Test buffer overflow handling"""
        # Try to write more data than buffer can hold
        overflow_data = np.ones(1200, dtype=np.float32)  # Larger than buffer
        success = self.buffer.write(overflow_data)
        assert success is False
    
    def test_buffer_clear(self):
        """Test buffer clear functionality"""
        # Write data
        test_data = np.array([1.0, 2.0, 3.0], dtype=np.float32)
        self.buffer.write(test_data)
        assert self.buffer.get_available_data() == 3
        
        # Clear buffer
        self.buffer.clear()
        assert self.buffer.get_available_data() == 0
        assert self.buffer.write_pos == 0
        assert self.buffer.read_pos == 0


class TestAudioProcessor:
    """Test cases for AudioProcessor"""
    
    def setup_method(self):
        """Set up test environment"""
        self.config = AudioConfig()
        self.processor = AudioProcessor(self.config)
    
    def test_format_conversion_int16_to_float32(self):
        """Test conversion from int16 to float32"""
        int16_data = np.array([16384, -16384, 32767, -32768], dtype=np.int16)
        float32_data = self.processor.convert_format(int16_data, AudioFormat.FLOAT32)
        
        expected = np.array([0.5, -0.5, 0.999969482, -1.0], dtype=np.float32)
        np.testing.assert_array_almost_equal(float32_data, expected, decimal=6)
    
    def test_format_conversion_float32_to_int16(self):
        """Test conversion from float32 to int16"""
        float32_data = np.array([0.5, -0.5, 1.0, -1.0], dtype=np.float32)
        int16_data = self.processor.convert_format(float32_data, AudioFormat.PCM_16)
        
        expected = np.array([16383, -16383, 32767, -32767], dtype=np.int16)
        np.testing.assert_array_equal(int16_data, expected)
    
    def test_normalize_audio(self):
        """Test audio normalization"""
        # Test data with different amplitudes
        test_data = np.array([0.1, -0.2, 0.8, -0.4], dtype=np.float32)
        normalized = self.processor.normalize_audio(test_data)
        
        # Should be normalized to max amplitude of 1.0
        assert np.max(np.abs(normalized)) == pytest.approx(1.0)
        
        # Relative proportions should be maintained
        expected_ratios = test_data / 0.8  # 0.8 was the max absolute value
        np.testing.assert_array_almost_equal(normalized, expected_ratios)
    
    def test_normalize_empty_audio(self):
        """Test normalization of empty audio"""
        empty_data = np.array([], dtype=np.float32)
        normalized = self.processor.normalize_audio(empty_data)
        assert len(normalized) == 0
    
    def test_apply_gain(self):
        """Test gain application"""
        test_data = np.array([0.1, -0.1, 0.5, -0.5], dtype=np.float32)
        
        # Apply +6dB gain (should double amplitude)
        gained = self.processor.apply_gain(test_data, 6.0)
        expected = test_data * 2.0
        np.testing.assert_array_almost_equal(gained, expected, decimal=6)
        
        # Apply -6dB gain (should halve amplitude)
        gained = self.processor.apply_gain(test_data, -6.0)
        expected = test_data * 0.5
        np.testing.assert_array_almost_equal(gained, expected, decimal=6)
    
    def test_resample_same_rate(self):
        """Test resampling with same source and target rate"""
        test_data = np.array([1.0, 2.0, 3.0, 4.0], dtype=np.float32)
        resampled = self.processor.resample(test_data, 16000, 16000)
        np.testing.assert_array_equal(resampled, test_data)
    
    def test_resample_upsample(self):
        """Test upsampling"""
        test_data = np.array([1.0, 2.0], dtype=np.float32)
        resampled = self.processor.resample(test_data, 8000, 16000)  # 2x upsample
        
        # Should have approximately double the length
        assert len(resampled) == 4
        
        # First and last samples should be preserved
        assert resampled[0] == pytest.approx(1.0)
        assert resampled[-1] == pytest.approx(2.0)
    
    def test_resample_downsample(self):
        """Test downsampling"""
        test_data = np.array([1.0, 1.5, 2.0, 2.5], dtype=np.float32)
        resampled = self.processor.resample(test_data, 16000, 8000)  # 2x downsample
        
        # Should have approximately half the length
        assert len(resampled) == 2
        
        # First and last samples should be preserved
        assert resampled[0] == pytest.approx(1.0)
        assert resampled[-1] == pytest.approx(2.5)


class TestAudioPipeline:
    """Test cases for AudioPipeline"""
    
    def setup_method(self):
        """Set up test environment"""
        self.config = AudioConfig(sample_rate=1000, buffer_duration=1.0)
        self.pipeline = AudioPipeline(self.config)
    
    def teardown_method(self):
        """Clean up test environment"""
        self.pipeline.stop()
    
    def test_pipeline_initialization(self):
        """Test AudioPipeline initialization"""
        assert self.pipeline.config == self.config
        assert self.pipeline.buffer is not None
        assert self.pipeline.processor is not None
        assert len(self.pipeline.processors) == 0
        assert self.pipeline.running is False
    
    def test_add_processor(self):
        """Test adding processors to pipeline"""
        def dummy_processor(data):
            return data * 2
        
        self.pipeline.add_processor(dummy_processor)
        assert len(self.pipeline.processors) == 1
        assert self.pipeline.processors[0] == dummy_processor
    
    def test_process_audio(self):
        """Test audio processing through pipeline"""
        # Add a simple processor
        def gain_processor(data):
            return data * 2
        
        self.pipeline.add_processor(gain_processor)
        
        # Process test data
        test_data = np.array([1.0, 2.0, 3.0], dtype=np.float32)
        processed = self.pipeline.process_audio(test_data)
        
        expected = np.array([2.0, 4.0, 6.0], dtype=np.float32)
        np.testing.assert_array_equal(processed, expected)
    
    def test_process_audio_multiple_processors(self):
        """Test audio processing with multiple processors"""
        # Add multiple processors
        def gain_processor(data):
            return data * 2
        
        def offset_processor(data):
            return data + 1
        
        self.pipeline.add_processor(gain_processor)
        self.pipeline.add_processor(offset_processor)
        
        # Process test data
        test_data = np.array([1.0, 2.0], dtype=np.float32)
        processed = self.pipeline.process_audio(test_data)
        
        # Should apply gain first, then offset: (data * 2) + 1
        expected = np.array([3.0, 5.0], dtype=np.float32)
        np.testing.assert_array_equal(processed, expected)
    
    def test_pipeline_start_stop(self):
        """Test pipeline start and stop"""
        assert self.pipeline.running is False
        
        self.pipeline.start()
        assert self.pipeline.running is True
        assert self.pipeline.processing_thread is not None
        
        self.pipeline.stop()
        assert self.pipeline.running is False
    
    def test_write_read_audio(self):
        """Test writing and reading audio through pipeline"""
        self.pipeline.start()
        
        # Write test data
        test_data = np.array([1.0, 2.0, 3.0], dtype=np.float32)
        success = self.pipeline.write_audio(test_data)
        assert success is True
        
        # Give processing thread time to work
        time.sleep(0.1)
        
        # Read processed data
        processed_data = self.pipeline.read_audio(timeout=1.0)
        assert processed_data is not None
        np.testing.assert_array_equal(processed_data, test_data)
    
    def test_get_buffer_status(self):
        """Test buffer status reporting"""
        status = self.pipeline.get_buffer_status()
        
        assert "available_samples" in status
        assert "buffer_size" in status
        assert "buffer_usage_percent" in status
        assert "duration_seconds" in status
        assert "input_queue_size" in status
        assert "output_queue_size" in status
        
        assert status["buffer_size"] == 1000  # From config
        assert status["available_samples"] == 0  # Empty buffer
        assert status["buffer_usage_percent"] == 0.0


class TestConvenienceFunctions:
    """Test cases for convenience functions"""
    
    def test_create_audio_pipeline(self):
        """Test create_audio_pipeline function"""
        pipeline = create_audio_pipeline(
            sample_rate=44100,
            channels=2,
            chunk_size=2048,
            buffer_duration=10.0
        )
        
        assert pipeline.config.sample_rate == 44100
        assert pipeline.config.channels == 2
        assert pipeline.config.chunk_size == 2048
        assert pipeline.config.buffer_duration == 10.0
    
    def test_create_simple_processor(self):
        """Test create_simple_processor function"""
        processor = create_simple_processor(gain_db=6.0, normalize=True)
        
        # Test the processor
        test_data = np.array([0.1, -0.2, 0.4], dtype=np.float32)
        processed = processor(test_data)
        
        # Should be normalized and gained
        assert processed is not None
        assert len(processed) == len(test_data)
    
    def test_create_simple_processor_no_normalize(self):
        """Test create_simple_processor without normalization"""
        processor = create_simple_processor(gain_db=0.0, normalize=False)
        
        test_data = np.array([0.1, -0.2, 0.4], dtype=np.float32)
        processed = processor(test_data)
        
        # Should be unchanged (no gain, no normalization)
        np.testing.assert_array_almost_equal(processed, test_data)


if __name__ == "__main__":
    pytest.main([__file__])