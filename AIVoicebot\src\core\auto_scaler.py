"""
Auto Scaler for AI Voice Customer Service

This module provides automatic scaling capabilities for the AI voice system,
including horizontal and vertical scaling based on load and performance metrics.
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics

from .base_component import BaseComponent


class ScalingDirection(Enum):
    """Scaling direction."""
    UP = "up"
    DOWN = "down"
    NONE = "none"


class ScalingType(Enum):
    """Scaling type."""
    HORIZONTAL = "horizontal"  # Add/remove instances
    VERTICAL = "vertical"      # Increase/decrease resources per instance


@dataclass
class ScalingMetric:
    """Scaling metric definition."""
    name: str
    current_value: float
    target_value: float
    threshold_up: float
    threshold_down: float
    weight: float = 1.0
    unit: str = ""
    
    @property
    def utilization_ratio(self) -> float:
        """Get utilization ratio (0.0 to 1.0+)."""
        if self.target_value == 0:
            return 0.0
        return self.current_value / self.target_value
    
    @property
    def scaling_pressure(self) -> float:
        """Get scaling pressure (-1.0 to 1.0)."""
        if self.current_value > self.threshold_up:
            return min(1.0, (self.current_value - self.threshold_up) / self.threshold_up)
        elif self.current_value < self.threshold_down:
            return max(-1.0, (self.current_value - self.threshold_down) / self.threshold_down)
        else:
            return 0.0


@dataclass
class ScalingAction:
    """Scaling action to be performed."""
    component: str
    scaling_type: ScalingType
    direction: ScalingDirection
    magnitude: float  # How much to scale (e.g., 1.5 = 50% increase)
    reason: str
    metrics: List[ScalingMetric] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def __str__(self) -> str:
        return f"Scale {self.direction.value} {self.component} by {self.magnitude:.2f}x ({self.reason})"


@dataclass
class ScalingPolicy:
    """Scaling policy configuration."""
    component: str
    enabled: bool = True
    min_instances: int = 1
    max_instances: int = 10
    target_cpu_utilization: float = 70.0
    target_memory_utilization: float = 80.0
    target_response_time_ms: float = 1000.0
    scale_up_cooldown: int = 300  # seconds
    scale_down_cooldown: int = 600  # seconds
    scale_up_factor: float = 1.5
    scale_down_factor: float = 0.7
    metric_window_size: int = 10  # number of data points
    stability_window: int = 60  # seconds to wait for stability


class ComponentScaler:
    """Base class for component scalers."""
    
    def __init__(self, component_name: str, policy: ScalingPolicy, logger=None):
        self.component_name = component_name
        self.policy = policy
        self.logger = logger or logging.getLogger(__name__)
        
        # Scaling state
        self.current_instances = policy.min_instances
        self.last_scale_up = datetime.min
        self.last_scale_down = datetime.min
        self.metric_history: List[Dict[str, float]] = []
        
    async def collect_metrics(self) -> Dict[str, float]:
        """Collect current metrics for this component."""
        # Override in subclasses
        return {
            "cpu_utilization": 0.0,
            "memory_utilization": 0.0,
            "response_time_ms": 0.0,
            "request_rate": 0.0,
            "error_rate": 0.0
        }
    
    async def scale_up(self, factor: float) -> bool:
        """Scale up the component."""
        # Override in subclasses
        new_instances = min(
            int(self.current_instances * factor),
            self.policy.max_instances
        )
        
        if new_instances > self.current_instances:
            self.logger.info(f"Scaling up {self.component_name} from {self.current_instances} to {new_instances}")
            self.current_instances = new_instances
            self.last_scale_up = datetime.now()
            return True
        
        return False
    
    async def scale_down(self, factor: float) -> bool:
        """Scale down the component."""
        # Override in subclasses
        new_instances = max(
            int(self.current_instances * factor),
            self.policy.min_instances
        )
        
        if new_instances < self.current_instances:
            self.logger.info(f"Scaling down {self.component_name} from {self.current_instances} to {new_instances}")
            self.current_instances = new_instances
            self.last_scale_down = datetime.now()
            return True
        
        return False
    
    def can_scale_up(self) -> bool:
        """Check if component can scale up."""
        if not self.policy.enabled:
            return False
        
        if self.current_instances >= self.policy.max_instances:
            return False
        
        cooldown_elapsed = (datetime.now() - self.last_scale_up).total_seconds()
        return cooldown_elapsed >= self.policy.scale_up_cooldown
    
    def can_scale_down(self) -> bool:
        """Check if component can scale down."""
        if not self.policy.enabled:
            return False
        
        if self.current_instances <= self.policy.min_instances:
            return False
        
        cooldown_elapsed = (datetime.now() - self.last_scale_down).total_seconds()
        return cooldown_elapsed >= self.policy.scale_down_cooldown
    
    def add_metrics(self, metrics: Dict[str, float]) -> None:
        """Add metrics to history."""
        metrics["timestamp"] = time.time()
        self.metric_history.append(metrics)
        
        # Keep only recent metrics
        if len(self.metric_history) > self.policy.metric_window_size:
            self.metric_history.pop(0)
    
    def get_average_metrics(self) -> Dict[str, float]:
        """Get average metrics over the window."""
        if not self.metric_history:
            return {}
        
        avg_metrics = {}
        for key in self.metric_history[0].keys():
            if key != "timestamp":
                values = [m[key] for m in self.metric_history if key in m]
                avg_metrics[key] = statistics.mean(values) if values else 0.0
        
        return avg_metrics
    
    def analyze_scaling_need(self) -> Optional[ScalingAction]:
        """Analyze if scaling is needed."""
        if len(self.metric_history) < 3:  # Need some history
            return None
        
        avg_metrics = self.get_average_metrics()
        
        # Create scaling metrics
        scaling_metrics = [
            ScalingMetric(
                name="CPU Utilization",
                current_value=avg_metrics.get("cpu_utilization", 0),
                target_value=self.policy.target_cpu_utilization,
                threshold_up=self.policy.target_cpu_utilization * 1.2,
                threshold_down=self.policy.target_cpu_utilization * 0.5,
                weight=1.0,
                unit="%"
            ),
            ScalingMetric(
                name="Memory Utilization",
                current_value=avg_metrics.get("memory_utilization", 0),
                target_value=self.policy.target_memory_utilization,
                threshold_up=self.policy.target_memory_utilization * 1.1,
                threshold_down=self.policy.target_memory_utilization * 0.6,
                weight=0.8,
                unit="%"
            ),
            ScalingMetric(
                name="Response Time",
                current_value=avg_metrics.get("response_time_ms", 0),
                target_value=self.policy.target_response_time_ms,
                threshold_up=self.policy.target_response_time_ms * 1.5,
                threshold_down=self.policy.target_response_time_ms * 0.7,
                weight=1.2,
                unit="ms"
            )
        ]
        
        # Calculate weighted scaling pressure
        total_pressure = 0.0
        total_weight = 0.0
        
        for metric in scaling_metrics:
            pressure = metric.scaling_pressure * metric.weight
            total_pressure += pressure
            total_weight += metric.weight
        
        if total_weight > 0:
            avg_pressure = total_pressure / total_weight
        else:
            avg_pressure = 0.0
        
        # Determine scaling action
        if avg_pressure > 0.3 and self.can_scale_up():
            return ScalingAction(
                component=self.component_name,
                scaling_type=ScalingType.HORIZONTAL,
                direction=ScalingDirection.UP,
                magnitude=self.policy.scale_up_factor,
                reason=f"High resource utilization (pressure: {avg_pressure:.2f})",
                metrics=scaling_metrics
            )
        elif avg_pressure < -0.3 and self.can_scale_down():
            return ScalingAction(
                component=self.component_name,
                scaling_type=ScalingType.HORIZONTAL,
                direction=ScalingDirection.DOWN,
                magnitude=self.policy.scale_down_factor,
                reason=f"Low resource utilization (pressure: {avg_pressure:.2f})",
                metrics=scaling_metrics
            )
        
        return None


class AutoScaler(BaseComponent):
    """Automatic scaling system for AI voice components."""
    
    def __init__(self, config_manager, logger=None):
        super().__init__("auto_scaler", config_manager, logger)
        
        # Component scalers
        self.scalers: Dict[str, ComponentScaler] = {}
        
        # Scaling configuration
        self.enabled = self.config_manager.get_config("auto_scaling.enabled", True)
        self.check_interval = self.config_manager.get_config("auto_scaling.check_interval", 30)
        self.global_cooldown = self.config_manager.get_config("auto_scaling.global_cooldown", 60)
        
        # Scaling history
        self.scaling_history: List[ScalingAction] = []
        self.last_global_scaling = datetime.min
        
        # Background task
        self.scaling_task: Optional[asyncio.Task] = None
        
    async def _initialize_impl(self) -> None:
        """Initialize the auto scaler."""
        self._log.info("Auto Scaler initialized")
        
        # Initialize default scaling policies
        await self._initialize_default_policies()
        
    async def _start_impl(self) -> None:
        """Start the auto scaler."""
        if not self.enabled:
            self._log.info("Auto Scaler is disabled")
            return
        
        self._log.info("Starting Auto Scaler...")
        
        # Start scaling loop
        self.scaling_task = asyncio.create_task(self._scaling_loop())
        
        self._log.info("Auto Scaler started")
    
    async def _stop_impl(self) -> None:
        """Stop the auto scaler."""
        self._log.info("Stopping Auto Scaler...")
        
        # Cancel scaling task
        if self.scaling_task:
            self.scaling_task.cancel()
        
        self._log.info("Auto Scaler stopped")
    
    async def _cleanup_impl(self) -> None:
        """Clean up the auto scaler."""
        self.scalers.clear()
        self.scaling_history.clear()
        self._log.info("Auto Scaler cleanup completed")
    
    async def _initialize_default_policies(self) -> None:
        """Initialize default scaling policies."""
        # Audio Pipeline Scaling Policy
        audio_policy = ScalingPolicy(
            component="audio_pipeline",
            min_instances=2,
            max_instances=20,
            target_cpu_utilization=70.0,
            target_memory_utilization=80.0,
            target_response_time_ms=500.0,
            scale_up_cooldown=180,
            scale_down_cooldown=300
        )
        
        # Conversation Manager Scaling Policy
        conversation_policy = ScalingPolicy(
            component="conversation_manager",
            min_instances=1,
            max_instances=10,
            target_cpu_utilization=60.0,
            target_memory_utilization=75.0,
            target_response_time_ms=200.0,
            scale_up_cooldown=120,
            scale_down_cooldown=240
        )
        
        # Call Manager Scaling Policy
        call_policy = ScalingPolicy(
            component="call_manager",
            min_instances=1,
            max_instances=15,
            target_cpu_utilization=65.0,
            target_memory_utilization=70.0,
            target_response_time_ms=300.0,
            scale_up_cooldown=150,
            scale_down_cooldown=300
        )
        
        # Register scalers
        self.register_scaler("audio_pipeline", ComponentScaler("audio_pipeline", audio_policy, self._log))
        self.register_scaler("conversation_manager", ComponentScaler("conversation_manager", conversation_policy, self._log))
        self.register_scaler("call_manager", ComponentScaler("call_manager", call_policy, self._log))
    
    def register_scaler(self, component_name: str, scaler: ComponentScaler) -> None:
        """Register a component scaler."""
        self.scalers[component_name] = scaler
        self._log.info(f"Registered scaler for component: {component_name}")
    
    async def _scaling_loop(self) -> None:
        """Main scaling loop."""
        while True:
            try:
                await asyncio.sleep(self.check_interval)
                await self._check_and_scale()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in scaling loop: {e}")
    
    async def _check_and_scale(self) -> None:
        """Check metrics and perform scaling if needed."""
        # Check global cooldown
        global_cooldown_elapsed = (datetime.now() - self.last_global_scaling).total_seconds()
        if global_cooldown_elapsed < self.global_cooldown:
            return
        
        scaling_actions = []
        
        # Collect metrics and analyze scaling needs
        for component_name, scaler in self.scalers.items():
            try:
                # Collect current metrics
                metrics = await scaler.collect_metrics()
                scaler.add_metrics(metrics)
                
                # Analyze scaling need
                action = scaler.analyze_scaling_need()
                if action:
                    scaling_actions.append(action)
                    
            except Exception as e:
                self._log.error(f"Error checking scaling for {component_name}: {e}")
        
        # Execute scaling actions
        if scaling_actions:
            await self._execute_scaling_actions(scaling_actions)
    
    async def _execute_scaling_actions(self, actions: List[ScalingAction]) -> None:
        """Execute scaling actions."""
        for action in actions:
            try:
                self._log.info(f"Executing scaling action: {action}")
                
                scaler = self.scalers.get(action.component)
                if not scaler:
                    continue
                
                success = False
                if action.direction == ScalingDirection.UP:
                    success = await scaler.scale_up(action.magnitude)
                elif action.direction == ScalingDirection.DOWN:
                    success = await scaler.scale_down(action.magnitude)
                
                if success:
                    self.scaling_history.append(action)
                    self.last_global_scaling = datetime.now()
                    
                    # Keep history limited
                    if len(self.scaling_history) > 100:
                        self.scaling_history.pop(0)
                
            except Exception as e:
                self._log.error(f"Failed to execute scaling action {action}: {e}")
    
    def get_scaling_status(self) -> Dict[str, Any]:
        """Get current scaling status."""
        scaler_status = {}
        
        for name, scaler in self.scalers.items():
            avg_metrics = scaler.get_average_metrics()
            scaler_status[name] = {
                "current_instances": scaler.current_instances,
                "policy": {
                    "min_instances": scaler.policy.min_instances,
                    "max_instances": scaler.policy.max_instances,
                    "enabled": scaler.policy.enabled
                },
                "metrics": avg_metrics,
                "can_scale_up": scaler.can_scale_up(),
                "can_scale_down": scaler.can_scale_down(),
                "last_scale_up": scaler.last_scale_up.isoformat(),
                "last_scale_down": scaler.last_scale_down.isoformat()
            }
        
        return {
            "enabled": self.enabled,
            "check_interval": self.check_interval,
            "global_cooldown": self.global_cooldown,
            "last_global_scaling": self.last_global_scaling.isoformat(),
            "scalers": scaler_status,
            "recent_actions": [
                {
                    "component": action.component,
                    "direction": action.direction.value,
                    "magnitude": action.magnitude,
                    "reason": action.reason,
                    "timestamp": action.timestamp.isoformat()
                }
                for action in self.scaling_history[-10:]  # Last 10 actions
            ]
        }
