"""
File watcher for Excel script hot-reloading.

This module provides file system monitoring capabilities for automatic
script reloading when Excel files are modified.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileModifiedEvent

from ...core.base_component import BaseComponent


@dataclass
class FileWatcherConfig:
    """Configuration for file watcher."""
    # Watch settings
    watch_directory: str = "docs"
    watch_extensions: List[str] = None  # Will default to ['.xlsx', '.xls']
    recursive: bool = False
    
    # Debouncing
    debounce_seconds: float = 2.0  # Debounce rapid file changes
    batch_changes: bool = True  # Batch multiple changes together
    
    # Performance
    enable_polling: bool = False  # Use polling instead of native events
    polling_interval: float = 1.0  # Polling interval in seconds
    
    def __post_init__(self):
        """Post-initialization setup."""
        if self.watch_extensions is None:
            self.watch_extensions = ['.xlsx', '.xls']


class ExcelFileHandler(FileSystemEventHandler):
    """File system event handler for Excel files."""
    
    def __init__(self, file_watcher):
        """Initialize handler with reference to file watcher."""
        self.file_watcher = file_watcher
        self.last_events: Dict[str, float] = {}
    
    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return
        
        # Check if it's an Excel file we care about
        file_path = Path(event.src_path)
        if file_path.suffix.lower() not in self.file_watcher.config.watch_extensions:
            return
        
        # Debounce rapid changes
        current_time = datetime.now().timestamp()
        file_key = str(file_path)
        
        if file_key in self.last_events:
            time_diff = current_time - self.last_events[file_key]
            if time_diff < self.file_watcher.config.debounce_seconds:
                return
        
        self.last_events[file_key] = current_time
        
        # Schedule callback
        asyncio.create_task(
            self.file_watcher._handle_file_change(file_path)
        )


class FileWatcher(BaseComponent):
    """
    File watcher for Excel script hot-reloading.
    
    Monitors Excel files for changes and triggers reload callbacks
    with debouncing and batching support.
    """
    
    def __init__(self, config: FileWatcherConfig, config_manager, logger=None):
        """
        Initialize file watcher.
        
        Args:
            config: File watcher configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("file_watcher", config_manager, logger)
        
        self.config = config
        self.observer: Optional[Observer] = None
        self.event_handler: Optional[ExcelFileHandler] = None
        
        # Callbacks
        self._change_callbacks: List[Callable[[Path], None]] = []
        
        # Change batching
        self._pending_changes: Dict[str, Path] = {}
        self._batch_task: Optional[asyncio.Task] = None
        self._batch_lock = asyncio.Lock()
        
        # Statistics
        self._total_events = 0
        self._processed_events = 0
        self._debounced_events = 0
        
        # Threading
        self._stats_lock = threading.Lock()  
  
    async def _initialize_impl(self) -> None:
        """Initialize file watcher."""
        self._log.info("Initializing file watcher...")
        
        # Validate watch directory
        watch_path = Path(self.config.watch_directory)
        if not watch_path.exists():
            self._log.warning(f"Watch directory does not exist: {watch_path}")
            # Create directory if it doesn't exist
            watch_path.mkdir(parents=True, exist_ok=True)
        
        # Setup event handler
        self.event_handler = ExcelFileHandler(self)
        
        # Setup observer
        self.observer = Observer()
        self.observer.schedule(
            self.event_handler,
            str(watch_path),
            recursive=self.config.recursive
        )
        
        self._log.info(f"File watcher initialized for: {watch_path}")
    
    async def _start_impl(self) -> None:
        """Start file watcher."""
        self._log.info("Starting file watcher...")
        
        if self.observer:
            self.observer.start()
            self._log.info(f"Watching directory: {self.config.watch_directory}")
            self._log.info(f"Extensions: {self.config.watch_extensions}")
        
        self._log.info("File watcher started")
    
    async def _stop_impl(self) -> None:
        """Stop file watcher."""
        self._log.info("Stopping file watcher...")
        
        # Stop observer
        if self.observer and self.observer.is_alive():
            self.observer.stop()
            self.observer.join(timeout=5.0)
        
        # Cancel batch task
        if self._batch_task and not self._batch_task.done():
            self._batch_task.cancel()
            try:
                await self._batch_task
            except asyncio.CancelledError:
                pass
        
        # Log statistics
        self._log_final_statistics()
        
        self._log.info("File watcher stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup file watcher resources."""
        self._log.info("Cleaning up file watcher...")
        
        # Clear callbacks and pending changes
        self._change_callbacks.clear()
        self._pending_changes.clear()
        
        # Reset statistics
        with self._stats_lock:
            self._total_events = 0
            self._processed_events = 0
            self._debounced_events = 0
        
        self._log.info("File watcher cleanup completed")
    
    def add_change_callback(self, callback: Callable[[Path], None]) -> None:
        """
        Add callback for file change events.
        
        Args:
            callback: Function to call when files change
        """
        self._change_callbacks.append(callback)
        self._log.debug(f"Added change callback: {callback.__name__}")
    
    def remove_change_callback(self, callback: Callable[[Path], None]) -> None:
        """
        Remove callback for file change events.
        
        Args:
            callback: Function to remove
        """
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
            self._log.debug(f"Removed change callback: {callback.__name__}")
    
    async def _handle_file_change(self, file_path: Path) -> None:
        """
        Handle file change event.
        
        Args:
            file_path: Path to changed file
        """
        with self._stats_lock:
            self._total_events += 1
        
        self._log.debug(f"File change detected: {file_path}")
        
        if self.config.batch_changes:
            await self._add_to_batch(file_path)
        else:
            await self._process_file_change(file_path)
    
    async def _add_to_batch(self, file_path: Path) -> None:
        """
        Add file change to batch for processing.
        
        Args:
            file_path: Path to changed file
        """
        async with self._batch_lock:
            file_key = str(file_path)
            self._pending_changes[file_key] = file_path
            
            # Start batch processing task if not already running
            if self._batch_task is None or self._batch_task.done():
                self._batch_task = asyncio.create_task(self._process_batch())
    
    async def _process_batch(self) -> None:
        """Process batched file changes."""
        # Wait for batch period
        await asyncio.sleep(self.config.debounce_seconds)
        
        async with self._batch_lock:
            if not self._pending_changes:
                return
            
            # Get all pending changes
            changes = list(self._pending_changes.values())
            self._pending_changes.clear()
            
            self._log.info(f"Processing batch of {len(changes)} file changes")
            
            # Process each change
            for file_path in changes:
                await self._process_file_change(file_path)
    
    async def _process_file_change(self, file_path: Path) -> None:
        """
        Process individual file change.
        
        Args:
            file_path: Path to changed file
        """
        try:
            # Call all registered callbacks
            for callback in self._change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(file_path)
                    else:
                        callback(file_path)
                except Exception as e:
                    self._log.error(f"Error in change callback {callback.__name__}: {e}")
            
            with self._stats_lock:
                self._processed_events += 1
            
            self._log.debug(f"Processed file change: {file_path}")
            
        except Exception as e:
            self._log.error(f"Error processing file change {file_path}: {e}")
    
    def get_watcher_stats(self) -> Dict[str, Any]:
        """Get file watcher statistics."""
        with self._stats_lock:
            return {
                "total_events": self._total_events,
                "processed_events": self._processed_events,
                "debounced_events": self._debounced_events,
                "pending_changes": len(self._pending_changes),
                "active_callbacks": len(self._change_callbacks),
                "watch_directory": self.config.watch_directory,
                "watch_extensions": self.config.watch_extensions,
                "is_watching": self.observer.is_alive() if self.observer else False
            }
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_watcher_stats()
        
        self._log.info(f"File watcher statistics:")
        self._log.info(f"  Total events: {stats['total_events']}")
        self._log.info(f"  Processed events: {stats['processed_events']}")
        self._log.info(f"  Debounced events: {stats['debounced_events']}")
        self._log.info(f"  Active callbacks: {stats['active_callbacks']}")
    
    def force_scan(self) -> List[Path]:
        """
        Force scan directory for Excel files.
        
        Returns:
            List of found Excel files
        """
        watch_path = Path(self.config.watch_directory)
        excel_files = []
        
        if not watch_path.exists():
            return excel_files
        
        # Scan for Excel files
        for ext in self.config.watch_extensions:
            if self.config.recursive:
                pattern = f"**/*{ext}"
            else:
                pattern = f"*{ext}"
            
            excel_files.extend(watch_path.glob(pattern))
        
        self._log.info(f"Force scan found {len(excel_files)} Excel files")
        return excel_files
    
    async def trigger_reload_all(self) -> None:
        """Trigger reload for all Excel files in directory."""
        excel_files = self.force_scan()
        
        for file_path in excel_files:
            await self._process_file_change(file_path)
        
        self._log.info(f"Triggered reload for {len(excel_files)} files")


# Utility functions for file watching

async def create_file_watcher(
    watch_directory: str = "docs",
    watch_extensions: Optional[List[str]] = None,
    debounce_seconds: float = 2.0,
    config_manager=None,
    **kwargs
) -> FileWatcher:
    """
    Create and initialize file watcher.
    
    Args:
        watch_directory: Directory to watch
        watch_extensions: File extensions to watch
        debounce_seconds: Debounce time for changes
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized file watcher
    """
    config = FileWatcherConfig(
        watch_directory=watch_directory,
        watch_extensions=watch_extensions,
        debounce_seconds=debounce_seconds,
        **kwargs
    )
    
    watcher = FileWatcher(config, config_manager)
    await watcher.initialize()
    await watcher.start()
    
    return watcher


def setup_script_hot_reload(script_manager, file_watcher: FileWatcher) -> None:
    """
    Setup hot reload integration between file watcher and script manager.
    
    Args:
        script_manager: Script manager instance
        file_watcher: File watcher instance
    """
    async def reload_callback(file_path: Path):
        """Callback for file changes."""
        try:
            await script_manager.reload_scripts()
        except Exception as e:
            logging.error(f"Error reloading scripts after file change {file_path}: {e}")
    
    file_watcher.add_change_callback(reload_callback)