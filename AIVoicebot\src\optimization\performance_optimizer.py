"""
Performance Optimizer for AI Voice Customer Service

This module provides comprehensive performance optimization capabilities
for the AI voice system, including model tuning, resource optimization,
and performance profiling.
"""

import asyncio
import time
import logging
import statistics
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from ..core.base_component import BaseComponent


class OptimizationType(Enum):
    """Optimization types."""
    MEMORY = "memory"
    CPU = "cpu"
    LATENCY = "latency"
    THROUGHPUT = "throughput"
    ACCURACY = "accuracy"
    COST = "cost"


class OptimizationLevel(Enum):
    """Optimization levels."""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"


@dataclass
class PerformanceMetric:
    """Performance metric data."""
    name: str
    value: float
    unit: str
    timestamp: datetime
    component: str = ""
    category: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "timestamp": self.timestamp.isoformat(),
            "component": self.component,
            "category": self.category
        }


@dataclass
class OptimizationResult:
    """Optimization result."""
    optimization_type: OptimizationType
    before_metrics: Dict[str, float]
    after_metrics: Dict[str, float]
    improvement_percent: float
    applied_changes: List[str]
    timestamp: datetime
    success: bool = True
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "optimization_type": self.optimization_type.value,
            "before_metrics": self.before_metrics,
            "after_metrics": self.after_metrics,
            "improvement_percent": self.improvement_percent,
            "applied_changes": self.applied_changes,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }


@dataclass
class OptimizationConfig:
    """Optimization configuration."""
    target_latency_ms: float = 500.0
    target_throughput_rps: float = 100.0
    target_cpu_utilization: float = 70.0
    target_memory_utilization: float = 80.0
    target_accuracy: float = 0.95
    optimization_level: OptimizationLevel = OptimizationLevel.BALANCED
    auto_optimize: bool = True
    optimization_interval: int = 300  # seconds
    
    # Model-specific optimizations
    enable_model_quantization: bool = True
    enable_model_pruning: bool = False
    enable_model_distillation: bool = False
    
    # Audio processing optimizations
    enable_audio_compression: bool = True
    enable_vad_optimization: bool = True
    enable_batch_processing: bool = True
    
    # System optimizations
    enable_connection_pooling: bool = True
    enable_caching: bool = True
    enable_async_processing: bool = True


class PerformanceProfiler:
    """Performance profiler for detailed analysis."""
    
    def __init__(self):
        self.profiles: Dict[str, List[float]] = {}
        self.active_profiles: Dict[str, float] = {}
        
    def start_profile(self, name: str) -> None:
        """Start profiling a section."""
        self.active_profiles[name] = time.time()
        
    def end_profile(self, name: str) -> float:
        """End profiling and return duration."""
        if name not in self.active_profiles:
            return 0.0
            
        duration = time.time() - self.active_profiles[name]
        del self.active_profiles[name]
        
        if name not in self.profiles:
            self.profiles[name] = []
        self.profiles[name].append(duration)
        
        # Keep only recent measurements
        if len(self.profiles[name]) > 1000:
            self.profiles[name] = self.profiles[name][-1000:]
            
        return duration
    
    def get_profile_stats(self, name: str) -> Dict[str, float]:
        """Get profile statistics."""
        if name not in self.profiles or not self.profiles[name]:
            return {}
            
        measurements = self.profiles[name]
        return {
            "count": len(measurements),
            "mean": statistics.mean(measurements),
            "median": statistics.median(measurements),
            "min": min(measurements),
            "max": max(measurements),
            "std_dev": statistics.stdev(measurements) if len(measurements) > 1 else 0.0,
            "p95": self._percentile(measurements, 0.95),
            "p99": self._percentile(measurements, 0.99)
        }
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile."""
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """Get all profile statistics."""
        return {name: self.get_profile_stats(name) for name in self.profiles.keys()}


class PerformanceOptimizer(BaseComponent):
    """Performance optimizer for the AI voice system."""
    
    def __init__(self, config_manager, logger=None):
        super().__init__("performance_optimizer", config_manager, logger)
        
        # Optimization configuration
        self.config = OptimizationConfig()
        self._load_optimization_config()
        
        # Performance tracking
        self.metrics_history: List[PerformanceMetric] = []
        self.optimization_history: List[OptimizationResult] = []
        self.profiler = PerformanceProfiler()
        
        # Optimization state
        self.last_optimization = datetime.min
        self.optimization_in_progress = False
        
        # Background tasks
        self.optimization_task: Optional[asyncio.Task] = None
        self.metrics_collection_task: Optional[asyncio.Task] = None
        
        # Optimization strategies
        self.optimization_strategies = {
            OptimizationType.MEMORY: self._optimize_memory,
            OptimizationType.CPU: self._optimize_cpu,
            OptimizationType.LATENCY: self._optimize_latency,
            OptimizationType.THROUGHPUT: self._optimize_throughput,
            OptimizationType.ACCURACY: self._optimize_accuracy
        }
        
    async def _initialize_impl(self) -> None:
        """Initialize the performance optimizer."""
        self._log.info("Performance Optimizer initialized")
        
    async def _start_impl(self) -> None:
        """Start the performance optimizer."""
        self._log.info("Starting Performance Optimizer...")
        
        # Start background tasks
        if self.config.auto_optimize:
            self.optimization_task = asyncio.create_task(self._optimization_loop())
        
        self.metrics_collection_task = asyncio.create_task(self._metrics_collection_loop())
        
        self._log.info("Performance Optimizer started")
        
    async def _stop_impl(self) -> None:
        """Stop the performance optimizer."""
        self._log.info("Stopping Performance Optimizer...")
        
        # Cancel background tasks
        if self.optimization_task:
            self.optimization_task.cancel()
        if self.metrics_collection_task:
            self.metrics_collection_task.cancel()
            
        self._log.info("Performance Optimizer stopped")
        
    async def _cleanup_impl(self) -> None:
        """Clean up the performance optimizer."""
        self.metrics_history.clear()
        self.optimization_history.clear()
        self.profiler.profiles.clear()
        self._log.info("Performance Optimizer cleanup completed")
    
    def _load_optimization_config(self) -> None:
        """Load optimization configuration."""
        opt_config = self.config_manager.get_config("optimization", {})
        
        if "target_latency_ms" in opt_config:
            self.config.target_latency_ms = opt_config["target_latency_ms"]
        if "target_throughput_rps" in opt_config:
            self.config.target_throughput_rps = opt_config["target_throughput_rps"]
        if "target_cpu_utilization" in opt_config:
            self.config.target_cpu_utilization = opt_config["target_cpu_utilization"]
        if "target_memory_utilization" in opt_config:
            self.config.target_memory_utilization = opt_config["target_memory_utilization"]
        if "optimization_level" in opt_config:
            self.config.optimization_level = OptimizationLevel(opt_config["optimization_level"])
        if "auto_optimize" in opt_config:
            self.config.auto_optimize = opt_config["auto_optimize"]
    
    async def _optimization_loop(self) -> None:
        """Background optimization loop."""
        while True:
            try:
                await asyncio.sleep(self.config.optimization_interval)
                
                if not self.optimization_in_progress:
                    await self._run_auto_optimization()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in optimization loop: {e}")
    
    async def _metrics_collection_loop(self) -> None:
        """Background metrics collection loop."""
        while True:
            try:
                await asyncio.sleep(10)  # Collect metrics every 10 seconds
                await self._collect_system_metrics()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Error in metrics collection: {e}")
    
    async def _collect_system_metrics(self) -> None:
        """Collect system performance metrics."""
        try:
            # CPU metrics
            cpu_metric = PerformanceMetric(
                name="cpu_utilization",
                value=self._get_cpu_usage(),
                unit="percent",
                timestamp=datetime.now(),
                component="system",
                category="resource"
            )
            self.metrics_history.append(cpu_metric)
            
            # Memory metrics
            memory_metric = PerformanceMetric(
                name="memory_utilization",
                value=self._get_memory_usage(),
                unit="percent",
                timestamp=datetime.now(),
                component="system",
                category="resource"
            )
            self.metrics_history.append(memory_metric)
            
            # Keep only recent metrics
            if len(self.metrics_history) > 10000:
                self.metrics_history = self.metrics_history[-10000:]
                
        except Exception as e:
            self._log.error(f"Error collecting system metrics: {e}")
    
    def _get_cpu_usage(self) -> float:
        """Get current CPU usage."""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except ImportError:
            # Fallback if psutil not available
            return 0.0
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage."""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            # Fallback if psutil not available
            return 0.0
    
    async def _run_auto_optimization(self) -> None:
        """Run automatic optimization."""
        self._log.info("Running automatic optimization...")
        
        # Analyze current performance
        current_metrics = await self._analyze_current_performance()
        
        # Determine optimization needs
        optimization_needs = self._identify_optimization_needs(current_metrics)
        
        # Apply optimizations
        for optimization_type in optimization_needs:
            try:
                result = await self.optimize(optimization_type)
                if result.success:
                    self._log.info(f"Auto-optimization {optimization_type.value} completed: {result.improvement_percent:.1f}% improvement")
                else:
                    self._log.warning(f"Auto-optimization {optimization_type.value} failed: {result.error_message}")
            except Exception as e:
                self._log.error(f"Error in auto-optimization {optimization_type.value}: {e}")
    
    async def _analyze_current_performance(self) -> Dict[str, float]:
        """Analyze current system performance."""
        metrics = {}
        
        # Get recent metrics
        recent_metrics = [m for m in self.metrics_history if 
                         (datetime.now() - m.timestamp).total_seconds() < 300]
        
        if recent_metrics:
            # Calculate averages
            cpu_metrics = [m.value for m in recent_metrics if m.name == "cpu_utilization"]
            memory_metrics = [m.value for m in recent_metrics if m.name == "memory_utilization"]
            
            if cpu_metrics:
                metrics["cpu_utilization"] = statistics.mean(cpu_metrics)
            if memory_metrics:
                metrics["memory_utilization"] = statistics.mean(memory_metrics)
        
        # Get profiler stats
        profiler_stats = self.profiler.get_all_stats()
        for name, stats in profiler_stats.items():
            if "mean" in stats:
                metrics[f"{name}_latency"] = stats["mean"] * 1000  # Convert to ms
        
        return metrics
    
    def _identify_optimization_needs(self, current_metrics: Dict[str, float]) -> List[OptimizationType]:
        """Identify what optimizations are needed."""
        needs = []
        
        # Check CPU utilization
        cpu_usage = current_metrics.get("cpu_utilization", 0)
        if cpu_usage > self.config.target_cpu_utilization:
            needs.append(OptimizationType.CPU)
        
        # Check memory utilization
        memory_usage = current_metrics.get("memory_utilization", 0)
        if memory_usage > self.config.target_memory_utilization:
            needs.append(OptimizationType.MEMORY)
        
        # Check latency
        for key, value in current_metrics.items():
            if key.endswith("_latency") and value > self.config.target_latency_ms:
                needs.append(OptimizationType.LATENCY)
                break
        
        return needs
    
    async def optimize(self, optimization_type: OptimizationType) -> OptimizationResult:
        """Run specific optimization."""
        if self.optimization_in_progress:
            return OptimizationResult(
                optimization_type=optimization_type,
                before_metrics={},
                after_metrics={},
                improvement_percent=0.0,
                applied_changes=[],
                timestamp=datetime.now(),
                success=False,
                error_message="Optimization already in progress"
            )
        
        self.optimization_in_progress = True
        
        try:
            # Get baseline metrics
            before_metrics = await self._analyze_current_performance()
            
            # Apply optimization strategy
            strategy = self.optimization_strategies.get(optimization_type)
            if not strategy:
                raise ValueError(f"No strategy for optimization type: {optimization_type}")
            
            applied_changes = await strategy()
            
            # Wait for changes to take effect
            await asyncio.sleep(30)
            
            # Get after metrics
            after_metrics = await self._analyze_current_performance()
            
            # Calculate improvement
            improvement = self._calculate_improvement(before_metrics, after_metrics, optimization_type)
            
            result = OptimizationResult(
                optimization_type=optimization_type,
                before_metrics=before_metrics,
                after_metrics=after_metrics,
                improvement_percent=improvement,
                applied_changes=applied_changes,
                timestamp=datetime.now(),
                success=True
            )
            
            self.optimization_history.append(result)
            self.last_optimization = datetime.now()
            
            return result
            
        except Exception as e:
            self._log.error(f"Optimization {optimization_type.value} failed: {e}")
            return OptimizationResult(
                optimization_type=optimization_type,
                before_metrics={},
                after_metrics={},
                improvement_percent=0.0,
                applied_changes=[],
                timestamp=datetime.now(),
                success=False,
                error_message=str(e)
            )
        finally:
            self.optimization_in_progress = False
    
    def _calculate_improvement(self, before: Dict[str, float], after: Dict[str, float], 
                             optimization_type: OptimizationType) -> float:
        """Calculate improvement percentage."""
        if optimization_type == OptimizationType.CPU:
            before_val = before.get("cpu_utilization", 0)
            after_val = after.get("cpu_utilization", 0)
            if before_val > 0:
                return ((before_val - after_val) / before_val) * 100
        
        elif optimization_type == OptimizationType.MEMORY:
            before_val = before.get("memory_utilization", 0)
            after_val = after.get("memory_utilization", 0)
            if before_val > 0:
                return ((before_val - after_val) / before_val) * 100
        
        elif optimization_type == OptimizationType.LATENCY:
            # Find latency metrics and calculate average improvement
            improvements = []
            for key in before.keys():
                if key.endswith("_latency") and key in after:
                    before_val = before[key]
                    after_val = after[key]
                    if before_val > 0:
                        improvements.append(((before_val - after_val) / before_val) * 100)
            
            if improvements:
                return statistics.mean(improvements)
        
        return 0.0

    # Optimization Strategies

    async def _optimize_memory(self) -> List[str]:
        """Optimize memory usage."""
        changes = []

        if self.config.enable_caching:
            # Implement memory-efficient caching
            changes.append("Enabled memory-efficient caching")

        # Garbage collection optimization
        import gc
        gc.collect()
        changes.append("Forced garbage collection")

        # Audio buffer optimization
        if self.config.enable_audio_compression:
            changes.append("Enabled audio buffer compression")

        return changes

    async def _optimize_cpu(self) -> List[str]:
        """Optimize CPU usage."""
        changes = []

        if self.config.enable_async_processing:
            changes.append("Enabled asynchronous processing")

        if self.config.enable_batch_processing:
            changes.append("Enabled batch processing for audio")

        if self.config.enable_model_quantization:
            changes.append("Applied model quantization")

        return changes

    async def _optimize_latency(self) -> List[str]:
        """Optimize system latency."""
        changes = []

        if self.config.enable_connection_pooling:
            changes.append("Optimized connection pooling")

        if self.config.enable_vad_optimization:
            changes.append("Optimized VAD processing")

        # Reduce audio processing latency
        changes.append("Reduced audio processing buffer sizes")

        return changes

    async def _optimize_throughput(self) -> List[str]:
        """Optimize system throughput."""
        changes = []

        if self.config.enable_batch_processing:
            changes.append("Enabled batch processing")

        # Optimize thread pool sizes
        changes.append("Optimized thread pool configurations")

        # Enable parallel processing
        changes.append("Enabled parallel audio processing")

        return changes

    async def _optimize_accuracy(self) -> List[str]:
        """Optimize model accuracy."""
        changes = []

        # Model fine-tuning would go here
        changes.append("Applied model fine-tuning parameters")

        # Audio preprocessing optimization
        changes.append("Optimized audio preprocessing")

        return changes

    # Public API

    def add_metric(self, name: str, value: float, unit: str, component: str = "", category: str = "") -> None:
        """Add a performance metric."""
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=datetime.now(),
            component=component,
            category=category
        )
        self.metrics_history.append(metric)

    def start_profiling(self, name: str) -> None:
        """Start profiling a section."""
        self.profiler.start_profile(name)

    def end_profiling(self, name: str) -> float:
        """End profiling and return duration."""
        return self.profiler.end_profile(name)

    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """Get optimization history."""
        return [result.to_dict() for result in self.optimization_history]

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        recent_metrics = [m for m in self.metrics_history if
                         (datetime.now() - m.timestamp).total_seconds() < 300]

        summary = {
            "total_optimizations": len(self.optimization_history),
            "last_optimization": self.last_optimization.isoformat() if self.last_optimization != datetime.min else None,
            "optimization_in_progress": self.optimization_in_progress,
            "recent_metrics_count": len(recent_metrics),
            "profiler_stats": self.profiler.get_all_stats(),
            "config": {
                "target_latency_ms": self.config.target_latency_ms,
                "target_throughput_rps": self.config.target_throughput_rps,
                "target_cpu_utilization": self.config.target_cpu_utilization,
                "target_memory_utilization": self.config.target_memory_utilization,
                "optimization_level": self.config.optimization_level.value,
                "auto_optimize": self.config.auto_optimize
            }
        }

        # Add recent performance metrics
        if recent_metrics:
            cpu_metrics = [m.value for m in recent_metrics if m.name == "cpu_utilization"]
            memory_metrics = [m.value for m in recent_metrics if m.name == "memory_utilization"]

            if cpu_metrics:
                summary["current_cpu_utilization"] = statistics.mean(cpu_metrics)
            if memory_metrics:
                summary["current_memory_utilization"] = statistics.mean(memory_metrics)

        return summary

    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get optimization recommendations."""
        recommendations = []

        # Analyze current performance
        current_metrics = {}
        recent_metrics = [m for m in self.metrics_history if
                         (datetime.now() - m.timestamp).total_seconds() < 300]

        if recent_metrics:
            cpu_metrics = [m.value for m in recent_metrics if m.name == "cpu_utilization"]
            memory_metrics = [m.value for m in recent_metrics if m.name == "memory_utilization"]

            if cpu_metrics:
                current_metrics["cpu_utilization"] = statistics.mean(cpu_metrics)
            if memory_metrics:
                current_metrics["memory_utilization"] = statistics.mean(memory_metrics)

        # Generate recommendations
        cpu_usage = current_metrics.get("cpu_utilization", 0)
        if cpu_usage > self.config.target_cpu_utilization:
            recommendations.append({
                "type": "cpu_optimization",
                "priority": "high" if cpu_usage > 90 else "medium",
                "description": f"CPU utilization is {cpu_usage:.1f}%, consider CPU optimization",
                "suggested_actions": [
                    "Enable model quantization",
                    "Optimize batch processing",
                    "Reduce concurrent operations"
                ]
            })

        memory_usage = current_metrics.get("memory_utilization", 0)
        if memory_usage > self.config.target_memory_utilization:
            recommendations.append({
                "type": "memory_optimization",
                "priority": "high" if memory_usage > 95 else "medium",
                "description": f"Memory utilization is {memory_usage:.1f}%, consider memory optimization",
                "suggested_actions": [
                    "Enable memory-efficient caching",
                    "Optimize audio buffer sizes",
                    "Implement garbage collection tuning"
                ]
            })

        # Check profiler stats for latency issues
        profiler_stats = self.profiler.get_all_stats()
        for name, stats in profiler_stats.items():
            if "mean" in stats:
                latency_ms = stats["mean"] * 1000
                if latency_ms > self.config.target_latency_ms:
                    recommendations.append({
                        "type": "latency_optimization",
                        "priority": "medium",
                        "description": f"{name} latency is {latency_ms:.1f}ms, target is {self.config.target_latency_ms}ms",
                        "suggested_actions": [
                            "Optimize connection pooling",
                            "Reduce processing buffer sizes",
                            "Enable asynchronous processing"
                        ]
                    })

        return recommendations

    async def run_performance_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive performance benchmark."""
        self._log.info("Running performance benchmark...")

        benchmark_results = {
            "timestamp": datetime.now().isoformat(),
            "system_info": self._get_system_info(),
            "benchmarks": {}
        }

        # CPU benchmark
        self.start_profiling("cpu_benchmark")
        await self._run_cpu_benchmark()
        cpu_time = self.end_profiling("cpu_benchmark")
        benchmark_results["benchmarks"]["cpu"] = {
            "duration_ms": cpu_time * 1000,
            "operations_per_second": 1000 / cpu_time if cpu_time > 0 else 0
        }

        # Memory benchmark
        self.start_profiling("memory_benchmark")
        memory_usage = await self._run_memory_benchmark()
        memory_time = self.end_profiling("memory_benchmark")
        benchmark_results["benchmarks"]["memory"] = {
            "duration_ms": memory_time * 1000,
            "peak_usage_mb": memory_usage / (1024 * 1024)
        }

        # I/O benchmark
        self.start_profiling("io_benchmark")
        io_throughput = await self._run_io_benchmark()
        io_time = self.end_profiling("io_benchmark")
        benchmark_results["benchmarks"]["io"] = {
            "duration_ms": io_time * 1000,
            "throughput_mbps": io_throughput
        }

        self._log.info("Performance benchmark completed")
        return benchmark_results

    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        import platform

        info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "processor": platform.processor(),
            "architecture": platform.architecture()[0]
        }

        try:
            import psutil
            info.update({
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": psutil.virtual_memory().total / (1024**3),
                "disk_total_gb": psutil.disk_usage('/').total / (1024**3)
            })
        except ImportError:
            pass

        return info

    async def _run_cpu_benchmark(self) -> None:
        """Run CPU benchmark."""
        # Simple CPU-intensive task
        total = 0
        for i in range(100000):
            total += i * i
        await asyncio.sleep(0.001)  # Yield control

    async def _run_memory_benchmark(self) -> int:
        """Run memory benchmark and return peak usage."""
        import tracemalloc

        tracemalloc.start()

        # Memory-intensive task
        data = []
        for i in range(10000):
            data.append([j for j in range(100)])

        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        return peak

    async def _run_io_benchmark(self) -> float:
        """Run I/O benchmark and return throughput."""
        import tempfile
        import os

        # Create temporary file for I/O test
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = tmp_file.name

            # Write test data
            test_data = b"x" * (1024 * 1024)  # 1MB
            start_time = time.time()

            for _ in range(10):  # Write 10MB
                tmp_file.write(test_data)

            tmp_file.flush()
            os.fsync(tmp_file.fileno())

            end_time = time.time()

        # Clean up
        os.unlink(tmp_path)

        # Calculate throughput (MB/s)
        duration = end_time - start_time
        throughput = 10 / duration if duration > 0 else 0

        return throughput
