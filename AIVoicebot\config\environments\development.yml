# AI Voice Customer Service System - Development Configuration

# Application settings
app:
  name: "AI Voice Customer Service (Dev)"
  version: "1.0.0-dev"
  environment: "development"
  debug: true
  secret_key: "dev-secret-key-not-for-production"
  
# Server configuration
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  reload: true
  timeout: 60

# Logging configuration
logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  file_rotation: false
  handlers:
    - type: "console"
      stream: "stdout"
      colored: true
    - type: "file"
      filename: "/app/logs/app/development.log"
  loggers:
    aivoice:
      level: "DEBUG"
    uvicorn:
      level: "DEBUG"
    sqlalchemy.engine:
      level: "INFO"  # Set to DEBUG to see SQL queries

# Database configuration
database:
  url: "***********************************************************/aivoice_dev"
  pool_size: 5
  max_overflow: 10
  pool_timeout: 30
  echo: true  # Log SQL queries
  
# Redis configuration
redis:
  url: "redis://redis-dev:6379"
  max_connections: 10
  socket_timeout: 5
  decode_responses: true

# Audio processing configuration
audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  format: "pcm_16"
  max_audio_length: 60  # Shorter for development
  
# VAD configuration (using mock in development)
vad:
  model_path: "/app/models/silero_vad.onnx"
  threshold: 0.3  # Lower threshold for easier testing
  min_speech_duration: 0.1
  min_silence_duration: 0.3
  mock_enabled: true
  
# ASR configuration (using mock in development)
asr:
  model_path: "/app/models/SenseVoiceSmall"
  language: "zh"
  use_itn: true
  timeout: 10
  mock_enabled: true
  mock_responses:
    - "你好，我想查询账户余额"
    - "我要投诉服务质量"
    - "请帮我转账到张三的账户"
    - "我忘记了密码怎么办"
  
# TTS configuration (using mock in development)
tts:
  voice: "zh-CN-XiaoxiaoNeural"
  rate: "+0%"
  pitch: "+0Hz"
  timeout: 10
  mock_enabled: true
  
# LLM configuration (using mock in development)
llm:
  model: "gpt-3.5-turbo"
  api_key: "dev-api-key"
  max_tokens: 200
  temperature: 0.8
  timeout: 15
  mock_enabled: true
  mock_responses:
    - "我理解您的问题，让我为您查询相关信息。"
    - "好的，我会帮助您解决这个问题。"
    - "感谢您的耐心，我正在处理您的请求。"
    - "根据您提供的信息，我建议您..."
  
# Conversation management
conversation:
  max_history_length: 20  # Smaller for development
  context_window_size: 5
  intent_confidence_threshold: 0.5  # Lower threshold for testing
  auto_save_interval: 10
  session_timeout: 600  # 10 minutes
  
# Prompt management
prompts:
  template_dir: "/app/templates"
  default_language: "zh-CN"
  cache_size: 20
  template_reload_interval: 10  # Faster reload for development
  
# Call management
calls:
  max_concurrent: 10
  default_timeout: 120
  cleanup_interval: 30
  recording_enabled: true
  recording_path: "/app/data/recordings"
  
# Telephony configuration
telephony:
  routing_strategy: "round_robin"
  max_concurrent_calls: 10
  call_timeout_seconds: 120
  esp32:
    enabled: true
    host: "0.0.0.0"
    port: 8765
    max_connections: 5
  sip:
    enabled: false
    
# Component coordination
coordination:
  max_queue_size: 100
  event_timeout_seconds: 10.0
  conflict_resolution_timeout: 5.0
  
# Performance monitoring
performance:
  metrics_interval: 5
  alert_thresholds:
    cpu_percent: 90
    memory_percent: 90
    response_time_ms: 2000
    error_rate_percent: 10
  prometheus:
    enabled: true
    gateway: "http://prometheus-dev:9090"
    job_name: "aivoice-app-dev"
    
# Health monitoring
health:
  check_interval_seconds: 10
  recovery_enabled: true
  max_recovery_attempts: 2
  endpoints:
    - name: "database"
      url: "postgresql://check"
      timeout: 3
    - name: "redis"
      url: "redis://check"
      timeout: 3
      
# Security configuration (relaxed for development)
security:
  cors:
    enabled: true
    origins: ["*"]  # Allow all origins in development
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    headers: ["*"]
  rate_limiting:
    enabled: false  # Disabled for development
  authentication:
    jwt_secret: "dev-jwt-secret"
    token_expiry: 86400  # 24 hours
    
# File storage
storage:
  type: "local"
  local:
    base_path: "/app/data"
    max_file_size: "50MB"
    
# Backup configuration (disabled in development)
backup:
  enabled: false
  
# Feature flags (all enabled for testing)
features:
  conversation_recording: true
  real_time_transcription: true
  sentiment_analysis: true
  call_analytics: true
  multi_language_support: true
  
# External integrations (mocked in development)
integrations:
  webhook:
    enabled: true
    url: "http://mock-services:8080/webhook"
    secret: "dev-webhook-secret"
    events: ["call_started", "call_ended", "transcription_complete"]
  crm:
    enabled: false
    
# Development-specific settings
development:
  hot_reload: true
  auto_restart: true
  mock_external_services: true
  test_data_enabled: true
  debug_toolbar: true
  profiling_enabled: true
  
# Testing configuration
testing:
  database_url: "******************************************************************/aivoice_test"
  redis_url: "redis://redis-test:6379"
  parallel_tests: false
  test_timeout: 30
  
# Mock services configuration
mock_services:
  enabled: true
  delay_ms: 100
  error_rate: 0.05
  endpoints:
    asr: "http://mock-services:8080/asr"
    tts: "http://mock-services:8081/tts"
    llm: "http://mock-services:8082/llm"
