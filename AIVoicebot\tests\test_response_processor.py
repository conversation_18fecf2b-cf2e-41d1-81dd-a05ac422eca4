"""
Tests for response processing and validation system.
"""

import asyncio
import pytest
from unittest.mock import Mock
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.llm.response_processor import (
    ResponseProcessor, ResponseProcessorConfig, ValidationRule,
    ProcessedResponse, ResponseQuality, ResponseSource,
    create_response_processor, create_validation_rule,
    create_banking_safety_rules, assess_response_appropriateness
)
from src.components.llm.qwen_client import QwenResponse
from src.components.scripts.script_parser import ConversationScript


class TestResponseProcessor:
    """Test response processor functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def processor_config(self):
        """Create test processor configuration."""
        return ResponseProcessorConfig(
            enable_validation=True,
            enable_safety_checks=True,
            min_confidence_threshold=0.6,
            min_response_length=5,
            max_response_length=500,
            enable_fallback=True
        )
    
    @pytest.fixture
    def sample_llm_response(self):
        """Create sample LLM response."""
        return QwenResponse(
            text="您好！我们的个人贷款利率根据您的信用状况，一般在年化3.5%-8%之间。如需了解详细信息，建议您到我行网点咨询。",
            finish_reason="stop",
            total_tokens=50,
            response_id="test_response_123"
        )
    
    @pytest.fixture
    def sample_scripts(self):
        """Create sample fallback scripts."""
        return [
            ConversationScript(
                script_id="fallback_1",
                user_input="利率咨询",
                response="我们的贷款利率在3.5%-8%之间，具体利率需要根据您的信用情况评估。",
                category="利率咨询",
                priority=2
            ),
            ConversationScript(
                script_id="fallback_2",
                user_input="通用回复",
                response="感谢您的咨询，请稍等，我为您查询相关信息。",
                category="通用",
                priority=1
            )
        ]
    
    def test_processor_creation(self, processor_config, mock_config_manager):
        """Test response processor creation."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        assert processor.config == processor_config
        assert len(processor.validation_rules) > 0
        assert len(processor.safety_rules) > 0
    
    @pytest.mark.asyncio
    async def test_processor_lifecycle(self, processor_config, mock_config_manager):
        """Test processor lifecycle."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        assert processor.is_initialized
        assert processor.is_running
        
        await processor.stop()
        await processor.cleanup()
    
    def test_validation_rules(self, processor_config, mock_config_manager):
        """Test validation rule functionality."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        # Test default rules
        assert any(rule.name == "min_length" for rule in processor.validation_rules)
        assert any(rule.name == "max_length" for rule in processor.validation_rules)
        
        # Test rule validation
        min_length_rule = next(rule for rule in processor.validation_rules if rule.name == "min_length")
        
        # Valid text
        is_valid, error = min_length_rule.validate("这是一个足够长的回复。")
        assert is_valid
        
        # Invalid text (too short)
        is_valid, error = min_length_rule.validate("短")
        assert not is_valid
        assert "too short" in error.lower()
    
    def test_safety_rules(self, processor_config, mock_config_manager):
        """Test safety rule functionality."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        # Test safety rules exist
        assert len(processor.safety_rules) > 0
        
        # Test personal info rule
        personal_info_rule = next(
            (rule for rule in processor.safety_rules if rule.name == "no_personal_info"),
            None
        )
        
        if personal_info_rule:
            # Valid text
            is_valid, error = personal_info_rule.validate("我们的利率在3.5%-8%之间。")
            assert is_valid
            
            # Invalid text (contains sensitive info)
            is_valid, error = personal_info_rule.validate("请提供您的身份证号码。")
            assert not is_valid
    
    @pytest.mark.asyncio
    async def test_text_processing(self, processor_config, mock_config_manager):
        """Test text processing functionality."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        try:
            # Test text normalization
            raw_text = "  您好   ！  我们的利率   在3.5%-8%之间  "
            processed = await processor._process_response_text(
                raw_text, ResponseSource.LLM, "test_id"
            )
            
            # Should be normalized
            assert processed.text.strip() != raw_text.strip()
            assert "normalization" in processed.applied_filters
            
            # Should end with proper punctuation
            assert processed.text.endswith('。')
            
        finally:
            await processor.stop()
            await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_llm_response_processing(self, processor_config, mock_config_manager, 
                                         sample_llm_response, sample_scripts):
        """Test LLM response processing."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        try:
            # Process successful LLM response
            processed = await processor.process_llm_response(
                sample_llm_response, sample_scripts
            )
            
            assert processed.source == ResponseSource.LLM
            assert processed.is_valid
            assert processed.confidence > 0.5
            assert processed.quality in [ResponseQuality.GOOD, ResponseQuality.EXCELLENT]
            assert not processed.fallback_used
            
        finally:
            await processor.stop()
            await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_fallback_mechanism(self, processor_config, mock_config_manager, sample_scripts):
        """Test fallback mechanism."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        try:
            # Create failed LLM response
            failed_response = QwenResponse(
                text="",  # Empty response
                error_code="500",
                error_message="Internal server error"
            )
            
            # Process with fallback
            processed = await processor.process_llm_response(
                failed_response, sample_scripts
            )
            
            assert processed.fallback_used
            assert processed.source == ResponseSource.SCRIPT
            assert processed.fallback_script is not None
            assert len(processed.text) > 0
            
        finally:
            await processor.stop()
            await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_script_response_processing(self, processor_config, mock_config_manager, sample_scripts):
        """Test script response processing."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        try:
            # Process script response
            processed = await processor.process_script_response(sample_scripts[0])
            
            assert processed.source == ResponseSource.SCRIPT
            assert processed.text == sample_scripts[0].response
            assert processed.confidence >= 0.7  # Script responses should have high confidence
            
        finally:
            await processor.stop()
            await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_hybrid_response(self, processor_config, mock_config_manager, 
                                 sample_llm_response, sample_scripts):
        """Test hybrid response creation."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        try:
            # Create hybrid response
            processed = await processor.create_hybrid_response(
                sample_llm_response, sample_scripts
            )
            
            # Should use LLM response if it's good quality
            if processed.quality in [ResponseQuality.EXCELLENT, ResponseQuality.GOOD]:
                assert processed.source == ResponseSource.LLM
            else:
                assert processed.source in [ResponseSource.HYBRID, ResponseSource.SCRIPT]
            
        finally:
            await processor.stop()
            await processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_content_filtering(self, processor_config, mock_config_manager):
        """Test content filtering."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        await processor.initialize()
        await processor.start()
        
        try:
            # Test profanity filtering
            profane_text = "你这个傻子，不知道什么是贷款吗？"
            processed = await processor._process_response_text(
                profane_text, ResponseSource.LLM, "test_id"
            )
            
            # Should filter profanity
            assert "傻" not in processed.text
            assert "*" in processed.text or processed.text != profane_text
            
            # Test sensitive info filtering
            sensitive_text = "您的手机号是13812345678，身份证号是123456789012345678。"
            processed = await processor._process_response_text(
                sensitive_text, ResponseSource.LLM, "test_id"
            )
            
            # Should mask sensitive information
            assert "13812345678" not in processed.text
            assert "123456789012345678" not in processed.text
            
        finally:
            await processor.stop()
            await processor.cleanup()
    
    def test_quality_assessment(self, processor_config, mock_config_manager):
        """Test response quality assessment."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        # Test excellent quality
        excellent_text = "您好！根据您的信用状况，我们的个人贷款利率在年化3.5%-8%之间。建议您携带相关材料到我行网点详细咨询。"
        quality = processor._assess_response_quality(excellent_text, True, [])
        assert quality in [ResponseQuality.EXCELLENT, ResponseQuality.GOOD]
        
        # Test poor quality
        poor_text = "不知道"
        quality = processor._assess_response_quality(poor_text, True, [])
        assert quality in [ResponseQuality.POOR, ResponseQuality.UNACCEPTABLE]
        
        # Test invalid response
        quality = processor._assess_response_quality("任何文本", False, ["错误"])
        assert quality == ResponseQuality.UNACCEPTABLE
    
    def test_confidence_calculation(self, processor_config, mock_config_manager):
        """Test confidence calculation."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        # Test high confidence
        good_text = "您好！我们很乐意为您提供贷款咨询服务。"
        confidence = processor._calculate_confidence(good_text, ResponseQuality.EXCELLENT, True)
        assert confidence > 0.8
        
        # Test low confidence
        poor_text = "不知道"
        confidence = processor._calculate_confidence(poor_text, ResponseQuality.POOR, True)
        assert confidence < 0.6
        
        # Test invalid response
        confidence = processor._calculate_confidence("任何文本", ResponseQuality.UNACCEPTABLE, False)
        assert confidence <= 0.1
    
    def test_rule_management(self, processor_config, mock_config_manager):
        """Test rule management operations."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        initial_count = len(processor.validation_rules)
        
        # Add custom rule
        custom_rule = ValidationRule(
            name="custom_test",
            description="Test rule",
            required_elements=["测试"],
            severity="warning"
        )
        
        processor.add_validation_rule(custom_rule)
        assert len(processor.validation_rules) == initial_count + 1
        
        # Remove rule
        success = processor.remove_rule("custom_test")
        assert success
        assert len(processor.validation_rules) == initial_count
        
        # Try to remove non-existent rule
        success = processor.remove_rule("non_existent")
        assert not success
    
    def test_statistics(self, processor_config, mock_config_manager):
        """Test statistics tracking."""
        processor = ResponseProcessor(processor_config, mock_config_manager)
        
        # Initial stats
        stats = processor.get_processor_stats()
        assert stats["total_processed"] == 0
        
        # Simulate processing
        processed_response = ProcessedResponse(
            text="测试回复",
            source=ResponseSource.LLM,
            quality=ResponseQuality.GOOD,
            confidence=0.8,
            is_valid=True
        )
        
        processor._update_statistics(processed_response)
        
        # Check updated stats
        stats = processor.get_processor_stats()
        assert stats["total_processed"] == 1
        assert stats["quality_distribution"]["good"] == 1


class TestUtilityFunctions:
    """Test utility functions."""
    
    @pytest.mark.asyncio
    async def test_create_response_processor(self):
        """Test response processor factory function."""
        processor = await create_response_processor(
            enable_validation=True,
            enable_safety_checks=True,
            config_manager=Mock()
        )
        
        assert processor.is_initialized
        assert processor.is_running
        assert processor.config.enable_validation
        assert processor.config.enable_safety_checks
        
        await processor.stop()
        await processor.cleanup()
    
    def test_create_validation_rule(self):
        """Test validation rule creation utility."""
        rule = create_validation_rule(
            name="test_rule",
            description="Test rule",
            min_length=10,
            forbidden_elements=["禁止词"],
            severity="error"
        )
        
        assert rule.name == "test_rule"
        assert rule.min_length == 10
        assert "禁止词" in rule.forbidden_elements
        assert rule.severity == "error"
    
    def test_banking_safety_rules(self):
        """Test banking safety rules creation."""
        rules = create_banking_safety_rules()
        
        assert len(rules) > 0
        assert any(rule.name == "no_financial_guarantees" for rule in rules)
        assert any(rule.name == "privacy_protection" for rule in rules)
    
    def test_response_appropriateness_assessment(self):
        """Test response appropriateness assessment."""
        # Appropriate response
        is_appropriate, score, issues = assess_response_appropriateness(
            "我们的贷款利率在3.5%-8%之间，具体需要根据您的信用情况评估。",
            "贷款利率是多少？"
        )
        
        assert is_appropriate
        assert score > 0.6
        assert len(issues) <= 1
        
        # Inappropriate response
        is_appropriate, score, issues = assess_response_appropriateness(
            "谢谢",
            "贷款利率是多少？"
        )
        
        assert not is_appropriate
        assert score < 0.6
        assert len(issues) > 0


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestUtilityFunctions().test_create_response_processor())