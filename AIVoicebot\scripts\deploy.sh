#!/bin/bash
# AI Voice Customer Service System - Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOY_ENV=${DEPLOY_ENV:-production}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-}
IMAGE_TAG=${IMAGE_TAG:-latest}
BACKUP_ENABLED=${BACKUP_ENABLED:-true}

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Help function
show_help() {
    cat << EOF
AI Voice Customer Service System - Deployment Script

Usage: $0 [OPTIONS] COMMAND

Commands:
    build       Build Docker images
    deploy      Deploy the application
    start       Start services
    stop        Stop services
    restart     Restart services
    status      Show service status
    logs        Show service logs
    backup      Create backup
    restore     Restore from backup
    cleanup     Clean up old images and containers
    health      Check system health

Options:
    -e, --env ENV           Deployment environment (production|staging|development)
    -t, --tag TAG          Docker image tag (default: latest)
    -r, --registry URL     Docker registry URL
    -b, --backup           Enable backup before deployment
    -f, --force            Force deployment without confirmation
    -h, --help             Show this help message

Examples:
    $0 build
    $0 deploy --env production --tag v1.2.3
    $0 start --env staging
    $0 logs --env production
EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                DEPLOY_ENV="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -r|--registry)
                DOCKER_REGISTRY="$2"
                shift 2
                ;;
            -b|--backup)
                BACKUP_ENABLED=true
                shift
                ;;
            -f|--force)
                FORCE_DEPLOY=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            build|deploy|start|stop|restart|status|logs|backup|restore|cleanup|health)
                COMMAND="$1"
                shift
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
    
    if [ -z "$COMMAND" ]; then
        error "No command specified. Use --help for usage information."
    fi
}

# Validate environment
validate_environment() {
    case "$DEPLOY_ENV" in
        production|staging|development)
            log "Deploying to $DEPLOY_ENV environment"
            ;;
        *)
            error "Invalid environment: $DEPLOY_ENV. Must be production, staging, or development."
            ;;
    esac
    
    # Check required tools
    for tool in docker docker-compose; do
        if ! command -v $tool &> /dev/null; then
            error "$tool is required but not installed"
        fi
    done
}

# Get Docker Compose file based on environment
get_compose_file() {
    case "$DEPLOY_ENV" in
        production)
            echo "docker-compose.yml"
            ;;
        staging)
            echo "docker-compose.staging.yml"
            ;;
        development)
            echo "docker-compose.dev.yml"
            ;;
    esac
}

# Build Docker images
build_images() {
    log "Building Docker images for $DEPLOY_ENV environment..."
    
    cd "$PROJECT_ROOT"
    
    local compose_file=$(get_compose_file)
    
    # Build images
    docker-compose -f "$compose_file" build
    
    # Tag images if registry is specified
    if [ -n "$DOCKER_REGISTRY" ]; then
        log "Tagging images for registry: $DOCKER_REGISTRY"
        
        local image_name="aivoice-app"
        docker tag "${image_name}:latest" "${DOCKER_REGISTRY}/${image_name}:${IMAGE_TAG}"
        docker tag "${image_name}:latest" "${DOCKER_REGISTRY}/${image_name}:latest"
        
        log "Pushing images to registry..."
        docker push "${DOCKER_REGISTRY}/${image_name}:${IMAGE_TAG}"
        docker push "${DOCKER_REGISTRY}/${image_name}:latest"
    fi
    
    log "Build completed successfully"
}

# Create backup
create_backup() {
    if [ "$BACKUP_ENABLED" != "true" ]; then
        return 0
    fi
    
    log "Creating backup..."
    
    local backup_dir="/tmp/aivoice-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    cd "$PROJECT_ROOT"
    local compose_file=$(get_compose_file)
    
    # Backup database
    if docker-compose -f "$compose_file" ps postgres | grep -q "Up"; then
        log "Backing up PostgreSQL database..."
        docker-compose -f "$compose_file" exec -T postgres pg_dump -U aivoice aivoice > "$backup_dir/database.sql"
    fi
    
    # Backup Redis data
    if docker-compose -f "$compose_file" ps redis | grep -q "Up"; then
        log "Backing up Redis data..."
        docker-compose -f "$compose_file" exec -T redis redis-cli BGSAVE
        sleep 2
        docker cp $(docker-compose -f "$compose_file" ps -q redis):/data/dump.rdb "$backup_dir/redis.rdb"
    fi
    
    # Backup configuration and data
    log "Backing up configuration and data..."
    cp -r config "$backup_dir/"
    cp -r data "$backup_dir/" 2>/dev/null || true
    cp -r logs "$backup_dir/" 2>/dev/null || true
    
    # Create archive
    tar -czf "/tmp/aivoice-backup-$(date +%Y%m%d-%H%M%S).tar.gz" -C "$(dirname "$backup_dir")" "$(basename "$backup_dir")"
    rm -rf "$backup_dir"
    
    log "Backup created successfully"
}

# Deploy application
deploy_application() {
    log "Deploying AI Voice Customer Service System..."
    
    cd "$PROJECT_ROOT"
    local compose_file=$(get_compose_file)
    
    # Confirmation for production
    if [ "$DEPLOY_ENV" = "production" ] && [ "$FORCE_DEPLOY" != "true" ]; then
        read -p "Are you sure you want to deploy to PRODUCTION? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            log "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Create backup
    create_backup
    
    # Pull latest images if using registry
    if [ -n "$DOCKER_REGISTRY" ]; then
        log "Pulling latest images from registry..."
        docker-compose -f "$compose_file" pull
    fi
    
    # Deploy with zero-downtime strategy
    log "Starting deployment..."
    
    # Start new containers
    docker-compose -f "$compose_file" up -d --remove-orphans
    
    # Wait for health checks
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Verify deployment
    if check_health; then
        log "Deployment completed successfully"
        
        # Clean up old images
        cleanup_old_images
    else
        error "Deployment failed - services are not healthy"
    fi
}

# Start services
start_services() {
    log "Starting services..."
    
    cd "$PROJECT_ROOT"
    local compose_file=$(get_compose_file)
    
    docker-compose -f "$compose_file" up -d
    
    log "Services started"
}

# Stop services
stop_services() {
    log "Stopping services..."
    
    cd "$PROJECT_ROOT"
    local compose_file=$(get_compose_file)
    
    docker-compose -f "$compose_file" down
    
    log "Services stopped"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    stop_services
    sleep 5
    start_services
    
    log "Services restarted"
}

# Show service status
show_status() {
    log "Service status:"
    
    cd "$PROJECT_ROOT"
    local compose_file=$(get_compose_file)
    
    docker-compose -f "$compose_file" ps
}

# Show logs
show_logs() {
    cd "$PROJECT_ROOT"
    local compose_file=$(get_compose_file)
    
    docker-compose -f "$compose_file" logs -f --tail=100
}

# Check system health
check_health() {
    log "Checking system health..."
    
    local health_url="http://localhost:8000/health"
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$health_url" >/dev/null 2>&1; then
            log "System is healthy"
            return 0
        fi
        
        info "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 10
        ((attempt++))
    done
    
    error "System health check failed after $max_attempts attempts"
    return 1
}

# Clean up old images and containers
cleanup_old_images() {
    log "Cleaning up old Docker images and containers..."
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this in production)
    if [ "$DEPLOY_ENV" != "production" ]; then
        docker volume prune -f
    fi
    
    log "Cleanup completed"
}

# Main function
main() {
    parse_args "$@"
    validate_environment
    
    case "$COMMAND" in
        build)
            build_images
            ;;
        deploy)
            deploy_application
            ;;
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        backup)
            create_backup
            ;;
        health)
            check_health
            ;;
        cleanup)
            cleanup_old_images
            ;;
        *)
            error "Unknown command: $COMMAND"
            ;;
    esac
}

# Run main function
main "$@"
