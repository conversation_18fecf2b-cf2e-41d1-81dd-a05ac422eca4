#!/usr/bin/env python3
"""
Stable Voice AI Test - Robust testing with proper timeout handling
================================================================

This script provides comprehensive but stable testing of the Voice AI system
with proper error handling and timeout management.
"""

import requests
import json
import time
import asyncio
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

# Base URL for the API
BASE_URL = "http://localhost:8000"

class StableVoiceAITester:
    """Robust Voice AI system tester with timeout handling."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 5  # Default timeout for requests
    
    def test_with_timeout(self, test_func, timeout_seconds: int = 10, test_name: str = "Test") -> Dict[str, Any]:
        """Execute a test function with timeout protection."""
        try:
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(test_func)
                result = future.result(timeout=timeout_seconds)
                return {"success": True, "result": result, "test_name": test_name}
        except FutureTimeoutError:
            return {"success": False, "error": f"Test timed out after {timeout_seconds}s", "test_name": test_name}
        except Exception as e:
            return {"success": False, "error": str(e), "test_name": test_name}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get basic system status."""
        response = self.session.get(f"{self.base_url}/status")
        response.raise_for_status()
        return response.json()
    
    def get_voice_info(self) -> Dict[str, Any]:
        """Get Voice AI system information."""
        response = self.session.get(f"{self.base_url}/voice/info")
        response.raise_for_status()
        return response.json()
    
    def test_voice_conversation_safe(self) -> Dict[str, Any]:
        """Test Voice AI conversation with safe timeout."""
        response = self.session.post(f"{self.base_url}/voice/test-conversation", timeout=15)
        response.raise_for_status()
        return response.json()
    
    def test_health_endpoint(self) -> Dict[str, Any]:
        """Test health endpoint."""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()

def print_test_result(result: Dict[str, Any], test_number: int):
    """Print formatted test result."""
    test_name = result.get("test_name", "Unknown Test")
    if result["success"]:
        print(f"   {test_number}. ✅ {test_name}: PASSED")
        if "result" in result:
            # Print key information from successful tests
            test_result = result["result"]
            if isinstance(test_result, dict):
                if "system_status" in test_result:
                    status = test_result["system_status"]
                    print(f"      📊 Components: {status.get('components_running', 0)}/{len(test_result.get('components', {}))}")
                elif "voice_processor" in test_result:
                    vp_status = test_result["voice_processor"]["status"]
                    print(f"      🎤 ASR: {'✅' if vp_status.get('asr_running') else '❌'}")
                    print(f"      🔊 TTS: {'✅' if vp_status.get('tts_running') else '❌'}")
                elif "success" in test_result and test_result["success"]:
                    print(f"      💬 Exchanges: {test_result.get('total_exchanges', 0)}")
                    print(f"      📞 Call ID: {test_result.get('test_call_id', 'N/A')}")
                elif "status" in test_result:
                    print(f"      🏥 Health: {test_result['status']}")
    else:
        print(f"   {test_number}. ❌ {test_name}: FAILED")
        print(f"      Error: {result.get('error', 'Unknown error')}")

def main():
    """Run stable Voice AI tests."""
    print("🤖 AI Voice System - Stable Voice AI Test")
    print("=" * 50)
    print("🔧 Testing with robust timeout handling...")
    
    tester = StableVoiceAITester()
    test_results = []
    
    # Test 1: System Status (Quick test)
    print("\n📋 Running Voice AI System Tests:")
    result1 = tester.test_with_timeout(
        tester.get_system_status, 
        timeout_seconds=5, 
        test_name="System Status Check"
    )
    test_results.append(result1)
    print_test_result(result1, 1)
    
    # Test 2: Voice Info (Medium test)
    result2 = tester.test_with_timeout(
        tester.get_voice_info, 
        timeout_seconds=8, 
        test_name="Voice AI Info Check"
    )
    test_results.append(result2)
    print_test_result(result2, 2)
    
    # Test 3: Health Check (Quick test)
    result3 = tester.test_with_timeout(
        tester.test_health_endpoint, 
        timeout_seconds=5, 
        test_name="Health Endpoint Check"
    )
    test_results.append(result3)
    print_test_result(result3, 3)
    
    # Test 4: Voice Conversation (Longer timeout, but protected)
    print("\n🗣️ Testing Voice AI Conversation (with protection):")
    result4 = tester.test_with_timeout(
        tester.test_voice_conversation_safe, 
        timeout_seconds=20, 
        test_name="Voice AI Conversation Flow"
    )
    test_results.append(result4)
    print_test_result(result4, 4)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed_tests = sum(1 for r in test_results if r["success"])
    total_tests = len(test_results)
    
    print(f"   Tests Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed analysis
    print("\n🔍 Detailed Analysis:")
    
    # Check if basic system is working
    basic_tests = [result1, result2, result3]
    basic_passed = sum(1 for r in basic_tests if r["success"])
    
    if basic_passed == 3:
        print("   ✅ Core Voice AI System: FULLY OPERATIONAL")
        print("      - System status ✅")
        print("      - Voice components ✅") 
        print("      - Health monitoring ✅")
    elif basic_passed >= 2:
        print("   🟡 Core Voice AI System: MOSTLY OPERATIONAL")
        print("      - Some components may need attention")
    else:
        print("   ❌ Core Voice AI System: NEEDS ATTENTION")
        print("      - Multiple basic tests failed")
    
    # Check conversation capability
    if result4["success"]:
        print("   ✅ Voice AI Conversation: WORKING")
        print("      - Complete conversation flow functional")
        print("      - ASR + AI + TTS pipeline operational")
    else:
        if "timed out" in result4.get("error", "").lower():
            print("   ⚠️ Voice AI Conversation: FUNCTIONAL BUT SLOW")
            print("      - System is working but TTS may be slow")
            print("      - Consider optimizing TTS performance")
        else:
            print("   ❌ Voice AI Conversation: NEEDS DEBUGGING")
            print(f"      - Error: {result4.get('error', 'Unknown')}")
    
    # Overall assessment
    print("\n🎯 Overall Assessment:")
    
    if passed_tests == total_tests:
        print("   🟢 EXCELLENT: Voice AI system is fully functional!")
        print("   🚀 Ready for production use")
        print("   💡 All core capabilities verified")
    elif passed_tests >= 3:
        print("   🟡 VERY GOOD: Voice AI system is operational")
        print("   🔧 Minor performance optimizations recommended")
        print("   ✅ Core functionality confirmed")
    elif basic_passed >= 2:
        print("   🟡 GOOD: Voice AI system is mostly working")
        print("   🛠️ Some components need attention")
        print("   📈 System is functional for basic use")
    else:
        print("   🔴 NEEDS WORK: Voice AI system has issues")
        print("   🚨 Multiple components need debugging")
    
    # Recommendations
    print("\n💡 Recommendations:")
    
    if result4["success"]:
        print("   🎉 System is ready for real-world testing!")
        print("   📞 Try making actual voice calls")
        print("   🔗 Consider integrating with real LLM providers")
    elif "timed out" in result4.get("error", "").lower():
        print("   ⚡ Optimize TTS performance:")
        print("      - Consider using faster TTS engines")
        print("      - Implement TTS caching")
        print("      - Add async TTS processing")
    else:
        print("   🔧 Debug conversation flow:")
        print("      - Check TTS engine configuration")
        print("      - Verify async operation handling")
        print("      - Review error logs")
    
    # Next steps
    print("\n🚀 Next Steps:")
    print("   1. 🎤 Test with real microphone input")
    print("   2. 🔊 Test with real speakers/audio output")
    print("   3. 🤖 Integrate with real LLM (OpenAI, etc.)")
    print("   4. 📞 Test with real telephony integration")
    print("   5. 📈 Monitor performance in production")
    
    print(f"\n✅ Stable Voice AI testing completed!")
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
