"""
End-to-End Conversation Flow Tests

This module provides comprehensive end-to-end testing for the complete
AI voice customer service conversation flow, from call initiation to completion.
"""

import asyncio
import pytest
import time
import uuid
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.services.call_manager import CallManager
from src.services.conversation_context import ConversationManager
from src.services.prompt_manager import PromptManager
from src.integrations.telephony_manager import TelephonyManager
from src.services.component_coordinator import ComponentCoordinator
from src.audio.audio_pipeline import AudioPipeline
from tests.test_utils import (
    MockConfigManager, MockLogger, AudioTestData, TextTestData,
    MockExternalServices, create_test_call_session, create_test_conversation_context
)


class E2ETestScenario:
    """End-to-end test scenario definition."""
    
    def __init__(
        self,
        scenario_id: str,
        name: str,
        description: str,
        steps: List[Dict[str, Any]],
        expected_outcomes: Dict[str, Any],
        timeout_seconds: float = 60.0
    ):
        self.scenario_id = scenario_id
        self.name = name
        self.description = description
        self.steps = steps
        self.expected_outcomes = expected_outcomes
        self.timeout_seconds = timeout_seconds
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.results: Dict[str, Any] = {}
        self.success = False


class E2ETestFramework:
    """End-to-end test framework for conversation flows."""
    
    def __init__(self, config_manager, logger=None):
        self.config_manager = config_manager
        self.logger = logger or MockLogger()
        
        # Core components
        self.call_manager: Optional[CallManager] = None
        self.conversation_manager: Optional[ConversationManager] = None
        self.prompt_manager: Optional[PromptManager] = None
        self.telephony_manager: Optional[TelephonyManager] = None
        self.component_coordinator: Optional[ComponentCoordinator] = None
        self.audio_pipeline: Optional[AudioPipeline] = None
        
        # Test state
        self.active_scenarios: Dict[str, E2ETestScenario] = {}
        self.completed_scenarios: List[E2ETestScenario] = []
        
        # Mock services
        self.mock_services = {
            "asr": MockExternalServices.MockASRService(),
            "tts": MockExternalServices.MockTTSService(),
            "llm": MockExternalServices.MockLLMService(),
            "vad": MockExternalServices.MockVADService()
        }
    
    async def initialize(self):
        """Initialize the E2E test framework."""
        self.logger.info("Initializing E2E test framework")
        
        # Initialize core components
        self.call_manager = CallManager(self.config_manager, self.logger)
        self.conversation_manager = ConversationManager(self.config_manager, self.logger)
        self.prompt_manager = PromptManager(self.config_manager, self.logger)
        self.telephony_manager = TelephonyManager(self.config_manager, self.logger)
        self.component_coordinator = ComponentCoordinator(self.config_manager, self.logger)
        self.audio_pipeline = AudioPipeline(self.config_manager, self.logger)
        
        # Initialize all components
        components = [
            self.call_manager,
            self.conversation_manager,
            self.prompt_manager,
            self.telephony_manager,
            self.component_coordinator,
            self.audio_pipeline
        ]
        
        for component in components:
            await component.initialize()
            await component.start()
        
        self.logger.info("E2E test framework initialized")
    
    async def cleanup(self):
        """Clean up the E2E test framework."""
        self.logger.info("Cleaning up E2E test framework")
        
        components = [
            self.audio_pipeline,
            self.component_coordinator,
            self.telephony_manager,
            self.prompt_manager,
            self.conversation_manager,
            self.call_manager
        ]
        
        for component in components:
            if component:
                await component.stop()
                await component.cleanup()
        
        self.logger.info("E2E test framework cleanup completed")
    
    async def run_scenario(self, scenario: E2ETestScenario) -> bool:
        """Run a single E2E test scenario."""
        self.logger.info(f"Starting E2E scenario: {scenario.name}")
        scenario.start_time = datetime.now()
        
        try:
            self.active_scenarios[scenario.scenario_id] = scenario
            
            # Execute scenario steps
            for i, step in enumerate(scenario.steps):
                self.logger.info(f"Executing step {i+1}/{len(scenario.steps)}: {step.get('name', 'Unnamed step')}")
                
                step_result = await self._execute_step(scenario, step)
                scenario.results[f"step_{i+1}"] = step_result
                
                if not step_result.get("success", False):
                    self.logger.error(f"Step {i+1} failed: {step_result.get('error', 'Unknown error')}")
                    scenario.success = False
                    return False
                
                # Add delay between steps if specified
                delay = step.get("delay_seconds", 0)
                if delay > 0:
                    await asyncio.sleep(delay)
            
            # Validate expected outcomes
            scenario.success = await self._validate_outcomes(scenario)
            
            return scenario.success
            
        except asyncio.TimeoutError:
            self.logger.error(f"Scenario {scenario.name} timed out after {scenario.timeout_seconds} seconds")
            scenario.success = False
            return False
        except Exception as e:
            self.logger.error(f"Scenario {scenario.name} failed with exception: {e}")
            scenario.success = False
            return False
        finally:
            scenario.end_time = datetime.now()
            self.active_scenarios.pop(scenario.scenario_id, None)
            self.completed_scenarios.append(scenario)
    
    async def _execute_step(self, scenario: E2ETestScenario, step: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single test step."""
        step_type = step.get("type")
        step_data = step.get("data", {})
        
        try:
            if step_type == "initiate_call":
                return await self._step_initiate_call(step_data)
            elif step_type == "send_audio":
                return await self._step_send_audio(step_data)
            elif step_type == "expect_response":
                return await self._step_expect_response(step_data)
            elif step_type == "validate_state":
                return await self._step_validate_state(step_data)
            elif step_type == "end_call":
                return await self._step_end_call(step_data)
            elif step_type == "wait":
                return await self._step_wait(step_data)
            else:
                return {"success": False, "error": f"Unknown step type: {step_type}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _step_initiate_call(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute call initiation step."""
        caller_number = data.get("caller_number", "+1234567890")
        callee_number = data.get("callee_number", "+0987654321")
        
        # Create call session
        call_id = await self.call_manager.create_call_session(
            caller_number=caller_number,
            callee_number=callee_number
        )
        
        if call_id:
            return {
                "success": True,
                "call_id": call_id,
                "message": f"Call initiated successfully: {call_id}"
            }
        else:
            return {
                "success": False,
                "error": "Failed to initiate call"
            }
    
    async def _step_send_audio(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute audio sending step."""
        call_id = data.get("call_id")
        audio_type = data.get("audio_type", "speech")
        text_content = data.get("text_content", "你好，我想查询账户余额")
        
        # Generate appropriate audio data
        if audio_type == "speech":
            audio_data = AudioTestData.generate_sine_wave(frequency=200, duration=2.0)
        elif audio_type == "silence":
            audio_data = AudioTestData.generate_sine_wave(frequency=0, duration=1.0, amplitude=0.0)
        else:
            audio_data = AudioTestData.generate_white_noise(duration=1.0)
        
        # Mock ASR to return expected text
        self.mock_services["asr"].transcription_results = [{
            "text": text_content,
            "confidence": 0.95,
            "language": "zh-CN"
        }]
        
        # Process audio through pipeline
        with patch.object(self.audio_pipeline, 'asr_processor', self.mock_services["asr"]):
            result = await self.audio_pipeline.process_asr(audio_data)
        
        return {
            "success": True,
            "transcription": result,
            "message": f"Audio processed: {text_content}"
        }
    
    async def _step_expect_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute response expectation step."""
        expected_keywords = data.get("expected_keywords", [])
        response_timeout = data.get("timeout_seconds", 5.0)
        
        # Mock LLM response
        response_text = "好的，我来帮您查询账户余额。请提供您的账户号码。"
        
        # Mock TTS synthesis
        self.mock_services["tts"].call_count = 0
        
        with patch.object(self.audio_pipeline, 'tts_processor', self.mock_services["tts"]):
            audio_response = await self.audio_pipeline.process_tts(response_text)
        
        # Validate response contains expected keywords
        keywords_found = []
        for keyword in expected_keywords:
            if keyword in response_text:
                keywords_found.append(keyword)
        
        return {
            "success": len(keywords_found) == len(expected_keywords),
            "response_text": response_text,
            "keywords_found": keywords_found,
            "audio_generated": len(audio_response) > 0 if audio_response else False
        }
    
    async def _step_validate_state(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute state validation step."""
        expected_state = data.get("expected_state")
        component = data.get("component")
        
        # Validate component state
        if component == "call_manager":
            # Check call manager state
            stats = self.call_manager.get_call_statistics()
            current_state = stats.get("active_calls", 0) > 0
        elif component == "conversation_manager":
            # Check conversation manager state
            stats = self.conversation_manager.get_conversation_statistics()
            current_state = stats.get("active_conversations", 0) > 0
        else:
            current_state = "unknown"
        
        return {
            "success": True,
            "current_state": current_state,
            "expected_state": expected_state,
            "state_matches": current_state == expected_state
        }
    
    async def _step_end_call(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute call termination step."""
        call_id = data.get("call_id")
        
        if call_id:
            success = await self.call_manager.end_call(call_id)
            return {
                "success": success,
                "message": f"Call ended: {call_id}" if success else "Failed to end call"
            }
        else:
            return {
                "success": False,
                "error": "No call_id provided"
            }
    
    async def _step_wait(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute wait step."""
        duration = data.get("duration_seconds", 1.0)
        await asyncio.sleep(duration)
        
        return {
            "success": True,
            "message": f"Waited {duration} seconds"
        }
    
    async def _validate_outcomes(self, scenario: E2ETestScenario) -> bool:
        """Validate scenario expected outcomes."""
        expected = scenario.expected_outcomes
        
        # Check duration
        if "max_duration_seconds" in expected:
            duration = (scenario.end_time - scenario.start_time).total_seconds()
            if duration > expected["max_duration_seconds"]:
                self.logger.error(f"Scenario exceeded maximum duration: {duration}s > {expected['max_duration_seconds']}s")
                return False
        
        # Check step success rate
        if "min_step_success_rate" in expected:
            successful_steps = sum(1 for result in scenario.results.values() if result.get("success", False))
            success_rate = successful_steps / len(scenario.steps) if scenario.steps else 0
            if success_rate < expected["min_step_success_rate"]:
                self.logger.error(f"Step success rate too low: {success_rate} < {expected['min_step_success_rate']}")
                return False
        
        return True
    
    def get_scenario_report(self, scenario: E2ETestScenario) -> Dict[str, Any]:
        """Generate detailed report for a scenario."""
        duration = 0
        if scenario.start_time and scenario.end_time:
            duration = (scenario.end_time - scenario.start_time).total_seconds()
        
        return {
            "scenario_id": scenario.scenario_id,
            "name": scenario.name,
            "description": scenario.description,
            "success": scenario.success,
            "duration_seconds": duration,
            "steps_executed": len(scenario.results),
            "steps_successful": sum(1 for r in scenario.results.values() if r.get("success", False)),
            "start_time": scenario.start_time.isoformat() if scenario.start_time else None,
            "end_time": scenario.end_time.isoformat() if scenario.end_time else None,
            "step_results": scenario.results
        }
