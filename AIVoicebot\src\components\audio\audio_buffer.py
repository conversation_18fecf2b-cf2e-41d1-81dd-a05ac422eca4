"""
Audio buffer management for real-time audio processing.

This module provides circular buffers and audio data management
for handling continuous audio streams with minimal latency.
"""

import threading
import time
import numpy as np
from typing import Optional, List, Tuple, Union
from collections import deque
from dataclasses import dataclass
import logging

from ...core.interfaces import AudioChunk, AudioFormat, AudioProcessingError


@dataclass
class BufferConfig:
    """Configuration for audio buffers."""
    max_size: int = 4096  # Maximum number of chunks
    chunk_size: int = 1024  # Size of each audio chunk in samples
    sample_rate: int = 16000
    channels: int = 1
    dtype: str = "int16"  # Audio data type
    
    # Buffer behavior
    overwrite_on_full: bool = True  # Overwrite oldest data when full
    underrun_padding: bool = True   # Pad with silence on underrun
    
    # Performance settings
    high_water_mark: float = 0.8    # Buffer full threshold (80%)
    low_water_mark: float = 0.2     # Buffer empty threshold (20%)


class CircularAudioBuffer:
    """
    Circular buffer for audio data with thread-safe operations.
    
    Provides efficient buffering for continuous audio streams with
    configurable overflow and underflow handling.
    """
    
    def __init__(self, config: BufferConfig):
        """
        Initialize circular audio buffer.
        
        Args:
            config: Buffer configuration
        """
        self.config = config
        self._buffer = deque(maxlen=config.max_size)
        self._lock = threading.RLock()
        
        # Statistics
        self._total_written = 0
        self._total_read = 0
        self._overruns = 0
        self._underruns = 0
        self._start_time = time.time()
        
        self._logger = logging.getLogger("aivoice.audio_buffer")
    
    def write(self, audio_chunk: AudioChunk) -> bool:
        """
        Write audio chunk to buffer.
        
        Args:
            audio_chunk: Audio chunk to write
            
        Returns:
            True if written successfully, False if buffer full and not overwriting
        """
        with self._lock:
            if len(self._buffer) >= self.config.max_size:
                if self.config.overwrite_on_full:
                    # Remove oldest chunk to make space
                    self._buffer.popleft()
                    self._overruns += 1
                else:
                    # Buffer full, cannot write
                    return False
            
            self._buffer.append(audio_chunk)
            self._total_written += 1
            return True
    
    def read(self) -> Optional[AudioChunk]:
        """
        Read audio chunk from buffer.
        
        Returns:
            Audio chunk or None if buffer empty
        """
        with self._lock:
            if self._buffer:
                chunk = self._buffer.popleft()
                self._total_read += 1
                return chunk
            else:
                self._underruns += 1
                
                if self.config.underrun_padding:
                    # Return silence chunk to maintain stream continuity
                    silence_data = np.zeros(
                        self.config.chunk_size * self.config.channels,
                        dtype=self.config.dtype
                    ).tobytes()
                    
                    return AudioChunk(
                        data=silence_data,
                        format=AudioFormat.PCM_16KHZ_MONO,
                        timestamp=time.time(),
                        duration_ms=int(self.config.chunk_size / self.config.sample_rate * 1000),
                        sample_rate=self.config.sample_rate
                    )
                
                return None
    
    def peek(self, count: int = 1) -> List[AudioChunk]:
        """
        Peek at audio chunks without removing them.
        
        Args:
            count: Number of chunks to peek at
            
        Returns:
            List of audio chunks (up to count)
        """
        with self._lock:
            return list(self._buffer)[:count]
    
    def read_multiple(self, count: int) -> List[AudioChunk]:
        """
        Read multiple audio chunks from buffer.
        
        Args:
            count: Number of chunks to read
            
        Returns:
            List of audio chunks (up to count available)
        """
        chunks = []
        for _ in range(count):
            chunk = self.read()
            if chunk is None:
                break
            chunks.append(chunk)
        return chunks
    
    def write_multiple(self, chunks: List[AudioChunk]) -> int:
        """
        Write multiple audio chunks to buffer.
        
        Args:
            chunks: List of audio chunks to write
            
        Returns:
            Number of chunks successfully written
        """
        written = 0
        for chunk in chunks:
            if self.write(chunk):
                written += 1
            else:
                break
        return written
    
    def size(self) -> int:
        """Get current buffer size."""
        with self._lock:
            return len(self._buffer)
    
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        with self._lock:
            return len(self._buffer) == 0
    
    def is_full(self) -> bool:
        """Check if buffer is full."""
        with self._lock:
            return len(self._buffer) >= self.config.max_size
    
    def fill_level(self) -> float:
        """
        Get buffer fill level as percentage.
        
        Returns:
            Fill level (0.0 to 1.0)
        """
        with self._lock:
            return len(self._buffer) / self.config.max_size
    
    def is_high_water(self) -> bool:
        """Check if buffer is above high water mark."""
        return self.fill_level() >= self.config.high_water_mark
    
    def is_low_water(self) -> bool:
        """Check if buffer is below low water mark."""
        return self.fill_level() <= self.config.low_water_mark
    
    def clear(self) -> None:
        """Clear all data from buffer."""
        with self._lock:
            self._buffer.clear()
    
    def get_stats(self) -> dict:
        """
        Get buffer statistics.
        
        Returns:
            Dictionary with buffer statistics
        """
        with self._lock:
            duration = time.time() - self._start_time
            return {
                "size": len(self._buffer),
                "max_size": self.config.max_size,
                "fill_level": self.fill_level(),
                "total_written": self._total_written,
                "total_read": self._total_read,
                "overruns": self._overruns,
                "underruns": self._underruns,
                "write_rate": self._total_written / duration if duration > 0 else 0,
                "read_rate": self._total_read / duration if duration > 0 else 0,
                "duration": duration
            }
    
    def reset_stats(self) -> None:
        """Reset buffer statistics."""
        with self._lock:
            self._total_written = 0
            self._total_read = 0
            self._overruns = 0
            self._underruns = 0
            self._start_time = time.time()


class AudioBufferManager:
    """
    Manager for multiple audio buffers with automatic sizing and monitoring.
    """
    
    def __init__(self, default_config: Optional[BufferConfig] = None):
        """
        Initialize audio buffer manager.
        
        Args:
            default_config: Default configuration for new buffers
        """
        self.default_config = default_config or BufferConfig()
        self.buffers: dict[str, CircularAudioBuffer] = {}
        self._lock = threading.Lock()
        
        self._logger = logging.getLogger("aivoice.audio_buffer_manager")
    
    def create_buffer(self, name: str, config: Optional[BufferConfig] = None) -> CircularAudioBuffer:
        """
        Create a new audio buffer.
        
        Args:
            name: Buffer name
            config: Optional buffer configuration
            
        Returns:
            Created audio buffer
        """
        with self._lock:
            if name in self.buffers:
                raise AudioProcessingError(f"Buffer already exists: {name}")
            
            buffer_config = config or self.default_config
            buffer = CircularAudioBuffer(buffer_config)
            self.buffers[name] = buffer
            
            self._logger.info(f"Created audio buffer: {name}")
            return buffer
    
    def get_buffer(self, name: str) -> Optional[CircularAudioBuffer]:
        """
        Get audio buffer by name.
        
        Args:
            name: Buffer name
            
        Returns:
            Audio buffer or None if not found
        """
        with self._lock:
            return self.buffers.get(name)
    
    def remove_buffer(self, name: str) -> bool:
        """
        Remove audio buffer.
        
        Args:
            name: Buffer name
            
        Returns:
            True if removed, False if not found
        """
        with self._lock:
            if name in self.buffers:
                del self.buffers[name]
                self._logger.info(f"Removed audio buffer: {name}")
                return True
            return False
    
    def list_buffers(self) -> List[str]:
        """
        List all buffer names.
        
        Returns:
            List of buffer names
        """
        with self._lock:
            return list(self.buffers.keys())
    
    def get_all_stats(self) -> dict:
        """
        Get statistics for all buffers.
        
        Returns:
            Dictionary with buffer statistics
        """
        with self._lock:
            return {name: buffer.get_stats() for name, buffer in self.buffers.items()}
    
    def monitor_buffers(self) -> dict:
        """
        Monitor buffer health and return status.
        
        Returns:
            Dictionary with buffer health status
        """
        status = {}
        
        with self._lock:
            for name, buffer in self.buffers.items():
                stats = buffer.get_stats()
                
                # Determine buffer health
                health = "healthy"
                issues = []
                
                if buffer.is_high_water():
                    health = "warning"
                    issues.append("high_water")
                
                if buffer.is_low_water():
                    health = "warning"
                    issues.append("low_water")
                
                if stats["overruns"] > 0:
                    health = "warning"
                    issues.append("overruns")
                
                if stats["underruns"] > 10:  # Allow some underruns
                    health = "warning"
                    issues.append("excessive_underruns")
                
                status[name] = {
                    "health": health,
                    "issues": issues,
                    "fill_level": stats["fill_level"],
                    "overruns": stats["overruns"],
                    "underruns": stats["underruns"]
                }
        
        return status
    
    def clear_all_buffers(self) -> None:
        """Clear all buffers."""
        with self._lock:
            for buffer in self.buffers.values():
                buffer.clear()
            self._logger.info("Cleared all audio buffers")
    
    def reset_all_stats(self) -> None:
        """Reset statistics for all buffers."""
        with self._lock:
            for buffer in self.buffers.values():
                buffer.reset_stats()
            self._logger.info("Reset statistics for all audio buffers")


class AudioChunkProcessor:
    """
    Utility class for processing audio chunks with format conversion and validation.
    """
    
    @staticmethod
    def validate_chunk(chunk: AudioChunk, expected_format: AudioFormat) -> bool:
        """
        Validate audio chunk format.
        
        Args:
            chunk: Audio chunk to validate
            expected_format: Expected audio format
            
        Returns:
            True if valid, False otherwise
        """
        if chunk.format != expected_format:
            return False
        
        # Validate data size
        expected_samples = chunk.duration_ms * chunk.sample_rate // 1000
        actual_samples = len(chunk.data) // 2  # Assuming 16-bit samples
        
        return abs(expected_samples - actual_samples) <= 1  # Allow 1 sample tolerance
    
    @staticmethod
    def convert_format(chunk: AudioChunk, target_format: AudioFormat) -> AudioChunk:
        """
        Convert audio chunk to target format.
        
        Args:
            chunk: Source audio chunk
            target_format: Target audio format
            
        Returns:
            Converted audio chunk
        """
        if chunk.format == target_format:
            return chunk
        
        # For now, we'll implement basic format conversion
        # In a full implementation, this would handle various format conversions
        
        if (chunk.format == AudioFormat.WAV_16KHZ_MONO and 
            target_format == AudioFormat.PCM_16KHZ_MONO):
            # Remove WAV header (assuming 44-byte header)
            pcm_data = chunk.data[44:] if len(chunk.data) > 44 else chunk.data
            
            return AudioChunk(
                data=pcm_data,
                format=target_format,
                timestamp=chunk.timestamp,
                duration_ms=chunk.duration_ms,
                sample_rate=chunk.sample_rate
            )
        
        # If conversion not supported, return original
        return chunk
    
    @staticmethod
    def merge_chunks(chunks: List[AudioChunk]) -> AudioChunk:
        """
        Merge multiple audio chunks into one.
        
        Args:
            chunks: List of audio chunks to merge
            
        Returns:
            Merged audio chunk
        """
        if not chunks:
            raise AudioProcessingError("No chunks to merge")
        
        if len(chunks) == 1:
            return chunks[0]
        
        # Validate all chunks have same format
        first_format = chunks[0].format
        first_sample_rate = chunks[0].sample_rate
        
        for chunk in chunks[1:]:
            if chunk.format != first_format or chunk.sample_rate != first_sample_rate:
                raise AudioProcessingError("Cannot merge chunks with different formats")
        
        # Merge data
        merged_data = b''.join(chunk.data for chunk in chunks)
        total_duration = sum(chunk.duration_ms for chunk in chunks)
        
        return AudioChunk(
            data=merged_data,
            format=first_format,
            timestamp=chunks[0].timestamp,
            duration_ms=total_duration,
            sample_rate=first_sample_rate
        )
    
    @staticmethod
    def split_chunk(chunk: AudioChunk, max_duration_ms: int) -> List[AudioChunk]:
        """
        Split audio chunk into smaller chunks.
        
        Args:
            chunk: Audio chunk to split
            max_duration_ms: Maximum duration per chunk in milliseconds
            
        Returns:
            List of smaller audio chunks
        """
        if chunk.duration_ms <= max_duration_ms:
            return [chunk]
        
        chunks = []
        bytes_per_ms = len(chunk.data) / chunk.duration_ms
        bytes_per_chunk = int(bytes_per_ms * max_duration_ms)
        
        # Ensure even number of bytes for 16-bit samples
        bytes_per_chunk = bytes_per_chunk - (bytes_per_chunk % 2)
        
        offset = 0
        timestamp = chunk.timestamp
        
        while offset < len(chunk.data):
            end_offset = min(offset + bytes_per_chunk, len(chunk.data))
            chunk_data = chunk.data[offset:end_offset]
            
            chunk_duration = int(len(chunk_data) / bytes_per_ms)
            
            chunks.append(AudioChunk(
                data=chunk_data,
                format=chunk.format,
                timestamp=timestamp,
                duration_ms=chunk_duration,
                sample_rate=chunk.sample_rate
            ))
            
            offset = end_offset
            timestamp += chunk_duration / 1000.0
        
        return chunks