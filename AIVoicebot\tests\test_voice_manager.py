"""
Tests for voice manager.
"""

import asyncio
import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
import tempfile
import json
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.tts.voice_manager import (
    VoiceManager, VoiceManagerConfig, VoiceProfile, VoicePersonality,
    SpeechContext, CompressionLevel, AudioOptimizationSettings,
    create_voice_manager, create_telephony_optimization,
    create_streaming_optimization, create_custom_voice_profile,
    PYDUB_AVAILABLE
)
from src.components.tts.edge_tts_generator import (
    EdgeTTSGenerator, EdgeTTSConfig, TTSResult, VoiceGender, AudioFormat
)


@pytest.mark.skipif(not PYDUB_AVAILABLE, reason="Pydub not available")
class TestVoiceManager:
    """Test voice manager functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def mock_tts_generator(self):
        """Create mock TTS generator."""
        generator = Mock(spec=EdgeTTSGenerator)
        
        # Mock generate_speech method
        async def mock_generate_speech(text, voice=None, **kwargs):
            return TTSResult(
                audio_data=b"mock_audio_data",
                format=AudioFormat.WAV,
                sample_rate=16000,
                channels=1,
                duration_ms=1000,
                text=text,
                voice_used=voice or "zh-CN-XiaoxiaoNeural",
                generation_time_ms=100,
                file_size_bytes=len(b"mock_audio_data")
            )
        
        generator.generate_speech = mock_generate_speech
        return generator
    
    @pytest.fixture
    def manager_config(self):
        """Create test manager configuration."""
        return VoiceManagerConfig(
            default_personality=VoicePersonality.PROFESSIONAL,
            default_gender=VoiceGender.FEMALE,
            auto_save_profiles=False,  # Disable for tests
            cache_optimized_audio=False  # Disable for tests
        )
    
    @pytest.fixture
    def sample_profile(self):
        """Create sample voice profile."""
        return VoiceProfile(
            name="Test Profile",
            voice_id="zh-CN-TestVoice",
            personality=VoicePersonality.FRIENDLY,
            gender=VoiceGender.FEMALE,
            base_speed="+5%",
            base_pitch="+10Hz",
            warmth=0.8,
            authority=0.6,
            energy=0.9,
            clarity=0.8
        )
    
    def test_manager_creation(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test voice manager creation."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        assert manager.config == manager_config
        assert manager.tts_generator == mock_tts_generator
        assert len(manager.voice_profiles) > 0  # Should have default profiles
    
    @pytest.mark.asyncio
    async def test_manager_lifecycle(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test manager lifecycle."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        assert manager.is_initialized
        assert manager.is_running
        assert manager.active_profile is not None
        
        await manager.stop()
        await manager.cleanup()
    
    def test_default_profiles(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test default voice profiles."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        # Should have created default profiles
        assert len(manager.voice_profiles) >= 4
        
        # Check specific profiles exist
        profile_names = [profile.name for profile in manager.voice_profiles.values()]
        assert "Professional Female" in profile_names
        assert "Friendly Female" in profile_names
        assert "Professional Male" in profile_names
        assert "Warm Female" in profile_names
        
        # Check profile properties
        prof_female = next(p for p in manager.voice_profiles.values() if p.name == "Professional Female")
        assert prof_female.personality == VoicePersonality.PROFESSIONAL
        assert prof_female.gender == VoiceGender.FEMALE
        assert prof_female.voice_id == "zh-CN-XiaoxiaoNeural"
    
    def test_profile_management(self, manager_config, mock_tts_generator, mock_config_manager, sample_profile):
        """Test profile management operations."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        initial_count = len(manager.voice_profiles)
        
        # Add profile
        manager.add_voice_profile(sample_profile)
        assert len(manager.voice_profiles) == initial_count + 1
        
        # Set active profile
        success = manager.set_active_profile("test_profile")
        assert success
        assert manager.get_active_profile() == sample_profile
        
        # List profiles
        profiles = manager.list_voice_profiles()
        assert len(profiles) == initial_count + 1
        assert sample_profile in profiles
        
        # Remove profile
        success = manager.remove_voice_profile("test_profile")
        assert success
        assert len(manager.voice_profiles) == initial_count
        assert manager.get_active_profile() is None  # Should be cleared
    
    def test_profile_filtering(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test profile filtering by personality and gender."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        # Filter by personality
        professional_profiles = manager.get_profile_by_personality(VoicePersonality.PROFESSIONAL)
        assert len(professional_profiles) >= 2  # Should have male and female professional
        assert all(p.personality == VoicePersonality.PROFESSIONAL for p in professional_profiles)
        
        # Filter by gender
        female_profiles = manager.get_profile_by_gender(VoiceGender.FEMALE)
        assert len(female_profiles) >= 3  # Should have multiple female profiles
        assert all(p.gender == VoiceGender.FEMALE for p in female_profiles)
    
    @pytest.mark.asyncio
    async def test_speech_generation(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test speech generation with voice management."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        await manager.initialize()
        await manager.start()
        
        try:
            # Generate speech with default settings
            result = await manager.generate_speech("测试语音生成")
            
            assert isinstance(result, TTSResult)
            assert result.text == "测试语音生成"
            assert result.audio_data == b"mock_audio_data"
            
            # Generate speech with specific context
            result = await manager.generate_speech(
                "欢迎致电我们银行",
                context=SpeechContext.GREETING
            )
            
            assert result.text == "欢迎致电我们银行"
            
        finally:
            await manager.stop()
            await manager.cleanup()
    
    def test_context_parameter_adjustment(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test context-based parameter adjustment."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        # Get a profile with context adjustments
        profile = next(iter(manager.voice_profiles.values()))
        
        # Test different contexts
        greeting_params = profile.get_parameters_for_context(SpeechContext.GREETING)
        explanation_params = profile.get_parameters_for_context(SpeechContext.EXPLANATION)
        
        # Should have different parameters for different contexts
        assert "speed" in greeting_params
        assert "pitch" in greeting_params
        assert "volume" in greeting_params
        
        # Greeting should typically be more energetic
        if SpeechContext.GREETING in profile.context_adjustments:
            assert greeting_params != explanation_params
    
    def test_scenario_recommendation(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test scenario-based profile recommendation."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        # Test different scenarios
        greeting_profile = manager.recommend_profile_for_scenario("greeting")
        support_profile = manager.recommend_profile_for_scenario("support")
        complaint_profile = manager.recommend_profile_for_scenario("complaint")
        
        assert greeting_profile is not None
        assert support_profile is not None
        assert complaint_profile is not None
        
        # Greeting should prefer friendly personalities
        # Support should prefer professional personalities
        # Complaints should prefer empathetic personalities (if available)
        
        # Test customer type preferences
        vip_profile = manager.recommend_profile_for_scenario("support", customer_type="vip")
        elderly_profile = manager.recommend_profile_for_scenario("support", customer_type="elderly")
        
        assert vip_profile is not None
        assert elderly_profile is not None
    
    @pytest.mark.asyncio
    async def test_audio_optimization(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test audio optimization functionality."""
        # Enable optimization for this test
        manager_config.default_optimization = AudioOptimizationSettings(
            compression_level=CompressionLevel.MEDIUM,
            normalize_volume=True
        )
        
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        # Mock pydub AudioSegment
        with patch('src.components.tts.voice_manager.AudioSegment') as mock_audio:
            mock_segment = Mock()
            mock_segment.export.return_value = None
            mock_audio.from_file.return_value = mock_segment
            
            # Mock export to return optimized data
            def mock_export(buffer, format=None, **kwargs):
                buffer.write(b"optimized_audio_data")
            
            mock_segment.export.side_effect = mock_export
            
            await manager.initialize()
            await manager.start()
            
            try:
                # Generate speech with optimization
                result = await manager.generate_speech("测试优化")
                
                # Should have called optimization
                assert result.optimized
                
            finally:
                await manager.stop()
                await manager.cleanup()
    
    def test_optimization_settings(self):
        """Test optimization settings creation."""
        # Test telephony optimization
        telephony = create_telephony_optimization()
        assert telephony.compression_level == CompressionLevel.HIGH
        assert telephony.telephony_mode == True
        assert telephony.frequency_range_hz == (300, 3400)
        
        # Test streaming optimization
        streaming = create_streaming_optimization()
        assert streaming.compression_level == CompressionLevel.MEDIUM
        assert streaming.optimize_for_streaming == True
        assert streaming.adaptive_bitrate == True
    
    def test_statistics_tracking(self, manager_config, mock_tts_generator, mock_config_manager):
        """Test statistics tracking."""
        manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
        
        # Initial stats
        stats = manager.get_voice_manager_stats()
        assert "total_profiles" in stats
        assert "profile_statistics" in stats
        assert "context_statistics" in stats
        
        # Simulate usage
        profile = next(iter(manager.voice_profiles.values()))
        manager._update_profile_usage(profile, SpeechContext.GREETING)
        manager._update_profile_usage(profile, SpeechContext.EXPLANATION)
        
        # Check updated stats
        stats = manager.get_voice_manager_stats()
        context_stats = stats["context_statistics"]
        assert context_stats["total_usage_count"] == 2
        assert len(context_stats["context_distribution"]) == 2
    
    @pytest.mark.asyncio
    async def test_profile_persistence(self, manager_config, mock_tts_generator, mock_config_manager, sample_profile):
        """Test profile saving and loading."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
            manager_config.voice_profiles_file = tmp_file.name
            manager_config.auto_save_profiles = True
        
        try:
            manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
            
            # Add custom profile
            manager.add_voice_profile(sample_profile)
            
            # Save profiles
            await manager._save_voice_profiles()
            
            # Create new manager and load profiles
            new_manager = VoiceManager(manager_config, mock_tts_generator, mock_config_manager)
            await new_manager._load_voice_profiles()
            
            # Should have loaded the custom profile
            profile_names = [p.name for p in new_manager.voice_profiles.values()]
            assert "Test Profile" in profile_names
            
        finally:
            # Clean up
            os.unlink(tmp_file.name)


class TestUtilityFunctions:
    """Test utility functions."""
    
    @pytest.mark.skipif(not PYDUB_AVAILABLE, reason="Pydub not available")
    @pytest.mark.asyncio
    async def test_create_voice_manager(self):
        """Test voice manager factory function."""
        mock_tts = Mock(spec=EdgeTTSGenerator)
        
        manager = await create_voice_manager(
            tts_generator=mock_tts,
            default_personality=VoicePersonality.FRIENDLY,
            default_gender=VoiceGender.MALE,
            config_manager=Mock()
        )
        
        assert manager.is_initialized
        assert manager.is_running
        assert manager.config.default_personality == VoicePersonality.FRIENDLY
        assert manager.config.default_gender == VoiceGender.MALE
        
        await manager.stop()
        await manager.cleanup()
    
    def test_create_custom_voice_profile(self):
        """Test custom voice profile creation."""
        profile = create_custom_voice_profile(
            name="Custom Voice",
            voice_id="zh-CN-CustomVoice",
            personality=VoicePersonality.ENERGETIC,
            gender=VoiceGender.FEMALE,
            warmth=0.9,
            authority=0.7,
            energy=0.95,
            clarity=0.85
        )
        
        assert profile.name == "Custom Voice"
        assert profile.voice_id == "zh-CN-CustomVoice"
        assert profile.personality == VoicePersonality.ENERGETIC
        assert profile.gender == VoiceGender.FEMALE
        assert profile.warmth == 0.9
        assert profile.authority == 0.7
        assert profile.energy == 0.95
        assert profile.clarity == 0.85
    
    def test_optimization_presets(self):
        """Test optimization preset creation."""
        # Test different optimization types
        telephony = create_telephony_optimization()
        streaming = create_streaming_optimization()
        
        # Telephony should be more compressed
        assert telephony.compression_level.value in ["high", "maximum"]
        assert telephony.telephony_mode == True
        
        # Streaming should be balanced
        assert streaming.compression_level == CompressionLevel.MEDIUM
        assert streaming.adaptive_bitrate == True


if __name__ == "__main__":
    # Run a simple test
    if PYDUB_AVAILABLE:
        asyncio.run(TestUtilityFunctions().test_create_voice_manager())
    else:
        print("Pydub not available, skipping tests")