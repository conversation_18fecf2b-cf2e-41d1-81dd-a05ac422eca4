"""
Audio stream manager for coordinating multiple real-time audio streams.

This module provides centralized management of multiple audio streams
with load balancing, resource allocation, and stream synchronization.
"""

import asyncio
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
import time
import logging

from ...core.interfaces import AudioChunk, AudioProcessingError
from ...core.base_component import BaseComponent
from .audio_stream import RealTimeAudioStream, StreamConfig, StreamState, StreamStats


@dataclass
class StreamManagerConfig:
    """Configuration for stream manager."""
    max_concurrent_streams: int = 10
    default_buffer_size: int = 4096
    default_chunk_size: int = 1024
    stream_timeout_seconds: int = 300  # 5 minutes
    
    # Resource management
    enable_load_balancing: bool = True
    enable_resource_monitoring: bool = True
    max_memory_usage_mb: int = 100
    
    # Health monitoring
    health_check_interval_seconds: int = 30
    max_error_rate: float = 0.1  # 10%
    max_latency_ms: float = 500.0


@dataclass
class StreamManagerStats:
    """Statistics for stream manager."""
    active_streams: int = 0
    total_streams_created: int = 0
    total_streams_destroyed: int = 0
    total_bytes_processed: int = 0
    average_latency_ms: float = 0.0
    error_rate: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0


class AudioStreamManager(BaseComponent):
    """
    Manager for multiple real-time audio streams.
    
    Provides centralized management, load balancing, and resource monitoring
    for multiple concurrent audio streams.
    """
    
    def __init__(self, config: StreamManagerConfig, config_manager, logger=None):
        """
        Initialize audio stream manager.
        
        Args:
            config: Stream manager configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("audio_stream_manager", config_manager, logger)
        
        self.config = config
        self.streams: Dict[str, RealTimeAudioStream] = {}
        self.stats = StreamManagerStats()
        
        # Resource monitoring
        self._resource_monitor_task: Optional[asyncio.Task] = None
        self._health_monitor_task: Optional[asyncio.Task] = None
        
        # Synchronization
        self._streams_lock = asyncio.Lock()
        self._stats_lock = threading.Lock()
        
        # Event handlers
        self._stream_created_handlers: List[Callable] = []
        self._stream_destroyed_handlers: List[Callable] = []
        self._error_handlers: List[Callable] = []
        
        # Load balancing
        self._load_balancer = StreamLoadBalancer() if config.enable_load_balancing else None
    
    async def _initialize_impl(self) -> None:
        """Initialize stream manager."""
        self._log.info("Initializing audio stream manager...")
        
        # Validate configuration
        self._validate_config()
        
        self._log.info(f"Stream manager initialized - Max streams: {self.config.max_concurrent_streams}")
    
    async def _start_impl(self) -> None:
        """Start stream manager."""
        self._log.info("Starting audio stream manager...")
        
        # Start monitoring tasks
        if self.config.enable_resource_monitoring:
            self._resource_monitor_task = asyncio.create_task(self._resource_monitor_loop())
        
        self._health_monitor_task = asyncio.create_task(self._health_monitor_loop())
        
        self._log.info("Audio stream manager started")
    
    async def _stop_impl(self) -> None:
        """Stop stream manager."""
        self._log.info("Stopping audio stream manager...")
        
        # Stop all streams
        await self.stop_all_streams()
        
        # Cancel monitoring tasks
        if self._resource_monitor_task:
            self._resource_monitor_task.cancel()
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
        
        self._log.info("Audio stream manager stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup stream manager resources."""
        self._log.info("Cleaning up audio stream manager...")
        
        # Clear all handlers
        self._stream_created_handlers.clear()
        self._stream_destroyed_handlers.clear()
        self._error_handlers.clear()
        
        self._log.info("Stream manager cleanup completed")
    
    def _validate_config(self) -> None:
        """Validate stream manager configuration."""
        if self.config.max_concurrent_streams <= 0:
            raise AudioProcessingError(f"Invalid max concurrent streams: {self.config.max_concurrent_streams}")
        
        if self.config.stream_timeout_seconds <= 0:
            raise AudioProcessingError(f"Invalid stream timeout: {self.config.stream_timeout_seconds}")
        
        if not 0.0 <= self.config.max_error_rate <= 1.0:
            raise AudioProcessingError(f"Invalid max error rate: {self.config.max_error_rate}")
    
    async def create_stream(self, stream_config: StreamConfig) -> RealTimeAudioStream:
        """
        Create a new audio stream.
        
        Args:
            stream_config: Stream configuration
            
        Returns:
            Created audio stream
            
        Raises:
            AudioProcessingError: If stream cannot be created
        """
        async with self._streams_lock:
            # Check if stream already exists
            if stream_config.stream_id in self.streams:
                raise AudioProcessingError(f"Stream already exists: {stream_config.stream_id}")
            
            # Check concurrent stream limit
            if len(self.streams) >= self.config.max_concurrent_streams:
                raise AudioProcessingError(f"Maximum concurrent streams reached: {self.config.max_concurrent_streams}")
            
            # Apply load balancing if enabled
            if self._load_balancer:
                stream_config = self._load_balancer.optimize_config(stream_config, self.streams)
            
            # Create stream
            stream = RealTimeAudioStream(stream_config, self.config_manager, self.logger)
            
            # Add error handler
            stream.add_error_handler(self._handle_stream_error)
            stream.add_state_handler(self._handle_stream_state_change)
            
            # Initialize and start stream
            await stream.initialize()
            await stream.start()
            
            # Register stream
            self.streams[stream_config.stream_id] = stream
            
            # Update statistics
            with self._stats_lock:
                self.stats.active_streams = len(self.streams)
                self.stats.total_streams_created += 1
            
            # Notify handlers
            await self._notify_stream_created(stream)
            
            self._log.info(f"Created audio stream: {stream_config.stream_id}")
            return stream
    
    async def get_stream(self, stream_id: str) -> Optional[RealTimeAudioStream]:
        """
        Get audio stream by ID.
        
        Args:
            stream_id: Stream identifier
            
        Returns:
            Audio stream or None if not found
        """
        async with self._streams_lock:
            return self.streams.get(stream_id)
    
    async def destroy_stream(self, stream_id: str) -> bool:
        """
        Destroy audio stream.
        
        Args:
            stream_id: Stream identifier
            
        Returns:
            True if stream was destroyed, False if not found
        """
        async with self._streams_lock:
            if stream_id not in self.streams:
                return False
            
            stream = self.streams[stream_id]
            
            # Stop stream
            try:
                await stream.stop()
                await stream.cleanup()
            except Exception as e:
                self._log.error(f"Error stopping stream {stream_id}: {e}")
            
            # Remove from registry
            del self.streams[stream_id]
            
            # Update statistics
            with self._stats_lock:
                self.stats.active_streams = len(self.streams)
                self.stats.total_streams_destroyed += 1
            
            # Notify handlers
            await self._notify_stream_destroyed(stream)
            
            self._log.info(f"Destroyed audio stream: {stream_id}")
            return True
    
    async def stop_all_streams(self) -> None:
        """Stop all active streams."""
        self._log.info("Stopping all audio streams...")
        
        async with self._streams_lock:
            stream_ids = list(self.streams.keys())
        
        # Stop streams concurrently
        tasks = [self.destroy_stream(stream_id) for stream_id in stream_ids]
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self._log.info(f"Stopped {len(stream_ids)} audio streams")
    
    async def pause_stream(self, stream_id: str) -> bool:
        """Pause audio stream."""
        stream = await self.get_stream(stream_id)
        if stream:
            await stream.pause()
            return True
        return False
    
    async def resume_stream(self, stream_id: str) -> bool:
        """Resume audio stream."""
        stream = await self.get_stream(stream_id)
        if stream:
            await stream.resume()
            return True
        return False
    
    async def write_to_stream(self, stream_id: str, chunk: AudioChunk) -> bool:
        """Write audio chunk to specific stream."""
        stream = await self.get_stream(stream_id)
        if stream:
            return await stream.write_audio(chunk)
        return False
    
    async def read_from_stream(self, stream_id: str) -> Optional[AudioChunk]:
        """Read audio chunk from specific stream."""
        stream = await self.get_stream(stream_id)
        if stream:
            return await stream.read_audio()
        return None
    
    async def get_stream_generator(self, stream_id: str):
        """Get async generator for stream audio chunks."""
        stream = await self.get_stream(stream_id)
        if stream:
            async for chunk in stream.get_audio_generator():
                yield chunk
    
    def list_streams(self) -> List[str]:
        """Get list of active stream IDs."""
        return list(self.streams.keys())
    
    def get_stream_count(self) -> int:
        """Get number of active streams."""
        return len(self.streams)
    
    def get_manager_stats(self) -> StreamManagerStats:
        """Get stream manager statistics."""
        with self._stats_lock:
            # Calculate aggregate statistics
            total_bytes = 0
            total_latency = 0.0
            active_count = 0
            
            for stream in self.streams.values():
                stream_stats = stream.get_stream_stats()
                total_bytes += stream_stats.bytes_processed
                if stream.is_active:
                    total_latency += stream_stats.average_latency_ms
                    active_count += 1
            
            # Update aggregate stats
            self.stats.total_bytes_processed = total_bytes
            self.stats.average_latency_ms = total_latency / active_count if active_count > 0 else 0.0
            
            return StreamManagerStats(
                active_streams=self.stats.active_streams,
                total_streams_created=self.stats.total_streams_created,
                total_streams_destroyed=self.stats.total_streams_destroyed,
                total_bytes_processed=self.stats.total_bytes_processed,
                average_latency_ms=self.stats.average_latency_ms,
                error_rate=self.stats.error_rate,
                memory_usage_mb=self.stats.memory_usage_mb,
                cpu_usage_percent=self.stats.cpu_usage_percent
            )
    
    def get_all_stream_stats(self) -> Dict[str, StreamStats]:
        """Get statistics for all streams."""
        return {
            stream_id: stream.get_stream_stats()
            for stream_id, stream in self.streams.items()
        }
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status."""
        stats = self.get_manager_stats()
        
        # Determine health status
        health_status = "healthy"
        issues = []
        
        if stats.active_streams >= self.config.max_concurrent_streams * 0.9:
            health_status = "warning"
            issues.append("High stream count")
        
        if stats.average_latency_ms > self.config.max_latency_ms:
            health_status = "warning"
            issues.append("High latency")
        
        if stats.error_rate > self.config.max_error_rate:
            health_status = "critical"
            issues.append("High error rate")
        
        if stats.memory_usage_mb > self.config.max_memory_usage_mb:
            health_status = "warning"
            issues.append("High memory usage")
        
        return {
            "status": health_status,
            "issues": issues,
            "stats": stats,
            "timestamp": time.time()
        }
    
    async def _resource_monitor_loop(self) -> None:
        """Monitor system resources."""
        self._log.debug("Starting resource monitor loop")
        
        try:
            while True:
                await self._update_resource_stats()
                await asyncio.sleep(self.config.health_check_interval_seconds)
        except asyncio.CancelledError:
            self._log.debug("Resource monitor loop cancelled")
        except Exception as e:
            self._log.error(f"Error in resource monitor loop: {e}")
    
    async def _health_monitor_loop(self) -> None:
        """Monitor stream health and cleanup inactive streams."""
        self._log.debug("Starting health monitor loop")
        
        try:
            while True:
                await self._cleanup_inactive_streams()
                await self._check_stream_health()
                await asyncio.sleep(self.config.health_check_interval_seconds)
        except asyncio.CancelledError:
            self._log.debug("Health monitor loop cancelled")
        except Exception as e:
            self._log.error(f"Error in health monitor loop: {e}")
    
    async def _update_resource_stats(self) -> None:
        """Update resource usage statistics."""
        try:
            import psutil
            
            # Get memory usage
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # Get CPU usage
            cpu_percent = process.cpu_percent()
            
            with self._stats_lock:
                self.stats.memory_usage_mb = memory_mb
                self.stats.cpu_usage_percent = cpu_percent
                
        except ImportError:
            # psutil not available, skip resource monitoring
            pass
        except Exception as e:
            self._log.error(f"Error updating resource stats: {e}")
    
    async def _cleanup_inactive_streams(self) -> None:
        """Cleanup inactive or timed-out streams."""
        current_time = time.time()
        timeout_threshold = current_time - self.config.stream_timeout_seconds
        
        streams_to_cleanup = []
        
        async with self._streams_lock:
            for stream_id, stream in self.streams.items():
                # Check if stream is in error state or timed out
                if (stream.state == StreamState.ERROR or 
                    stream.get_stream_stats().start_time < timeout_threshold):
                    streams_to_cleanup.append(stream_id)
        
        # Cleanup streams outside the lock
        for stream_id in streams_to_cleanup:
            self._log.info(f"Cleaning up inactive stream: {stream_id}")
            await self.destroy_stream(stream_id)
    
    async def _check_stream_health(self) -> None:
        """Check health of all active streams."""
        unhealthy_streams = []
        
        for stream_id, stream in self.streams.items():
            try:
                # Check stream health
                buffer_health = stream.buffer_health
                if buffer_health["overall"] != "healthy":
                    self._log.warning(f"Stream {stream_id} buffer health: {buffer_health}")
                
                # Check latency
                if stream.current_latency_ms > self.config.max_latency_ms:
                    self._log.warning(f"Stream {stream_id} high latency: {stream.current_latency_ms}ms")
                
                # Check for errors
                if stream.state == StreamState.ERROR:
                    unhealthy_streams.append(stream_id)
                    
            except Exception as e:
                self._log.error(f"Error checking health of stream {stream_id}: {e}")
                unhealthy_streams.append(stream_id)
        
        # Handle unhealthy streams
        for stream_id in unhealthy_streams:
            self._log.warning(f"Marking unhealthy stream for cleanup: {stream_id}")
            # Could implement recovery logic here instead of cleanup
    
    async def _handle_stream_error(self, error: Exception) -> None:
        """Handle stream error."""
        self._log.error(f"Stream error: {error}")
        
        # Update error rate
        with self._stats_lock:
            # Simple error rate calculation (could be more sophisticated)
            total_operations = self.stats.total_streams_created
            if total_operations > 0:
                error_count = getattr(self.stats, '_error_count', 0) + 1
                self.stats._error_count = error_count
                self.stats.error_rate = error_count / total_operations
        
        # Notify error handlers
        for handler in self._error_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(error)
                else:
                    handler(error)
            except Exception as e:
                self._log.error(f"Error in error handler: {e}")
    
    async def _handle_stream_state_change(self, stream_id: str, old_state: StreamState, new_state: StreamState) -> None:
        """Handle stream state change."""
        self._log.debug(f"Stream {stream_id} state changed: {old_state.value} -> {new_state.value}")
        
        # Update active stream count
        if new_state == StreamState.ACTIVE and old_state != StreamState.ACTIVE:
            with self._stats_lock:
                self.stats.active_streams = len([s for s in self.streams.values() if s.is_active])
        elif old_state == StreamState.ACTIVE and new_state != StreamState.ACTIVE:
            with self._stats_lock:
                self.stats.active_streams = len([s for s in self.streams.values() if s.is_active])
    
    async def _notify_stream_created(self, stream: RealTimeAudioStream) -> None:
        """Notify stream created handlers."""
        for handler in self._stream_created_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(stream)
                else:
                    handler(stream)
            except Exception as e:
                self._log.error(f"Error in stream created handler: {e}")
    
    async def _notify_stream_destroyed(self, stream: RealTimeAudioStream) -> None:
        """Notify stream destroyed handlers."""
        for handler in self._stream_destroyed_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(stream)
                else:
                    handler(stream)
            except Exception as e:
                self._log.error(f"Error in stream destroyed handler: {e}")
    
    # Event handler management
    
    def add_stream_created_handler(self, handler: Callable) -> None:
        """Add stream created event handler."""
        self._stream_created_handlers.append(handler)
    
    def remove_stream_created_handler(self, handler: Callable) -> None:
        """Remove stream created event handler."""
        if handler in self._stream_created_handlers:
            self._stream_created_handlers.remove(handler)
    
    def add_stream_destroyed_handler(self, handler: Callable) -> None:
        """Add stream destroyed event handler."""
        self._stream_destroyed_handlers.append(handler)
    
    def remove_stream_destroyed_handler(self, handler: Callable) -> None:
        """Remove stream destroyed event handler."""
        if handler in self._stream_destroyed_handlers:
            self._stream_destroyed_handlers.remove(handler)
    
    def add_error_handler(self, handler: Callable) -> None:
        """Add error event handler."""
        self._error_handlers.append(handler)
    
    def remove_error_handler(self, handler: Callable) -> None:
        """Remove error event handler."""
        if handler in self._error_handlers:
            self._error_handlers.remove(handler)


class StreamLoadBalancer:
    """Load balancer for optimizing stream configurations."""
    
    def __init__(self):
        self._log = logging.getLogger(self.__class__.__name__)
    
    def optimize_config(self, config: StreamConfig, existing_streams: Dict[str, RealTimeAudioStream]) -> StreamConfig:
        """
        Optimize stream configuration based on current load.
        
        Args:
            config: Original stream configuration
            existing_streams: Currently active streams
            
        Returns:
            Optimized stream configuration
        """
        # Simple load balancing - adjust buffer sizes based on current load
        stream_count = len(existing_streams)
        
        if stream_count > 5:
            # High load - reduce buffer sizes to save memory
            config.buffer_size = max(2048, config.buffer_size // 2)
            config.chunk_size = max(512, config.chunk_size // 2)
            self._log.debug(f"High load detected, reducing buffer sizes for stream {config.stream_id}")
        
        elif stream_count < 2:
            # Low load - can afford larger buffers for better quality
            config.buffer_size = min(8192, config.buffer_size * 2)
            config.chunk_size = min(2048, config.chunk_size * 2)
            self._log.debug(f"Low load detected, increasing buffer sizes for stream {config.stream_id}")
        
        return config


# Utility functions for stream management

async def create_default_stream_manager(config_manager, logger=None) -> AudioStreamManager:
    """
    Create audio stream manager with default configuration.
    
    Args:
        config_manager: Configuration manager instance
        logger: Optional logger instance
        
    Returns:
        Configured audio stream manager
    """
    config = StreamManagerConfig()
    manager = AudioStreamManager(config, config_manager, logger)
    
    await manager.initialize()
    await manager.start()
    
    return manager


async def create_audio_stream(
    stream_id: str,
    manager: AudioStreamManager,
    sample_rate: int = 16000,
    channels: int = 1,
    **kwargs
) -> RealTimeAudioStream:
    """
    Create and register audio stream with manager.
    
    Args:
        stream_id: Unique stream identifier
        manager: Stream manager instance
        sample_rate: Audio sample rate
        channels: Number of audio channels
        **kwargs: Additional stream configuration options
        
    Returns:
        Created audio stream
    """
    config = StreamConfig(
        stream_id=stream_id,
        sample_rate=sample_rate,
        channels=channels,
        **kwargs
    )
    
    return await manager.create_stream(config)