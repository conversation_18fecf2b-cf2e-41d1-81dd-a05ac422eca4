"""Simple test runner for speech segmenter"""

import sys
import numpy as np
from pathlib import Path

sys.path.append("src")

from components.vad.speech_segmenter import (
    SpeechSegmenter,
    SpeechSegment,
    SegmentationConfig,
    SegmentType,
    create_speech_segmenter,
    segment_audio_with_vad
)


def test_speech_segment():
    """Test SpeechSegment creation"""
    print("Testing SpeechSegment...")
    
    segment = SpeechSegment(
        start_time=1.0,
        end_time=3.0,
        segment_type=SegmentType.SPEECH,
        confidence=0.8
    )
    
    assert segment.duration == 2.0
    assert segment.start_sample == 16000
    assert segment.end_sample == 48000
    print("✓ SpeechSegment test passed")


def test_segmentation_config():
    """Test SegmentationConfig"""
    print("Testing SegmentationConfig...")
    
    config = SegmentationConfig()
    assert config.sample_rate == 16000
    assert config.speech_threshold == 0.5
    
    custom_config = SegmentationConfig(sample_rate=8000, speech_threshold=0.7)
    assert custom_config.sample_rate == 8000
    assert custom_config.speech_threshold == 0.7
    print("✓ SegmentationConfig test passed")


def test_speech_segmenter():
    """Test SpeechSegmenter basic functionality"""
    print("Testing SpeechSegmenter...")
    
    config = SegmentationConfig(min_speech_duration=0.1)
    segmenter = SpeechSegmenter(config)
    
    assert segmenter.current_state == SegmentType.SILENCE
    assert len(segmenter.completed_segments) == 0
    
    # Test simple speech detection
    vad_probs = np.array([0.1, 0.1, 0.8, 0.8, 0.8, 0.1, 0.1])
    timestamps = np.arange(len(vad_probs)) * 0.1
    
    segments = segmenter.process_vad_results(vad_probs, timestamps)
    
    # Should detect speech
    speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
    assert len(speech_segments) >= 0  # May or may not detect depending on timing
    
    print("✓ SpeechSegmenter test passed")


def test_convenience_functions():
    """Test convenience functions"""
    print("Testing convenience functions...")
    
    # Test create_speech_segmenter
    segmenter = create_speech_segmenter(
        sample_rate=8000,
        speech_threshold=0.7
    )
    assert segmenter.config.sample_rate == 8000
    assert segmenter.config.speech_threshold == 0.7
    
    # Test segment_audio_with_vad
    audio_data = np.random.randn(1600).astype(np.float32)
    vad_probs = np.array([0.1, 0.8, 0.8, 0.1])
    
    segments = segment_audio_with_vad(
        audio_data=audio_data,
        vad_probabilities=vad_probs,
        sample_rate=16000
    )
    
    assert isinstance(segments, list)
    print("✓ Convenience functions test passed")


def test_adaptive_thresholding():
    """Test adaptive thresholding"""
    print("Testing adaptive thresholding...")
    
    config = SegmentationConfig(adaptive_threshold=True)
    segmenter = SpeechSegmenter(config)
    
    # Process some noisy data
    noisy_probs = np.array([0.2, 0.15, 0.25, 0.1, 0.18])
    timestamps = np.arange(len(noisy_probs)) * 0.1
    
    original_threshold = segmenter.adaptive_speech_threshold
    segmenter.process_vad_results(noisy_probs, timestamps)
    
    # Should have updated noise floor
    assert segmenter.noise_floor >= 0
    print("✓ Adaptive thresholding test passed")


def test_finalization():
    """Test segment finalization"""
    print("Testing segment finalization...")
    
    segmenter = create_speech_segmenter()
    
    # Start with speech
    vad_probs = np.array([0.8, 0.8, 0.8])
    timestamps = np.array([0.0, 0.1, 0.2])
    
    segmenter.process_vad_results(vad_probs, timestamps)
    
    # Finalize
    final_segment = segmenter.finalize_current_segment(end_time=0.3)
    
    if final_segment:
        assert final_segment.segment_type == SegmentType.SPEECH
        assert final_segment.end_time == 0.3
    
    print("✓ Finalization test passed")


def main():
    """Run all tests"""
    print("Running Speech Segmenter Tests")
    print("=" * 40)
    
    try:
        test_speech_segment()
        test_segmentation_config()
        test_speech_segmenter()
        test_convenience_functions()
        test_adaptive_thresholding()
        test_finalization()
        
        print("\n" + "=" * 40)
        print("All tests passed successfully!")
        
    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)