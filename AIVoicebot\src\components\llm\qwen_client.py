"""
Qwen-turbo API client for language model integration.

This module provides a client for interacting with the Qwen-turbo language model,
including API key authentication, request/response handling, and error management.
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
import time
from enum import Enum

from ...core.interfaces import AudioProcessingError
from ...core.base_component import BaseComponent


class QwenModelType(Enum):
    """Qwen model types."""
    QWEN_TURBO = "qwen-turbo"
    QWEN_PLUS = "qwen-plus"
    QWEN_MAX = "qwen-max"


@dataclass
class QwenClientConfig:
    """Configuration for Qwen API client."""
    # API settings
    api_key: str = ""
    api_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"  # OpenAI compatible API
    model: QwenModelType = QwenModelType.QWEN_TURBO

    @property
    def api_url(self) -> str:
        """Get the complete API URL for chat completions."""
        return f"{self.api_base_url}/chat/completions"
    
    # Request parameters
    max_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 0.8
    top_k: int = 50
    
    # Retry settings
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0
    
    # Timeout settings
    request_timeout: float = 30.0
    connect_timeout: float = 10.0
    
    # Rate limiting
    requests_per_minute: int = 60
    enable_rate_limiting: bool = True
    
    # Safety settings
    enable_content_filter: bool = True
    max_input_length: int = 8000


@dataclass
class QwenMessage:
    """Qwen conversation message."""
    role: str  # "system", "user", "assistant"
    content: str
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary format."""
        return {"role": self.role, "content": self.content}


@dataclass
class QwenResponse:
    """Response from Qwen API."""
    text: str
    finish_reason: str = ""
    
    # Usage statistics
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    # Response metadata
    model: str = ""
    response_id: str = ""
    created_time: datetime = field(default_factory=datetime.now)
    
    # Performance metrics
    response_time_ms: float = 0.0
    
    # Error information
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    
    @property
    def is_success(self) -> bool:
        """Check if response is successful."""
        return self.error_code is None
    
    @property
    def is_complete(self) -> bool:
        """Check if response is complete."""
        return self.finish_reason in ["stop", "length"]


class QwenLLMClient(BaseComponent):
    """
    Qwen-turbo API client for language model integration.
    
    Provides API key authentication, request/response handling,
    retry logic with exponential backoff, and error management.
    """
    
    def __init__(self, config: QwenClientConfig, config_manager, logger=None):
        """
        Initialize Qwen client.
        
        Args:
            config: Client configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("qwen_client", config_manager, logger)
        
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Rate limiting
        self._request_times: List[float] = []
        self._rate_limit_lock = asyncio.Lock()
        
        # Statistics
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._total_tokens_used = 0
        self._total_response_time = 0.0
        
        # Circuit breaker state
        self._circuit_breaker_failures = 0
        self._circuit_breaker_last_failure = 0.0
        self._circuit_breaker_open = False 
   
    async def _initialize_impl(self) -> None:
        """Initialize Qwen client."""
        self._log.info("Initializing Qwen client...")
        
        # Validate API key
        if not self.config.api_key:
            raise AudioProcessingError("Qwen API key is required")
        
        # Create HTTP session
        timeout = aiohttp.ClientTimeout(
            total=self.config.request_timeout,
            connect=self.config.connect_timeout
        )
        
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
        )
        
        self._log.info("Qwen client initialized")
    
    async def _start_impl(self) -> None:
        """Start Qwen client."""
        self._log.info("Starting Qwen client...")
        
        # Reset statistics
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._total_tokens_used = 0
        self._total_response_time = 0.0
        
        # Reset circuit breaker
        self._circuit_breaker_failures = 0
        self._circuit_breaker_open = False
        
        self._log.info("Qwen client started")
    
    async def _stop_impl(self) -> None:
        """Stop Qwen client."""
        self._log.info("Stopping Qwen client...")
        
        # Log final statistics
        self._log_final_statistics()
        
        self._log.info("Qwen client stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup Qwen client resources."""
        self._log.info("Cleaning up Qwen client...")
        
        # Close HTTP session
        if self.session:
            await self.session.close()
            self.session = None
        
        # Clear request history
        self._request_times.clear()
        
        self._log.info("Qwen client cleanup completed")
    
    async def generate_response(
        self,
        messages: List[QwenMessage],
        **kwargs
    ) -> QwenResponse:
        """
        Generate response from Qwen model.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional parameters to override config
            
        Returns:
            Qwen response
        """
        start_time = time.time()
        
        try:
            # Check circuit breaker
            if self._is_circuit_breaker_open():
                raise AudioProcessingError("Circuit breaker is open")
            
            # Apply rate limiting
            if self.config.enable_rate_limiting:
                await self._apply_rate_limiting()
            
            # Validate input
            self._validate_messages(messages)
            
            # Prepare request
            request_data = self._prepare_request(messages, **kwargs)
            
            # Make API request with retries
            response = await self._make_request_with_retries(request_data)
            
            # Update statistics
            response_time = (time.time() - start_time) * 1000
            response.response_time_ms = response_time
            
            self._update_statistics(response, response_time)
            
            return response
            
        except Exception as e:
            self._handle_request_error(e)
            
            # Return error response
            return QwenResponse(
                text="",
                error_code="request_failed",
                error_message=str(e),
                response_time_ms=(time.time() - start_time) * 1000
            )
    
    async def generate_simple_response(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        **kwargs
    ) -> QwenResponse:
        """
        Generate simple response from text prompt.
        
        Args:
            prompt: User prompt
            system_message: Optional system message
            **kwargs: Additional parameters
            
        Returns:
            Qwen response
        """
        messages = []
        
        # Add system message if provided
        if system_message:
            messages.append(QwenMessage(role="system", content=system_message))
        
        # Add user prompt
        messages.append(QwenMessage(role="user", content=prompt))
        
        return await self.generate_response(messages, **kwargs)
    
    def _validate_messages(self, messages: List[QwenMessage]) -> None:
        """Validate input messages."""
        if not messages:
            raise ValueError("Messages list cannot be empty")
        
        # Check total input length
        total_length = sum(len(msg.content) for msg in messages)
        if total_length > self.config.max_input_length:
            raise ValueError(f"Input too long: {total_length} > {self.config.max_input_length}")
        
        # Validate message roles
        valid_roles = {"system", "user", "assistant"}
        for msg in messages:
            if msg.role not in valid_roles:
                raise ValueError(f"Invalid message role: {msg.role}")
    
    def _prepare_request(self, messages: List[QwenMessage], **kwargs) -> Dict[str, Any]:
        """Prepare API request data in OpenAI compatible format."""
        # Override config with kwargs
        max_tokens = kwargs.get("max_tokens", self.config.max_tokens)
        temperature = kwargs.get("temperature", self.config.temperature)
        top_p = kwargs.get("top_p", self.config.top_p)

        # Use OpenAI compatible format for Qwen compatible-mode API
        request_data = {
            "model": self.config.model.value,
            "messages": [msg.to_dict() for msg in messages],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "stream": False
        }

        return request_data
    
    async def _make_request_with_retries(self, request_data: Dict[str, Any]) -> QwenResponse:
        """Make API request with retry logic."""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                return await self._make_single_request(request_data)
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.config.max_retries:
                    # Calculate retry delay with exponential backoff
                    delay = self.config.retry_delay * (self.config.retry_backoff ** attempt)
                    
                    self._log.warning(f"Request failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    self._log.error(f"Request failed after {self.config.max_retries + 1} attempts: {e}")
        
        # All retries failed
        raise last_exception
    
    async def _make_single_request(self, request_data: Dict[str, Any]) -> QwenResponse:
        """Make single API request."""
        if not self.session:
            raise AudioProcessingError("Client not initialized")
        
        try:
            async with self.session.post(
                self.config.api_url,
                json=request_data
            ) as response:
                
                response_text = await response.text()
                
                if response.status != 200:
                    raise AudioProcessingError(f"API request failed: {response.status} - {response_text}")
                
                # Parse response
                response_data = json.loads(response_text)
                return self._parse_response(response_data)
                
        except aiohttp.ClientError as e:
            raise AudioProcessingError(f"HTTP client error: {e}")
        except json.JSONDecodeError as e:
            raise AudioProcessingError(f"Failed to parse response JSON: {e}")
    
    def _parse_response(self, response_data: Dict[str, Any]) -> QwenResponse:
        """Parse API response data in OpenAI compatible format."""
        try:
            # Check for API errors (OpenAI format)
            if "error" in response_data:
                error = response_data["error"]
                return QwenResponse(
                    text="",
                    error_code=error.get("code", "unknown"),
                    error_message=error.get("message", "Unknown API error")
                )

            # Extract response content (OpenAI compatible format)
            choices = response_data.get("choices", [])

            if not choices:
                raise ValueError("No choices in response")

            choice = choices[0]
            message = choice.get("message", {})
            content = message.get("content", "")
            finish_reason = choice.get("finish_reason", "")

            # Extract usage statistics (OpenAI format)
            usage = response_data.get("usage", {})
            prompt_tokens = usage.get("prompt_tokens", 0)
            completion_tokens = usage.get("completion_tokens", 0)
            total_tokens = usage.get("total_tokens", prompt_tokens + completion_tokens)

            return QwenResponse(
                text=content,
                finish_reason=finish_reason,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                model=self.config.model.value,
                response_id=response_data.get("id", "")
            )

        except Exception as e:
            raise AudioProcessingError(f"Failed to parse response: {e}")
    
    async def _apply_rate_limiting(self) -> None:
        """Apply rate limiting to requests."""
        async with self._rate_limit_lock:
            current_time = time.time()
            
            # Remove old requests (older than 1 minute)
            cutoff_time = current_time - 60.0
            self._request_times = [t for t in self._request_times if t > cutoff_time]
            
            # Check if we're at the rate limit
            if len(self._request_times) >= self.config.requests_per_minute:
                # Calculate wait time
                oldest_request = min(self._request_times)
                wait_time = 60.0 - (current_time - oldest_request)
                
                if wait_time > 0:
                    self._log.debug(f"Rate limit reached, waiting {wait_time:.2f}s")
                    await asyncio.sleep(wait_time)
            
            # Record this request
            self._request_times.append(current_time)
    
    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open."""
        if not self._circuit_breaker_open:
            return False
        
        # Check if enough time has passed to try again
        current_time = time.time()
        if current_time - self._circuit_breaker_last_failure > 60.0:  # 1 minute cooldown
            self._circuit_breaker_open = False
            self._circuit_breaker_failures = 0
            return False
        
        return True
    
    def _handle_request_error(self, error: Exception) -> None:
        """Handle request error and update circuit breaker."""
        self._failed_requests += 1
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = time.time()
        
        # Open circuit breaker after 5 consecutive failures
        if self._circuit_breaker_failures >= 5:
            self._circuit_breaker_open = True
            self._log.warning("Circuit breaker opened due to consecutive failures")
    
    def _update_statistics(self, response: QwenResponse, response_time: float) -> None:
        """Update client statistics."""
        self._total_requests += 1
        
        if response.is_success:
            self._successful_requests += 1
            self._total_tokens_used += response.total_tokens
            self._total_response_time += response_time
            
            # Reset circuit breaker on success
            self._circuit_breaker_failures = 0
            self._circuit_breaker_open = False
        else:
            self._failed_requests += 1
    
    def get_client_stats(self) -> Dict[str, Any]:
        """Get client statistics."""
        avg_response_time = (
            self._total_response_time / max(1, self._successful_requests)
        )
        
        success_rate = (
            self._successful_requests / max(1, self._total_requests)
        )
        
        return {
            "total_requests": self._total_requests,
            "successful_requests": self._successful_requests,
            "failed_requests": self._failed_requests,
            "success_rate": success_rate,
            "total_tokens_used": self._total_tokens_used,
            "average_response_time_ms": avg_response_time,
            "circuit_breaker_open": self._circuit_breaker_open,
            "circuit_breaker_failures": self._circuit_breaker_failures,
            "rate_limit_active": len(self._request_times),
            "model": self.config.model.value
        }
    
    def _log_final_statistics(self) -> None:
        """Log final statistics when stopping."""
        stats = self.get_client_stats()
        
        self._log.info(f"Qwen client statistics:")
        self._log.info(f"  Total requests: {stats['total_requests']}")
        self._log.info(f"  Success rate: {stats['success_rate']:.2%}")
        self._log.info(f"  Total tokens used: {stats['total_tokens_used']}")
        self._log.info(f"  Average response time: {stats['average_response_time_ms']:.2f}ms")
    
    def reset_stats(self) -> None:
        """Reset client statistics."""
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._total_tokens_used = 0
        self._total_response_time = 0.0
        
        self._log.info("Client statistics reset")


# Utility functions for Qwen client

async def create_qwen_client(
    api_key: str,
    model: QwenModelType = QwenModelType.QWEN_TURBO,
    max_tokens: int = 2000,
    temperature: float = 0.7,
    config_manager=None,
    **kwargs
) -> QwenLLMClient:
    """
    Create and initialize Qwen client.
    
    Args:
        api_key: Qwen API key
        model: Model type to use
        max_tokens: Maximum tokens in response
        temperature: Response randomness
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized Qwen client
    """
    config = QwenClientConfig(
        api_key=api_key,
        model=model,
        max_tokens=max_tokens,
        temperature=temperature,
        **kwargs
    )
    
    client = QwenLLMClient(config, config_manager)
    await client.initialize()
    await client.start()
    
    return client


def create_conversation_messages(
    system_prompt: str,
    conversation_history: List[Dict[str, str]],
    current_query: str
) -> List[QwenMessage]:
    """
    Create conversation messages for Qwen API.
    
    Args:
        system_prompt: System prompt for context
        conversation_history: Previous conversation turns
        current_query: Current user query
        
    Returns:
        List of formatted messages
    """
    messages = []
    
    # Add system message
    if system_prompt:
        messages.append(QwenMessage(role="system", content=system_prompt))
    
    # Add conversation history
    for turn in conversation_history:
        if "user" in turn:
            messages.append(QwenMessage(role="user", content=turn["user"]))
        if "assistant" in turn:
            messages.append(QwenMessage(role="assistant", content=turn["assistant"]))
    
    # Add current query
    messages.append(QwenMessage(role="user", content=current_query))
    
    return messages