"""
Tests for comprehensive conversation logging system.
"""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock
from datetime import datetime
import json
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.conversation_logger import (
    ConversationLogger, LogEntry, LogLevel, EventType, QualityMetrics,
    ConversationAnalytics
)
from src.core.interfaces import CallSession, ConversationTurn, CallSummary


class TestConversationLogger:
    """Test comprehensive conversation logging functionality."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test logs."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_config_manager(self, temp_dir):
        """Create mock configuration manager."""
        config_manager = Mock()
        config_manager.get_config.side_effect = lambda key, default: {
            "logging.conversation_log_path": str(temp_dir / "conversations"),
            "logging.metrics_log_path": str(temp_dir / "metrics"),
            "logging.analytics_log_path": str(temp_dir / "analytics"),
            "logging.enable_structured": True,
            "logging.enable_quality_metrics": True,
            "logging.enable_analytics": True,
            "logging.max_file_size_mb": 100,
            "logging.retention_days": 30
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def logger(self, mock_config_manager):
        """Create ConversationLogger instance."""
        return ConversationLogger(mock_config_manager)
    
    @pytest.fixture
    def sample_session(self):
        """Create sample call session."""
        session = Mock(spec=CallSession)
        session.session_id = "test_session_123"
        session.phone_number = "+**********"
        session.script_id = "script_001"
        session.start_time = datetime.now()
        session.conversation_context = Mock()
        session.conversation_context.customer_info = {"name": "张先生", "account": "12345"}
        return session
    
    @pytest.fixture
    def sample_turn(self):
        """Create sample conversation turn."""
        turn = Mock(spec=ConversationTurn)
        turn.turn_id = "turn_001"
        turn.user_input = "我想了解贷款产品"
        turn.final_response = "我们有多种贷款产品可供选择"
        turn.user_intent = Mock()
        turn.user_intent.value = "inquiry"
        turn.confidence_score = 0.85
        turn.start_time = datetime.now()
        turn.processing_time_ms = 1500
        return turn
    
    @pytest.fixture
    def sample_summary(self):
        """Create sample call summary."""
        summary = Mock(spec=CallSummary)
        summary.session_id = "test_session_123"
        summary.end_time = datetime.now()
        summary.duration = 120.5
        summary.total_turns = 5
        summary.final_intent = "inquiry"
        summary.success = True
        return summary
    
    @pytest.mark.asyncio
    async def test_logger_lifecycle(self, logger):
        """Test logger lifecycle."""
        await logger.initialize()
        await logger.start()
        
        assert logger.is_initialized
        assert logger.is_running
        
        await logger.stop()
        await logger.cleanup()
    
    @pytest.mark.asyncio
    async def test_correlation_id_generation(self, logger):
        """Test correlation ID generation."""
        await logger.initialize()
        
        session_id = "test_session"
        correlation_id = logger.generate_correlation_id(session_id)
        
        assert correlation_id is not None
        assert len(correlation_id) > 0
        assert logger.correlation_map[correlation_id] == session_id
    
    @pytest.mark.asyncio
    async def test_structured_logging(self, logger, temp_dir):
        """Test structured logging functionality."""
        await logger.initialize()
        
        session_id = "test_session"
        correlation_id = logger.generate_correlation_id(session_id)
        
        await logger.log_structured_event(
            correlation_id=correlation_id,
            session_id=session_id,
            event_type=EventType.SYSTEM_EVENT,
            message="Test event",
            level=LogLevel.INFO,
            data={"key": "value"},
            metadata={"source": "test"}
        )
        
        # Check if log file was created
        log_file = temp_dir / "conversations" / f"{session_id}_structured.jsonl"
        assert log_file.exists()
        
        # Check log content
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.loads(f.read().strip())
            assert log_data["correlation_id"] == correlation_id
            assert log_data["session_id"] == session_id
            assert log_data["event_type"] == "system_event"
            assert log_data["message"] == "Test event"
            assert log_data["data"]["key"] == "value"
    
    @pytest.mark.asyncio
    async def test_quality_metrics_logging(self, logger, temp_dir):
        """Test quality metrics logging."""
        await logger.initialize()
        
        session_id = "test_session"
        correlation_id = logger.generate_correlation_id(session_id)
        
        metrics = QualityMetrics(
            session_id=session_id,
            correlation_id=correlation_id,
            response_time_ms=1500.0,
            transcription_accuracy=0.95,
            intent_confidence=0.88,
            audio_clarity=0.92
        )
        
        await logger.log_quality_metrics(metrics)
        
        # Check if metrics file was created
        metrics_file = temp_dir / "metrics" / f"{session_id}_metrics.jsonl"
        assert metrics_file.exists()
        
        # Check metrics content
        with open(metrics_file, 'r', encoding='utf-8') as f:
            metrics_data = json.loads(f.read().strip())
            assert metrics_data["session_id"] == session_id
            assert metrics_data["response_time_ms"] == 1500.0
            assert metrics_data["transcription_accuracy"] == 0.95
    
    @pytest.mark.asyncio
    async def test_session_logging(self, logger, sample_session, temp_dir):
        """Test session start/end logging."""
        await logger.initialize()
        
        # Log session start
        correlation_id = await logger.log_session_start(sample_session)
        
        assert correlation_id is not None
        assert sample_session.session_id in logger.session_analytics
        
        # Check structured log
        log_file = temp_dir / "conversations" / f"{sample_session.session_id}_structured.jsonl"
        assert log_file.exists()
    
    @pytest.mark.asyncio
    async def test_turn_logging(self, logger, sample_turn, temp_dir):
        """Test conversation turn logging."""
        await logger.initialize()
        
        session_id = "test_session"
        logger.start_session_analytics(session_id)
        
        await logger.log_turn(session_id, sample_turn)
        
        # Check analytics update
        analytics = logger.session_analytics[session_id]
        assert analytics.total_turns == 1
        assert "inquiry" in analytics.intent_distribution
        
        # Check structured log
        log_file = temp_dir / "conversations" / f"{session_id}_structured.jsonl"
        assert log_file.exists()
    
    @pytest.mark.asyncio
    async def test_transcription_logging(self, logger, temp_dir):
        """Test transcription logging."""
        await logger.initialize()
        
        session_id = "test_session"
        correlation_id = logger.generate_correlation_id(session_id)
        
        await logger.log_transcription(
            correlation_id=correlation_id,
            session_id=session_id,
            audio_duration_ms=2000.0,
            transcribed_text="我想了解贷款产品",
            confidence=0.95,
            processing_time_ms=500.0
        )
        
        # Check structured log
        log_file = temp_dir / "conversations" / f"{session_id}_structured.jsonl"
        assert log_file.exists()
        
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.loads(f.read().strip())
            assert log_data["event_type"] == "transcription"
            assert log_data["data"]["transcribed_text"] == "我想了解贷款产品"
            assert log_data["data"]["confidence"] == 0.95
    
    @pytest.mark.asyncio
    async def test_analytics_functionality(self, logger):
        """Test analytics functionality."""
        await logger.initialize()
        
        session_id = "test_session"
        logger.start_session_analytics(session_id)
        
        # Update analytics
        logger.update_session_analytics(
            session_id=session_id,
            intent="inquiry",
            sentiment_score=0.7,
            response_time_ms=1500.0,
            topic_change=True
        )
        
        analytics = logger.session_analytics[session_id]
        assert analytics.total_turns == 1
        assert analytics.intent_distribution["inquiry"] == 1
        assert len(analytics.sentiment_scores) == 1
        assert analytics.topic_changes == 1
        assert analytics.average_response_time_ms == 1500.0
        
        # Finalize analytics
        final_analytics = logger.finalize_session_analytics(
            session_id=session_id,
            duration_seconds=120.0,
            customer_satisfaction_score=4.5,
            resolution_status="resolved"
        )
        
        assert final_analytics is not None
        assert final_analytics.total_duration_seconds == 120.0
        assert final_analytics.customer_satisfaction_score == 4.5
    
    @pytest.mark.asyncio
    async def test_error_logging(self, logger, temp_dir):
        """Test error logging."""
        await logger.initialize()
        
        session_id = "test_session"
        correlation_id = logger.generate_correlation_id(session_id)
        
        await logger.log_error(
            correlation_id=correlation_id,
            session_id=session_id,
            error_type="TranscriptionError",
            error_message="Failed to transcribe audio",
            component="ASR",
            recovery_action="Retry with different model"
        )
        
        # Check structured log
        log_file = temp_dir / "conversations" / f"{session_id}_structured.jsonl"
        assert log_file.exists()
        
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.loads(f.read().strip())
            assert log_data["event_type"] == "error_occurred"
            assert log_data["level"] == "error"
            assert log_data["data"]["error_type"] == "TranscriptionError"
    
    def test_log_entry_creation(self):
        """Test LogEntry creation and serialization."""
        entry = LogEntry(
            correlation_id="corr_123",
            session_id="session_123",
            event_type=EventType.TRANSCRIPTION,
            timestamp=datetime.now(),
            level=LogLevel.INFO,
            message="Test message",
            data={"key": "value"}
        )
        
        entry_dict = entry.to_dict()
        assert entry_dict["correlation_id"] == "corr_123"
        assert entry_dict["session_id"] == "session_123"
        assert entry_dict["event_type"] == "transcription"
        assert entry_dict["level"] == "info"
    
    def test_quality_metrics_creation(self):
        """Test QualityMetrics creation and serialization."""
        metrics = QualityMetrics(
            session_id="session_123",
            correlation_id="corr_123",
            response_time_ms=1500.0,
            transcription_accuracy=0.95
        )
        
        metrics_dict = metrics.to_dict()
        assert metrics_dict["session_id"] == "session_123"
        assert metrics_dict["response_time_ms"] == 1500.0
        assert metrics_dict["transcription_accuracy"] == 0.95


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestConversationLogger().test_logger_lifecycle(ConversationLogger(Mock())))
