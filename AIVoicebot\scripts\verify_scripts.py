#!/usr/bin/env python3
"""
Script Verification Tool

This script verifies that the conversation scripts (Excel files) are properly
loaded and parsed by the system.
"""

import asyncio
import sys
import os
from pathlib import Path
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    from components.scripts.script_manager import ScriptManager, ScriptManagerConfig
    from components.scripts.script_parser import <PERSON>riptParser, ScriptParserConfig
    from core.config_manager import ConfigManager
except ImportError:
    # Fallback for different import structure
    try:
        from src.components.scripts.script_manager import ScriptManager, ScriptManagerConfig
        from src.components.scripts.script_parser import ScriptParser, ScriptParserConfig
        from src.core.config_manager import ConfigManager
    except ImportError:
        print("❌ Cannot import required modules. Please check the project structure.")
        sys.exit(1)


async def verify_excel_files():
    """Verify that Excel files exist and are readable."""
    print("🔍 Verifying Excel files...")
    
    excel_files = [
        "docs/言犀复贷话术.xlsx",
        "docs/零犀复贷AI话术调优240914.xlsx"
    ]
    
    results = {}
    
    for file_path in excel_files:
        full_path = Path(file_path)
        print(f"\n📄 Checking: {file_path}")
        
        if not full_path.exists():
            print(f"  ❌ File not found: {full_path}")
            results[file_path] = {"exists": False, "readable": False, "sheets": []}
            continue
        
        print(f"  ✅ File exists: {full_path}")
        results[file_path] = {"exists": True, "readable": False, "sheets": []}
        
        try:
            # Try to read Excel file
            excel_file = pd.ExcelFile(full_path)
            sheet_names = excel_file.sheet_names
            
            print(f"  ✅ File readable")
            print(f"  📋 Sheets found: {len(sheet_names)}")
            
            for sheet_name in sheet_names:
                print(f"    - {sheet_name}")
                
                # Try to read first few rows of each sheet
                try:
                    df = pd.read_excel(full_path, sheet_name=sheet_name, nrows=5)
                    print(f"      📊 Columns: {list(df.columns)}")
                    print(f"      📈 Rows (sample): {len(df)}")
                except Exception as e:
                    print(f"      ⚠️  Error reading sheet: {e}")
            
            results[file_path]["readable"] = True
            results[file_path]["sheets"] = sheet_names
            
        except Exception as e:
            print(f"  ❌ Error reading file: {e}")
            results[file_path]["readable"] = False
    
    return results


async def verify_script_parser():
    """Verify script parser functionality."""
    print("\n🔧 Verifying Script Parser...")
    
    try:
        config_manager = ConfigManager()
        
        # Create script parser config
        parser_config = ScriptParserConfig(
            required_columns=['用户输入', '系统回复'],
            optional_columns=['场景', '意图', '置信度'],
            default_confidence=0.8,
            validate_responses=True
        )
        
        # Create script parser
        parser = ScriptParser(parser_config, config_manager)
        await parser.initialize()
        
        print("  ✅ Script parser initialized")
        
        # Test parsing directory
        results = parser.parse_directory("docs")
        
        print(f"  📊 Parsed {len(results)} files")
        
        total_scripts = 0
        for file_path, scripts in results.items():
            print(f"    📄 {file_path}: {len(scripts)} scripts")
            total_scripts += len(scripts)
            
            # Show sample scripts
            if scripts:
                sample_script = scripts[0]
                print(f"      📝 Sample script ID: {sample_script.script_id}")
                print(f"      🎯 Sample scenario: {sample_script.scenario}")
                print(f"      💬 Sample exchanges: {len(sample_script.exchanges)}")
        
        print(f"  📈 Total scripts loaded: {total_scripts}")
        
        await parser.cleanup()
        return True
        
    except Exception as e:
        print(f"  ❌ Error in script parser: {e}")
        return False


async def verify_script_manager():
    """Verify script manager functionality."""
    print("\n🎛️  Verifying Script Manager...")
    
    try:
        config_manager = ConfigManager()
        
        # Create script manager config
        manager_config = ScriptManagerConfig(
            script_directory="docs",
            auto_reload=True,
            reload_debounce_seconds=2.0,
            max_scripts_per_file=1000
        )
        
        # Create script manager
        manager = ScriptManager(manager_config, config_manager)
        await manager.initialize()
        await manager.start()
        
        print("  ✅ Script manager initialized and started")
        
        # Load scripts
        await manager.load_scripts()
        
        # Get script statistics
        stats = manager.get_script_statistics()
        print(f"  📊 Script statistics:")
        print(f"    📄 Total files: {stats['total_files']}")
        print(f"    📝 Total scripts: {stats['total_scripts']}")
        print(f"    ⚠️  Total errors: {stats['total_errors']}")
        print(f"    🕐 Last reload: {stats['last_reload_time']}")
        
        # Test script retrieval
        all_scripts = manager.get_all_scripts()
        print(f"  📋 Retrieved {len(all_scripts)} scripts")
        
        if all_scripts:
            # Show sample script details
            sample_script = list(all_scripts.values())[0]
            print(f"    📝 Sample script: {sample_script.script_id}")
            print(f"    🎯 Scenario: {sample_script.scenario}")
            print(f"    💬 Exchanges: {len(sample_script.exchanges)}")
            
            if sample_script.exchanges:
                sample_exchange = sample_script.exchanges[0]
                print(f"    👤 Sample user input: {sample_exchange.user_input[:50]}...")
                print(f"    🤖 Sample system response: {sample_exchange.system_response[:50]}...")
        
        await manager.stop()
        await manager.cleanup()
        return True
        
    except Exception as e:
        print(f"  ❌ Error in script manager: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main verification function."""
    print("🚀 Starting Script Verification...")
    print("=" * 60)
    
    # Verify Excel files
    excel_results = await verify_excel_files()
    
    # Verify script parser
    parser_success = await verify_script_parser()
    
    # Verify script manager
    manager_success = await verify_script_manager()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    # Excel files summary
    print("📄 Excel Files:")
    for file_path, result in excel_results.items():
        status = "✅" if result["exists"] and result["readable"] else "❌"
        print(f"  {status} {file_path}")
        if result["readable"]:
            print(f"    📋 Sheets: {len(result['sheets'])}")
    
    # Components summary
    print("\n🔧 Components:")
    print(f"  {'✅' if parser_success else '❌'} Script Parser")
    print(f"  {'✅' if manager_success else '❌'} Script Manager")
    
    # Overall status
    all_files_ok = all(r["exists"] and r["readable"] for r in excel_results.values())
    overall_success = all_files_ok and parser_success and manager_success
    
    print(f"\n🎯 Overall Status: {'✅ SUCCESS' if overall_success else '❌ FAILED'}")
    
    if overall_success:
        print("\n🎉 All conversation scripts are properly integrated!")
        print("The system can successfully load and parse the Excel files:")
        print("  - 言犀复贷话术.xlsx")
        print("  - 零犀复贷AI话术调优240914.xlsx")
    else:
        print("\n⚠️  Some issues were found. Please check the details above.")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
