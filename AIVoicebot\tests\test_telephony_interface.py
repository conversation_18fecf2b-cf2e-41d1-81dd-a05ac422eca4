"""
Tests for enhanced telephony interface with quality monitoring and problem detection.
"""

import asyncio
import pytest
from unittest.mock import Mock
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.mock_telephony import MockTelephony
from src.services.telephony_interface import (
    CallStatus, CallQualityMetrics, ConnectionQuality, ConnectionProblem,
    TelephonyError
)
from src.core.interfaces import AudioChunk, AudioFormat
from datetime import datetime


class TestTelephonyInterface:
    """Test enhanced telephony interface functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def telephony(self, mock_config_manager):
        """Create MockTelephony instance."""
        return MockTelephony(mock_config_manager)
    
    @pytest.mark.asyncio
    async def test_telephony_lifecycle(self, telephony):
        """Test telephony interface lifecycle."""
        await telephony.initialize()
        await telephony.start()
        
        assert telephony.is_initialized
        assert telephony.is_running
        
        await telephony.stop()
        await telephony.cleanup()
    
    @pytest.mark.asyncio
    async def test_make_call_success(self, telephony):
        """Test successful call creation."""
        await telephony.initialize()
        await telephony.start()
        
        # Set successful outcome
        telephony.set_next_call_outcome("+1234567890", "answered")
        
        call_id = await telephony.make_call("+1234567890", "session_123")
        
        assert call_id == "session_123"
        
        # Check call info
        call_info = await telephony.get_call_info(call_id)
        assert call_info is not None
        assert call_info.phone_number == "+1234567890"
        assert call_info.status == CallStatus.ANSWERED
        assert call_info.quality_metrics is not None
        
        await telephony.stop()
    
    @pytest.mark.asyncio
    async def test_make_call_failed(self, telephony):
        """Test failed call creation."""
        await telephony.initialize()
        await telephony.start()
        
        # Set failed outcome
        telephony.set_next_call_outcome("+1234567890", "failed")
        
        with pytest.raises(TelephonyError):
            await telephony.make_call("+1234567890", "session_123")
        
        await telephony.stop()
    
    @pytest.mark.asyncio
    async def test_call_quality_monitoring(self, telephony):
        """Test call quality monitoring."""
        await telephony.initialize()
        await telephony.start()
        
        telephony.set_next_call_outcome("+1234567890", "answered")
        call_id = await telephony.make_call("+1234567890", "session_123")
        
        # Get initial quality metrics
        quality = await telephony.get_call_quality(call_id)
        assert quality is not None
        assert isinstance(quality, CallQualityMetrics)
        assert quality.jitter_ms >= 0
        assert quality.packet_loss_percent >= 0
        assert quality.latency_ms >= 0
        
        # Check quality level determination
        assert quality.quality_level in [
            ConnectionQuality.EXCELLENT,
            ConnectionQuality.GOOD,
            ConnectionQuality.FAIR,
            ConnectionQuality.POOR,
            ConnectionQuality.CRITICAL
        ]
        
        await telephony.end_call(call_id)
        await telephony.stop()
    
    @pytest.mark.asyncio
    async def test_connection_problem_detection(self, telephony):
        """Test connection problem detection."""
        await telephony.initialize()
        await telephony.start()
        
        telephony.set_next_call_outcome("+1234567890", "answered")
        call_id = await telephony.make_call("+1234567890", "session_123")
        
        # Manually set poor quality metrics to trigger problems
        call_info = await telephony.get_call_info(call_id)
        if call_info and call_info.quality_metrics:
            call_info.quality_metrics.packet_loss_percent = 6.0  # High packet loss
            call_info.quality_metrics.jitter_ms = 200  # High jitter
            call_info.quality_metrics.latency_ms = 350  # High latency
        
        # Detect problems
        problems = await telephony.detect_connection_problems(call_id)
        
        assert len(problems) > 0
        
        # Check for expected problems
        problem_types = [p.problem_type for p in problems]
        assert "high_packet_loss" in problem_types
        assert "high_jitter" in problem_types
        assert "high_latency" in problem_types
        
        # Check problem details
        for problem in problems:
            assert isinstance(problem, ConnectionProblem)
            assert problem.call_id == call_id
            assert problem.severity in ["low", "medium", "high"]
            assert len(problem.description) > 0
        
        await telephony.end_call(call_id)
        await telephony.stop()
    
    @pytest.mark.asyncio
    async def test_active_calls_management(self, telephony):
        """Test active calls management."""
        await telephony.initialize()
        await telephony.start()
        
        # Initially no active calls
        active_calls = await telephony.get_active_calls()
        assert len(active_calls) == 0
        
        # Make multiple calls
        telephony.set_next_call_outcome("+1111111111", "answered")
        telephony.set_next_call_outcome("+2222222222", "answered")
        
        call_id1 = await telephony.make_call("+1111111111", "session_1")
        call_id2 = await telephony.make_call("+2222222222", "session_2")
        
        # Check active calls
        active_calls = await telephony.get_active_calls()
        assert len(active_calls) == 2
        
        call_ids = [call.call_id for call in active_calls]
        assert call_id1 in call_ids
        assert call_id2 in call_ids
        
        # End one call
        await telephony.end_call(call_id1)
        
        active_calls = await telephony.get_active_calls()
        assert len(active_calls) == 1
        assert active_calls[0].call_id == call_id2
        
        # End remaining call
        await telephony.end_call(call_id2)
        
        active_calls = await telephony.get_active_calls()
        assert len(active_calls) == 0
        
        await telephony.stop()
    
    @pytest.mark.asyncio
    async def test_system_status(self, telephony):
        """Test system status reporting."""
        await telephony.initialize()
        await telephony.start()
        
        # Get system status with no active calls
        status = await telephony.get_system_status()
        
        assert isinstance(status, dict)
        assert "status" in status
        assert "active_calls" in status
        assert "max_concurrent_calls" in status
        assert "average_quality" in status
        assert "system_load" in status
        assert "uptime_seconds" in status
        assert "last_updated" in status
        
        assert status["active_calls"] == 0
        
        # Make a call and check status again
        telephony.set_next_call_outcome("+1234567890", "answered")
        call_id = await telephony.make_call("+1234567890", "session_123")
        
        status = await telephony.get_system_status()
        assert status["active_calls"] == 1
        assert "jitter_ms" in status["average_quality"]
        assert "packet_loss_percent" in status["average_quality"]
        assert "latency_ms" in status["average_quality"]
        
        await telephony.end_call(call_id)
        await telephony.stop()
    
    @pytest.mark.asyncio
    async def test_audio_streaming(self, telephony):
        """Test audio streaming functionality."""
        await telephony.initialize()
        await telephony.start()
        
        telephony.set_next_call_outcome("+1234567890", "answered")
        call_id = await telephony.make_call("+1234567890", "session_123")
        
        # Test audio streaming
        chunk_count = 0
        async for audio_chunk in telephony.get_audio_stream(call_id):
            assert isinstance(audio_chunk, AudioChunk)
            assert len(audio_chunk.data) > 0
            assert audio_chunk.format == AudioFormat.PCM_16KHZ_MONO
            assert audio_chunk.sample_rate == 16000
            
            chunk_count += 1
            if chunk_count >= 3:  # Test a few chunks
                break
        
        assert chunk_count >= 3
        
        # Test sending audio
        test_audio = AudioChunk(
            data=b"test_audio_data",
            format=AudioFormat.PCM_16KHZ_MONO,
            timestamp=datetime.now(),
            duration_ms=100,
            sample_rate=16000
        )
        
        # Should not raise exception
        await telephony.send_audio(call_id, test_audio)
        
        await telephony.end_call(call_id)
        await telephony.stop()
    
    def test_quality_metrics_properties(self):
        """Test quality metrics properties."""
        # Test excellent quality
        excellent_metrics = CallQualityMetrics(
            jitter_ms=20,
            packet_loss_percent=0.1,
            latency_ms=80
        )
        assert excellent_metrics.quality_level == ConnectionQuality.EXCELLENT
        
        # Test poor quality
        poor_metrics = CallQualityMetrics(
            jitter_ms=120,
            packet_loss_percent=4.0,
            latency_ms=250
        )
        assert poor_metrics.quality_level == ConnectionQuality.POOR
        
        # Test critical quality
        critical_metrics = CallQualityMetrics(
            jitter_ms=200,
            packet_loss_percent=8.0,
            latency_ms=400
        )
        assert critical_metrics.quality_level == ConnectionQuality.CRITICAL


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestTelephonyInterface().test_telephony_lifecycle(MockTelephony(Mock())))
