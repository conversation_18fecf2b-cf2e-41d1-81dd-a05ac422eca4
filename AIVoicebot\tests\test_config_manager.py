"""
Tests for Configuration Manager

This module tests the configuration management system including:
- Configuration loading from files and environment variables
- Environment-specific configuration handling
- Configuration validation and error handling
- Hot-reloading functionality
"""

import pytest
import os
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.config.config_manager import (
    ConfigManager,
    AudioConfig,
    LLMConfig,
    TTSConfig,
    Environment,
    get_config,
    initialize_config
)


class TestConfigManager:
    """Test cases for ConfigManager class"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Create test configuration files
        self._create_test_config_files()
    
    def teardown_method(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_test_config_files(self):
        """Create test configuration files"""
        # Base configuration
        base_config = {
            "audio": {
                "sample_rate": 16000,
                "channels": 1,
                "vad_threshold": 0.5
            },
            "llm": {
                "api_key": "test_key",
                "model_name": "qwen-turbo",
                "timeout": 30
            },
            "tts": {
                "voice": "zh-CN-XiaoxiaoNeural",
                "rate": "+0%"
            }
        }
        
        with open(self.config_dir / "base.yaml", 'w') as f:
            yaml.dump(base_config, f)
        
        # Development configuration
        dev_config = {
            "audio": {
                "vad_threshold": 0.4
            },
            "llm": {
                "timeout": 10
            }
        }
        
        with open(self.config_dir / "development.yaml", 'w') as f:
            yaml.dump(dev_config, f)
    
    def test_config_manager_initialization(self):
        """Test ConfigManager initialization"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        assert config_manager.environment == Environment.DEVELOPMENT
        assert config_manager.config_dir == self.config_dir
        assert config_manager.audio is not None
        assert config_manager.llm is not None
        assert config_manager.tts is not None
    
    def test_configuration_loading(self):
        """Test configuration loading and merging"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        # Check that development config overrides base config
        assert config_manager.audio.vad_threshold == 0.4  # From development.yaml
        assert config_manager.audio.sample_rate == 16000  # From base.yaml
        assert config_manager.llm.timeout == 10  # From development.yaml
        assert config_manager.llm.api_key == "test_key"  # From base.yaml
    
    def test_environment_variable_overrides(self):
        """Test environment variable configuration overrides"""
        with patch.dict(os.environ, {
            'QWEN_API_KEY': 'env_api_key',
            'AUDIO_SAMPLE_RATE': '22050',
            'VAD_THRESHOLD': '0.7'
        }):
            config_manager = ConfigManager(
                config_dir=str(self.config_dir),
                environment="development"
            )
            
            assert config_manager.llm.api_key == "env_api_key"
            assert config_manager.audio.sample_rate == 22050
            assert config_manager.audio.vad_threshold == 0.7
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        # Create invalid configuration
        invalid_config = {
            "audio": {
                "sample_rate": 999,  # Invalid sample rate
                "channels": 5  # Invalid channels
            },
            "llm": {
                "api_key": ""  # Missing API key
            }
        }
        
        with open(self.config_dir / "invalid.yaml", 'w') as f:
            yaml.dump(invalid_config, f)
        
        # Should raise validation error
        with pytest.raises(ValueError):
            ConfigManager(
                config_dir=str(self.config_dir),
                environment="invalid"
            )
    
    def test_get_config_value(self):
        """Test getting configuration values by key path"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        # Test existing values
        assert config_manager.get_config_value("audio.sample_rate") == 16000
        assert config_manager.get_config_value("llm.api_key") == "test_key"
        
        # Test non-existing values with default
        assert config_manager.get_config_value("nonexistent.key", "default") == "default"
        assert config_manager.get_config_value("audio.nonexistent") is None
    
    def test_set_config_value(self):
        """Test setting configuration values by key path"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        # Set new value
        config_manager.set_config_value("audio.sample_rate", 22050)
        assert config_manager.audio.sample_rate == 22050
        assert config_manager.get_config_value("audio.sample_rate") == 22050
        
        # Set nested value
        config_manager.set_config_value("new.nested.value", "test")
        assert config_manager.get_config_value("new.nested.value") == "test"
    
    def test_configuration_reload(self):
        """Test configuration hot-reloading"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        original_threshold = config_manager.audio.vad_threshold
        
        # Modify configuration file
        import time
        time.sleep(0.1)  # Ensure timestamp difference
        
        modified_config = {
            "audio": {
                "vad_threshold": 0.9
            }
        }
        
        with open(self.config_dir / "development.yaml", 'w') as f:
            yaml.dump(modified_config, f)
        
        # Reload configuration
        reloaded = config_manager.reload_configuration()
        assert reloaded is True
        assert config_manager.audio.vad_threshold == 0.9
        assert config_manager.audio.vad_threshold != original_threshold
    
    def test_export_config(self):
        """Test configuration export functionality"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        # Test YAML export
        yaml_export = config_manager.export_config("yaml")
        assert "audio:" in yaml_export
        assert "sample_rate: 16000" in yaml_export
        
        # Test JSON export
        json_export = config_manager.export_config("json")
        assert '"audio"' in json_export
        assert '"sample_rate": 16000' in json_export
        
        # Test invalid format
        with pytest.raises(ValueError):
            config_manager.export_config("invalid")
    
    def test_environment_info(self):
        """Test environment information retrieval"""
        config_manager = ConfigManager(
            config_dir=str(self.config_dir),
            environment="development"
        )
        
        env_info = config_manager.get_environment_info()
        
        assert env_info["environment"] == "development"
        assert env_info["config_dir"] == str(self.config_dir)
        assert "loaded_files" in env_info
        assert "python_version" in env_info
        assert "platform" in env_info
    
    def test_default_configuration_fallback(self):
        """Test fallback to default configuration"""
        # Create ConfigManager with non-existent config directory
        config_manager = ConfigManager(
            config_dir="/nonexistent/path",
            environment="development"
        )
        
        # Should have default configuration
        assert config_manager.audio is not None
        assert config_manager.llm is not None
        assert config_manager.tts is not None
        assert isinstance(config_manager.audio, AudioConfig)
        assert isinstance(config_manager.llm, LLMConfig)
        assert isinstance(config_manager.tts, TTSConfig)


class TestGlobalConfigManager:
    """Test cases for global configuration manager functions"""
    
    def test_get_config_singleton(self):
        """Test global configuration manager singleton"""
        # Reset global config
        import src.config.config_manager as config_module
        config_module.config_manager = None
        
        # Get config instances
        config1 = get_config()
        config2 = get_config()
        
        # Should be the same instance
        assert config1 is config2
    
    def test_initialize_config(self):
        """Test global configuration manager initialization"""
        # Reset global config
        import src.config.config_manager as config_module
        config_module.config_manager = None
        
        # Initialize with custom settings
        config = initialize_config(
            config_dir="custom_config",
            environment="production"
        )
        
        assert config.environment == Environment.PRODUCTION
        
        # Get config should return the same instance
        assert get_config() is config


class TestConfigurationObjects:
    """Test cases for configuration data classes"""
    
    def test_audio_config_defaults(self):
        """Test AudioConfig default values"""
        config = AudioConfig()
        
        assert config.sample_rate == 16000
        assert config.channels == 1
        assert config.chunk_size == 1024
        assert config.vad_threshold == 0.5
        assert config.noise_reduction is True
    
    def test_llm_config_defaults(self):
        """Test LLMConfig default values"""
        config = LLMConfig()
        
        assert config.model_name == "qwen-turbo"
        assert config.max_tokens == 1000
        assert config.temperature == 0.7
        assert config.max_retries == 3
    
    def test_tts_config_defaults(self):
        """Test TTSConfig default values"""
        config = TTSConfig()
        
        assert config.voice == "zh-CN-XiaoxiaoNeural"
        assert config.rate == "+0%"
        assert config.volume == "+0%"
        assert config.cache_enabled is True


if __name__ == "__main__":
    pytest.main([__file__])