#!/usr/bin/env python3
"""
AI Voice Customer Service System - Performance Test
==================================================

This file performs basic performance testing of the AI Voice System
to ensure it can handle multiple concurrent requests.

Prerequisites:
- System running on http://localhost:8000
- Python with requests library installed
"""

import requests
import json
import time
import asyncio
import concurrent.futures
from typing import Dict, Any, List

# Base URL for the API
BASE_URL = "http://localhost:8000"

class PerformanceTester:
    """Performance testing client for the AI Voice System."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_single_request(self, endpoint: str) -> Dict[str, Any]:
        """Test a single request and measure response time."""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}{endpoint}")
            response.raise_for_status()
            end_time = time.time()
            
            return {
                "success": True,
                "response_time": end_time - start_time,
                "status_code": response.status_code,
                "content_length": len(response.content)
            }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e)
            }
    
    def test_call_creation(self, phone_number: str) -> Dict[str, Any]:
        """Test call creation and measure response time."""
        start_time = time.time()
        try:
            data = {
                "caller_number": phone_number,
                "callee_number": "+0987654321"
            }
            response = self.session.post(f"{self.base_url}/calls/incoming", json=data)
            response.raise_for_status()
            end_time = time.time()
            
            result = response.json()
            return {
                "success": True,
                "response_time": end_time - start_time,
                "call_id": result.get("call_id"),
                "status_code": response.status_code
            }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e)
            }
    
    def run_concurrent_tests(self, endpoint: str, num_requests: int = 10) -> List[Dict[str, Any]]:
        """Run multiple concurrent requests to test system performance."""
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_requests) as executor:
            futures = [executor.submit(self.test_single_request, endpoint) for _ in range(num_requests)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        return results
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance test results."""
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        if successful_requests:
            response_times = [r["response_time"] for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = min_response_time = max_response_time = 0
        
        return {
            "total_requests": len(results),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": len(successful_requests) / len(results) * 100,
            "avg_response_time": avg_response_time,
            "min_response_time": min_response_time,
            "max_response_time": max_response_time
        }

def main():
    """Run performance tests."""
    print("🚀 AI Voice System - Performance Test Suite")
    print("=" * 50)
    
    tester = PerformanceTester()
    
    try:
        # Test 1: Basic endpoint performance
        print("\n1. 📊 Basic Endpoint Performance Test")
        print("-" * 40)
        
        endpoints = ["/", "/health", "/status"]
        for endpoint in endpoints:
            print(f"   Testing {endpoint}...")
            result = tester.test_single_request(endpoint)
            if result["success"]:
                print(f"   ✅ {endpoint}: {result['response_time']:.3f}s")
            else:
                print(f"   ❌ {endpoint}: {result['error']}")
        
        # Test 2: Concurrent request handling
        print("\n2. 🔄 Concurrent Request Test")
        print("-" * 40)
        print("   Testing 10 concurrent health checks...")
        
        concurrent_results = tester.run_concurrent_tests("/health", 10)
        analysis = tester.analyze_results(concurrent_results)
        
        print(f"   📊 Results:")
        print(f"      Total requests: {analysis['total_requests']}")
        print(f"      Success rate: {analysis['success_rate']:.1f}%")
        print(f"      Avg response time: {analysis['avg_response_time']:.3f}s")
        print(f"      Min response time: {analysis['min_response_time']:.3f}s")
        print(f"      Max response time: {analysis['max_response_time']:.3f}s")
        
        # Test 3: Call creation performance
        print("\n3. 📞 Call Creation Performance Test")
        print("-" * 40)
        print("   Testing call creation...")
        
        call_results = []
        for i in range(5):
            phone_number = f"+123456789{i}"
            result = tester.test_call_creation(phone_number)
            call_results.append(result)
            if result["success"]:
                print(f"   ✅ Call {i+1}: {result['response_time']:.3f}s (ID: {result['call_id'][:8]}...)")
            else:
                print(f"   ❌ Call {i+1}: {result['error']}")
        
        call_analysis = tester.analyze_results(call_results)
        print(f"   📊 Call Creation Summary:")
        print(f"      Success rate: {call_analysis['success_rate']:.1f}%")
        print(f"      Avg response time: {call_analysis['avg_response_time']:.3f}s")
        
        # Test 4: System stress test
        print("\n4. 💪 System Stress Test")
        print("-" * 40)
        print("   Testing 20 concurrent status requests...")
        
        stress_results = tester.run_concurrent_tests("/status", 20)
        stress_analysis = tester.analyze_results(stress_results)
        
        print(f"   📊 Stress Test Results:")
        print(f"      Total requests: {stress_analysis['total_requests']}")
        print(f"      Success rate: {stress_analysis['success_rate']:.1f}%")
        print(f"      Avg response time: {stress_analysis['avg_response_time']:.3f}s")
        print(f"      Max response time: {stress_analysis['max_response_time']:.3f}s")
        
        # Performance Assessment
        print("\n5. 🎯 Performance Assessment")
        print("-" * 40)
        
        overall_success_rate = (analysis['success_rate'] + call_analysis['success_rate'] + stress_analysis['success_rate']) / 3
        overall_avg_response = (analysis['avg_response_time'] + call_analysis['avg_response_time'] + stress_analysis['avg_response_time']) / 3
        
        print(f"   📈 Overall Success Rate: {overall_success_rate:.1f}%")
        print(f"   ⚡ Overall Avg Response Time: {overall_avg_response:.3f}s")
        
        if overall_success_rate >= 95 and overall_avg_response < 1.0:
            print("   🟢 EXCELLENT: System performance is excellent!")
        elif overall_success_rate >= 90 and overall_avg_response < 2.0:
            print("   🟡 GOOD: System performance is good")
        else:
            print("   🔴 NEEDS ATTENTION: System performance needs optimization")
        
        print("\n✅ Performance testing completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Cannot connect to the AI Voice System.")
        print("   Make sure the system is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def main():
    """Run performance tests."""
    print("🚀 AI Voice System - Performance Test Suite")
    print("=" * 50)

    tester = PerformanceTester()

    try:
        # Test 1: Basic endpoint performance
        print("\n1. 📊 Basic Endpoint Performance Test")
        print("-" * 40)

        endpoints = ["/", "/health", "/status"]
        for endpoint in endpoints:
            print(f"   Testing {endpoint}...")
            result = tester.test_single_request(endpoint)
            if result["success"]:
                print(f"   ✅ {endpoint}: {result['response_time']:.3f}s")
            else:
                print(f"   ❌ {endpoint}: {result['error']}")

        # Test 2: Concurrent request handling
        print("\n2. 🔄 Concurrent Request Test")
        print("-" * 40)
        print("   Testing 10 concurrent health checks...")

        concurrent_results = tester.run_concurrent_tests("/health", 10)
        analysis = tester.analyze_results(concurrent_results)

        print(f"   📊 Results:")
        print(f"      Total requests: {analysis['total_requests']}")
        print(f"      Success rate: {analysis['success_rate']:.1f}%")
        print(f"      Avg response time: {analysis['avg_response_time']:.3f}s")
        print(f"      Min response time: {analysis['min_response_time']:.3f}s")
        print(f"      Max response time: {analysis['max_response_time']:.3f}s")

        # Test 3: Call creation performance
        print("\n3. 📞 Call Creation Performance Test")
        print("-" * 40)
        print("   Testing call creation...")

        call_results = []
        for i in range(5):
            phone_number = f"+123456789{i}"
            result = tester.test_call_creation(phone_number)
            call_results.append(result)
            if result["success"]:
                print(f"   ✅ Call {i+1}: {result['response_time']:.3f}s (ID: {result['call_id'][:8]}...)")
            else:
                print(f"   ❌ Call {i+1}: {result['error']}")

        call_analysis = tester.analyze_results(call_results)
        print(f"   📊 Call Creation Summary:")
        print(f"      Success rate: {call_analysis['success_rate']:.1f}%")
        print(f"      Avg response time: {call_analysis['avg_response_time']:.3f}s")

        print("\n✅ Performance testing completed!")

    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
