"""
Real-time audio streaming system with low latency buffering.

This module provides real-time audio streaming capabilities with
circular buffering, format standardization, and latency optimization.
"""

import asyncio
import threading
import time
import numpy as np
from typing import AsyncGenerator, Optional, Callable, Dict, Any, List
from dataclasses import dataclass
from collections import deque
from datetime import datetime
import logging

from ...core.interfaces import AudioChunk, AudioFormat, AudioProcessingError
from .audio_buffer import CircularAudioBuffer, BufferConfig


@dataclass
class StreamingConfig:
    """Configuration for audio streaming."""
    sample_rate: int = 16000
    channels: int = 1
    chunk_duration_ms: int = 64  # 64ms chunks for low latency
    buffer_duration_ms: int = 1000  # 1 second buffer
    format: AudioFormat = AudioFormat.PCM_16KHZ_MONO
    
    # Latency optimization
    target_latency_ms: int = 100
    max_latency_ms: int = 200
    
    # Quality settings
    enable_jitter_buffer: bool = True
    adaptive_buffering: bool = True


class RealTimeAudioStreamer:
    """
    Real-time audio streaming with adaptive buffering and latency control.
    
    Manages continuous audio streams with automatic format standardization
    and latency optimization for real-time voice processing.
    """
    
    def __init__(self, stream_id: str, config: StreamingConfig):
        """
        Initialize real-time audio streamer.
        
        Args:
            stream_id: Unique identifier for this stream
            config: Streaming configuration
        """
        self.stream_id = stream_id
        self.config = config
        
        # Calculate buffer parameters
        chunks_per_second = 1000 // config.chunk_duration_ms
        buffer_chunks = (config.buffer_duration_ms * chunks_per_second) // 1000
        
        # Create circular buffer
        buffer_config = BufferConfig(
            max_size=buffer_chunks,
            chunk_size=config.sample_rate * config.chunk_duration_ms // 1000,
            sample_rate=config.sample_rate,
            channels=config.channels
        )
        
        self.buffer = CircularAudioBuffer(buffer_config)
        
        # Streaming state
        self._active = False
        self._paused = False
        self._stream_task: Optional[asyncio.Task] = None
        
        # Latency tracking
        self._latency_samples = deque(maxlen=100)
        self._current_latency = 0.0
        
        # Event callbacks
        self.on_chunk_received: Optional[Callable[[AudioChunk], None]] = None
        self.on_latency_warning: Optional[Callable[[float], None]] = None
        self.on_buffer_warning: Optional[Callable[[str], None]] = None
        
        self._logger = logging.getLogger(f"aivoice.audio_streamer.{stream_id}")
    
    async def start_streaming(self) -> None:
        """Start real-time audio streaming."""
        if self._active:
            return
        
        self._active = True
        self._paused = False
        
        # Start streaming task
        self._stream_task = asyncio.create_task(self._streaming_loop())
        
        self._logger.info(f"Started audio streaming: {self.stream_id}")
    
    async def stop_streaming(self) -> None:
        """Stop audio streaming."""
        if not self._active:
            return
        
        self._active = False
        
        # Cancel streaming task
        if self._stream_task:
            self._stream_task.cancel()
            try:
                await self._stream_task
            except asyncio.CancelledError:
                pass
        
        # Clear buffer
        self.buffer.clear()
        
        self._logger.info(f"Stopped audio streaming: {self.stream_id}")
    
    async def pause_streaming(self) -> None:
        """Pause audio streaming without stopping."""
        self._paused = True
        self._logger.debug(f"Paused audio streaming: {self.stream_id}")
    
    async def resume_streaming(self) -> None:
        """Resume paused audio streaming."""
        self._paused = False
        self._logger.debug(f"Resumed audio streaming: {self.stream_id}")
    
    async def write_audio(self, audio_chunk: AudioChunk) -> None:
        """
        Write audio chunk to stream.
        
        Args:
            audio_chunk: Audio chunk to write
        """
        if not self._active:
            raise AudioProcessingError(f"Stream not active: {self.stream_id}")
        
        # Standardize format
        standardized_chunk = await self._standardize_chunk(audio_chunk)
        
        # Write to buffer
        if not self.buffer.write(standardized_chunk):
            self._logger.warning(f"Buffer full, dropping audio chunk: {self.stream_id}")
            if self.on_buffer_warning:
                self.on_buffer_warning("buffer_full")
        
        # Update latency tracking
        self._update_latency_tracking(standardized_chunk)
    
    async def get_audio_stream(self) -> AsyncGenerator[AudioChunk, None]:
        """
        Get async generator for audio chunks.
        
        Yields:
            Audio chunks from the stream
        """
        while self._active:
            if self._paused:
                await asyncio.sleep(0.01)
                continue
            
            chunk = self.buffer.read()
            if chunk:
                # Call callback if registered
                if self.on_chunk_received:
                    try:
                        self.on_chunk_received(chunk)
                    except Exception as e:
                        self._logger.error(f"Error in chunk callback: {e}")
                
                yield chunk
            else:
                # No data available, wait for next chunk
                await asyncio.sleep(self.config.chunk_duration_ms / 1000 / 2)
    
    async def _streaming_loop(self) -> None:
        """Main streaming loop for latency monitoring and adaptive buffering."""
        while self._active:
            try:
                # Monitor buffer health
                await self._monitor_buffer_health()
                
                # Adjust buffering if adaptive mode enabled
                if self.config.adaptive_buffering:
                    await self._adjust_buffering()
                
                # Check latency
                await self._check_latency()
                
                # Sleep for monitoring interval
                await asyncio.sleep(0.1)  # 100ms monitoring interval
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.error(f"Error in streaming loop: {e}")
                await asyncio.sleep(1)  # Wait before retrying
    
    async def _standardize_chunk(self, chunk: AudioChunk) -> AudioChunk:
        """
        Standardize audio chunk to target format.
        
        Args:
            chunk: Input audio chunk
            
        Returns:
            Standardized audio chunk
        """
        # If already in target format, return as-is
        if (chunk.format == self.config.format and 
            chunk.sample_rate == self.config.sample_rate):
            return chunk
        
        try:
            # Convert audio data to numpy array
            if chunk.format in [AudioFormat.PCM_16KHZ_MONO, AudioFormat.WAV_16KHZ_MONO]:
                audio_data = np.frombuffer(chunk.data, dtype=np.int16).astype(np.float32) / 32768.0
            else:
                raise AudioProcessingError(f"Unsupported source format: {chunk.format}")
            
            # Resample if needed
            if chunk.sample_rate != self.config.sample_rate:
                import librosa
                audio_data = librosa.resample(
                    audio_data,
                    orig_sr=chunk.sample_rate,
                    target_sr=self.config.sample_rate
                )
            
            # Convert to mono if needed
            if len(audio_data.shape) > 1 and audio_data.shape[1] > self.config.channels:
                audio_data = np.mean(audio_data, axis=1)
            
            # Convert back to int16 PCM
            audio_data = (audio_data * 32767).astype(np.int16)
            standardized_data = audio_data.tobytes()
            
            # Calculate new duration
            new_duration_ms = int(len(audio_data) / self.config.sample_rate * 1000)
            
            return AudioChunk(
                data=standardized_data,
                format=self.config.format,
                timestamp=chunk.timestamp,
                duration_ms=new_duration_ms,
                sample_rate=self.config.sample_rate
            )
            
        except Exception as e:
            self._logger.error(f"Audio standardization failed: {e}")
            raise AudioProcessingError(f"Failed to standardize audio chunk: {e}")
    
    def _update_latency_tracking(self, chunk: AudioChunk) -> None:
        """Update latency tracking with new chunk."""
        current_time = time.time()
        chunk_latency = (current_time - chunk.timestamp) * 1000  # Convert to ms
        
        self._latency_samples.append(chunk_latency)
        self._current_latency = np.mean(self._latency_samples)
    
    async def _monitor_buffer_health(self) -> None:
        """Monitor buffer health and emit warnings."""
        buffer_stats = self.buffer.get_stats()
        
        # Check for buffer issues
        if buffer_stats["overruns"] > 0:
            self._logger.warning(f"Buffer overruns detected: {buffer_stats['overruns']}")
            if self.on_buffer_warning:
                self.on_buffer_warning("overruns")
        
        if buffer_stats["underruns"] > 10:  # Allow some underruns
            self._logger.warning(f"Excessive buffer underruns: {buffer_stats['underruns']}")
            if self.on_buffer_warning:
                self.on_buffer_warning("underruns")
    
    async def _adjust_buffering(self) -> None:
        """Adjust buffering parameters based on current conditions."""
        # Simple adaptive buffering based on latency
        if self._current_latency > self.config.max_latency_ms:
            # High latency - reduce buffer size
            if self.buffer.config.max_size > 10:
                self.buffer.config.max_size = max(10, self.buffer.config.max_size - 1)
                self._logger.debug(f"Reduced buffer size to {self.buffer.config.max_size}")
        
        elif self._current_latency < self.config.target_latency_ms * 0.5:
            # Very low latency - can increase buffer for stability
            if self.buffer.config.max_size < 100:
                self.buffer.config.max_size = min(100, self.buffer.config.max_size + 1)
                self._logger.debug(f"Increased buffer size to {self.buffer.config.max_size}")
    
    async def _check_latency(self) -> None:
        """Check current latency and emit warnings if too high."""
        if self._current_latency > self.config.max_latency_ms:
            self._logger.warning(f"High latency detected: {self._current_latency:.2f}ms")
            if self.on_latency_warning:
                self.on_latency_warning(self._current_latency)
    
    def get_stream_stats(self) -> Dict[str, Any]:
        """
        Get streaming statistics.
        
        Returns:
            Dictionary with streaming statistics
        """
        buffer_stats = self.buffer.get_stats()
        
        return {
            "stream_id": self.stream_id,
            "active": self._active,
            "paused": self._paused,
            "current_latency_ms": self._current_latency,
            "target_latency_ms": self.config.target_latency_ms,
            "buffer_fill_level": buffer_stats["fill_level"],
            "buffer_size": buffer_stats["size"],
            "buffer_overruns": buffer_stats["overruns"],
            "buffer_underruns": buffer_stats["underruns"],
            "sample_rate": self.config.sample_rate,
            "channels": self.config.channels
        }
    
    def is_healthy(self) -> bool:
        """
        Check if stream is healthy.
        
        Returns:
            True if stream is healthy, False otherwise
        """
        if not self._active:
            return False
        
        # Check latency
        if self._current_latency > self.config.max_latency_ms:
            return False
        
        # Check buffer health
        buffer_stats = self.buffer.get_stats()
        if buffer_stats["overruns"] > 5 or buffer_stats["underruns"] > 20:
            return False
        
        return True


class AudioStreamManager:
    """
    Manager for multiple real-time audio streams.
    
    Coordinates multiple audio streams and provides centralized
    monitoring and control.
    """
    
    def __init__(self, default_config: Optional[StreamingConfig] = None):
        """
        Initialize audio stream manager.
        
        Args:
            default_config: Default configuration for new streams
        """
        self.default_config = default_config or StreamingConfig()
        self.streams: Dict[str, RealTimeAudioStreamer] = {}
        self._lock = asyncio.Lock()
        
        # Global monitoring
        self._monitor_task: Optional[asyncio.Task] = None
        self._monitoring_active = False
        
        self._logger = logging.getLogger("aivoice.audio_stream_manager")
    
    async def create_stream(self, 
                          stream_id: str, 
                          config: Optional[StreamingConfig] = None) -> RealTimeAudioStreamer:
        """
        Create a new audio stream.
        
        Args:
            stream_id: Unique stream identifier
            config: Optional stream configuration
            
        Returns:
            Created audio streamer
        """
        async with self._lock:
            if stream_id in self.streams:
                raise AudioProcessingError(f"Stream already exists: {stream_id}")
            
            stream_config = config or self.default_config
            streamer = RealTimeAudioStreamer(stream_id, stream_config)
            
            self.streams[stream_id] = streamer
            
            # Start monitoring if this is the first stream
            if len(self.streams) == 1 and not self._monitoring_active:
                await self._start_monitoring()
            
            self._logger.info(f"Created audio stream: {stream_id}")
            return streamer
    
    async def get_stream(self, stream_id: str) -> Optional[RealTimeAudioStreamer]:
        """
        Get existing audio stream.
        
        Args:
            stream_id: Stream identifier
            
        Returns:
            Audio streamer or None if not found
        """
        async with self._lock:
            return self.streams.get(stream_id)
    
    async def remove_stream(self, stream_id: str) -> bool:
        """
        Remove audio stream.
        
        Args:
            stream_id: Stream identifier
            
        Returns:
            True if removed, False if not found
        """
        async with self._lock:
            if stream_id not in self.streams:
                return False
            
            # Stop the stream
            streamer = self.streams[stream_id]
            await streamer.stop_streaming()
            
            # Remove from manager
            del self.streams[stream_id]
            
            # Stop monitoring if no streams left
            if len(self.streams) == 0 and self._monitoring_active:
                await self._stop_monitoring()
            
            self._logger.info(f"Removed audio stream: {stream_id}")
            return True
    
    async def start_all_streams(self) -> None:
        """Start all managed streams."""
        async with self._lock:
            for streamer in self.streams.values():
                await streamer.start_streaming()
        
        self._logger.info(f"Started {len(self.streams)} audio streams")
    
    async def stop_all_streams(self) -> None:
        """Stop all managed streams."""
        async with self._lock:
            for streamer in self.streams.values():
                await streamer.stop_streaming()
        
        await self._stop_monitoring()
        self._logger.info(f"Stopped {len(self.streams)} audio streams")
    
    async def _start_monitoring(self) -> None:
        """Start global stream monitoring."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitor_task = asyncio.create_task(self._monitoring_loop())
        self._logger.info("Started audio stream monitoring")
    
    async def _stop_monitoring(self) -> None:
        """Stop global stream monitoring."""
        if not self._monitoring_active:
            return
        
        self._monitoring_active = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        self._logger.info("Stopped audio stream monitoring")
    
    async def _monitoring_loop(self) -> None:
        """Global monitoring loop for all streams."""
        while self._monitoring_active:
            try:
                async with self._lock:
                    # Check health of all streams
                    unhealthy_streams = []
                    
                    for stream_id, streamer in self.streams.items():
                        if not streamer.is_healthy():
                            unhealthy_streams.append(stream_id)
                    
                    if unhealthy_streams:
                        self._logger.warning(f"Unhealthy streams detected: {unhealthy_streams}")
                
                # Sleep for monitoring interval
                await asyncio.sleep(5)  # Monitor every 5 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(1)
    
    def get_all_stream_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all streams.
        
        Returns:
            Dictionary with stream statistics
        """
        return {
            stream_id: streamer.get_stream_stats()
            for stream_id, streamer in self.streams.items()
        }
    
    def get_global_stats(self) -> Dict[str, Any]:
        """
        Get global streaming statistics.
        
        Returns:
            Dictionary with global statistics
        """
        total_streams = len(self.streams)
        active_streams = sum(1 for s in self.streams.values() if s._active)
        healthy_streams = sum(1 for s in self.streams.values() if s.is_healthy())
        
        # Calculate average latency
        all_latencies = []
        for streamer in self.streams.values():
            if streamer._latency_samples:
                all_latencies.extend(streamer._latency_samples)
        
        avg_latency = np.mean(all_latencies) if all_latencies else 0.0
        
        return {
            "total_streams": total_streams,
            "active_streams": active_streams,
            "healthy_streams": healthy_streams,
            "average_latency_ms": avg_latency,
            "monitoring_active": self._monitoring_active
        }
    
    async def cleanup(self) -> None:
        """Cleanup all streams and resources."""
        await self.stop_all_streams()
        
        async with self._lock:
            self.streams.clear()
        
        self._logger.info("Audio stream manager cleanup completed")


class JitterBuffer:
    """
    Jitter buffer for handling network jitter in audio streams.
    
    Provides adaptive buffering to smooth out timing variations
    in received audio packets.
    """
    
    def __init__(self, 
                 target_delay_ms: int = 50,
                 max_delay_ms: int = 200,
                 adaptive: bool = True):
        """
        Initialize jitter buffer.
        
        Args:
            target_delay_ms: Target buffering delay
            max_delay_ms: Maximum buffering delay
            adaptive: Enable adaptive delay adjustment
        """
        self.target_delay_ms = target_delay_ms
        self.max_delay_ms = max_delay_ms
        self.adaptive = adaptive
        
        self._buffer: List[Tuple[float, AudioChunk]] = []  # (arrival_time, chunk)
        self._lock = threading.Lock()
        
        # Adaptive parameters
        self._delay_samples = deque(maxlen=100)
        self._current_delay = target_delay_ms
        
        self._logger = logging.getLogger("aivoice.jitter_buffer")
    
    def put(self, chunk: AudioChunk) -> None:
        """
        Add audio chunk to jitter buffer.
        
        Args:
            chunk: Audio chunk to buffer
        """
        arrival_time = time.time()
        
        with self._lock:
            # Insert in timestamp order
            inserted = False
            for i, (_, buffered_chunk) in enumerate(self._buffer):
                if chunk.timestamp < buffered_chunk.timestamp:
                    self._buffer.insert(i, (arrival_time, chunk))
                    inserted = True
                    break
            
            if not inserted:
                self._buffer.append((arrival_time, chunk))
            
            # Update delay tracking for adaptive buffering
            if self.adaptive:
                self._update_delay_tracking(arrival_time, chunk.timestamp)
    
    def get(self) -> Optional[AudioChunk]:
        """
        Get audio chunk from jitter buffer.
        
        Returns:
            Audio chunk if available and ready, None otherwise
        """
        current_time = time.time()
        
        with self._lock:
            if not self._buffer:
                return None
            
            # Check if oldest chunk is ready to be played
            arrival_time, chunk = self._buffer[0]
            buffer_delay = (current_time - arrival_time) * 1000  # Convert to ms
            
            if buffer_delay >= self._current_delay:
                return self._buffer.pop(0)[1]
            
            return None
    
    def _update_delay_tracking(self, arrival_time: float, chunk_timestamp: float) -> None:
        """Update adaptive delay tracking."""
        # Calculate network delay
        network_delay = (arrival_time - chunk_timestamp) * 1000
        self._delay_samples.append(network_delay)
        
        # Adjust current delay based on network conditions
        if len(self._delay_samples) >= 10:
            mean_delay = np.mean(self._delay_samples)
            std_delay = np.std(self._delay_samples)
            
            # Set delay to mean + 2*std to handle 95% of packets
            adaptive_delay = mean_delay + 2 * std_delay
            adaptive_delay = max(self.target_delay_ms, min(self.max_delay_ms, adaptive_delay))
            
            if abs(adaptive_delay - self._current_delay) > 10:  # Only adjust if significant change
                self._current_delay = adaptive_delay
                self._logger.debug(f"Adjusted jitter buffer delay to {self._current_delay:.1f}ms")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get jitter buffer statistics."""
        with self._lock:
            return {
                "buffer_size": len(self._buffer),
                "current_delay_ms": self._current_delay,
                "target_delay_ms": self.target_delay_ms,
                "adaptive": self.adaptive,
                "delay_samples": len(self._delay_samples)
            }