"""
Performance Benchmarks

This module provides performance benchmarking for individual components
and system-wide performance characteristics of the AI voice customer service system.
"""

import asyncio
import pytest
import time
import statistics
import gc
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import sys
import os
from contextlib import asynccontextmanager

# Optional psutil import
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.services.prompt_manager import PromptManager
from src.services.conversation_context import ConversationManager
from src.audio.audio_pipeline import AudioPipeline
from src.services.component_coordinator import ComponentCoordinator
from tests.test_utils import MockConfigManager, MockLogger, AudioTestData, TextTestData


@dataclass
class BenchmarkResult:
    """Benchmark test result."""
    benchmark_name: str
    component_name: str
    operation_name: str
    iterations: int
    total_time_seconds: float
    memory_usage_mb: float
    cpu_usage_percent: float
    individual_times: List[float] = field(default_factory=list)
    
    @property
    def average_time_ms(self) -> float:
        """Average execution time in milliseconds."""
        return (self.total_time_seconds / self.iterations) * 1000 if self.iterations > 0 else 0
    
    @property
    def operations_per_second(self) -> float:
        """Operations per second."""
        return self.iterations / self.total_time_seconds if self.total_time_seconds > 0 else 0
    
    @property
    def p95_time_ms(self) -> float:
        """95th percentile execution time in milliseconds."""
        if not self.individual_times:
            return 0
        sorted_times = sorted(self.individual_times)
        index = int(0.95 * len(sorted_times))
        return sorted_times[index] * 1000 if index < len(sorted_times) else sorted_times[-1] * 1000


class PerformanceBenchmark:
    """Performance benchmark runner."""
    
    def __init__(self, config_manager=None, logger=None):
        self.config_manager = config_manager or MockConfigManager()
        self.logger = logger or MockLogger()
        
        # Benchmark configuration
        self.default_iterations = 100
        self.warmup_iterations = 10
        self.memory_measurement_interval = 0.1
        
        # Results storage
        self.benchmark_results: List[BenchmarkResult] = []
        self.baseline_results: Dict[str, BenchmarkResult] = {}
    
    @asynccontextmanager
    async def benchmark_context(
        self,
        benchmark_name: str,
        component_name: str,
        operation_name: str,
        iterations: int = None
    ):
        """Context manager for benchmarking operations."""
        iterations = iterations or self.default_iterations
        
        # Prepare for benchmarking
        gc.collect()  # Clean up memory

        # Measure initial state (if psutil available)
        initial_memory = 0
        initial_cpu = 0
        if PSUTIL_AVAILABLE:
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            initial_cpu = process.cpu_percent()
        
        # Start timing
        start_time = time.perf_counter()
        individual_times = []
        
        benchmark_data = {
            "individual_times": individual_times,
            "start_time": start_time
        }
        
        try:
            yield benchmark_data
        finally:
            # End timing
            end_time = time.perf_counter()
            total_time = end_time - start_time
            
            # Measure final state (if psutil available)
            final_memory = 0
            final_cpu = 0
            if PSUTIL_AVAILABLE:
                process = psutil.Process()
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                final_cpu = process.cpu_percent()
            
            # Create result
            result = BenchmarkResult(
                benchmark_name=benchmark_name,
                component_name=component_name,
                operation_name=operation_name,
                iterations=iterations,
                total_time_seconds=total_time,
                memory_usage_mb=final_memory - initial_memory,
                cpu_usage_percent=(initial_cpu + final_cpu) / 2,
                individual_times=individual_times
            )
            
            self.benchmark_results.append(result)
            self.logger.info(f"Benchmark completed: {benchmark_name} - {result.average_time_ms:.2f}ms avg")
    
    async def benchmark_prompt_generation(self, iterations: int = None) -> BenchmarkResult:
        """Benchmark prompt generation performance."""
        iterations = iterations or self.default_iterations
        
        # Initialize prompt manager
        prompt_manager = PromptManager(self.config_manager, self.logger)
        await prompt_manager.initialize()
        await prompt_manager.start()
        
        try:
            async with self.benchmark_context(
                "prompt_generation",
                "PromptManager",
                "generate_prompt",
                iterations
            ) as ctx:
                
                # Warmup
                for _ in range(self.warmup_iterations):
                    await prompt_manager.generate_prompt(
                        "greeting",
                        {"user_name": "测试用户"}
                    )
                
                # Actual benchmark
                for i in range(iterations):
                    start = time.perf_counter()
                    
                    await prompt_manager.generate_prompt(
                        "greeting",
                        {"user_name": f"用户{i}"}
                    )
                    
                    end = time.perf_counter()
                    ctx["individual_times"].append(end - start)
        
        finally:
            await prompt_manager.stop()
            await prompt_manager.cleanup()
        
        return self.benchmark_results[-1]
    
    async def benchmark_conversation_processing(self, iterations: int = None) -> BenchmarkResult:
        """Benchmark conversation processing performance."""
        iterations = iterations or self.default_iterations
        
        # Initialize conversation manager
        conversation_manager = ConversationManager(self.config_manager, self.logger)
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        try:
            async with self.benchmark_context(
                "conversation_processing",
                "ConversationManager",
                "process_message",
                iterations
            ) as ctx:
                
                # Create test conversation
                conv_id = await conversation_manager.create_conversation(
                    user_id="benchmark_user",
                    session_id="benchmark_session"
                )
                
                # Warmup
                for _ in range(self.warmup_iterations):
                    await conversation_manager.add_message(
                        conv_id,
                        "user",
                        "测试消息"
                    )
                
                # Actual benchmark
                for i in range(iterations):
                    start = time.perf_counter()
                    
                    await conversation_manager.add_message(
                        conv_id,
                        "user",
                        f"基准测试消息 {i}"
                    )
                    
                    end = time.perf_counter()
                    ctx["individual_times"].append(end - start)
        
        finally:
            await conversation_manager.stop()
            await conversation_manager.cleanup()
        
        return self.benchmark_results[-1]
    
    async def benchmark_audio_processing(self, iterations: int = None) -> BenchmarkResult:
        """Benchmark audio processing performance."""
        iterations = iterations or self.default_iterations
        
        # Initialize audio pipeline
        audio_pipeline = AudioPipeline(self.config_manager, self.logger)
        await audio_pipeline.initialize()
        await audio_pipeline.start()
        
        # Generate test audio
        test_audio = AudioTestData.generate_sine_wave(duration=1.0)
        
        try:
            async with self.benchmark_context(
                "audio_processing",
                "AudioPipeline",
                "process_audio",
                iterations
            ) as ctx:
                
                # Warmup
                for _ in range(self.warmup_iterations):
                    # Mock audio processing
                    await asyncio.sleep(0.001)  # Simulate processing time
                
                # Actual benchmark
                for i in range(iterations):
                    start = time.perf_counter()
                    
                    # Simulate audio processing
                    await asyncio.sleep(0.001)  # Simulate processing time
                    
                    end = time.perf_counter()
                    ctx["individual_times"].append(end - start)
        
        finally:
            await audio_pipeline.stop()
            await audio_pipeline.cleanup()
        
        return self.benchmark_results[-1]
    
    async def benchmark_component_coordination(self, iterations: int = None) -> BenchmarkResult:
        """Benchmark component coordination performance."""
        iterations = iterations or self.default_iterations
        
        # Initialize component coordinator
        coordinator = ComponentCoordinator(self.config_manager, self.logger)
        await coordinator.initialize()
        await coordinator.start()
        
        try:
            async with self.benchmark_context(
                "component_coordination",
                "ComponentCoordinator",
                "publish_event",
                iterations
            ) as ctx:
                
                # Register test component
                coordinator.register_component("test_component", "test")
                
                # Warmup
                for _ in range(self.warmup_iterations):
                    await coordinator.publish_event_simple(
                        event_type="audio_data",
                        source_component="test_component",
                        data={"test": "data"}
                    )
                
                # Actual benchmark
                for i in range(iterations):
                    start = time.perf_counter()
                    
                    await coordinator.publish_event_simple(
                        event_type="audio_data",
                        source_component="test_component",
                        data={"iteration": i}
                    )
                    
                    end = time.perf_counter()
                    ctx["individual_times"].append(end - start)
        
        finally:
            await coordinator.stop()
            await coordinator.cleanup()
        
        return self.benchmark_results[-1]
    
    async def run_comprehensive_benchmark(self) -> Dict[str, BenchmarkResult]:
        """Run comprehensive benchmark suite."""
        self.logger.info("Starting comprehensive performance benchmark")
        
        benchmarks = {
            "prompt_generation": self.benchmark_prompt_generation,
            "conversation_processing": self.benchmark_conversation_processing,
            "audio_processing": self.benchmark_audio_processing,
            "component_coordination": self.benchmark_component_coordination
        }
        
        results = {}
        
        for benchmark_name, benchmark_func in benchmarks.items():
            self.logger.info(f"Running benchmark: {benchmark_name}")
            
            try:
                result = await benchmark_func()
                results[benchmark_name] = result
                
                self.logger.info(
                    f"Benchmark {benchmark_name} completed: "
                    f"{result.average_time_ms:.2f}ms avg, "
                    f"{result.operations_per_second:.2f} ops/sec"
                )
                
            except Exception as e:
                self.logger.error(f"Benchmark {benchmark_name} failed: {e}")
        
        self.logger.info("Comprehensive benchmark completed")
        return results
    
    def save_baseline(self, benchmark_name: str, result: BenchmarkResult):
        """Save benchmark result as baseline for comparison."""
        self.baseline_results[benchmark_name] = result
        self.logger.info(f"Saved baseline for {benchmark_name}")
    
    def compare_with_baseline(self, benchmark_name: str, current_result: BenchmarkResult) -> Dict[str, Any]:
        """Compare current result with baseline."""
        if benchmark_name not in self.baseline_results:
            return {"error": f"No baseline found for {benchmark_name}"}
        
        baseline = self.baseline_results[benchmark_name]
        
        # Calculate performance changes
        time_change = (
            (current_result.average_time_ms - baseline.average_time_ms) /
            baseline.average_time_ms * 100
        ) if baseline.average_time_ms > 0 else 0
        
        throughput_change = (
            (current_result.operations_per_second - baseline.operations_per_second) /
            baseline.operations_per_second * 100
        ) if baseline.operations_per_second > 0 else 0
        
        memory_change = current_result.memory_usage_mb - baseline.memory_usage_mb
        
        return {
            "baseline_avg_time_ms": baseline.average_time_ms,
            "current_avg_time_ms": current_result.average_time_ms,
            "time_change_percent": time_change,
            "baseline_ops_per_sec": baseline.operations_per_second,
            "current_ops_per_sec": current_result.operations_per_second,
            "throughput_change_percent": throughput_change,
            "memory_change_mb": memory_change,
            "performance_regression": time_change > 10 or throughput_change < -10,
            "significant_improvement": time_change < -10 or throughput_change > 10
        }
    
    def generate_benchmark_report(self) -> Dict[str, Any]:
        """Generate comprehensive benchmark report."""
        if not self.benchmark_results:
            return {"error": "No benchmark results available"}
        
        # Group results by component
        by_component = {}
        for result in self.benchmark_results:
            if result.component_name not in by_component:
                by_component[result.component_name] = []
            by_component[result.component_name].append(result)
        
        # Generate summary statistics
        all_times = [r.average_time_ms for r in self.benchmark_results]
        all_throughputs = [r.operations_per_second for r in self.benchmark_results]
        
        return {
            "summary": {
                "total_benchmarks": len(self.benchmark_results),
                "average_response_time_ms": statistics.mean(all_times),
                "median_response_time_ms": statistics.median(all_times),
                "average_throughput_ops_sec": statistics.mean(all_throughputs),
                "total_operations": sum(r.iterations for r in self.benchmark_results)
            },
            "by_component": {
                component: {
                    "benchmarks": len(results),
                    "average_time_ms": statistics.mean([r.average_time_ms for r in results]),
                    "average_throughput": statistics.mean([r.operations_per_second for r in results]),
                    "total_memory_usage_mb": sum(r.memory_usage_mb for r in results)
                }
                for component, results in by_component.items()
            },
            "detailed_results": [
                {
                    "benchmark_name": r.benchmark_name,
                    "component_name": r.component_name,
                    "operation_name": r.operation_name,
                    "iterations": r.iterations,
                    "average_time_ms": r.average_time_ms,
                    "p95_time_ms": r.p95_time_ms,
                    "operations_per_second": r.operations_per_second,
                    "memory_usage_mb": r.memory_usage_mb,
                    "cpu_usage_percent": r.cpu_usage_percent
                }
                for r in self.benchmark_results
            ]
        }
