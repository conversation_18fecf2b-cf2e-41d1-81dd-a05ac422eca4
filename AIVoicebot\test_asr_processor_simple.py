"""Simple test runner for ASR processor"""

import sys
import numpy as np
from pathlib import Path

sys.path.append("src")

from components.asr.asr_processor import (
    ASRProcessor,
    TranscriptionBuffer,
    TranscriptionResult,
    ASRProcessorConfig,
    TranscriptionStatus,
    create_asr_processor,
    process_audio_batch
)


def test_transcription_result():
    """Test TranscriptionResult creation"""
    print("Testing TranscriptionResult...")
    
    result = TranscriptionResult(
        text="Hello world",
        confidence=0.8,
        status=TranscriptionStatus.FINAL,
        timestamp=1234567890.0,
        duration=2.5
    )
    
    assert result.text == "Hello world"
    assert result.confidence == 0.8
    assert result.is_final is True
    assert result.is_partial is False
    assert result.has_error is False
    assert result.duration == 2.5
    
    print("✓ TranscriptionResult test passed")


def test_asr_processor_config():
    """Test ASRProcessorConfig"""
    print("Testing ASRProcessorConfig...")
    
    config = ASRProcessorConfig()
    assert config.sample_rate == 16000
    assert config.min_confidence == 0.3
    assert config.max_retries == 3
    
    custom_config = ASRProcessorConfig(
        sample_rate=8000,
        min_confidence=0.5,
        max_retries=5
    )
    assert custom_config.sample_rate == 8000
    assert custom_config.min_confidence == 0.5
    assert custom_config.max_retries == 5
    
    print("✓ ASRProcessorConfig test passed")


def test_transcription_buffer():
    """Test TranscriptionBuffer functionality"""
    print("Testing TranscriptionBuffer...")
    
    config = ASRProcessorConfig(max_buffer_size=3)
    buffer = TranscriptionBuffer(config)
    
    assert len(buffer.results) == 0
    assert buffer.current_partial is None
    
    # Add final result
    final_result = TranscriptionResult(
        "Final result", 0.8, TranscriptionStatus.FINAL, 0.0
    )
    success = buffer.add_result(final_result)
    assert success is True
    assert len(buffer.results) == 1
    
    # Add partial result
    partial_result = TranscriptionResult(
        "Partial result", 0.6, TranscriptionStatus.PARTIAL, 1.0
    )
    buffer.add_result(partial_result)
    assert buffer.current_partial == partial_result
    
    # Test latest result
    latest = buffer.get_latest_result()
    assert latest == partial_result  # Partial should be latest
    
    # Test buffer stats
    stats = buffer.get_buffer_stats()
    assert stats["total_results"] == 1
    assert stats["has_partial"] is True
    
    print("✓ TranscriptionBuffer test passed")


def test_asr_processor():
    """Test ASRProcessor basic functionality"""
    print("Testing ASRProcessor...")
    
    processor = create_asr_processor()
    assert processor.config.sample_rate == 16000
    assert processor.total_processed == 0
    
    # Test audio preprocessing
    valid_audio = np.random.randn(16000).astype(np.float32) * 0.5
    processed = processor.preprocess_audio(valid_audio)
    assert processed is not None
    assert processed.dtype == np.float32
    
    # Test with too short audio
    short_audio = np.random.randn(800).astype(np.float32)
    processed_short = processor.preprocess_audio(short_audio)
    assert processed_short is None  # Should reject
    
    # Test audio processing
    result = processor.process_audio(valid_audio)
    assert isinstance(result, TranscriptionResult)
    assert result.text != ""  # Mock should return text
    
    print("✓ ASRProcessor test passed")


def test_batch_processing():
    """Test batch processing functionality"""
    print("Testing batch processing...")
    
    processor = create_asr_processor()
    
    # Create test audio segments
    audio_segments = [
        np.random.randn(16000).astype(np.float32) * 0.5,
        np.random.randn(8000).astype(np.float32) * 0.3,
        np.random.randn(24000).astype(np.float32) * 0.4,
    ]
    
    results = process_audio_batch(audio_segments, processor)
    
    assert len(results) == len(audio_segments)
    for result in results:
        assert isinstance(result, TranscriptionResult)
    
    # Check processor stats
    stats = processor.get_processing_stats()
    assert stats["total_processed"] == len(audio_segments)
    
    print("✓ Batch processing test passed")


def test_error_handling():
    """Test error handling"""
    print("Testing error handling...")
    
    processor = create_asr_processor()
    
    # Test with None input
    try:
        result = processor.process_audio(None)
        assert result.has_error
    except:
        pass  # Expected to handle gracefully
    
    # Test with empty array
    empty_audio = np.array([])
    result = processor.process_audio(empty_audio)
    assert result.has_error
    
    print("✓ Error handling test passed")


def test_callbacks():
    """Test result callbacks"""
    print("Testing callbacks...")
    
    processor = create_asr_processor()
    
    # Add callback
    callback_results = []
    processor.add_result_callback(lambda r: callback_results.append(r))
    
    # Process audio
    audio_data = np.random.randn(16000).astype(np.float32) * 0.5
    result = processor.process_and_buffer(audio_data)
    
    # Check callback was called
    assert len(callback_results) == 1
    assert callback_results[0] == result
    
    print("✓ Callbacks test passed")


def main():
    """Run all tests"""
    print("Running ASR Processor Tests")
    print("=" * 40)
    
    try:
        test_transcription_result()
        test_asr_processor_config()
        test_transcription_buffer()
        test_asr_processor()
        test_batch_processing()
        test_error_handling()
        test_callbacks()
        
        print("\n" + "=" * 40)
        print("All tests passed successfully!")
        
    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)