"""Simple Audio Components"""

import numpy as np
from dataclasses import dataclass
from enum import Enum
import threading
import queue
import logging

logger = logging.getLogger(__name__)


class AudioFormat(Enum):
    PCM_16 = "pcm_16"
    FLOAT32 = "float32"


@dataclass
class AudioConfig:
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    buffer_duration: float = 5.0


class AudioBuffer:
    def __init__(self, config: AudioConfig):
        self.config = config
        self.buffer_size = int(config.sample_rate * config.buffer_duration)
        self.buffer = np.zeros(self.buffer_size, dtype=np.float32)
        self.write_pos = 0
        self.read_pos = 0
        self.lock = threading.Lock()
    
    def write(self, data: np.ndarray) -> bool:
        with self.lock:
            if len(data) > self.buffer_size - self._get_data_length():
                return False
            
            end_pos = (self.write_pos + len(data)) % self.buffer_size
            if self.write_pos + len(data) <= self.buffer_size:
                self.buffer[self.write_pos:self.write_pos + len(data)] = data
            else:
                split = self.buffer_size - self.write_pos
                self.buffer[self.write_pos:] = data[:split]
                self.buffer[:len(data) - split] = data[split:]
            
            self.write_pos = end_pos
            return True
    
    def read(self, length: int):
        with self.lock:
            if self._get_data_length() < length:
                return None
            
            if self.read_pos + length <= self.buffer_size:
                data = self.buffer[self.read_pos:self.read_pos + length].copy()
            else:
                split = self.buffer_size - self.read_pos
                data = np.concatenate([
                    self.buffer[self.read_pos:],
                    self.buffer[:length - split]
                ])
            
            self.read_pos = (self.read_pos + length) % self.buffer_size
            return data
    
    def _get_data_length(self):
        if self.write_pos >= self.read_pos:
            return self.write_pos - self.read_pos
        return self.buffer_size - self.read_pos + self.write_pos
    
    def get_available_data(self):
        with self.lock:
            return self._get_data_length()


class AudioProcessor:
    def __init__(self, config: AudioConfig):
        self.config = config
    
    def normalize_audio(self, data: np.ndarray):
        if len(data) == 0:
            return data
        max_val = np.max(np.abs(data))
        return data / max_val if max_val > 0 else data


class AudioPipeline:
    def __init__(self, config: AudioConfig):
        self.config = config
        self.buffer = AudioBuffer(config)
        self.processor = AudioProcessor(config)
        self.processors = []
        self.running = False
        self.input_queue = queue.Queue()
        self.output_queue = queue.Queue()
    
    def add_processor(self, processor):
        self.processors.append(processor)
    
    def start(self):
        self.running = True
        self.thread = threading.Thread(target=self._process_loop, daemon=True)
        self.thread.start()
    
    def stop(self):
        self.running = False
    
    def write_audio(self, data: np.ndarray):
        try:
            self.input_queue.put(data, timeout=0.1)
            return True
        except queue.Full:
            return False
    
    def read_audio(self, timeout=0.1):
        try:
            return self.output_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def _process_loop(self):
        while self.running:
            try:
                data = self.input_queue.get(timeout=0.1)
                for proc in self.processors:
                    data = proc(data)
                self.buffer.write(data)
                self.output_queue.put(data)
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Processing error: {e}")
    
    def get_buffer_status(self):
        return {
            "available_samples": self.buffer.get_available_data(),
            "buffer_size": self.buffer.buffer_size
        }


def create_audio_pipeline(sample_rate=16000, channels=1, chunk_size=1024, buffer_duration=5.0):
    config = AudioConfig(sample_rate, channels, chunk_size, buffer_duration)
    return AudioPipeline(config)


def create_simple_processor(normalize=True):
    def processor(data):
        if normalize:
            max_val = np.max(np.abs(data))
            return data / max_val if max_val > 0 else data
        return data
    return processor