"""
Comprehensive tests for ConversationContext.
"""

import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.conversation_context import (
    ConversationContext, ConversationManager, IntentClassifier,
    ConversationState, Intent, ContextVariable
)
from tests.test_utils import (
    MockConfigManager, MockLogger, TextTestData, MockExternalServices,
    create_test_conversation_context
)


class TestConversationContextComprehensive:
    """Comprehensive tests for ConversationContext."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return MockConfigManager({
            "conversation.max_history_length": 50,
            "conversation.context_window_size": 10,
            "conversation.intent_confidence_threshold": 0.7,
            "conversation.auto_save_interval": 30,
            "conversation.session_timeout": 1800
        })
    
    @pytest.fixture
    def mock_logger(self):
        """Create mock logger."""
        return MockLogger()
    
    @pytest.fixture
    def conversation_manager(self, mock_config_manager, mock_logger):
        """Create ConversationManager instance."""
        return ConversationManager(mock_config_manager, mock_logger)
    
    @pytest.fixture
    def mock_llm_service(self):
        """Create mock LLM service."""
        return MockExternalServices.MockLLMService()
    
    @pytest.mark.asyncio
    async def test_conversation_manager_lifecycle(self, conversation_manager):
        """Test ConversationManager lifecycle."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        assert conversation_manager.is_initialized
        assert conversation_manager.is_running
        
        await conversation_manager.stop()
        await conversation_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_conversation_creation(self, conversation_manager):
        """Test conversation creation."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create new conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session",
            language="zh-CN"
        )
        
        assert conversation_id is not None
        
        # Get conversation
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert conversation is not None
        assert conversation.user_id == "test_user"
        assert conversation.session_id == "test_session"
        assert conversation.language == "zh-CN"
        assert conversation.state == ConversationState.ACTIVE
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_message_handling(self, conversation_manager):
        """Test message handling and history tracking."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Add messages
        messages = [
            {"role": "user", "content": "你好"},
            {"role": "assistant", "content": "您好！有什么可以帮助您的吗？"},
            {"role": "user", "content": "我想查询账户余额"},
            {"role": "assistant", "content": "好的，我来帮您查询账户余额。"}
        ]
        
        for message in messages:
            await conversation_manager.add_message(
                conversation_id,
                message["role"],
                message["content"]
            )
        
        # Get conversation and check history
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert len(conversation.message_history) == 4
        
        # Check message order
        for i, message in enumerate(messages):
            assert conversation.message_history[i]["role"] == message["role"]
            assert conversation.message_history[i]["content"] == message["content"]
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_intent_classification(self, conversation_manager):
        """Test intent classification."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Test different user inputs
        test_inputs = [
            ("我想查询账户余额", "balance_inquiry"),
            ("我要投诉服务", "complaint"),
            ("转账什么时候到账", "transfer_inquiry"),
            ("忘记密码了", "password_reset")
        ]
        
        for user_input, expected_intent in test_inputs:
            # Add user message
            await conversation_manager.add_message(
                conversation_id,
                "user",
                user_input
            )
            
            # Classify intent
            intent = await conversation_manager.classify_intent(
                conversation_id,
                user_input
            )
            
            assert intent is not None
            assert intent.name in ["balance_inquiry", "complaint", "transfer_inquiry", "password_reset", "general"]
            assert intent.confidence >= 0.0
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_context_variables(self, conversation_manager):
        """Test context variable management."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Set context variables
        variables = {
            "user_name": "张三",
            "account_type": "储蓄账户",
            "last_login": "2024-01-15",
            "preferred_language": "zh-CN"
        }
        
        for key, value in variables.items():
            await conversation_manager.set_context_variable(
                conversation_id,
                key,
                value
            )
        
        # Get context variables
        conversation = await conversation_manager.get_conversation(conversation_id)
        for key, expected_value in variables.items():
            assert key in conversation.context_variables
            assert conversation.context_variables[key].value == expected_value
        
        # Update context variable
        await conversation_manager.set_context_variable(
            conversation_id,
            "user_name",
            "李四"
        )
        
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert conversation.context_variables["user_name"].value == "李四"
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_conversation_state_management(self, conversation_manager):
        """Test conversation state transitions."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Check initial state
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert conversation.state == ConversationState.ACTIVE
        
        # Pause conversation
        await conversation_manager.update_conversation_state(
            conversation_id,
            ConversationState.PAUSED
        )
        
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert conversation.state == ConversationState.PAUSED
        
        # Resume conversation
        await conversation_manager.update_conversation_state(
            conversation_id,
            ConversationState.ACTIVE
        )
        
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert conversation.state == ConversationState.ACTIVE
        
        # End conversation
        await conversation_manager.end_conversation(conversation_id)
        
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert conversation.state == ConversationState.ENDED
        assert conversation.end_time is not None
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_conversation_flow_control(self, conversation_manager):
        """Test conversation flow control."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Simulate a multi-step conversation flow
        steps = [
            {
                "user_input": "我想转账",
                "expected_flow_step": "transfer_initiation",
                "context_updates": {"current_operation": "transfer"}
            },
            {
                "user_input": "转给张三",
                "expected_flow_step": "recipient_confirmation",
                "context_updates": {"recipient_name": "张三"}
            },
            {
                "user_input": "转1000元",
                "expected_flow_step": "amount_confirmation",
                "context_updates": {"transfer_amount": "1000"}
            },
            {
                "user_input": "确认转账",
                "expected_flow_step": "transfer_execution",
                "context_updates": {"transfer_confirmed": True}
            }
        ]
        
        for step in steps:
            # Add user message
            await conversation_manager.add_message(
                conversation_id,
                "user",
                step["user_input"]
            )
            
            # Update context based on step
            for key, value in step["context_updates"].items():
                await conversation_manager.set_context_variable(
                    conversation_id,
                    key,
                    value
                )
            
            # Check conversation flow
            conversation = await conversation_manager.get_conversation(conversation_id)
            assert len(conversation.context_variables) > 0
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_conversation_history_management(self, conversation_manager):
        """Test conversation history management."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Add many messages to test history limits
        for i in range(60):  # More than max_history_length (50)
            await conversation_manager.add_message(
                conversation_id,
                "user" if i % 2 == 0 else "assistant",
                f"Message {i}"
            )
        
        # Check that history is limited
        conversation = await conversation_manager.get_conversation(conversation_id)
        assert len(conversation.message_history) <= 50
        
        # Check that recent messages are preserved
        last_message = conversation.message_history[-1]
        assert "Message 59" in last_message["content"]
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_conversation_context_window(self, conversation_manager):
        """Test conversation context window."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Add messages
        messages = [f"Message {i}" for i in range(15)]
        for i, message in enumerate(messages):
            await conversation_manager.add_message(
                conversation_id,
                "user" if i % 2 == 0 else "assistant",
                message
            )
        
        # Get context window
        context_window = await conversation_manager.get_context_window(conversation_id)
        
        # Should be limited to context_window_size (10)
        assert len(context_window) <= 10
        
        # Should contain recent messages
        assert any("Message 14" in msg["content"] for msg in context_window)
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_conversation_persistence(self, conversation_manager):
        """Test conversation persistence and retrieval."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create conversation
        conversation_id = await conversation_manager.create_conversation(
            user_id="test_user",
            session_id="test_session"
        )
        
        # Add data
        await conversation_manager.add_message(conversation_id, "user", "测试消息")
        await conversation_manager.set_context_variable(conversation_id, "test_var", "test_value")
        
        # Get conversation data
        original_conversation = await conversation_manager.get_conversation(conversation_id)
        
        # Simulate restart (stop and start again)
        await conversation_manager.stop()
        await conversation_manager.start()
        
        # Check that conversation data is still available
        restored_conversation = await conversation_manager.get_conversation(conversation_id)
        
        if restored_conversation:  # If persistence is implemented
            assert restored_conversation.user_id == original_conversation.user_id
            assert len(restored_conversation.message_history) == len(original_conversation.message_history)
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_multiple_conversations(self, conversation_manager):
        """Test handling multiple concurrent conversations."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create multiple conversations
        conversation_ids = []
        for i in range(5):
            conv_id = await conversation_manager.create_conversation(
                user_id=f"user_{i}",
                session_id=f"session_{i}"
            )
            conversation_ids.append(conv_id)
        
        # Add messages to each conversation
        for i, conv_id in enumerate(conversation_ids):
            await conversation_manager.add_message(
                conv_id,
                "user",
                f"Hello from user {i}"
            )
            await conversation_manager.set_context_variable(
                conv_id,
                "user_number",
                str(i)
            )
        
        # Verify each conversation is independent
        for i, conv_id in enumerate(conversation_ids):
            conversation = await conversation_manager.get_conversation(conv_id)
            assert conversation.user_id == f"user_{i}"
            assert conversation.context_variables["user_number"].value == str(i)
            assert f"Hello from user {i}" in conversation.message_history[0]["content"]
        
        await conversation_manager.stop()
    
    @pytest.mark.asyncio
    async def test_conversation_statistics(self, conversation_manager):
        """Test conversation statistics."""
        await conversation_manager.initialize()
        await conversation_manager.start()
        
        # Create and interact with conversations
        for i in range(3):
            conv_id = await conversation_manager.create_conversation(
                user_id=f"user_{i}",
                session_id=f"session_{i}"
            )
            
            # Add some messages
            for j in range(i + 1):
                await conversation_manager.add_message(
                    conv_id,
                    "user",
                    f"Message {j}"
                )
        
        # Get statistics
        stats = conversation_manager.get_conversation_statistics()
        
        assert stats is not None
        assert "total_conversations" in stats
        assert "active_conversations" in stats
        assert stats["total_conversations"] >= 3
        
        await conversation_manager.stop()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestConversationContextComprehensive().test_conversation_manager_lifecycle(
        ConversationManager(MockConfigManager(), MockLogger())
    ))
