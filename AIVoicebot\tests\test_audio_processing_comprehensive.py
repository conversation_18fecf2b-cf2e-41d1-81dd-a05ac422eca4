"""
Comprehensive tests for audio processing components.
"""

import pytest
import asyncio
import numpy as np
from unittest.mock import <PERSON><PERSON>, Async<PERSON>ock, patch
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.audio.audio_pipeline import AudioPipeline, AudioProcessor
from src.audio.vad_processor import VADProcessor
from src.audio.asr_processor import ASRProcessor
from src.audio.tts_processor import TTSProcessor
from tests.test_utils import (
    MockConfigManager, MockLogger, AudioTestData, MockExternalServices,
    create_test_audio_frame
)


class TestAudioProcessingComprehensive:
    """Comprehensive tests for audio processing components."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return MockConfigManager({
            "audio.sample_rate": 16000,
            "audio.channels": 1,
            "audio.chunk_size": 1024,
            "audio.format": "pcm_16",
            "vad.model_path": "models/silero_vad.onnx",
            "vad.threshold": 0.5,
            "vad.min_speech_duration": 0.25,
            "vad.min_silence_duration": 0.5,
            "asr.model_path": "models/SenseVoiceSmall",
            "asr.language": "zh",
            "asr.use_itn": True,
            "tts.voice": "zh-CN-XiaoxiaoNeural",
            "tts.rate": "+0%",
            "tts.pitch": "+0Hz"
        })
    
    @pytest.fixture
    def mock_logger(self):
        """Create mock logger."""
        return MockLogger()
    
    @pytest.fixture
    def audio_pipeline(self, mock_config_manager, mock_logger):
        """Create AudioPipeline instance."""
        return AudioPipeline(mock_config_manager, mock_logger)
    
    @pytest.fixture
    def vad_processor(self, mock_config_manager, mock_logger):
        """Create VADProcessor instance."""
        return VADProcessor(mock_config_manager, mock_logger)
    
    @pytest.fixture
    def asr_processor(self, mock_config_manager, mock_logger):
        """Create ASRProcessor instance."""
        return ASRProcessor(mock_config_manager, mock_logger)
    
    @pytest.fixture
    def tts_processor(self, mock_config_manager, mock_logger):
        """Create TTSProcessor instance."""
        return TTSProcessor(mock_config_manager, mock_logger)
    
    @pytest.fixture
    def test_audio_data(self):
        """Create test audio data."""
        return {
            "sine_wave": AudioTestData.generate_sine_wave(frequency=440, duration=1.0),
            "white_noise": AudioTestData.generate_white_noise(duration=1.0),
            "silence": AudioTestData.generate_sine_wave(frequency=0, duration=1.0, amplitude=0.0),
            "speech_like": AudioTestData.generate_sine_wave(frequency=200, duration=2.0, amplitude=0.3)
        }
    
    @pytest.mark.asyncio
    async def test_audio_pipeline_lifecycle(self, audio_pipeline):
        """Test AudioPipeline lifecycle."""
        await audio_pipeline.initialize()
        await audio_pipeline.start()
        
        assert audio_pipeline.is_initialized
        assert audio_pipeline.is_running
        
        await audio_pipeline.stop()
        await audio_pipeline.cleanup()
    
    @pytest.mark.asyncio
    async def test_vad_processor_lifecycle(self, vad_processor):
        """Test VADProcessor lifecycle."""
        await vad_processor.initialize()
        await vad_processor.start()
        
        assert vad_processor.is_initialized
        assert vad_processor.is_running
        
        await vad_processor.stop()
        await vad_processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_vad_speech_detection(self, vad_processor, test_audio_data):
        """Test VAD speech detection."""
        await vad_processor.initialize()
        await vad_processor.start()
        
        # Test with speech-like audio
        speech_result = await vad_processor.process_audio(test_audio_data["speech_like"])
        assert speech_result is not None
        assert "speech_detected" in speech_result
        
        # Test with silence
        silence_result = await vad_processor.process_audio(test_audio_data["silence"])
        assert silence_result is not None
        assert "speech_detected" in silence_result
        
        # Speech should be detected in speech-like audio more than in silence
        # (This depends on the actual VAD implementation)
        
        await vad_processor.stop()
    
    @pytest.mark.asyncio
    async def test_vad_continuous_processing(self, vad_processor, test_audio_data):
        """Test VAD continuous audio processing."""
        await vad_processor.initialize()
        await vad_processor.start()
        
        # Process multiple audio chunks
        results = []
        audio_chunks = [
            test_audio_data["silence"],
            test_audio_data["speech_like"],
            test_audio_data["white_noise"],
            test_audio_data["silence"]
        ]
        
        for chunk in audio_chunks:
            result = await vad_processor.process_audio(chunk)
            results.append(result)
        
        assert len(results) == 4
        for result in results:
            assert result is not None
            assert "speech_detected" in result
        
        await vad_processor.stop()
    
    @pytest.mark.asyncio
    async def test_asr_processor_lifecycle(self, asr_processor):
        """Test ASRProcessor lifecycle."""
        await asr_processor.initialize()
        await asr_processor.start()
        
        assert asr_processor.is_initialized
        assert asr_processor.is_running
        
        await asr_processor.stop()
        await asr_processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_asr_transcription(self, asr_processor, test_audio_data):
        """Test ASR transcription."""
        await asr_processor.initialize()
        await asr_processor.start()
        
        # Mock the actual ASR service
        with patch.object(asr_processor, '_transcribe_audio') as mock_transcribe:
            mock_transcribe.return_value = {
                "text": "你好，我想查询账户余额",
                "confidence": 0.95,
                "language": "zh-CN"
            }
            
            # Test transcription
            result = await asr_processor.process_audio(test_audio_data["speech_like"])
            
            assert result is not None
            assert "text" in result
            assert "confidence" in result
            assert result["text"] == "你好，我想查询账户余额"
            assert result["confidence"] == 0.95
        
        await asr_processor.stop()
    
    @pytest.mark.asyncio
    async def test_asr_batch_processing(self, asr_processor, test_audio_data):
        """Test ASR batch processing."""
        await asr_processor.initialize()
        await asr_processor.start()
        
        # Mock transcription results
        mock_results = [
            {"text": "第一段语音", "confidence": 0.9},
            {"text": "第二段语音", "confidence": 0.85},
            {"text": "第三段语音", "confidence": 0.92}
        ]
        
        with patch.object(asr_processor, '_transcribe_audio') as mock_transcribe:
            mock_transcribe.side_effect = mock_results
            
            # Process multiple audio chunks
            audio_chunks = [
                test_audio_data["speech_like"],
                test_audio_data["sine_wave"],
                test_audio_data["white_noise"]
            ]
            
            results = []
            for chunk in audio_chunks:
                result = await asr_processor.process_audio(chunk)
                results.append(result)
            
            assert len(results) == 3
            for i, result in enumerate(results):
                assert result["text"] == mock_results[i]["text"]
                assert result["confidence"] == mock_results[i]["confidence"]
        
        await asr_processor.stop()
    
    @pytest.mark.asyncio
    async def test_tts_processor_lifecycle(self, tts_processor):
        """Test TTSProcessor lifecycle."""
        await tts_processor.initialize()
        await tts_processor.start()
        
        assert tts_processor.is_initialized
        assert tts_processor.is_running
        
        await tts_processor.stop()
        await tts_processor.cleanup()
    
    @pytest.mark.asyncio
    async def test_tts_synthesis(self, tts_processor):
        """Test TTS synthesis."""
        await tts_processor.initialize()
        await tts_processor.start()
        
        # Mock the actual TTS service
        expected_audio = AudioTestData.generate_sine_wave(duration=2.0)
        
        with patch.object(tts_processor, '_synthesize_speech') as mock_synthesize:
            mock_synthesize.return_value = expected_audio
            
            # Test synthesis
            text = "您好，我是AI客服助手，有什么可以帮助您的吗？"
            result = await tts_processor.synthesize_text(text)
            
            assert result is not None
            assert isinstance(result, bytes)
            assert len(result) > 0
            assert result == expected_audio
        
        await tts_processor.stop()
    
    @pytest.mark.asyncio
    async def test_tts_multiple_synthesis(self, tts_processor):
        """Test TTS multiple text synthesis."""
        await tts_processor.initialize()
        await tts_processor.start()
        
        texts = [
            "欢迎使用AI客服系统",
            "请问有什么可以帮助您的吗？",
            "感谢您的使用，再见！"
        ]
        
        # Mock synthesis results
        mock_audio_results = [
            AudioTestData.generate_sine_wave(duration=1.0),
            AudioTestData.generate_sine_wave(duration=1.5),
            AudioTestData.generate_sine_wave(duration=1.2)
        ]
        
        with patch.object(tts_processor, '_synthesize_speech') as mock_synthesize:
            mock_synthesize.side_effect = mock_audio_results
            
            results = []
            for text in texts:
                result = await tts_processor.synthesize_text(text)
                results.append(result)
            
            assert len(results) == 3
            for i, result in enumerate(results):
                assert result == mock_audio_results[i]
        
        await tts_processor.stop()
    
    @pytest.mark.asyncio
    async def test_audio_pipeline_integration(self, audio_pipeline, test_audio_data):
        """Test AudioPipeline integration with all components."""
        await audio_pipeline.initialize()
        await audio_pipeline.start()
        
        # Mock all processing stages
        with patch.object(audio_pipeline, 'vad_processor') as mock_vad, \
             patch.object(audio_pipeline, 'asr_processor') as mock_asr, \
             patch.object(audio_pipeline, 'tts_processor') as mock_tts:
            
            # Configure mocks
            mock_vad.process_audio = AsyncMock(return_value={
                "speech_detected": True,
                "confidence": 0.9,
                "start_time": 0.0,
                "end_time": 1.0
            })
            
            mock_asr.process_audio = AsyncMock(return_value={
                "text": "你好，我想查询余额",
                "confidence": 0.95,
                "language": "zh-CN"
            })
            
            mock_tts.synthesize_text = AsyncMock(return_value=AudioTestData.generate_sine_wave())
            
            # Process audio through pipeline
            audio_input = test_audio_data["speech_like"]
            
            # Step 1: VAD processing
            vad_result = await audio_pipeline.process_vad(audio_input)
            assert vad_result["speech_detected"] is True
            
            # Step 2: ASR processing (if speech detected)
            if vad_result["speech_detected"]:
                asr_result = await audio_pipeline.process_asr(audio_input)
                assert "text" in asr_result
                assert asr_result["text"] == "你好，我想查询余额"
                
                # Step 3: Generate response and TTS
                response_text = "好的，我来帮您查询账户余额"
                tts_result = await audio_pipeline.process_tts(response_text)
                assert isinstance(tts_result, bytes)
                assert len(tts_result) > 0
        
        await audio_pipeline.stop()
    
    @pytest.mark.asyncio
    async def test_audio_pipeline_error_handling(self, audio_pipeline, test_audio_data):
        """Test AudioPipeline error handling."""
        await audio_pipeline.initialize()
        await audio_pipeline.start()
        
        # Test VAD error handling
        with patch.object(audio_pipeline, 'vad_processor') as mock_vad:
            mock_vad.process_audio = AsyncMock(side_effect=Exception("VAD processing failed"))
            
            result = await audio_pipeline.process_vad(test_audio_data["speech_like"])
            # Should handle error gracefully
            assert result is not None or result is None  # Depends on error handling strategy
        
        # Test ASR error handling
        with patch.object(audio_pipeline, 'asr_processor') as mock_asr:
            mock_asr.process_audio = AsyncMock(side_effect=Exception("ASR processing failed"))
            
            result = await audio_pipeline.process_asr(test_audio_data["speech_like"])
            # Should handle error gracefully
            assert result is not None or result is None  # Depends on error handling strategy
        
        # Test TTS error handling
        with patch.object(audio_pipeline, 'tts_processor') as mock_tts:
            mock_tts.synthesize_text = AsyncMock(side_effect=Exception("TTS synthesis failed"))
            
            result = await audio_pipeline.process_tts("测试文本")
            # Should handle error gracefully
            assert result is not None or result is None  # Depends on error handling strategy
        
        await audio_pipeline.stop()
    
    @pytest.mark.asyncio
    async def test_audio_format_conversion(self, audio_pipeline):
        """Test audio format conversion."""
        await audio_pipeline.initialize()
        await audio_pipeline.start()
        
        # Test different audio formats
        test_formats = [
            {"sample_rate": 8000, "channels": 1},
            {"sample_rate": 16000, "channels": 1},
            {"sample_rate": 44100, "channels": 2},
            {"sample_rate": 48000, "channels": 1}
        ]
        
        for format_config in test_formats:
            # Generate audio with specific format
            audio_data = AudioTestData.generate_sine_wave(
                duration=1.0,
                sample_rate=format_config["sample_rate"]
            )
            
            # Test format conversion (if implemented)
            converted_audio = await audio_pipeline.convert_audio_format(
                audio_data,
                target_sample_rate=16000,
                target_channels=1
            )
            
            # Should return converted audio or original if conversion not needed
            assert converted_audio is not None
            assert isinstance(converted_audio, bytes)
        
        await audio_pipeline.stop()
    
    @pytest.mark.asyncio
    async def test_audio_processing_statistics(self, audio_pipeline, test_audio_data):
        """Test audio processing statistics."""
        await audio_pipeline.initialize()
        await audio_pipeline.start()
        
        # Mock processors to collect statistics
        with patch.object(audio_pipeline, 'vad_processor') as mock_vad, \
             patch.object(audio_pipeline, 'asr_processor') as mock_asr:
            
            mock_vad.process_audio = AsyncMock(return_value={"speech_detected": True})
            mock_asr.process_audio = AsyncMock(return_value={"text": "测试", "confidence": 0.9})
            
            # Process multiple audio chunks
            for _ in range(5):
                await audio_pipeline.process_vad(test_audio_data["speech_like"])
                await audio_pipeline.process_asr(test_audio_data["speech_like"])
            
            # Get processing statistics
            stats = audio_pipeline.get_processing_statistics()
            
            assert stats is not None
            # Check for expected statistics fields
            expected_fields = ["total_processed", "vad_calls", "asr_calls", "processing_times"]
            for field in expected_fields:
                if field in stats:  # Only check if implemented
                    assert isinstance(stats[field], (int, float, dict, list))
        
        await audio_pipeline.stop()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(TestAudioProcessingComprehensive().test_audio_pipeline_lifecycle(
        AudioPipeline(MockConfigManager(), MockLogger())
    ))
