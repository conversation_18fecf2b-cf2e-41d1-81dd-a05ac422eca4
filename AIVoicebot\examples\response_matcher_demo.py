"""
Response Matcher Demo

This demo shows how to use the ResponseMatcher for:
- Query-to-script matching with multiple algorithms
- Fuzzy matching and intent classification
- Combining multiple script elements for responses
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from components.scripts.response_matcher_simple import (
    ResponseMatcher,
    ScriptEntry,
    MatchResult,
    MatchType,
    create_sample_scripts,
    create_response_matcher
)


def demo_basic_matching():
    """Demonstrate basic response matching functionality"""
    print("=== Basic Response Matching Demo ===")
    
    # Create matcher with sample scripts
    matcher = create_response_matcher(fuzzy_threshold=0.6)
    
    print(f"Created response matcher with {len(matcher.script_entries)} script entries")
    
    # Show loaded scripts
    print(f"\nLoaded script categories:")
    stats = matcher.get_statistics()
    for category, count in stats["categories"].items():
        print(f"  {category}: {count} entries")
    
    # Test various queries
    test_queries = [
        "你好",
        "查询余额",
        "忘记密码了",
        "我要投诉",
        "再见"
    ]
    
    print(f"\nTesting queries:")
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        
        # Get best response
        response = matcher.get_best_response(query)
        if response:
            print(f"  Response: {response}")
        else:
            print(f"  Response: [No matching response found]")
        
        # Show detailed matching results
        results = matcher.match_query(query, max_results=3)
        print(f"  Matching details ({len(results)} results):")
        
        for i, result in enumerate(results):
            print(f"    {i+1}. {result.script_entry.id} "
                  f"(confidence: {result.confidence:.3f}, "
                  f"type: {result.match_type.value})")
            if result.matched_text:
                print(f"       Matched: '{result.matched_text}'")


def demo_matching_algorithms():
    """Demonstrate different matching algorithms"""
    print("\n=== Matching Algorithms Demo ===")
    
    matcher = create_response_matcher()
    test_query = "你好吗"
    
    print(f"Testing query: '{test_query}' with different algorithms:")
    
    # Exact matching
    exact_results = matcher.exact_match(test_query)
    print(f"\nExact matching: {len(exact_results)} results")
    for result in exact_results:
        print(f"  - {result.script_entry.id}: {result.confidence:.3f}")
    
    # Fuzzy matching
    fuzzy_results = matcher.fuzzy_match(test_query)
    print(f"\nFuzzy matching: {len(fuzzy_results)} results")
    for result in fuzzy_results:
        print(f"  - {result.script_entry.id}: {result.confidence:.3f} "
              f"(matched: '{result.matched_text}')")
    
    # Keyword matching
    keyword_results = matcher.keyword_match(test_query)
    print(f"\nKeyword matching: {len(keyword_results)} results")
    for result in keyword_results:
        print(f"  - {result.script_entry.id}: {result.confidence:.3f}")
    
    # Intent matching
    intent_results = matcher.intent_match(test_query)
    print(f"\nIntent matching: {len(intent_results)} results")
    for result in intent_results:
        print(f"  - {result.script_entry.id}: {result.confidence:.3f} "
              f"(intent: '{result.matched_text}')")


def demo_complex_queries():
    """Demonstrate handling of complex queries"""
    print("\n=== Complex Queries Demo ===")
    
    matcher = create_response_matcher()
    
    complex_queries = [
        "你好，我想查询一下我的账户余额",
        "请帮我重置密码，我忘记了",
        "我对你们的服务很不满意，要投诉",
        "谢谢你的帮助，再见",
        "什么是账户余额？怎么查询？"
    ]
    
    print("Testing complex queries:")
    
    for query in complex_queries:
        print(f"\nQuery: '{query}'")
        
        results = matcher.match_query(query, max_results=2)
        
        if results:
            print(f"  Best match: {results[0].script_entry.id}")
            print(f"  Confidence: {results[0].confidence:.3f}")
            print(f"  Match type: {results[0].match_type.value}")
            print(f"  Response: {results[0].script_entry.response}")
            
            if len(results) > 1:
                print(f"  Alternative: {results[1].script_entry.id} "
                      f"(confidence: {results[1].confidence:.3f})")
        else:
            print("  No matches found")


def demo_custom_scripts():
    """Demonstrate adding custom script entries"""
    print("\n=== Custom Scripts Demo ===")
    
    matcher = ResponseMatcher(fuzzy_threshold=0.5)
    
    # Add custom script entries
    custom_scripts = [
        ScriptEntry(
            id="weather_query",
            category="information",
            keywords=["天气", "weather", "温度", "下雨"],
            patterns=["今天天气怎么样", "会下雨吗", "温度多少"],
            response="抱歉，我无法提供天气信息，建议您查看天气预报应用。",
            confidence_threshold=0.6
        ),
        ScriptEntry(
            id="time_query",
            category="information",
            keywords=["时间", "time", "几点", "现在"],
            patterns=["现在几点", "什么时间", "时间"],
            response="当前时间信息请查看您的设备时钟。",
            confidence_threshold=0.7
        ),
        ScriptEntry(
            id="technical_support",
            category="support",
            keywords=["技术", "technical", "故障", "bug", "问题"],
            patterns=["技术支持", "系统故障", "有bug"],
            response="我将为您转接技术支持专员，请稍等。",
            priority=2  # Higher priority
        )
    ]
    
    print(f"Adding {len(custom_scripts)} custom script entries...")
    
    for script in custom_scripts:
        matcher.add_script_entry(script)
        print(f"  Added: {script.id} ({script.category})")
    
    # Test custom scripts
    test_queries = [
        "今天天气怎么样？",
        "现在几点了？",
        "系统有bug，需要技术支持"
    ]
    
    print(f"\nTesting custom scripts:")
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        response = matcher.get_best_response(query)
        print(f"  Response: {response or '[No match]'}")


def demo_similarity_testing():
    """Demonstrate similarity calculation"""
    print("\n=== Similarity Testing Demo ===")
    
    matcher = ResponseMatcher()
    
    # Test similarity between different strings
    test_pairs = [
        ("你好", "你好"),           # Identical
        ("你好", "您好"),           # Similar
        ("你好", "你好吗"),         # Partial match
        ("查询余额", "余额查询"),    # Word order
        ("hello", "world"),        # Different
        ("", "hello"),             # Empty
    ]
    
    print("String similarity testing:")
    for str1, str2 in test_pairs:
        similarity = matcher._calculate_similarity(str1, str2)
        print(f"  '{str1}' vs '{str2}': {similarity:.3f}")


def demo_performance_testing():
    """Demonstrate performance with many scripts"""
    print("\n=== Performance Testing Demo ===")
    
    matcher = ResponseMatcher()
    
    # Add many script entries
    print("Creating large script database...")
    for i in range(100):
        script = ScriptEntry(
            id=f"script_{i}",
            category=f"category_{i % 10}",
            keywords=[f"keyword_{i}", f"word_{i}"],
            patterns=[f"pattern {i}", f"query {i}"],
            response=f"Response for script {i}"
        )
        matcher.add_script_entry(script)
    
    stats = matcher.get_statistics()
    print(f"Created database with {stats['total_entries']} entries")
    print(f"Categories: {len(stats['categories'])}")
    print(f"Keyword index size: {stats['keyword_index_size']}")
    
    # Test query performance
    import time
    
    test_queries = ["keyword_50", "pattern 25", "unknown query", "word_75"]
    
    print(f"\nTesting query performance:")
    for query in test_queries:
        start_time = time.time()
        results = matcher.match_query(query, max_results=5)
        end_time = time.time()
        
        print(f"  Query: '{query}'")
        print(f"    Results: {len(results)}")
        print(f"    Time: {(end_time - start_time) * 1000:.2f}ms")
        
        if results:
            print(f"    Best match: {results[0].script_entry.id} "
                  f"(confidence: {results[0].confidence:.3f})")


def demo_edge_cases():
    """Demonstrate handling of edge cases"""
    print("\n=== Edge Cases Demo ===")
    
    matcher = create_response_matcher()
    
    edge_cases = [
        "",                    # Empty query
        "   ",                 # Whitespace only
        "a",                   # Single character
        "xyz123!@#",          # Special characters
        "很长很长很长很长很长的查询语句包含很多字符", # Very long query
        "HELLO",              # All caps
        "你好hello混合语言",    # Mixed languages
    ]
    
    print("Testing edge cases:")
    for query in edge_cases:
        print(f"\nQuery: '{query}'")
        try:
            results = matcher.match_query(query, max_results=1)
            if results:
                print(f"  Match: {results[0].script_entry.id} "
                      f"(confidence: {results[0].confidence:.3f})")
            else:
                print("  No matches")
        except Exception as e:
            print(f"  Error: {e}")


def main():
    """Run all response matcher demos"""
    print("AI Voice Customer Service - Response Matcher Demo")
    print("=" * 60)
    
    try:
        demo_basic_matching()
        demo_matching_algorithms()
        demo_complex_queries()
        demo_custom_scripts()
        demo_similarity_testing()
        demo_performance_testing()
        demo_edge_cases()
        
        print("\n" + "=" * 60)
        print("Response Matcher Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()