"""
Tests for file watcher functionality.
"""

import asyncio
import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock
import pandas as pd
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.scripts.file_watcher import (
    FileWatcher, FileWatcherConfig, ExcelFileHandler,
    create_file_watcher, setup_script_hot_reload
)


class TestFileWatcher:
    """Test file watcher functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def temp_directory(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def watcher_config(self, temp_directory):
        """Create test watcher configuration."""
        return FileWatcherConfig(
            watch_directory=temp_directory,
            watch_extensions=['.xlsx', '.xls'],
            debounce_seconds=0.1,  # Short debounce for testing
            batch_changes=True
        )
    
    def test_watcher_creation(self, watcher_config, mock_config_manager):
        """Test file watcher creation."""
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        assert watcher.config == watcher_config
        assert len(watcher._change_callbacks) == 0
        assert watcher.observer is None
    
    @pytest.mark.asyncio
    async def test_watcher_lifecycle(self, watcher_config, mock_config_manager):
        """Test watcher lifecycle."""
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        await watcher.initialize()
        await watcher.start()
        
        assert watcher.is_initialized
        assert watcher.is_running
        assert watcher.observer is not None
        assert watcher.observer.is_alive()
        
        await watcher.stop()
        await watcher.cleanup()
    
    @pytest.mark.asyncio
    async def test_callback_management(self, watcher_config, mock_config_manager):
        """Test callback management."""
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        # Create mock callbacks
        callback1 = Mock()
        callback2 = AsyncMock()
        
        # Add callbacks
        watcher.add_change_callback(callback1)
        watcher.add_change_callback(callback2)
        
        assert len(watcher._change_callbacks) == 2
        
        # Remove callback
        watcher.remove_change_callback(callback1)
        
        assert len(watcher._change_callbacks) == 1
        assert callback2 in watcher._change_callbacks
    
    @pytest.mark.asyncio
    async def test_force_scan(self, watcher_config, mock_config_manager, temp_directory):
        """Test force scan functionality."""
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        # Create test Excel files
        test_files = []
        for i, ext in enumerate(['.xlsx', '.xls']):
            file_path = Path(temp_directory) / f"test_{i}{ext}"
            
            # Create dummy Excel file
            df = pd.DataFrame({"A": [1, 2], "B": [3, 4]})
            if ext == '.xlsx':
                df.to_excel(file_path, index=False)
            else:
                # For .xls, we'll just create a file (xlwt might not be available)
                file_path.touch()
            
            test_files.append(file_path)
        
        # Force scan
        found_files = watcher.force_scan()
        
        # Should find the Excel files
        assert len(found_files) >= 1  # At least the .xlsx file
        
        # Clean up
        for file_path in test_files:
            if file_path.exists():
                file_path.unlink()
    
    @pytest.mark.asyncio
    async def test_file_change_processing(self, watcher_config, mock_config_manager, temp_directory):
        """Test file change processing."""
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        await watcher.initialize()
        await watcher.start()
        
        # Create mock callback
        callback_called = asyncio.Event()
        changed_file = None
        
        async def test_callback(file_path):
            nonlocal changed_file
            changed_file = file_path
            callback_called.set()
        
        watcher.add_change_callback(test_callback)
        
        # Simulate file change
        test_file = Path(temp_directory) / "test.xlsx"
        await watcher._handle_file_change(test_file)
        
        # Wait for callback
        await asyncio.wait_for(callback_called.wait(), timeout=1.0)
        
        assert changed_file == test_file
        
        await watcher.stop()
        await watcher.cleanup()
    
    @pytest.mark.asyncio
    async def test_batch_processing(self, watcher_config, mock_config_manager, temp_directory):
        """Test batch processing of file changes."""
        # Enable batching
        watcher_config.batch_changes = True
        watcher_config.debounce_seconds = 0.1
        
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        await watcher.initialize()
        await watcher.start()
        
        # Track callback calls
        callback_calls = []
        
        async def test_callback(file_path):
            callback_calls.append(file_path)
        
        watcher.add_change_callback(test_callback)
        
        # Simulate multiple rapid file changes
        test_files = [
            Path(temp_directory) / "test1.xlsx",
            Path(temp_directory) / "test2.xlsx",
            Path(temp_directory) / "test1.xlsx"  # Duplicate
        ]
        
        for file_path in test_files:
            await watcher._handle_file_change(file_path)
        
        # Wait for batch processing
        await asyncio.sleep(0.2)
        
        # Should have processed unique files
        assert len(callback_calls) == 2  # test1.xlsx and test2.xlsx
        
        await watcher.stop()
        await watcher.cleanup()
    
    def test_watcher_statistics(self, watcher_config, mock_config_manager):
        """Test watcher statistics."""
        watcher = FileWatcher(watcher_config, mock_config_manager)
        
        stats = watcher.get_watcher_stats()
        
        assert "total_events" in stats
        assert "processed_events" in stats
        assert "pending_changes" in stats
        assert "active_callbacks" in stats
        assert "watch_directory" in stats
        assert "watch_extensions" in stats


class TestFileWatcherIntegration:
    """Test file watcher integration with script manager."""
    
    @pytest.mark.asyncio
    async def test_script_hot_reload_integration(self):
        """Test integration with script manager."""
        # Create mock script manager
        script_manager = Mock()
        script_manager.reload_scripts = AsyncMock()
        
        # Create file watcher
        with tempfile.TemporaryDirectory() as temp_dir:
            config = FileWatcherConfig(
                watch_directory=temp_dir,
                debounce_seconds=0.1
            )
            
            watcher = FileWatcher(config, Mock())
            await watcher.initialize()
            await watcher.start()
            
            # Setup hot reload integration
            setup_script_hot_reload(script_manager, watcher)
            
            # Simulate file change
            test_file = Path(temp_dir) / "test.xlsx"
            await watcher._handle_file_change(test_file)
            
            # Wait for processing
            await asyncio.sleep(0.2)
            
            # Verify script manager reload was called
            script_manager.reload_scripts.assert_called_once()
            
            await watcher.stop()
            await watcher.cleanup()


@pytest.mark.asyncio
async def test_create_file_watcher():
    """Test file watcher factory function."""
    with tempfile.TemporaryDirectory() as temp_dir:
        watcher = await create_file_watcher(
            watch_directory=temp_dir,
            debounce_seconds=0.1,
            config_manager=Mock()
        )
        
        assert watcher.is_initialized
        assert watcher.is_running
        
        await watcher.stop()
        await watcher.cleanup()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(test_create_file_watcher())