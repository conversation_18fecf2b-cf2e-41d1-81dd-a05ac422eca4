"""
Tests for Qwen API client.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
import aiohttp
import json
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.llm.qwen_client import (
    QwenLLMClient, QwenClientConfig, QwenMessage, QwenResponse,
    QwenModelType, create_qwen_client, create_conversation_messages
)


class TestQwenClient:
    """Test Qwen API client functionality."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create mock configuration manager."""
        return Mock()
    
    @pytest.fixture
    def client_config(self):
        """Create test client configuration."""
        return QwenClientConfig(
            api_key="test_api_key",
            model=QwenModelType.QWEN_TURBO,
            max_tokens=1000,
            temperature=0.7,
            max_retries=2,
            retry_delay=0.1,  # Short delay for testing
            request_timeout=5.0
        )
    
    @pytest.fixture
    def sample_messages(self):
        """Create sample messages for testing."""
        return [
            QwenMessage(role="system", content="You are a helpful assistant."),
            QwenMessage(role="user", content="Hello, how are you?")
        ]
    
    def test_client_creation(self, client_config, mock_config_manager):
        """Test Qwen client creation."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        assert client.config == client_config
        assert client.session is None
        assert client._total_requests == 0
    
    @pytest.mark.asyncio
    async def test_client_lifecycle(self, client_config, mock_config_manager):
        """Test client lifecycle."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        await client.initialize()
        await client.start()
        
        assert client.is_initialized
        assert client.is_running
        assert client.session is not None
        
        await client.stop()
        await client.cleanup()
        
        assert client.session is None
    
    def test_message_validation(self, client_config, mock_config_manager):
        """Test message validation."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        # Valid messages
        valid_messages = [
            QwenMessage(role="user", content="Hello")
        ]
        client._validate_messages(valid_messages)  # Should not raise
        
        # Empty messages
        with pytest.raises(ValueError, match="Messages list cannot be empty"):
            client._validate_messages([])
        
        # Invalid role
        invalid_messages = [
            QwenMessage(role="invalid", content="Hello")
        ]
        with pytest.raises(ValueError, match="Invalid message role"):
            client._validate_messages(invalid_messages)
        
        # Too long input
        long_message = QwenMessage(role="user", content="x" * 10000)
        with pytest.raises(ValueError, match="Input too long"):
            client._validate_messages([long_message])
    
    def test_request_preparation(self, client_config, mock_config_manager, sample_messages):
        """Test API request preparation."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        request_data = client._prepare_request(sample_messages)
        
        assert request_data["model"] == "qwen-turbo"
        assert "input" in request_data
        assert "messages" in request_data["input"]
        assert len(request_data["input"]["messages"]) == 2
        assert "parameters" in request_data
        assert request_data["parameters"]["max_tokens"] == 1000
        assert request_data["parameters"]["temperature"] == 0.7
    
    def test_response_parsing(self, client_config, mock_config_manager):
        """Test API response parsing."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        # Successful response
        response_data = {
            "output": {
                "choices": [{
                    "message": {
                        "content": "Hello! I'm doing well, thank you."
                    },
                    "finish_reason": "stop"
                }]
            },
            "usage": {
                "input_tokens": 10,
                "output_tokens": 15,
                "total_tokens": 25
            },
            "request_id": "test_request_123"
        }
        
        response = client._parse_response(response_data)
        
        assert response.text == "Hello! I'm doing well, thank you."
        assert response.finish_reason == "stop"
        assert response.prompt_tokens == 10
        assert response.completion_tokens == 15
        assert response.total_tokens == 25
        assert response.response_id == "test_request_123"
        assert response.is_success
        assert response.is_complete
        
        # Error response
        error_data = {
            "code": "400",
            "message": "Invalid request"
        }
        
        error_response = client._parse_response(error_data)
        
        assert error_response.text == ""
        assert error_response.error_code == "400"
        assert error_response.error_message == "Invalid request"
        assert not error_response.is_success
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, client_config, mock_config_manager):
        """Test rate limiting functionality."""
        # Set low rate limit for testing
        client_config.requests_per_minute = 2
        client_config.enable_rate_limiting = True
        
        client = QwenLLMClient(client_config, mock_config_manager)
        
        # First two requests should go through quickly
        start_time = asyncio.get_event_loop().time()
        
        await client._apply_rate_limiting()
        await client._apply_rate_limiting()
        
        first_duration = asyncio.get_event_loop().time() - start_time
        assert first_duration < 0.1  # Should be very fast
        
        # Third request should be rate limited
        start_time = asyncio.get_event_loop().time()
        await client._apply_rate_limiting()
        third_duration = asyncio.get_event_loop().time() - start_time
        
        # Should have been delayed (but we'll use a reasonable threshold for CI)
        assert third_duration > 0.01  # Some delay expected
    
    def test_circuit_breaker(self, client_config, mock_config_manager):
        """Test circuit breaker functionality."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        # Initially closed
        assert not client._is_circuit_breaker_open()
        
        # Simulate failures
        for _ in range(5):
            client._handle_request_error(Exception("Test error"))
        
        # Should be open now
        assert client._is_circuit_breaker_open()
        
        # Successful response should reset
        response = QwenResponse(text="Success")
        client._update_statistics(response, 100.0)
        
        assert not client._is_circuit_breaker_open()
    
    @pytest.mark.asyncio
    async def test_mock_api_request(self, client_config, mock_config_manager, sample_messages):
        """Test API request with mocked HTTP response."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        # Mock successful response
        mock_response_data = {
            "output": {
                "choices": [{
                    "message": {
                        "content": "Mocked response"
                    },
                    "finish_reason": "stop"
                }]
            },
            "usage": {
                "input_tokens": 5,
                "output_tokens": 10,
                "total_tokens": 15
            }
        }
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            # Setup mock response
            mock_resp = AsyncMock()
            mock_resp.status = 200
            mock_resp.text = AsyncMock(return_value=json.dumps(mock_response_data))
            mock_post.return_value.__aenter__.return_value = mock_resp
            
            await client.initialize()
            await client.start()
            
            response = await client.generate_response(sample_messages)
            
            assert response.text == "Mocked response"
            assert response.is_success
            assert response.total_tokens == 15
            
            await client.stop()
            await client.cleanup()
    
    def test_client_statistics(self, client_config, mock_config_manager):
        """Test client statistics."""
        client = QwenLLMClient(client_config, mock_config_manager)
        
        # Initial stats
        stats = client.get_client_stats()
        assert stats["total_requests"] == 0
        assert stats["success_rate"] == 0.0
        
        # Simulate successful request
        response = QwenResponse(text="Success", total_tokens=100)
        client._update_statistics(response, 150.0)
        
        stats = client.get_client_stats()
        assert stats["total_requests"] == 1
        assert stats["successful_requests"] == 1
        assert stats["success_rate"] == 1.0
        assert stats["total_tokens_used"] == 100
        assert stats["average_response_time_ms"] == 150.0


class TestQwenUtilities:
    """Test utility functions."""
    
    def test_message_creation(self):
        """Test message creation utilities."""
        system_prompt = "You are a helpful assistant."
        conversation_history = [
            {"user": "Hello", "assistant": "Hi there!"},
            {"user": "How are you?", "assistant": "I'm doing well."}
        ]
        current_query = "What can you help me with?"
        
        messages = create_conversation_messages(
            system_prompt, conversation_history, current_query
        )
        
        assert len(messages) == 6  # system + 2 turns + current query
        assert messages[0].role == "system"
        assert messages[0].content == system_prompt
        assert messages[1].role == "user"
        assert messages[1].content == "Hello"
        assert messages[2].role == "assistant"
        assert messages[2].content == "Hi there!"
        assert messages[-1].role == "user"
        assert messages[-1].content == current_query
    
    def test_qwen_message(self):
        """Test QwenMessage class."""
        message = QwenMessage(role="user", content="Hello")
        
        message_dict = message.to_dict()
        assert message_dict == {"role": "user", "content": "Hello"}
    
    def test_qwen_response(self):
        """Test QwenResponse class."""
        # Successful response
        response = QwenResponse(
            text="Hello!",
            finish_reason="stop",
            total_tokens=10
        )
        
        assert response.is_success
        assert response.is_complete
        
        # Error response
        error_response = QwenResponse(
            text="",
            error_code="400",
            error_message="Bad request"
        )
        
        assert not error_response.is_success
        assert not error_response.is_complete


@pytest.mark.asyncio
async def test_create_qwen_client():
    """Test Qwen client factory function."""
    with patch('aiohttp.ClientSession'):
        client = await create_qwen_client(
            api_key="test_key",
            model=QwenModelType.QWEN_TURBO,
            config_manager=Mock()
        )
        
        assert client.is_initialized
        assert client.is_running
        assert client.config.api_key == "test_key"
        assert client.config.model == QwenModelType.QWEN_TURBO
        
        await client.stop()
        await client.cleanup()


if __name__ == "__main__":
    # Run a simple test
    asyncio.run(test_create_qwen_client())