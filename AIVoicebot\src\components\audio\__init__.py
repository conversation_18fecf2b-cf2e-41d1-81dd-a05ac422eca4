"""
Audio processing components for AI Voice Customer Service System

This package provides audio processing infrastructure including:
- Audio pipeline coordination
- Real-time audio buffering
- Audio format conversion and preprocessing
"""

from .audio_simple import (
    AudioPipeline,
    AudioBuffer,
    AudioProcessor,
    AudioConfig,
    AudioFormat,
    create_audio_pipeline,
    create_simple_processor
)

__all__ = [
    "AudioPipeline",
    "AudioBuffer", 
    "AudioProcessor",
    "AudioConfig",
    "AudioFormat",
    "create_audio_pipeline",
    "create_simple_processor"
]