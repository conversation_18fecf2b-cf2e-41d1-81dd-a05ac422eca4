# Environment variables for AI Voice Customer Service System
# Copy this file to .env and fill in your actual values
# DO NOT commit the actual .env file to version control

# Environment
AIVOICE_ENVIRONMENT=development

# Qwen API Configuration
AIVOICE_QWEN_API_KEY=your_qwen_api_key_here
AIVOICE_QWEN_API_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# Logging Configuration
AIVOICE_LOGGING_LEVEL=INFO
AIVOICE_LOGGING_DIR=logs
AIVOICE_LOGGING_ENABLE_CONSOLE=true
AIVOICE_LOGGING_ENABLE_FILE=true

# Audio Configuration
AIVOICE_AUDIO_SAMPLE_RATE=16000
AIVOICE_AUDIO_CHANNELS=1
AIVOICE_AUDIO_CHUNK_SIZE=1024

# Model Paths
AIVOICE_MODELS_SENSEVOICE_PATH=models/SenseVoiceSmall/model.pt
AIVOICE_MODELS_SILERO_VAD_PATH=models/snakers4_silero-vad
AIVOICE_MODELS_DEVICE=auto

# EdgeTTS Configuration
AIVOICE_EDGE_TTS_VOICE=zh-CN-XiaoxiaoNeural
AIVOICE_EDGE_TTS_RATE=+0%
AIVOICE_EDGE_TTS_PITCH=+0Hz

# Telephony Configuration
AIVOICE_TELEPHONY_PROVIDER=custom
AIVOICE_TELEPHONY_CONFIG_PATH=config/telephony.yaml
AIVOICE_TELEPHONY_MAX_CONCURRENT_CALLS=10

# Performance Configuration
AIVOICE_PERFORMANCE_VAD_THRESHOLD=0.5
AIVOICE_PERFORMANCE_SPEECH_TIMEOUT=3.0
AIVOICE_PERFORMANCE_SILENCE_TIMEOUT=1.0

# Monitoring Configuration
AIVOICE_MONITORING_ENABLE_METRICS=true
AIVOICE_MONITORING_METRICS_PORT=8080

# Development Configuration
AIVOICE_DEVELOPMENT_DEBUG_MODE=false
AIVOICE_DEVELOPMENT_MOCK_TELEPHONY=false
AIVOICE_DEVELOPMENT_MOCK_MODELS=false

# Security Configuration
AIVOICE_SECURITY_RATE_LIMIT_CALLS_PER_MINUTE=60
AIVOICE_SECURITY_CONVERSATION_RETENTION_DAYS=30

# Database Configuration (if using external database)
# AIVOICE_DATABASE_HOST=localhost
# AIVOICE_DATABASE_PORT=5432
# AIVOICE_DATABASE_NAME=aivoice
# AIVOICE_DATABASE_USERNAME=aivoice_user
# AIVOICE_DATABASE_PASSWORD=your_database_password

# Redis Configuration (if using Redis for session management)
# AIVOICE_REDIS_HOST=localhost
# AIVOICE_REDIS_PORT=6379
# AIVOICE_REDIS_PASSWORD=your_redis_password
# AIVOICE_REDIS_DB=0

# Alerting Configuration (for production)
# AIVOICE_MONITORING_ALERT_WEBHOOK_URL=https://your-webhook-url.com/alerts

# Backup Configuration (for production)
# AIVOICE_BACKUP_ENABLE=true
# AIVOICE_BACKUP_STORAGE_PATH=/backup/aivoice