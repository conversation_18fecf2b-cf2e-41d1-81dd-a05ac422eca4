"""
SileroVAD model wrapper for voice activity detection.

This module provides a wrapper around the SileroVAD model for detecting
voice activity in audio streams with configurable thresholds and parameters.
"""

import torch
import torchaudio
import numpy as np
import logging
import sys
import os
from typing import Optional, List, Tuple, Dict, Any
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
import threading

from ...core.interfaces import AudioChunk, AudioFormat, IVoiceActivityDetector, AudioProcessingError
from ...core.base_component import BaseComponent


@dataclass
class VADConfig:
    """Configuration for Voice Activity Detection."""
    model_path: str = "models/snakers4_silero-vad"
    
    # Detection parameters
    threshold: float = 0.5  # Voice activity threshold (0.0 to 1.0)
    min_speech_duration_ms: int = 250  # Minimum speech duration to consider
    min_silence_duration_ms: int = 100  # Minimum silence duration between speech
    
    # Audio processing
    sample_rate: int = 16000  # Required sample rate for SileroVAD
    window_size_samples: int = 512  # Processing window size
    
    # Advanced parameters
    speech_pad_ms: int = 30  # Padding around detected speech
    return_seconds: bool = False  # Return timestamps in seconds vs samples
    
    # Performance settings
    use_onnx: bool = False  # Use ONNX version for better performance
    force_onnx_cpu: bool = False  # Force ONNX to use CPU
    
    # Adaptive threshold
    enable_adaptive_threshold: bool = True
    noise_level_adaptation: bool = True
    threshold_adaptation_rate: float = 0.1


@dataclass
class VADResult:
    """Result from voice activity detection."""
    has_speech: bool
    confidence: float  # Voice activity probability (0.0 to 1.0)
    timestamp: datetime
    
    # Optional detailed information
    speech_start_time: Optional[float] = None
    speech_end_time: Optional[float] = None
    background_noise_level: Optional[float] = None
    adapted_threshold: Optional[float] = None


class SileroVADDetector(BaseComponent, IVoiceActivityDetector):
    """
    SileroVAD model wrapper for voice activity detection.
    
    Provides real-time voice activity detection using the SileroVAD model
    with adaptive thresholding and noise level estimation.
    """
    
    def __init__(self, config: VADConfig, config_manager, logger=None):
        """
        Initialize SileroVAD detector.
        
        Args:
            config: VAD configuration
            config_manager: Configuration manager instance
            logger: Optional logger instance
        """
        super().__init__("silero_vad_detector", config_manager, logger)
        
        self.config = config
        self.model = None
        self.utils = None
        
        # State tracking
        self._current_threshold = config.threshold
        self._background_noise_level = 0.0
        self._noise_samples = []
        self._speech_history = []
        
        # Performance tracking
        self._detection_count = 0
        self._speech_detected_count = 0
        self._processing_times = []
        
        # Threading for model operations
        self._model_lock = threading.Lock()
        
        # Adaptive threshold parameters
        self._threshold_history = []
        self._noise_estimation_window = 100  # samples for noise estimation
    
    async def _initialize_impl(self) -> None:
        """Initialize the SileroVAD model."""
        self._log.info("Initializing SileroVAD model...")
        
        try:
            # Add model path to Python path
            model_path = os.path.abspath(self.config.model_path)
            if model_path not in sys.path:
                sys.path.insert(0, model_path)
            
            # Import SileroVAD
            from hubconf import silero_vad
            
            # Load model
            self._log.info(f"Loading SileroVAD model from {model_path}")
            self.model, self.utils = silero_vad(
                onnx=self.config.use_onnx,
                force_onnx_cpu=self.config.force_onnx_cpu
            )
            
            # Validate model
            if self.model is None:
                raise AudioProcessingError("Failed to load SileroVAD model")
            
            self._log.info("SileroVAD model loaded successfully")
            
            # Initialize adaptive threshold if enabled
            if self.config.enable_adaptive_threshold:
                self._setup_adaptive_threshold()
            
        except Exception as e:
            self._log.error(f"Failed to initialize SileroVAD: {e}")
            raise AudioProcessingError(f"SileroVAD initialization failed: {e}")
    
    async def _start_impl(self) -> None:
        """Start the VAD detector."""
        self._log.info("Starting SileroVAD detector...")
        
        # Reset statistics
        self._detection_count = 0
        self._speech_detected_count = 0
        self._processing_times.clear()
        
        self._log.info("SileroVAD detector started")
    
    async def _stop_impl(self) -> None:
        """Stop the VAD detector."""
        self._log.info("Stopping SileroVAD detector...")
        
        # Log final statistics
        if self._detection_count > 0:
            speech_rate = self._speech_detected_count / self._detection_count
            avg_processing_time = np.mean(self._processing_times) if self._processing_times else 0
            
            self._log.info(f"VAD Statistics:")
            self._log.info(f"  Total detections: {self._detection_count}")
            self._log.info(f"  Speech detected: {self._speech_detected_count}")
            self._log.info(f"  Speech rate: {speech_rate:.2%}")
            self._log.info(f"  Avg processing time: {avg_processing_time:.3f}ms")
        
        self._log.info("SileroVAD detector stopped")
    
    async def _cleanup_impl(self) -> None:
        """Cleanup VAD detector resources."""
        self._log.info("Cleaning up SileroVAD detector...")
        
        # Clear model references
        self.model = None
        self.utils = None
        
        # Clear state
        self._speech_history.clear()
        self._noise_samples.clear()
        self._threshold_history.clear()
        self._processing_times.clear()
        
        self._log.info("SileroVAD detector cleanup completed")
    
    def _setup_adaptive_threshold(self) -> None:
        """Setup adaptive threshold parameters."""
        self._log.debug("Setting up adaptive threshold mechanism")
        self._current_threshold = self.config.threshold
        self._threshold_history = [self.config.threshold]
    
    def _preprocess_audio(self, audio_chunk: AudioChunk) -> torch.Tensor:
        """
        Preprocess audio chunk for SileroVAD.
        
        Args:
            audio_chunk: Input audio chunk
            
        Returns:
            Preprocessed audio tensor
        """
        # Convert bytes to numpy array
        if isinstance(audio_chunk.data, bytes):
            audio_np = np.frombuffer(audio_chunk.data, dtype=np.int16)
        else:
            audio_np = audio_chunk.data
        
        # Convert to float32 and normalize
        audio_float = audio_np.astype(np.float32) / 32768.0
        
        # Ensure correct sample rate
        if audio_chunk.sample_rate != self.config.sample_rate:
            # Resample if necessary (simplified - in production use proper resampling)
            self._log.warning(f"Audio sample rate {audio_chunk.sample_rate} != expected {self.config.sample_rate}")
        
        # Convert to tensor
        audio_tensor = torch.from_numpy(audio_float)
        
        # Ensure minimum length
        if len(audio_tensor) < self.config.window_size_samples:
            # Pad with zeros
            padding = self.config.window_size_samples - len(audio_tensor)
            audio_tensor = torch.nn.functional.pad(audio_tensor, (0, padding))
        
        return audio_tensor
    
    def _estimate_noise_level(self, audio_tensor: torch.Tensor) -> float:
        """
        Estimate background noise level from audio.
        
        Args:
            audio_tensor: Audio tensor
            
        Returns:
            Estimated noise level
        """
        # Simple noise estimation using RMS of lower percentiles
        audio_np = audio_tensor.numpy()
        
        # Calculate RMS
        rms = np.sqrt(np.mean(audio_np ** 2))
        
        # Use lower percentile as noise estimate
        noise_estimate = np.percentile(np.abs(audio_np), 25)
        
        return float(noise_estimate)
    
    def _update_adaptive_threshold(self, confidence: float, noise_level: float) -> float:
        """
        Update adaptive threshold based on recent detections and noise level.
        
        Args:
            confidence: Current detection confidence
            noise_level: Current noise level
            
        Returns:
            Updated threshold
        """
        if not self.config.enable_adaptive_threshold:
            return self.config.threshold
        
        # Update noise level estimation
        self._noise_samples.append(noise_level)
        if len(self._noise_samples) > self._noise_estimation_window:
            self._noise_samples = self._noise_samples[-self._noise_estimation_window:]
        
        # Calculate average noise level
        avg_noise = np.mean(self._noise_samples) if self._noise_samples else 0.0
        self._background_noise_level = avg_noise
        
        # Adapt threshold based on noise level
        if self.config.noise_level_adaptation:
            # Higher noise -> higher threshold
            noise_factor = min(2.0, max(0.5, 1.0 + avg_noise * 2.0))
            adapted_threshold = self.config.threshold * noise_factor
        else:
            adapted_threshold = self.config.threshold
        
        # Smooth threshold changes
        adaptation_rate = self.config.threshold_adaptation_rate
        self._current_threshold = (
            (1 - adaptation_rate) * self._current_threshold +
            adaptation_rate * adapted_threshold
        )
        
        # Keep threshold in reasonable bounds
        self._current_threshold = max(0.1, min(0.9, self._current_threshold))
        
        # Track threshold history
        self._threshold_history.append(self._current_threshold)
        if len(self._threshold_history) > 100:
            self._threshold_history = self._threshold_history[-100:]
        
        return self._current_threshold
    
    async def detect_voice_activity(self, audio_chunk: AudioChunk) -> float:
        """
        Detect voice activity in audio chunk.
        
        Args:
            audio_chunk: Audio chunk to analyze
            
        Returns:
            Voice activity probability (0.0 to 1.0)
        """
        if self.model is None:
            raise AudioProcessingError("SileroVAD model not initialized")
        
        start_time = datetime.now()
        
        try:
            # Preprocess audio
            audio_tensor = self._preprocess_audio(audio_chunk)
            
            # Estimate noise level
            noise_level = self._estimate_noise_level(audio_tensor)
            
            # Run VAD model
            with self._model_lock:
                # SileroVAD expects audio at 16kHz
                confidence = self.model(audio_tensor, self.config.sample_rate).item()
            
            # Update adaptive threshold
            current_threshold = self._update_adaptive_threshold(confidence, noise_level)
            
            # Update statistics
            self._detection_count += 1
            if confidence > current_threshold:
                self._speech_detected_count += 1
            
            # Track processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self._processing_times.append(processing_time)
            if len(self._processing_times) > 1000:
                self._processing_times = self._processing_times[-1000:]
            
            # Log detailed info periodically
            if self._detection_count % 100 == 0:
                self._log.debug(f"VAD Stats: confidence={confidence:.3f}, "
                              f"threshold={current_threshold:.3f}, "
                              f"noise={noise_level:.3f}, "
                              f"processing_time={processing_time:.1f}ms")
            
            return confidence
            
        except Exception as e:
            self._log.error(f"Error in voice activity detection: {e}")
            raise AudioProcessingError(f"VAD detection failed: {e}")
    
    async def detect_voice_activity_detailed(self, audio_chunk: AudioChunk) -> VADResult:
        """
        Detect voice activity with detailed result information.
        
        Args:
            audio_chunk: Audio chunk to analyze
            
        Returns:
            Detailed VAD result
        """
        confidence = await self.detect_voice_activity(audio_chunk)
        
        has_speech = confidence > self._current_threshold
        
        return VADResult(
            has_speech=has_speech,
            confidence=confidence,
            timestamp=datetime.now(),
            background_noise_level=self._background_noise_level,
            adapted_threshold=self._current_threshold
        )
    
    async def segment_speech(self, audio_stream) -> List[AudioChunk]:
        """
        Segment continuous speech from audio stream.
        
        Args:
            audio_stream: Async generator of audio chunks
            
        Yields:
            Speech segments as audio chunks
        """
        speech_buffer = []
        silence_counter = 0
        speech_active = False
        
        min_speech_samples = int(
            self.config.min_speech_duration_ms * self.config.sample_rate / 1000
        )
        min_silence_samples = int(
            self.config.min_silence_duration_ms * self.config.sample_rate / 1000
        )
        
        async for audio_chunk in audio_stream:
            try:
                # Detect voice activity
                vad_result = await self.detect_voice_activity_detailed(audio_chunk)
                
                if vad_result.has_speech:
                    # Speech detected
                    if not speech_active:
                        # Start of speech
                        speech_active = True
                        speech_buffer = [audio_chunk]
                        self._log.debug("Speech segment started")
                    else:
                        # Continue speech
                        speech_buffer.append(audio_chunk)
                    
                    silence_counter = 0
                
                else:
                    # No speech detected
                    if speech_active:
                        silence_counter += len(audio_chunk.data) // 2  # Assuming 16-bit samples
                        
                        # Add to buffer during silence (for padding)
                        speech_buffer.append(audio_chunk)
                        
                        # Check if silence is long enough to end speech segment
                        if silence_counter >= min_silence_samples:
                            # End of speech segment
                            if len(speech_buffer) > 0:
                                # Check if speech segment is long enough
                                total_samples = sum(len(chunk.data) // 2 for chunk in speech_buffer)
                                
                                if total_samples >= min_speech_samples:
                                    # Yield speech segment
                                    yield speech_buffer
                                    self._log.debug(f"Speech segment ended: {total_samples} samples")
                                else:
                                    self._log.debug(f"Speech segment too short: {total_samples} samples")
                            
                            # Reset for next segment
                            speech_buffer = []
                            speech_active = False
                            silence_counter = 0
                
            except Exception as e:
                self._log.error(f"Error in speech segmentation: {e}")
                # Continue processing despite errors
                continue
    
    def get_detection_stats(self) -> Dict[str, Any]:
        """
        Get voice activity detection statistics.
        
        Returns:
            Dictionary with detection statistics
        """
        if self._detection_count == 0:
            return {
                "total_detections": 0,
                "speech_detected": 0,
                "speech_rate": 0.0,
                "average_processing_time_ms": 0.0,
                "current_threshold": self._current_threshold,
                "background_noise_level": self._background_noise_level
            }
        
        speech_rate = self._speech_detected_count / self._detection_count
        avg_processing_time = np.mean(self._processing_times) if self._processing_times else 0.0
        
        return {
            "total_detections": self._detection_count,
            "speech_detected": self._speech_detected_count,
            "speech_rate": speech_rate,
            "average_processing_time_ms": avg_processing_time,
            "current_threshold": self._current_threshold,
            "background_noise_level": self._background_noise_level,
            "threshold_history": self._threshold_history[-10:],  # Last 10 values
            "adaptive_threshold_enabled": self.config.enable_adaptive_threshold
        }
    
    def reset_stats(self) -> None:
        """Reset detection statistics."""
        self._detection_count = 0
        self._speech_detected_count = 0
        self._processing_times.clear()
        self._speech_history.clear()
        self._noise_samples.clear()
        self._threshold_history = [self.config.threshold]
        self._current_threshold = self.config.threshold
        self._background_noise_level = 0.0
        
        self._log.info("VAD statistics reset")
    
    def update_threshold(self, new_threshold: float) -> None:
        """
        Update voice activity detection threshold.
        
        Args:
            new_threshold: New threshold value (0.0 to 1.0)
        """
        if not 0.0 <= new_threshold <= 1.0:
            raise ValueError(f"Threshold must be between 0.0 and 1.0, got {new_threshold}")
        
        old_threshold = self._current_threshold
        self._current_threshold = new_threshold
        
        self._log.info(f"VAD threshold updated: {old_threshold:.3f} -> {new_threshold:.3f}")
    
    @property
    def current_threshold(self) -> float:
        """Get current detection threshold."""
        return self._current_threshold
    
    @property
    def background_noise_level(self) -> float:
        """Get current background noise level estimate."""
        return self._background_noise_level
    
    @property
    def is_adaptive_threshold_enabled(self) -> bool:
        """Check if adaptive threshold is enabled."""
        return self.config.enable_adaptive_threshold


# Utility functions for VAD

async def create_vad_detector(
    model_path: str = "models/snakers4_silero-vad",
    threshold: float = 0.5,
    config_manager=None,
    **kwargs
) -> SileroVADDetector:
    """
    Create and initialize a SileroVAD detector.
    
    Args:
        model_path: Path to SileroVAD model
        threshold: Voice activity threshold
        config_manager: Configuration manager instance
        **kwargs: Additional configuration parameters
        
    Returns:
        Initialized VAD detector
    """
    config = VADConfig(
        model_path=model_path,
        threshold=threshold,
        **kwargs
    )
    
    detector = SileroVADDetector(config, config_manager)
    await detector.initialize()
    await detector.start()
    
    return detector


def calculate_speech_statistics(vad_results: List[VADResult]) -> Dict[str, float]:
    """
    Calculate speech statistics from VAD results.
    
    Args:
        vad_results: List of VAD results
        
    Returns:
        Dictionary with speech statistics
    """
    if not vad_results:
        return {
            "total_frames": 0,
            "speech_frames": 0,
            "speech_ratio": 0.0,
            "average_confidence": 0.0,
            "max_confidence": 0.0,
            "min_confidence": 0.0
        }
    
    total_frames = len(vad_results)
    speech_frames = sum(1 for result in vad_results if result.has_speech)
    confidences = [result.confidence for result in vad_results]
    
    return {
        "total_frames": total_frames,
        "speech_frames": speech_frames,
        "speech_ratio": speech_frames / total_frames,
        "average_confidence": np.mean(confidences),
        "max_confidence": np.max(confidences),
        "min_confidence": np.min(confidences)
    }