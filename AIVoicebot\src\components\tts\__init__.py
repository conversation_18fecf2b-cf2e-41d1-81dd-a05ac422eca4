"""Text-to-speech components for the AI Voice Customer Service system."""

from .edge_tts_generator import (
    EdgeTTSGenerator, EdgeTTSConfig, TTSResult, TTSVoice,
    VoiceGender, AudioFormat, create_edge_tts_generator,
    get_telephony_optimized_config, get_high_quality_config,
    create_customer_service_voice_config, select_best_chinese_voice,
    recommend_voice_for_banking, get_voice_parameters_for_context
)
from .voice_manager import (
    VoiceManager, VoiceManagerConfig, VoiceProfile, VoicePersonality,
    SpeechContext, CompressionLevel, AudioOptimizationSettings,
    create_voice_manager, create_telephony_optimization,
    create_streaming_optimization, create_high_quality_optimization,
    create_custom_voice_profile
)

__all__ = [
    # EdgeTTS generator
    'EdgeTTSGenerator',
    'EdgeTTSConfig',
    'TTSResult',
    'TTSVoice',
    'VoiceGender',
    'AudioFormat',
    
    # Voice manager
    'VoiceManager',
    'VoiceManagerConfig',
    'VoiceProfile',
    'VoicePersonality',
    'SpeechContext',
    'CompressionLevel',
    'AudioOptimizationSettings',
    
    # Utility functions
    'create_edge_tts_generator',
    'get_telephony_optimized_config',
    'get_high_quality_config',
    'create_customer_service_voice_config',
    'select_best_chinese_voice',
    'recommend_voice_for_banking',
    'get_voice_parameters_for_context',
    'create_voice_manager',
    'create_telephony_optimization',
    'create_streaming_optimization',
    'create_high_quality_optimization',
    'create_custom_voice_profile',
]