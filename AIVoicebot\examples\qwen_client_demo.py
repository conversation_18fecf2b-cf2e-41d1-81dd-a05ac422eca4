"""
Demo script for Qwen API client.

This script demonstrates how to use the Qwen client for language model integration,
including basic chat, conversation history, and error handling.
"""

import asyncio
import logging
import os
import sys
from typing import List, Dict

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.llm.qwen_client import (
    QwenLLMClient, QwenClientConfig, QwenMessage, QwenModelType,
    create_qwen_client, create_conversation_messages
)
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_basic_chat():
    """Demonstrate basic chat functionality."""
    logger.info("=== Basic Chat Demo ===")
    
    # Note: Replace with actual API key for real testing
    api_key = os.getenv("QWEN_API_KEY", "demo_key_replace_with_real")
    
    if api_key == "demo_key_replace_with_real":
        logger.warning("Using demo API key - replace with real key for actual testing")
        return
    
    try:
        # Create Qwen client
        client = await create_qwen_client(
            api_key=api_key,
            model=QwenModelType.QWEN_TURBO,
            max_tokens=1000,
            temperature=0.7,
            config_manager=Mock()
        )
        
        # Simple chat
        system_message = "你是一个专业的客服助手，请用中文回答用户问题。"
        user_query = "你好，我想了解一下贷款产品。"
        
        logger.info(f"User: {user_query}")
        
        response = await client.generate_simple_response(
            prompt=user_query,
            system_message=system_message
        )
        
        if response.is_success:
            logger.info(f"Assistant: {response.text}")
            logger.info(f"Tokens used: {response.total_tokens}")
            logger.info(f"Response time: {response.response_time_ms:.2f}ms")
        else:
            logger.error(f"Error: {response.error_message}")
        
        # Show client statistics
        stats = client.get_client_stats()
        logger.info(f"Client stats: {stats}")
        
        await client.stop()
        await client.cleanup()
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


async def demo_conversation_history():
    """Demonstrate conversation with history."""
    logger.info("=== Conversation History Demo ===")
    
    # Mock client for demo (replace with real client for actual testing)
    config = QwenClientConfig(
        api_key="demo_key",
        model=QwenModelType.QWEN_TURBO
    )
    
    client = QwenLLMClient(config, Mock())
    
    # Simulate conversation history
    system_prompt = "你是一个银行客服助手，专门帮助客户了解贷款产品。"
    
    conversation_history = [
        {"user": "你好", "assistant": "您好！欢迎咨询我们的贷款服务。"},
        {"user": "我想了解个人贷款", "assistant": "我们有多种个人贷款产品，包括信用贷款、抵押贷款等。"}
    ]
    
    current_query = "利率是多少？"
    
    # Create conversation messages
    messages = create_conversation_messages(
        system_prompt, conversation_history, current_query
    )
    
    logger.info("Conversation messages:")
    for i, msg in enumerate(messages):
        logger.info(f"  {i+1}. {msg.role}: {msg.content}")
    
    # Validate messages (would normally make API call here)
    try:
        client._validate_messages(messages)
        logger.info("Messages validation passed")
        
        # Prepare request (would normally send to API)
        request_data = client._prepare_request(messages)
        logger.info(f"Request prepared with model: {request_data['model']}")
        logger.info(f"Message count: {len(request_data['input']['messages'])}")
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")


async def demo_error_handling():
    """Demonstrate error handling and retry logic."""
    logger.info("=== Error Handling Demo ===")
    
    # Create client with short timeouts for demo
    config = QwenClientConfig(
        api_key="invalid_key",
        max_retries=2,
        retry_delay=0.5,
        request_timeout=1.0
    )
    
    client = QwenLLMClient(config, Mock())
    
    try:
        await client.initialize()
        await client.start()
        
        # This would fail with invalid API key
        messages = [QwenMessage(role="user", content="Hello")]
        
        logger.info("Attempting request with invalid API key...")
        response = await client.generate_response(messages)
        
        if not response.is_success:
            logger.info(f"Expected error: {response.error_message}")
        
        # Show error statistics
        stats = client.get_client_stats()
        logger.info(f"Failed requests: {stats['failed_requests']}")
        logger.info(f"Circuit breaker open: {stats['circuit_breaker_open']}")
        
        await client.stop()
        await client.cleanup()
        
    except Exception as e:
        logger.info(f"Expected exception: {e}")


async def demo_rate_limiting():
    """Demonstrate rate limiting functionality."""
    logger.info("=== Rate Limiting Demo ===")
    
    # Create client with low rate limit
    config = QwenClientConfig(
        api_key="demo_key",
        requests_per_minute=3,
        enable_rate_limiting=True
    )
    
    client = QwenLLMClient(config, Mock())
    
    logger.info("Testing rate limiting with 3 requests per minute...")
    
    # Simulate multiple rapid requests
    for i in range(5):
        start_time = asyncio.get_event_loop().time()
        
        await client._apply_rate_limiting()
        
        duration = asyncio.get_event_loop().time() - start_time
        logger.info(f"Request {i+1}: {duration:.3f}s delay")
    
    # Show rate limiting stats
    stats = client.get_client_stats()
    logger.info(f"Rate limit active requests: {stats['rate_limit_active']}")


async def demo_circuit_breaker():
    """Demonstrate circuit breaker functionality."""
    logger.info("=== Circuit Breaker Demo ===")
    
    client = QwenLLMClient(QwenClientConfig(api_key="demo"), Mock())
    
    logger.info("Simulating consecutive failures...")
    
    # Simulate failures
    for i in range(6):
        client._handle_request_error(Exception(f"Simulated error {i+1}"))
        
        is_open = client._is_circuit_breaker_open()
        logger.info(f"After failure {i+1}: Circuit breaker open = {is_open}")
    
    # Show circuit breaker stats
    stats = client.get_client_stats()
    logger.info(f"Circuit breaker failures: {stats['circuit_breaker_failures']}")
    logger.info(f"Circuit breaker open: {stats['circuit_breaker_open']}")


async def main():
    """Run all demos."""
    logger.info("Starting Qwen Client Demo")
    
    try:
        # Run demos (comment out basic_chat if no API key)
        # await demo_basic_chat()
        await demo_conversation_history()
        await demo_error_handling()
        await demo_rate_limiting()
        await demo_circuit_breaker()
        
        logger.info("All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())