# Development environment configuration
# Overrides default configuration for development environment

# Development-specific logging
logging:
  level: DEBUG
  enable_console: true
  enable_file: true
  enable_json: false

# Development audio settings
audio:
  # Smaller chunks for faster development iteration
  chunk_size: 512
  buffer_size: 2048

# Development model settings
models:
  # Use CPU for development if no GPU available
  device: cpu
  batch_size: 1

# Development Qwen settings
qwen:
  # Lower timeout for faster development feedback
  timeout: 15
  max_retries: 2
  temperature: 0.8  # Slightly more creative for testing

# Development telephony settings
telephony:
  # Reduced concurrent calls for development
  max_concurrent_calls: 3
  call_timeout: 120  # 2 minutes for testing

# Development conversation settings
conversation:
  # Shorter conversations for testing
  max_conversation_turns: 20
  response_timeout: 15
  
  # More frequent script reloading for development
  auto_reload_scripts: true
  script_check_interval: 10  # Check every 10 seconds

# Development performance settings
performance:
  # Smaller buffers for development
  max_audio_buffer_size: 524288  # 512KB
  audio_processing_threads: 1
  
  # More lenient thresholds for development
  vad_threshold: 0.3
  speech_timeout: 5.0
  silence_timeout: 2.0
  
  # Shorter timeouts for faster iteration
  llm_timeout: 10.0
  tts_timeout: 8.0
  
  # More frequent cleanup for development
  cleanup_interval: 60  # 1 minute

# Development monitoring
monitoring:
  enable_metrics: true
  metrics_port: 8081  # Different port to avoid conflicts
  health_check_interval: 10
  
  # More lenient thresholds for development
  max_response_time: 10.0
  max_error_rate: 0.1

# Development-specific settings
development:
  debug_mode: true
  mock_telephony: true  # Use mock telephony for development
  mock_models: false    # Use real models unless specified
  save_audio_files: true  # Save audio for debugging
  audio_debug_dir: debug/audio

# Relaxed security for development
security:
  # Higher rate limits for development testing
  rate_limit_calls_per_minute: 120
  rate_limit_calls_per_hour: 2000
  
  # Shorter retention for development
  conversation_retention_days: 7
  audio_retention_days: 3
  log_retention_days: 30