#!/usr/bin/env python3
"""
Quick Voice AI Test - Diagnose conversation test issues
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_voice_info():
    """Test voice info endpoint."""
    print("🔍 Testing Voice Info...")
    try:
        response = requests.get(f"{BASE_URL}/voice/info", timeout=5)
        response.raise_for_status()
        data = response.json()
        print("✅ Voice Info successful!")
        print(f"   Voice Processor Running: {data['voice_processor']['status']['is_running']}")
        print(f"   Conversation Engine Running: {data['conversation_engine']['llm_provider']}")
        return True
    except Exception as e:
        print(f"❌ Voice Info failed: {e}")
        return False

def test_conversation_simple():
    """Test conversation endpoint with timeout."""
    print("🗣️ Testing Voice Conversation (with timeout)...")
    try:
        response = requests.post(f"{BASE_URL}/voice/test-conversation", timeout=10)
        response.raise_for_status()
        data = response.json()
        print("✅ Voice Conversation successful!")
        print(f"   Success: {data.get('success')}")
        print(f"   Test Call ID: {data.get('test_call_id')}")
        print(f"   Total Exchanges: {data.get('total_exchanges', 0)}")
        return True
    except requests.exceptions.Timeout:
        print("⏰ Voice Conversation test timed out (>10s)")
        return False
    except Exception as e:
        print(f"❌ Voice Conversation failed: {e}")
        return False

def main():
    print("🚀 Quick Voice AI Test")
    print("=" * 30)
    
    # Test 1: Voice Info
    info_ok = test_voice_info()
    time.sleep(1)
    
    # Test 2: Conversation (with timeout)
    conv_ok = test_conversation_simple()
    
    print("\n📊 Results:")
    print(f"   Voice Info: {'✅' if info_ok else '❌'}")
    print(f"   Conversation: {'✅' if conv_ok else '❌'}")
    
    if info_ok and conv_ok:
        print("\n🎉 All tests passed! Voice AI is working!")
    elif info_ok:
        print("\n⚠️ Voice AI is running but conversation test has issues")
    else:
        print("\n❌ Voice AI has issues")

if __name__ == "__main__":
    main()
