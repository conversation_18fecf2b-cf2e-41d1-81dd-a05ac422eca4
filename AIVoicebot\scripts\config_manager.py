#!/usr/bin/env python3
"""
Configuration Manager for AI Voice Customer Service System

This script manages configuration across different environments and
provides utilities for configuration validation and deployment.
"""

import os
import sys
import yaml
import json
import argparse
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging
from dataclasses import dataclass
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))


@dataclass
class ConfigValidationResult:
    """Configuration validation result."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    environment: str


class ConfigurationManager:
    """Configuration manager for deployment environments."""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.environments_dir = self.config_dir / "environments"
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Supported environments
        self.supported_environments = ["development", "staging", "production"]
        
        # Required configuration keys for validation
        self.required_keys = [
            "app.name",
            "app.environment",
            "server.host",
            "server.port",
            "database.url",
            "redis.url",
            "logging.level"
        ]
        
        # Sensitive keys that should be environment variables
        self.sensitive_keys = [
            "app.secret_key",
            "database.url",
            "redis.url",
            "llm.api_key",
            "security.jwt_secret"
        ]
    
    def load_config(self, environment: str) -> Dict[str, Any]:
        """Load configuration for specified environment."""
        if environment not in self.supported_environments:
            raise ValueError(f"Unsupported environment: {environment}")
        
        config_file = self.environments_dir / f"{environment}.yml"
        
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_file}")
        
        self.logger.info(f"Loading configuration for {environment} environment")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Resolve environment variables
        config = self._resolve_environment_variables(config)
        
        return config
    
    def _resolve_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve environment variables in configuration."""
        def resolve_value(value):
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                env_var = value[2:-1]
                default_value = None
                
                # Handle default values: ${VAR_NAME:default_value}
                if ":" in env_var:
                    env_var, default_value = env_var.split(":", 1)
                
                return os.getenv(env_var, default_value)
            elif isinstance(value, dict):
                return {k: resolve_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [resolve_value(item) for item in value]
            else:
                return value
        
        return resolve_value(config)
    
    def validate_config(self, environment: str) -> ConfigValidationResult:
        """Validate configuration for specified environment."""
        self.logger.info(f"Validating configuration for {environment} environment")
        
        errors = []
        warnings = []
        
        try:
            config = self.load_config(environment)
        except Exception as e:
            return ConfigValidationResult(
                is_valid=False,
                errors=[f"Failed to load configuration: {str(e)}"],
                warnings=[],
                environment=environment
            )
        
        # Check required keys
        for key in self.required_keys:
            if not self._get_nested_value(config, key):
                errors.append(f"Missing required configuration key: {key}")
        
        # Check sensitive keys
        for key in self.sensitive_keys:
            value = self._get_nested_value(config, key)
            if value and not (isinstance(value, str) and value.startswith("${")):
                if environment == "production":
                    errors.append(f"Sensitive key '{key}' should use environment variable in production")
                else:
                    warnings.append(f"Sensitive key '{key}' should use environment variable")
        
        # Environment-specific validations
        if environment == "production":
            # Production-specific checks
            if self._get_nested_value(config, "app.debug"):
                errors.append("Debug mode should be disabled in production")
            
            if self._get_nested_value(config, "logging.level") == "DEBUG":
                warnings.append("Debug logging level in production may impact performance")
            
            if not self._get_nested_value(config, "security.cors.enabled"):
                warnings.append("CORS should be properly configured in production")
        
        # Database URL validation
        db_url = self._get_nested_value(config, "database.url")
        if db_url and not self._validate_database_url(db_url):
            errors.append("Invalid database URL format")
        
        # Redis URL validation
        redis_url = self._get_nested_value(config, "redis.url")
        if redis_url and not self._validate_redis_url(redis_url):
            errors.append("Invalid Redis URL format")
        
        return ConfigValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            environment=environment
        )
    
    def _get_nested_value(self, config: Dict[str, Any], key: str) -> Any:
        """Get nested configuration value using dot notation."""
        keys = key.split(".")
        value = config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        
        return value
    
    def _validate_database_url(self, url: str) -> bool:
        """Validate database URL format."""
        if not url or not isinstance(url, str):
            return False
        
        # Basic validation for PostgreSQL URL
        return url.startswith(("postgresql://", "postgres://")) and "@" in url
    
    def _validate_redis_url(self, url: str) -> bool:
        """Validate Redis URL format."""
        if not url or not isinstance(url, str):
            return False
        
        return url.startswith("redis://")
    
    def generate_env_file(self, environment: str, output_file: str = None) -> str:
        """Generate .env file for specified environment."""
        config = self.load_config(environment)
        
        if output_file is None:
            output_file = self.project_root / f".env.{environment}"
        
        env_vars = []
        env_vars.append(f"# AI Voice Customer Service System - {environment.title()} Environment")
        env_vars.append(f"# Generated on {datetime.now().isoformat()}")
        env_vars.append("")
        
        # Extract environment variables from config
        sensitive_values = {}
        for key in self.sensitive_keys:
            value = self._get_nested_value(config, key)
            if value:
                env_key = key.upper().replace(".", "_")
                if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                    # Already an environment variable reference
                    env_var = value[2:-1].split(":")[0]
                    env_vars.append(f"{env_var}=your_{env_var.lower()}_here")
                else:
                    env_vars.append(f"{env_key}={value}")
        
        # Add common environment variables
        env_vars.extend([
            "",
            "# Application Configuration",
            f"ENVIRONMENT={environment}",
            f"LOG_LEVEL={self._get_nested_value(config, 'logging.level') or 'INFO'}",
            "",
            "# Database Configuration",
            "DATABASE_URL=postgresql://user:password@localhost:5432/aivoice",
            "",
            "# Redis Configuration", 
            "REDIS_URL=redis://localhost:6379",
            "",
            "# External API Keys",
            "OPENAI_API_KEY=your_openai_api_key_here",
            "",
            "# Security",
            "SECRET_KEY=your_secret_key_here",
            "JWT_SECRET=your_jwt_secret_here",
            ""
        ])
        
        env_content = "\n".join(env_vars)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        self.logger.info(f"Generated .env file: {output_file}")
        return str(output_file)
    
    def compare_configs(self, env1: str, env2: str) -> Dict[str, Any]:
        """Compare configurations between two environments."""
        config1 = self.load_config(env1)
        config2 = self.load_config(env2)
        
        differences = {
            "only_in_env1": [],
            "only_in_env2": [],
            "different_values": [],
            "same_values": []
        }
        
        all_keys = set()
        self._collect_keys(config1, "", all_keys)
        self._collect_keys(config2, "", all_keys)
        
        for key in all_keys:
            value1 = self._get_nested_value(config1, key)
            value2 = self._get_nested_value(config2, key)
            
            if value1 is None and value2 is not None:
                differences["only_in_env2"].append({"key": key, "value": value2})
            elif value1 is not None and value2 is None:
                differences["only_in_env1"].append({"key": key, "value": value1})
            elif value1 != value2:
                differences["different_values"].append({
                    "key": key,
                    "env1_value": value1,
                    "env2_value": value2
                })
            else:
                differences["same_values"].append({"key": key, "value": value1})
        
        return differences
    
    def _collect_keys(self, config: Dict[str, Any], prefix: str, keys: set):
        """Recursively collect all configuration keys."""
        for key, value in config.items():
            full_key = f"{prefix}.{key}" if prefix else key
            keys.add(full_key)
            
            if isinstance(value, dict):
                self._collect_keys(value, full_key, keys)
    
    def export_config(self, environment: str, format: str = "json", output_file: str = None) -> str:
        """Export configuration in specified format."""
        config = self.load_config(environment)
        
        if output_file is None:
            output_file = self.project_root / f"config_{environment}.{format}"
        
        if format.lower() == "json":
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        elif format.lower() == "yaml":
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        self.logger.info(f"Exported {environment} configuration to {output_file}")
        return str(output_file)


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="AI Voice Customer Service Configuration Manager")
    parser.add_argument("--project-root", help="Project root directory")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate configuration")
    validate_parser.add_argument("environment", choices=["development", "staging", "production"])
    
    # Generate env file command
    env_parser = subparsers.add_parser("generate-env", help="Generate .env file")
    env_parser.add_argument("environment", choices=["development", "staging", "production"])
    env_parser.add_argument("--output", help="Output file path")
    
    # Compare command
    compare_parser = subparsers.add_parser("compare", help="Compare configurations")
    compare_parser.add_argument("env1", choices=["development", "staging", "production"])
    compare_parser.add_argument("env2", choices=["development", "staging", "production"])
    
    # Export command
    export_parser = subparsers.add_parser("export", help="Export configuration")
    export_parser.add_argument("environment", choices=["development", "staging", "production"])
    export_parser.add_argument("--format", choices=["json", "yaml"], default="json")
    export_parser.add_argument("--output", help="Output file path")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    config_manager = ConfigurationManager(args.project_root)
    
    try:
        if args.command == "validate":
            result = config_manager.validate_config(args.environment)
            
            print(f"Configuration validation for {args.environment}:")
            print(f"Valid: {'✓' if result.is_valid else '✗'}")
            
            if result.errors:
                print("\nErrors:")
                for error in result.errors:
                    print(f"  ✗ {error}")
            
            if result.warnings:
                print("\nWarnings:")
                for warning in result.warnings:
                    print(f"  ⚠ {warning}")
            
            if result.is_valid:
                print("\n✓ Configuration is valid!")
            else:
                print(f"\n✗ Configuration has {len(result.errors)} error(s)")
                sys.exit(1)
        
        elif args.command == "generate-env":
            output_file = config_manager.generate_env_file(args.environment, args.output)
            print(f"Generated .env file: {output_file}")
        
        elif args.command == "compare":
            differences = config_manager.compare_configs(args.env1, args.env2)
            
            print(f"Configuration comparison: {args.env1} vs {args.env2}")
            
            if differences["different_values"]:
                print(f"\nDifferent values ({len(differences['different_values'])}):")
                for diff in differences["different_values"]:
                    print(f"  {diff['key']}: {diff['env1_value']} → {diff['env2_value']}")
            
            if differences["only_in_env1"]:
                print(f"\nOnly in {args.env1} ({len(differences['only_in_env1'])}):")
                for item in differences["only_in_env1"]:
                    print(f"  {item['key']}: {item['value']}")
            
            if differences["only_in_env2"]:
                print(f"\nOnly in {args.env2} ({len(differences['only_in_env2'])}):")
                for item in differences["only_in_env2"]:
                    print(f"  {item['key']}: {item['value']}")
        
        elif args.command == "export":
            output_file = config_manager.export_config(args.environment, args.format, args.output)
            print(f"Exported configuration: {output_file}")
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
