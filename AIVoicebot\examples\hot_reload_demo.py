"""
Demo script for hot-reloading functionality.

This script demonstrates how to use the file watcher and script manager
for automatic reloading of Excel conversation scripts.
"""

import asyncio
import logging
import tempfile
import pandas as pd
from pathlib import Path
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.components.scripts import (
    ScriptManager, ScriptManagerConfig,
    FileWatcherConfig, ScriptParserConfig, ResponseMatcherConfig
)
from unittest.mock import Mock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def create_sample_excel_file(file_path: Path) -> None:
    """Create a sample Excel file with conversation scripts."""
    data = pd.DataFrame({
        "用户问题": [
            "你好",
            "我想了解贷款",
            "利率是多少",
            "如何申请"
        ],
        "回复内容": [
            "您好！欢迎咨询我们的服务。",
            "我们提供多种贷款产品，包括个人贷款和企业贷款。",
            "我们的利率根据您的信用状况而定，一般在3.5%-8%之间。",
            "您可以通过我们的官网或手机APP进行在线申请。"
        ],
        "意图分类": [
            "问候",
            "产品咨询",
            "利率咨询",
            "申请流程"
        ],
        "优先级": [1, 2, 2, 1]
    })
    
    data.to_excel(file_path, index=False)
    logger.info(f"Created sample Excel file: {file_path}")


async def update_excel_file(file_path: Path) -> None:
    """Update the Excel file with new content."""
    data = pd.DataFrame({
        "用户问题": [
            "你好",
            "我想了解贷款",
            "利率是多少",
            "如何申请",
            "营业时间"  # New entry
        ],
        "回复内容": [
            "您好！欢迎咨询我们的服务。",
            "我们提供多种贷款产品，包括个人贷款和企业贷款。",
            "我们的利率根据您的信用状况而定，一般在3.5%-8%之间。",
            "您可以通过我们的官网或手机APP进行在线申请。",
            "我们的营业时间是周一到周五9:00-17:00。"  # New entry
        ],
        "意图分类": [
            "问候",
            "产品咨询",
            "利率咨询",
            "申请流程",
            "营业时间咨询"  # New entry
        ],
        "优先级": [1, 2, 2, 1, 1]
    })
    
    data.to_excel(file_path, index=False)
    logger.info(f"Updated Excel file: {file_path}")


async def test_script_responses(manager: ScriptManager, queries: list) -> None:
    """Test script responses for given queries."""
    logger.info("Testing script responses:")
    
    for query in queries:
        response = await manager.find_response(query)
        if response:
            logger.info(f"  Query: '{query}' -> Response: '{response.text[:50]}...'")
        else:
            logger.info(f"  Query: '{query}' -> No response found")


async def hot_reload_demo():
    """Demonstrate hot-reload functionality."""
    logger.info("Starting hot-reload demo...")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        excel_file = temp_path / "conversation_scripts.xlsx"
        
        # Create initial Excel file
        await create_sample_excel_file(excel_file)
        
        # Create script manager with hot-reload enabled
        config_manager = Mock()
        
        # Configure components
        parser_config = ScriptParserConfig(
            script_directory=str(temp_path),
            user_input_column="用户问题",
            response_column="回复内容",
            category_column="意图分类",
            priority_column="优先级"
        )
        
        matcher_config = ResponseMatcherConfig(
            fuzzy_threshold=0.6,
            enable_fuzzy_matching=True,
            enable_keyword_matching=True
        )
        
        file_watcher_config = FileWatcherConfig(
            watch_directory=str(temp_path),
            debounce_seconds=1.0,  # Short debounce for demo
            batch_changes=True
        )
        
        manager_config = ScriptManagerConfig(
            script_directory=str(temp_path),
            enable_hot_reload=True,
            parser_config=parser_config,
            matcher_config=matcher_config,
            file_watcher_config=file_watcher_config,
            preload_scripts=True
        )
        
        # Create and start script manager
        manager = ScriptManager(manager_config, config_manager)
        await manager.initialize()
        await manager.start()
        
        try:
            # Test initial responses
            test_queries = ["你好", "贷款", "利率", "申请", "营业时间"]
            
            logger.info("=== Initial Script Responses ===")
            await test_script_responses(manager, test_queries)
            
            # Show initial statistics
            stats = manager.get_manager_stats()
            logger.info(f"Initial loaded scripts: {stats['loaded_scripts']}")
            
            # Wait a moment
            await asyncio.sleep(2)
            
            # Update Excel file
            logger.info("=== Updating Excel File ===")
            await update_excel_file(excel_file)
            
            # Wait for hot-reload to trigger
            logger.info("Waiting for hot-reload to trigger...")
            await asyncio.sleep(3)
            
            # Test responses after reload
            logger.info("=== Script Responses After Hot-Reload ===")
            await test_script_responses(manager, test_queries)
            
            # Show updated statistics
            stats = manager.get_manager_stats()
            logger.info(f"Updated loaded scripts: {stats['loaded_scripts']}")
            logger.info(f"Reload count: {stats['reload_count']}")
            
            if stats.get('file_watcher_stats'):
                fw_stats = stats['file_watcher_stats']
                logger.info(f"File watcher events: {fw_stats.get('total_events', 0)}")
                logger.info(f"Processed events: {fw_stats.get('processed_events', 0)}")
            
            # Test manual reload
            logger.info("=== Testing Manual Reload ===")
            await manager.force_reload_all_scripts()
            
            # Show final statistics
            stats = manager.get_manager_stats()
            logger.info(f"Final reload count: {stats['reload_count']}")
            
        finally:
            # Cleanup
            await manager.stop()
            await manager.cleanup()
    
    logger.info("Hot-reload demo completed!")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(hot_reload_demo())