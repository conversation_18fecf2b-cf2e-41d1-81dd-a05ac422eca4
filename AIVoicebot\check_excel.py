#!/usr/bin/env python3
import pandas as pd
import os

files = [
    'docs/言犀复贷话术.xlsx', 
    'docs/零犀复贷AI话术调优240914.xlsx'
]

print("检查Excel文件:")
for f in files:
    if os.path.exists(f):
        print(f"✅ {f}: 存在")
        try:
            excel_file = pd.ExcelFile(f)
            print(f"   工作表: {excel_file.sheet_names}")
            
            # 读取第一个工作表的前几行
            df = pd.read_excel(f, sheet_name=0, nrows=3)
            print(f"   列名: {list(df.columns)}")
            print(f"   行数(样本): {len(df)}")
        except Exception as e:
            print(f"   ❌ 读取错误: {e}")
    else:
        print(f"❌ {f}: 不存在")
