"""
Speech Segmentation Demo

This demo shows how to use the SpeechSegmenter for:
- Identifying speech boundaries from VAD probabilities
- Adaptive threshold adjustment
- Real-time speech segmentation
"""

import sys
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from components.vad.speech_segmenter import (
    SpeechSegmenter,
    SpeechSegment,
    SegmentationConfig,
    SegmentType,
    create_speech_segmenter,
    segment_audio_with_vad
)


def generate_test_vad_data(duration: float = 10.0, sample_rate: int = 16000) -> tuple:
    """Generate synthetic VAD data for testing"""
    # Create time axis
    frame_rate = 100  # 100 frames per second (10ms frames)
    num_frames = int(duration * frame_rate)
    timestamps = np.linspace(0, duration, num_frames)
    
    # Create synthetic VAD probabilities with speech patterns
    vad_probs = np.zeros(num_frames)
    
    # Add speech segments at specific times
    speech_segments = [
        (1.0, 2.5),   # 1.5 seconds of speech
        (3.5, 4.2),   # 0.7 seconds of speech
        (5.0, 7.5),   # 2.5 seconds of speech
        (8.2, 9.0),   # 0.8 seconds of speech
    ]
    
    for start_time, end_time in speech_segments:
        start_idx = int(start_time * frame_rate)
        end_idx = int(end_time * frame_rate)
        
        # Ensure indices are valid
        start_idx = max(0, min(start_idx, num_frames))
        end_idx = max(start_idx, min(end_idx, num_frames))
        
        if end_idx > start_idx:
            # Add speech with some variation and noise
            segment_length = end_idx - start_idx
            speech_prob = 0.8 + 0.15 * np.random.randn(segment_length)
            speech_prob = np.clip(speech_prob, 0.0, 1.0)
            vad_probs[start_idx:end_idx] = speech_prob
    
    # Add background noise
    noise_level = 0.1 + 0.05 * np.random.randn(num_frames)
    noise_level = np.clip(noise_level, 0.0, 0.3)
    
    # Combine speech and noise
    vad_probs = np.maximum(vad_probs, noise_level)
    
    # Generate corresponding audio data
    audio_samples = int(duration * sample_rate)
    audio_data = np.random.randn(audio_samples) * 0.1  # Background noise
    
    # Add synthetic speech signals
    for start_time, end_time in speech_segments:
        start_sample = int(start_time * sample_rate)
        end_sample = int(end_time * sample_rate)
        
        # Ensure indices are valid
        start_sample = max(0, min(start_sample, audio_samples))
        end_sample = max(start_sample, min(end_sample, audio_samples))
        
        if end_sample > start_sample:
            # Generate synthetic speech (mix of tones)
            segment_duration = end_time - start_time
            segment_samples = end_sample - start_sample
            t = np.linspace(0, segment_duration, segment_samples)
            speech_signal = (
                0.3 * np.sin(2 * np.pi * 440 * t) +  # 440 Hz
                0.2 * np.sin(2 * np.pi * 880 * t) +  # 880 Hz
                0.1 * np.sin(2 * np.pi * 220 * t)    # 220 Hz
            )
            
            audio_data[start_sample:end_sample] += speech_signal
    
    return audio_data, vad_probs, timestamps


def demo_basic_segmentation():
    """Demonstrate basic speech segmentation"""
    print("=== Basic Speech Segmentation Demo ===")
    
    # Generate test data
    audio_data, vad_probs, timestamps = generate_test_vad_data(duration=5.0)
    
    print(f"Generated {len(vad_probs)} VAD frames over {timestamps[-1]:.1f} seconds")
    print(f"Audio data: {len(audio_data)} samples")
    
    # Create segmenter
    segmenter = create_speech_segmenter(
        speech_threshold=0.5,
        min_speech_duration=0.2,
        adaptive_threshold=False
    )
    
    print(f"Created segmenter with threshold: {segmenter.config.speech_threshold}")
    
    # Process VAD results
    segments = segmenter.process_vad_results(vad_probs, timestamps)
    
    # Finalize any remaining segment
    final_segment = segmenter.finalize_current_segment()
    if final_segment:
        segments.append(final_segment)
    
    # Display results
    print(f"\nDetected {len(segments)} segments:")
    for i, segment in enumerate(segments):
        print(f"  Segment {i+1}: {segment.start_time:.2f}s - {segment.end_time:.2f}s "
              f"({segment.duration:.2f}s) - {segment.segment_type.value}")
    
    # Get statistics
    stats = segmenter.get_segmentation_stats()
    print(f"\nSegmentation Statistics:")
    print(f"  Total segments: {stats['total_segments']}")
    print(f"  Speech segments: {stats['speech_segments']}")
    print(f"  Total speech time: {stats['total_speech_time']:.2f}s")
    print(f"  Speech ratio: {stats['speech_ratio']:.1%}")


def demo_adaptive_thresholding():
    """Demonstrate adaptive threshold adjustment"""
    print("\n=== Adaptive Thresholding Demo ===")
    
    # Generate test data with varying noise levels
    duration = 8.0
    frame_rate = 100
    num_frames = int(duration * frame_rate)
    timestamps = np.linspace(0, duration, num_frames)
    
    # Create VAD data with changing noise floor
    vad_probs = np.zeros(num_frames)
    
    # Low noise period (0-3s)
    low_noise_frames = int(3 * frame_rate)
    vad_probs[:low_noise_frames] = 0.05 + 0.02 * np.random.randn(low_noise_frames)
    
    # Add speech in low noise
    speech_start = int(1 * frame_rate)
    speech_end = int(2 * frame_rate)
    vad_probs[speech_start:speech_end] = 0.7 + 0.1 * np.random.randn(speech_end - speech_start)
    
    # High noise period (3-6s)
    high_noise_start = int(3 * frame_rate)
    high_noise_end = int(6 * frame_rate)
    vad_probs[high_noise_start:high_noise_end] = 0.25 + 0.05 * np.random.randn(high_noise_end - high_noise_start)
    
    # Add speech in high noise
    speech_start = int(4 * frame_rate)
    speech_end = int(5 * frame_rate)
    vad_probs[speech_start:speech_end] = 0.8 + 0.1 * np.random.randn(speech_end - speech_start)
    
    # Return to low noise (6-8s)
    low_noise_start = int(6 * frame_rate)
    vad_probs[low_noise_start:] = 0.08 + 0.03 * np.random.randn(num_frames - low_noise_start)
    
    # Add final speech
    speech_start = int(7 * frame_rate)
    speech_end = int(7.5 * frame_rate)
    vad_probs[speech_start:speech_end] = 0.75 + 0.1 * np.random.randn(speech_end - speech_start)
    
    # Clip probabilities
    vad_probs = np.clip(vad_probs, 0.0, 1.0)
    
    # Test with and without adaptive thresholding
    configs = [
        ("Fixed Threshold", False),
        ("Adaptive Threshold", True)
    ]
    
    for config_name, adaptive in configs:
        print(f"\n{config_name}:")
        
        segmenter = create_speech_segmenter(
            speech_threshold=0.5,
            adaptive_threshold=adaptive,
            min_speech_duration=0.1
        )
        
        # Process in chunks to show adaptation
        chunk_size = int(1 * frame_rate)  # 1 second chunks
        all_segments = []
        
        for i in range(0, len(vad_probs), chunk_size):
            end_idx = min(i + chunk_size, len(vad_probs))
            chunk_probs = vad_probs[i:end_idx]
            chunk_timestamps = timestamps[i:end_idx]
            
            segments = segmenter.process_vad_results(chunk_probs, chunk_timestamps)
            all_segments.extend(segments)
            
            # Show threshold adaptation
            if adaptive:
                print(f"  Time {timestamps[i]:.1f}s: threshold={segmenter.adaptive_speech_threshold:.3f}, "
                      f"noise_floor={segmenter.noise_floor:.3f}")
        
        # Finalize
        final_segment = segmenter.finalize_current_segment()
        if final_segment:
            all_segments.append(final_segment)
        
        # Show results
        speech_segments = [s for s in all_segments if s.segment_type == SegmentType.SPEECH]
        print(f"  Detected {len(speech_segments)} speech segments:")
        for segment in speech_segments:
            print(f"    {segment.start_time:.2f}s - {segment.end_time:.2f}s ({segment.duration:.2f}s)")


def demo_real_time_segmentation():
    """Demonstrate real-time speech segmentation"""
    print("\n=== Real-time Segmentation Demo ===")
    
    # Generate longer test data
    audio_data, vad_probs, timestamps = generate_test_vad_data(duration=15.0)
    
    # Create segmenter
    segmenter = create_speech_segmenter(
        speech_threshold=0.5,
        min_speech_duration=0.15,
        adaptive_threshold=True
    )
    
    print("Simulating real-time processing...")
    print("Processing 1-second chunks:")
    
    # Process in real-time chunks
    chunk_duration = 1.0  # 1 second chunks
    frame_rate = len(vad_probs) / timestamps[-1]
    chunk_size = int(chunk_duration * frame_rate)
    
    all_segments = []
    
    for i in range(0, len(vad_probs), chunk_size):
        end_idx = min(i + chunk_size, len(vad_probs))
        chunk_probs = vad_probs[i:end_idx]
        chunk_timestamps = timestamps[i:end_idx]
        
        # Process chunk
        segments = segmenter.process_vad_results(chunk_probs, chunk_timestamps)
        all_segments.extend(segments)
        
        # Show real-time status
        current_time = chunk_timestamps[-1] if len(chunk_timestamps) > 0 else 0
        stats = segmenter.get_segmentation_stats()
        
        print(f"  Time {current_time:5.1f}s: "
              f"State={segmenter.current_state.value:7s}, "
              f"Segments={len(all_segments):2d}, "
              f"Speech={stats['total_speech_time']:5.1f}s")
        
        # Show any new segments
        if segments:
            for segment in segments:
                print(f"    -> New segment: {segment.start_time:.2f}s - {segment.end_time:.2f}s "
                      f"({segment.segment_type.value})")
    
    # Finalize
    final_segment = segmenter.finalize_current_segment()
    if final_segment:
        all_segments.append(final_segment)
        print(f"    -> Final segment: {final_segment.start_time:.2f}s - {final_segment.end_time:.2f}s "
              f"({final_segment.segment_type.value})")
    
    # Final statistics
    final_stats = segmenter.get_segmentation_stats()
    print(f"\nFinal Results:")
    print(f"  Total segments: {final_stats['total_segments']}")
    print(f"  Speech segments: {final_stats['speech_segments']}")
    print(f"  Total speech time: {final_stats['total_speech_time']:.2f}s")
    print(f"  Speech ratio: {final_stats['speech_ratio']:.1%}")


def demo_convenience_function():
    """Demonstrate convenience function usage"""
    print("\n=== Convenience Function Demo ===")
    
    # Generate test data
    audio_data, vad_probs, timestamps = generate_test_vad_data(duration=6.0)
    
    print(f"Using segment_audio_with_vad() convenience function")
    print(f"Audio: {len(audio_data)} samples, VAD: {len(vad_probs)} frames")
    
    # Use convenience function
    segments = segment_audio_with_vad(
        audio_data=audio_data,
        vad_probabilities=vad_probs,
        sample_rate=16000,
        speech_threshold=0.6,
        min_speech_duration=0.2,
        adaptive_threshold=True
    )
    
    print(f"\nDetected {len(segments)} segments:")
    
    speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
    print(f"Speech segments: {len(speech_segments)}")
    
    for i, segment in enumerate(speech_segments):
        audio_length = len(segment.audio_data) if segment.audio_data is not None else 0
        print(f"  Segment {i+1}: {segment.start_time:.2f}s - {segment.end_time:.2f}s "
              f"({segment.duration:.2f}s) - {audio_length} audio samples")
        
        # Show audio statistics if available
        if segment.audio_data is not None and len(segment.audio_data) > 0:
            rms = np.sqrt(np.mean(segment.audio_data ** 2))
            peak = np.max(np.abs(segment.audio_data))
            print(f"    Audio RMS: {rms:.4f}, Peak: {peak:.4f}")


def demo_parameter_comparison():
    """Demonstrate effect of different parameters"""
    print("\n=== Parameter Comparison Demo ===")
    
    # Generate test data with challenging conditions
    audio_data, vad_probs, timestamps = generate_test_vad_data(duration=8.0)
    
    # Test different parameter combinations
    parameter_sets = [
        ("Conservative", {"speech_threshold": 0.7, "min_speech_duration": 0.3}),
        ("Balanced", {"speech_threshold": 0.5, "min_speech_duration": 0.15}),
        ("Aggressive", {"speech_threshold": 0.3, "min_speech_duration": 0.05}),
    ]
    
    print("Comparing different parameter settings:")
    
    for name, params in parameter_sets:
        print(f"\n{name} Settings:")
        print(f"  Speech threshold: {params['speech_threshold']}")
        print(f"  Min speech duration: {params['min_speech_duration']}s")
        
        segments = segment_audio_with_vad(
            audio_data=audio_data,
            vad_probabilities=vad_probs,
            sample_rate=16000,
            **params
        )
        
        speech_segments = [s for s in segments if s.segment_type == SegmentType.SPEECH]
        total_speech_time = sum(s.duration for s in speech_segments)
        
        print(f"  Results: {len(speech_segments)} segments, {total_speech_time:.2f}s total speech")
        
        # Show segment details
        for i, segment in enumerate(speech_segments):
            print(f"    {i+1}: {segment.start_time:.2f}s - {segment.end_time:.2f}s ({segment.duration:.2f}s)")


def main():
    """Run all speech segmentation demos"""
    print("AI Voice Customer Service - Speech Segmentation Demo")
    print("=" * 60)
    
    try:
        demo_basic_segmentation()
        demo_adaptive_thresholding()
        demo_real_time_segmentation()
        demo_convenience_function()
        demo_parameter_comparison()
        
        print("\n" + "=" * 60)
        print("Speech Segmentation Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()