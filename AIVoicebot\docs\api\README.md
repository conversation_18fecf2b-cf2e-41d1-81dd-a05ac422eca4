# API 文档

AI语音客服系统提供完整的REST API和WebSocket接口，支持系统管理、通话处理和实时通信。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1
- **内容类型**: `application/json`
- **认证方式**: <PERSON><PERSON> (生产环境)

## 健康检查接口

### GET /health

基础健康检查接口，返回系统基本状态。

**响应示例**:
```json
{
  "status": "healthy",
  "state": "running",
  "uptime": 3600.5,
  "active_calls": 5,
  "components_running": 10,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### GET /health/detailed

详细健康检查接口，返回所有组件状态。

**响应示例**:
```json
{
  "system": {
    "state": "running",
    "uptime": 3600.5,
    "total_calls_handled": 150,
    "active_calls": 5,
    "components_initialized": 10,
    "components_running": 10,
    "components_error": 0
  },
  "components": {
    "extension_manager": {
      "name": "extension_manager",
      "type": "core",
      "initialized": true,
      "running": true
    },
    "call_manager": {
      "name": "call_manager",
      "type": "service",
      "initialized": true,
      "running": true
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 系统状态接口

### GET /status

获取系统综合状态信息。

**响应示例**:
```json
{
  "system_status": {
    "state": "running",
    "uptime": 3600.5,
    "total_calls_handled": 150,
    "active_calls": 5,
    "components_initialized": 10,
    "components_running": 10,
    "components_error": 0,
    "last_updated": "2024-01-15T10:30:00Z"
  },
  "statistics": {
    "extensions": {
      "total_plugins": 3,
      "active_plugins": 3,
      "resource_pools": 3,
      "total_resources": 50,
      "resources_in_use": 15
    },
    "performance": {
      "cpu_utilization": 45.2,
      "memory_utilization": 62.8,
      "response_time_avg": 245.5
    },
    "calls": {
      "total_calls": 150,
      "active_calls": 5,
      "completed_calls": 145,
      "failed_calls": 2,
      "average_duration": 180.5
    }
  }
}
```

## 通话管理接口

### POST /calls/incoming

处理来电请求。

**请求体**:
```json
{
  "caller_number": "+86138****1234",
  "callee_number": "+86400****5678",
  "call_metadata": {
    "source": "phone_system",
    "priority": "normal"
  }
}
```

**响应示例**:
```json
{
  "status": "success",
  "call_id": "call_20240115_103000_001",
  "message": "Call session created successfully"
}
```

### GET /calls/{call_id}/status

获取指定通话的状态信息。

**路径参数**:
- `call_id`: 通话ID

**响应示例**:
```json
{
  "call_id": "call_20240115_103000_001",
  "status": "active",
  "caller_number": "+86138****1234",
  "callee_number": "+86400****5678",
  "start_time": "2024-01-15T10:30:00Z",
  "duration": 120.5,
  "conversation_turns": 8,
  "current_state": "waiting_for_user_input",
  "metadata": {
    "audio_quality": "good",
    "connection_stable": true,
    "last_activity": "2024-01-15T10:32:00Z"
  }
}
```

### POST /calls/{call_id}/hangup

挂断指定通话。

**路径参数**:
- `call_id`: 通话ID

**响应示例**:
```json
{
  "status": "success",
  "call_id": "call_20240115_103000_001",
  "message": "Call ended successfully",
  "final_duration": 180.5
}
```

## 对话管理接口

### GET /conversations/{conversation_id}

获取对话详情。

**路径参数**:
- `conversation_id`: 对话ID

**响应示例**:
```json
{
  "conversation_id": "conv_20240115_103000_001",
  "call_id": "call_20240115_103000_001",
  "status": "active",
  "turns": [
    {
      "turn_id": 1,
      "timestamp": "2024-01-15T10:30:05Z",
      "speaker": "system",
      "content": "您好，欢迎致电客服热线，请问有什么可以帮助您的？",
      "audio_duration": 3.2
    },
    {
      "turn_id": 2,
      "timestamp": "2024-01-15T10:30:10Z",
      "speaker": "user",
      "content": "我想查询我的订单状态",
      "confidence": 0.95,
      "intent": "order_inquiry"
    }
  ],
  "context": {
    "user_intent": "order_inquiry",
    "conversation_stage": "information_gathering",
    "extracted_entities": {
      "order_type": "product_order"
    }
  }
}
```

### POST /conversations/{conversation_id}/message

向对话发送消息。

**路径参数**:
- `conversation_id`: 对话ID

**请求体**:
```json
{
  "content": "我的订单号是12345",
  "message_type": "text",
  "metadata": {
    "confidence": 0.98,
    "audio_duration": 2.1
  }
}
```

**响应示例**:
```json
{
  "status": "success",
  "response": {
    "content": "好的，我来为您查询订单12345的状态，请稍等...",
    "message_type": "text",
    "intent": "order_status_check",
    "next_action": "query_order_system"
  }
}
```

## 系统管理接口

### GET /api/v1/plugins

获取插件列表。

**响应示例**:
```json
{
  "plugins": [
    {
      "name": "example_audio_processor",
      "version": "1.0.0",
      "status": "active",
      "type": "audio_processor",
      "description": "示例音频处理插件"
    }
  ],
  "total": 1,
  "active": 1
}
```

### POST /api/v1/plugins/{plugin_name}/start

启动指定插件。

**路径参数**:
- `plugin_name`: 插件名称

**响应示例**:
```json
{
  "status": "success",
  "plugin_name": "example_audio_processor",
  "message": "Plugin started successfully"
}
```

### GET /api/v1/metrics

获取系统指标。

**查询参数**:
- `hours`: 时间范围（小时），默认1小时
- `component`: 组件名称过滤，可选

**响应示例**:
```json
{
  "time_period_hours": 1,
  "total_data_points": 360,
  "metrics": {
    "cpu_utilization": {
      "count": 60,
      "mean": 45.2,
      "min": 30.1,
      "max": 78.5,
      "latest": 47.3
    },
    "memory_utilization": {
      "count": 60,
      "mean": 62.8,
      "min": 55.2,
      "max": 75.1,
      "latest": 64.2
    },
    "response_time_ms": {
      "count": 150,
      "mean": 245.5,
      "min": 120.0,
      "max": 890.0,
      "latest": 230.0
    }
  },
  "generated_at": "2024-01-15T10:30:00Z"
}
```

## WebSocket 接口

### 连接端点

**URL**: `ws://localhost:8000/ws`

### 消息格式

所有WebSocket消息都使用JSON格式：

```json
{
  "type": "message_type",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 支持的消息类型

#### 1. 心跳检测

**客户端发送**:
```json
{
  "type": "ping",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**服务端响应**:
```json
{
  "type": "pong",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 2. 订阅事件

**客户端发送**:
```json
{
  "type": "subscribe",
  "topic": "call_events"
}
```

**支持的订阅主题**:
- `call_events`: 通话事件
- `system_events`: 系统事件
- `audio_stream`: 音频流
- `performance_metrics`: 性能指标

#### 3. 通话事件通知

**服务端推送**:
```json
{
  "type": "call_event",
  "call_id": "call_20240115_103000_001",
  "event_type": "call_started",
  "data": {
    "caller_number": "+86138****1234",
    "callee_number": "+86400****5678",
    "start_time": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 4. 系统事件通知

**服务端推送**:
```json
{
  "type": "system_event",
  "event_type": "component_status_changed",
  "data": {
    "component": "call_manager",
    "old_status": "starting",
    "new_status": "running"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 5. 音频数据传输

**客户端发送**:
```json
{
  "type": "audio_data",
  "call_id": "call_20240115_103000_001",
  "data": "base64_encoded_audio_data",
  "format": {
    "sample_rate": 16000,
    "channels": 1,
    "bit_depth": 16
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### 常见错误码

- `SYSTEM_NOT_READY`: 系统未就绪
- `CALL_NOT_FOUND`: 通话不存在
- `INVALID_REQUEST`: 请求参数无效
- `INTERNAL_ERROR`: 内部服务器错误
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `AUTHENTICATION_FAILED`: 认证失败
- `PERMISSION_DENIED`: 权限不足

### HTTP状态码

- `200`: 请求成功
- `201`: 资源创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率超限
- `500`: 内部服务器错误
- `503`: 服务不可用

## 使用示例

### Python客户端示例

```python
import requests
import websocket
import json

# REST API调用
def check_system_health():
    response = requests.get("http://localhost:8000/health")
    return response.json()

# WebSocket连接
def connect_websocket():
    def on_message(ws, message):
        data = json.loads(message)
        print(f"Received: {data}")
    
    def on_open(ws):
        # 订阅通话事件
        ws.send(json.dumps({
            "type": "subscribe",
            "topic": "call_events"
        }))
    
    ws = websocket.WebSocketApp(
        "ws://localhost:8000/ws",
        on_message=on_message,
        on_open=on_open
    )
    ws.run_forever()
```

### JavaScript客户端示例

```javascript
// REST API调用
async function checkSystemHealth() {
    const response = await fetch('http://localhost:8000/health');
    return await response.json();
}

// WebSocket连接
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onopen = function() {
    // 订阅系统事件
    ws.send(JSON.stringify({
        type: 'subscribe',
        topic: 'system_events'
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};
```

## 限制和配额

### 请求限制
- **REST API**: 1000 请求/分钟/IP
- **WebSocket**: 10 连接/IP
- **音频上传**: 最大 10MB/请求

### 数据保留
- **通话记录**: 30天
- **性能指标**: 7天
- **错误日志**: 14天
- **音频文件**: 24小时

## 版本兼容性

当前API版本为v1，我们承诺：
- 向后兼容性保证
- 废弃功能提前通知
- 平滑升级路径
- 版本迁移指南
