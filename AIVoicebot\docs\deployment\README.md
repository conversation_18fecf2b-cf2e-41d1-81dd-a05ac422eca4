# 部署指南

本指南详细介绍了AI语音客服系统在不同环境下的部署方法和最佳实践。

## 部署概览

### 支持的部署方式
- **本地开发部署**: 适用于开发和测试
- **Docker容器部署**: 适用于快速部署和测试
- **Docker Compose部署**: 适用于完整系统部署
- **Kubernetes部署**: 适用于生产环境和大规模部署
- **云平台部署**: 支持AWS、Azure、GCP等主流云平台

### 系统要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 100Mbps带宽

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB+ RAM
- **存储**: 50GB+ SSD
- **网络**: 1Gbps带宽
- **GPU**: NVIDIA GPU (可选，用于AI加速)

## 本地开发部署

### 1. 环境准备

```bash
# 安装Python 3.9+
python --version

# 安装Git
git --version

# 安装Docker (可选)
docker --version
```

### 2. 克隆项目

```bash
git clone https://github.com/your-org/ai-voice-customer-service.git
cd ai-voice-customer-service
```

### 3. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/macOS
source venv/bin/activate
# Windows
venv\Scripts\activate
```

### 4. 安装依赖

```bash
# 安装生产依赖
pip install -r requirements.txt

# 安装开发依赖 (可选)
pip install -r requirements-dev.txt
```

### 5. 配置设置

```bash
# 复制配置模板
cp config/environments/development.yml.template config/environments/development.yml

# 编辑配置文件
nano config/environments/development.yml
```

**配置示例**:
```yaml
app:
  name: "AI Voice Customer Service"
  environment: "development"
  debug: true
  secret_key: "${SECRET_KEY:dev-secret-key}"

server:
  host: "0.0.0.0"
  port: 8000
  workers: 1

database:
  url: "${DATABASE_URL:postgresql://user:password@localhost:5432/aivoice_dev}"

redis:
  url: "${REDIS_URL:redis://localhost:6379}"

logging:
  level: "DEBUG"
  file: "logs/app/aivoice.log"
```

### 6. 启动外部服务

```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis

# 或手动启动服务
# PostgreSQL
sudo systemctl start postgresql
# Redis
sudo systemctl start redis
```

### 7. 数据库初始化

```bash
# 运行数据库迁移
python scripts/migrate_database.py

# 创建初始数据 (可选)
python scripts/create_sample_data.py
```

### 8. 启动系统

```bash
# 启动主应用
python src/main.py

# 或使用开发模式
python src/main.py --reload
```

### 9. 验证部署

```bash
# 检查系统健康状态
curl http://localhost:8000/health

# 查看详细状态
curl http://localhost:8000/status
```

## Docker容器部署

### 1. 构建镜像

```bash
# 构建生产镜像
docker build -t aivoice:latest .

# 构建开发镜像
docker build -f Dockerfile.dev -t aivoice:dev .
```

### 2. 运行容器

```bash
# 运行单个容器
docker run -d \
  --name aivoice-app \
  -p 8000:8000 \
  -e DATABASE_URL=************************************/aivoice \
  -e REDIS_URL=redis://host:6379 \
  aivoice:latest

# 查看容器日志
docker logs -f aivoice-app
```

### 3. 容器健康检查

```bash
# 检查容器状态
docker ps

# 进入容器
docker exec -it aivoice-app bash

# 检查应用健康状态
docker exec aivoice-app curl http://localhost:8000/health
```

## Docker Compose部署

### 1. 使用生产配置

```bash
# 启动完整系统
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 2. 扩展服务

```bash
# 扩展应用实例
docker-compose -f docker-compose.prod.yml up -d --scale app=3

# 扩展工作进程
docker-compose -f docker-compose.prod.yml up -d --scale worker=2
```

### 3. 数据持久化

确保数据目录已创建：
```bash
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p logs
```

### 4. 备份和恢复

```bash
# 备份数据库
docker-compose exec postgres pg_dump -U aivoice aivoice > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U aivoice aivoice < backup.sql
```

## Kubernetes部署

### 1. 准备Kubernetes集群

```bash
# 检查集群状态
kubectl cluster-info

# 创建命名空间
kubectl create namespace aivoice
```

### 2. 配置Secret和ConfigMap

```bash
# 创建Secret
kubectl create secret generic aivoice-secrets \
  --from-literal=database-url=****************************************/aivoice \
  --from-literal=redis-url=redis://redis:6379 \
  --from-literal=secret-key=your-secret-key \
  -n aivoice

# 创建ConfigMap
kubectl create configmap aivoice-config \
  --from-file=config/environments/production.yml \
  -n aivoice
```

### 3. 部署应用

```bash
# 部署所有组件
kubectl apply -f k8s/ -n aivoice

# 检查部署状态
kubectl get pods -n aivoice
kubectl get services -n aivoice
```

### 4. 配置Ingress

```yaml
# ingress.yml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aivoice-ingress
  namespace: aivoice
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - aivoice.yourdomain.com
    secretName: aivoice-tls
  rules:
  - host: aivoice.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: aivoice-service
            port:
              number: 80
```

### 5. 自动扩缩容

```yaml
# hpa.yml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: aivoice-hpa
  namespace: aivoice
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: aivoice-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 6. 监控和日志

```bash
# 部署Prometheus监控
kubectl apply -f k8s/monitoring/

# 查看应用日志
kubectl logs -f deployment/aivoice-app -n aivoice

# 查看指标
kubectl top pods -n aivoice
```

## 云平台部署

### AWS部署

#### 1. 使用ECS

```bash
# 创建ECS集群
aws ecs create-cluster --cluster-name aivoice-cluster

# 注册任务定义
aws ecs register-task-definition --cli-input-json file://aws/ecs-task-definition.json

# 创建服务
aws ecs create-service \
  --cluster aivoice-cluster \
  --service-name aivoice-service \
  --task-definition aivoice:1 \
  --desired-count 2
```

#### 2. 使用EKS

```bash
# 创建EKS集群
eksctl create cluster --name aivoice-cluster --region us-west-2

# 部署应用
kubectl apply -f k8s/ -n aivoice
```

### Azure部署

#### 1. 使用Container Instances

```bash
# 创建资源组
az group create --name aivoice-rg --location eastus

# 部署容器实例
az container create \
  --resource-group aivoice-rg \
  --name aivoice-app \
  --image aivoice:latest \
  --ports 8000 \
  --environment-variables DATABASE_URL=... REDIS_URL=...
```

#### 2. 使用AKS

```bash
# 创建AKS集群
az aks create \
  --resource-group aivoice-rg \
  --name aivoice-cluster \
  --node-count 3 \
  --enable-addons monitoring

# 获取凭据
az aks get-credentials --resource-group aivoice-rg --name aivoice-cluster

# 部署应用
kubectl apply -f k8s/ -n aivoice
```

### GCP部署

#### 1. 使用Cloud Run

```bash
# 构建并推送镜像
gcloud builds submit --tag gcr.io/PROJECT_ID/aivoice

# 部署到Cloud Run
gcloud run deploy aivoice \
  --image gcr.io/PROJECT_ID/aivoice \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

#### 2. 使用GKE

```bash
# 创建GKE集群
gcloud container clusters create aivoice-cluster \
  --zone us-central1-a \
  --num-nodes 3

# 获取凭据
gcloud container clusters get-credentials aivoice-cluster --zone us-central1-a

# 部署应用
kubectl apply -f k8s/ -n aivoice
```

## 生产环境优化

### 1. 性能优化

```yaml
# 生产配置优化
app:
  debug: false
  workers: 4
  max_connections: 1000

database:
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30

redis:
  max_connections: 100
  connection_pool_size: 50

audio:
  buffer_size: 4096
  sample_rate: 16000
  enable_compression: true
```

### 2. 安全配置

```yaml
security:
  cors:
    enabled: true
    allowed_origins: ["https://yourdomain.com"]
  rate_limiting:
    enabled: true
    requests_per_minute: 1000
  authentication:
    enabled: true
    jwt_secret: "${JWT_SECRET}"
```

### 3. 监控配置

```yaml
monitoring:
  prometheus:
    enabled: true
    port: 9090
  grafana:
    enabled: true
    port: 3000
  alerting:
    enabled: true
    webhook_url: "${ALERT_WEBHOOK_URL}"
```

### 4. 日志配置

```yaml
logging:
  level: "INFO"
  format: "json"
  handlers:
    - type: "file"
      filename: "/var/log/aivoice/app.log"
      max_size: "100MB"
      backup_count: 10
    - type: "syslog"
      facility: "local0"
```

## 故障排除

### 常见问题

#### 1. 应用启动失败

```bash
# 检查日志
docker logs aivoice-app
kubectl logs deployment/aivoice-app -n aivoice

# 检查配置
python scripts/config_manager.py validate production

# 检查依赖服务
curl http://postgres:5432
redis-cli -h redis ping
```

#### 2. 数据库连接问题

```bash
# 测试数据库连接
python -c "
import psycopg2
conn = psycopg2.connect('************************************/aivoice')
print('Database connection successful')
"

# 检查数据库状态
docker exec postgres pg_isready
```

#### 3. 性能问题

```bash
# 检查系统资源
docker stats
kubectl top pods -n aivoice

# 检查应用指标
curl http://localhost:8000/api/v1/metrics

# 运行性能测试
python scripts/performance_test.py
```

### 监控和告警

#### 1. 健康检查

```bash
# 设置健康检查脚本
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
if [ $response -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy"
    exit 1
fi
```

#### 2. 日志监控

```bash
# 监控错误日志
tail -f /var/log/aivoice/app.log | grep ERROR

# 使用ELK Stack
# Elasticsearch + Logstash + Kibana
```

#### 3. 指标收集

```bash
# Prometheus配置
scrape_configs:
  - job_name: 'aivoice'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

## 维护和更新

### 1. 滚动更新

```bash
# Kubernetes滚动更新
kubectl set image deployment/aivoice-app aivoice=aivoice:v2.0.0 -n aivoice

# Docker Compose更新
docker-compose pull
docker-compose up -d
```

### 2. 数据备份

```bash
# 自动备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec postgres pg_dump -U aivoice aivoice > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://aivoice-backups/
```

### 3. 版本回滚

```bash
# Kubernetes回滚
kubectl rollout undo deployment/aivoice-app -n aivoice

# Docker回滚
docker-compose down
docker-compose up -d aivoice:v1.0.0
```

## 最佳实践

### 1. 安全最佳实践
- 使用HTTPS加密通信
- 定期更新依赖包
- 实施访问控制
- 定期安全审计

### 2. 性能最佳实践
- 使用CDN加速静态资源
- 实施缓存策略
- 优化数据库查询
- 监控系统性能

### 3. 运维最佳实践
- 自动化部署流程
- 实施蓝绿部署
- 定期备份数据
- 建立监控告警

### 4. 开发最佳实践
- 使用版本控制
- 实施代码审查
- 编写单元测试
- 文档及时更新
