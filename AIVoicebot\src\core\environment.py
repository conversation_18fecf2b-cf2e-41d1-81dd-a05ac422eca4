"""
Environment detection and configuration utilities.

This module provides utilities for detecting the current environment
and loading appropriate configuration settings.
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from .config_manager import ConfigManager, create_default_config


class Environment:
    """Environment detection and management."""
    
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"
    
    @classmethod
    def detect_environment(cls) -> str:
        """
        Detect current environment from various sources.
        
        Returns:
            Environment name (development, staging, production, testing)
        """
        # Check environment variable first
        env = os.getenv('AIVOICE_ENVIRONMENT', '').lower()
        if env in [cls.DEVELOPMENT, cls.STAGING, cls.PRODUCTION, cls.TESTING]:
            return env
        
        # Check Python environment indicators
        if hasattr(sys, '_called_from_test'):
            return cls.TESTING
        
        # Check for common development indicators
        if (os.getenv('DEBUG', '').lower() in ['true', '1'] or
            os.getenv('DEVELOPMENT', '').lower() in ['true', '1'] or
            'pytest' in sys.modules):
            return cls.DEVELOPMENT
        
        # Check for production indicators
        if (os.getenv('PRODUCTION', '').lower() in ['true', '1'] or
            os.getenv('PROD', '').lower() in ['true', '1']):
            return cls.PRODUCTION
        
        # Default to development
        return cls.DEVELOPMENT
    
    @classmethod
    def is_development(cls) -> bool:
        """Check if running in development environment."""
        return cls.detect_environment() == cls.DEVELOPMENT
    
    @classmethod
    def is_production(cls) -> bool:
        """Check if running in production environment."""
        return cls.detect_environment() == cls.PRODUCTION
    
    @classmethod
    def is_testing(cls) -> bool:
        """Check if running in testing environment."""
        return cls.detect_environment() == cls.TESTING


def load_dotenv_file(env_file: Optional[str] = None) -> None:
    """
    Load environment variables from .env file.
    
    Args:
        env_file: Path to .env file (defaults to .env in project root)
    """
    try:
        from dotenv import load_dotenv
        
        if env_file:
            env_path = Path(env_file)
        else:
            # Look for .env file in current directory and parent directories
            current_dir = Path.cwd()
            env_path = None
            
            for path in [current_dir] + list(current_dir.parents):
                potential_env = path / '.env'
                if potential_env.exists():
                    env_path = potential_env
                    break
        
        if env_path and env_path.exists():
            load_dotenv(env_path)
            logging.info(f"Loaded environment variables from: {env_path}")
        else:
            logging.info("No .env file found, using system environment variables only")
            
    except ImportError:
        logging.warning("python-dotenv not installed, skipping .env file loading")


def setup_environment(config_dir: str = "config", 
                     env_file: Optional[str] = None) -> ConfigManager:
    """
    Set up environment and return configured ConfigManager.
    
    Args:
        config_dir: Directory containing configuration files
        env_file: Path to .env file
        
    Returns:
        Configured ConfigManager instance
    """
    # Load environment variables from .env file
    load_dotenv_file(env_file)
    
    # Detect environment
    environment = Environment.detect_environment()
    logging.info(f"Detected environment: {environment}")
    
    # Create configuration manager
    config_manager = ConfigManager(config_dir=config_dir, environment=environment)
    
    # Validate configuration
    from .config_manager import DEFAULT_CONFIG_SCHEMA
    errors = config_manager.validate_config(DEFAULT_CONFIG_SCHEMA)
    
    if errors:
        logging.warning(f"Configuration validation warnings: {errors}")
        
        # In development, we can be more lenient
        if not Environment.is_production():
            logging.info("Running in non-production environment, continuing with validation warnings")
        else:
            # In production, we should be strict about configuration
            raise ValueError(f"Configuration validation failed: {errors}")
    
    return config_manager


def create_config_files(config_dir: str = "config", force: bool = False) -> None:
    """
    Create default configuration files if they don't exist.
    
    Args:
        config_dir: Directory to create configuration files in
        force: Overwrite existing files
    """
    config_path = Path(config_dir)
    config_path.mkdir(exist_ok=True)
    
    # Create default config.yaml
    default_config_file = config_path / "config.yaml"
    if not default_config_file.exists() or force:
        import yaml
        default_config = create_default_config()
        
        with open(default_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        logging.info(f"Created default configuration file: {default_config_file}")
    
    # Create .env.example if it doesn't exist
    env_example_file = Path(".env.example")
    if not env_example_file.exists() or force:
        # The .env.example content is already created in the previous task
        logging.info(f"Environment example file already exists: {env_example_file}")


def get_project_root() -> Path:
    """
    Get the project root directory.
    
    Returns:
        Path to project root
    """
    # Start from current file and go up until we find a marker file
    current_path = Path(__file__).parent
    
    # Look for common project root markers
    markers = ['.git', 'requirements.txt', 'setup.py', 'pyproject.toml', 'README.md']
    
    for path in [current_path] + list(current_path.parents):
        if any((path / marker).exists() for marker in markers):
            return path
    
    # Fallback to current working directory
    return Path.cwd()


def get_config_value(key: str, 
                    default: Any = None, 
                    config_manager: Optional[ConfigManager] = None) -> Any:
    """
    Get configuration value with fallback to environment variables.
    
    Args:
        key: Configuration key
        default: Default value if not found
        config_manager: Optional ConfigManager instance
        
    Returns:
        Configuration value
    """
    # Try config manager first
    if config_manager:
        value = config_manager.get_config(key, None)
        if value is not None:
            return value
    
    # Try environment variable
    env_key = f"AIVOICE_{key.upper().replace('.', '_')}"
    env_value = os.getenv(env_key)
    if env_value is not None:
        # Try to parse as JSON, fallback to string
        try:
            import json
            return json.loads(env_value)
        except (json.JSONDecodeError, ValueError):
            return env_value
    
    return default


# Global configuration manager instance
_global_config_manager: Optional[ConfigManager] = None


def get_global_config_manager() -> ConfigManager:
    """
    Get or create global configuration manager instance.
    
    Returns:
        Global ConfigManager instance
    """
    global _global_config_manager
    
    if _global_config_manager is None:
        _global_config_manager = setup_environment()
    
    return _global_config_manager


def set_global_config_manager(config_manager: ConfigManager) -> None:
    """
    Set global configuration manager instance.
    
    Args:
        config_manager: ConfigManager instance to set as global
    """
    global _global_config_manager
    _global_config_manager = config_manager