# AI Voice Customer Service System - Development Environment
version: '3.8'

services:
  # Development AI Voice Service
  aivoice-app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: aivoice-app-dev
    restart: unless-stopped
    ports:
      - "8000:8000"    # HTTP API
      - "8765:8765"    # WebSocket (ESP32)
      - "5060:5060"    # SIP
      - "5678:5678"    # Debug port
    volumes:
      - .:/app                    # Mount entire project for live development
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
      - ./temp:/app/temp
      - dev-cache:/app/.cache     # Cache for faster rebuilds
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - REDIS_URL=redis://redis-dev:6379
      - DATABASE_URL=***********************************************************/aivoice_dev
      - PROMETHEUS_GATEWAY=http://prometheus-dev:9090
      - PYTHONPATH=/app
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    depends_on:
      - redis-dev
      - postgres-dev
    networks:
      - aivoice-dev-network
    stdin_open: true
    tty: true
    command: ["python", "main.py", "--debug", "--reload"]

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    container_name: aivoice-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"    # Different port to avoid conflicts
    volumes:
      - redis-dev-data:/data
    networks:
      - aivoice-dev-network
    command: redis-server --appendonly yes

  # PostgreSQL for development
  postgres-dev:
    image: postgres:15-alpine
    container_name: aivoice-postgres-dev
    restart: unless-stopped
    ports:
      - "5433:5432"    # Different port to avoid conflicts
    environment:
      - POSTGRES_DB=aivoice_dev
      - POSTGRES_USER=aivoice
      - POSTGRES_PASSWORD=aivoice_dev_password
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./config/postgres/init-dev.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - aivoice-dev-network

  # Prometheus for development
  prometheus-dev:
    image: prom/prometheus:latest
    container_name: aivoice-prometheus-dev
    restart: unless-stopped
    ports:
      - "9091:9090"    # Different port to avoid conflicts
    volumes:
      - ./config/prometheus/prometheus-dev.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-dev-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=24h'
      - '--web.enable-lifecycle'
    networks:
      - aivoice-dev-network

  # Grafana for development
  grafana-dev:
    image: grafana/grafana:latest
    container_name: aivoice-grafana-dev
    restart: unless-stopped
    ports:
      - "3001:3000"    # Different port to avoid conflicts
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=dev123
      - GF_USERS_ALLOW_SIGN_UP=true
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-dev-data:/var/lib/grafana
      - ./config/grafana/provisioning-dev:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus-dev
    networks:
      - aivoice-dev-network

  # Test database for integration tests
  postgres-test:
    image: postgres:15-alpine
    container_name: aivoice-postgres-test
    restart: unless-stopped
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=aivoice_test
      - POSTGRES_USER=aivoice_test
      - POSTGRES_PASSWORD=aivoice_test_password
    volumes:
      - ./config/postgres/init-test.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - aivoice-dev-network
    profiles:
      - testing

  # Redis for testing
  redis-test:
    image: redis:7-alpine
    container_name: aivoice-redis-test
    restart: unless-stopped
    ports:
      - "6381:6379"
    networks:
      - aivoice-dev-network
    profiles:
      - testing

  # Jupyter notebook for data analysis and experimentation
  jupyter:
    build:
      context: .
      dockerfile: docker/Dockerfile.jupyter
    container_name: aivoice-jupyter
    restart: unless-stopped
    ports:
      - "8888:8888"
    volumes:
      - .:/workspace
      - jupyter-data:/home/<USER>/.jupyter
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=aivoice-dev-token
    networks:
      - aivoice-dev-network
    profiles:
      - analysis

  # Mock external services for development
  mock-services:
    build:
      context: .
      dockerfile: docker/Dockerfile.mock-services
    container_name: aivoice-mock-services
    restart: unless-stopped
    ports:
      - "8080:8080"    # Mock ASR service
      - "8081:8081"    # Mock TTS service
      - "8082:8082"    # Mock LLM service
    volumes:
      - ./tests/mock_data:/app/mock_data:ro
    environment:
      - MOCK_DELAY_MS=100
      - MOCK_ERROR_RATE=0.05
    networks:
      - aivoice-dev-network
    profiles:
      - mocking

# Networks
networks:
  aivoice-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  redis-dev-data:
    driver: local
  postgres-dev-data:
    driver: local
  prometheus-dev-data:
    driver: local
  grafana-dev-data:
    driver: local
  jupyter-data:
    driver: local
  dev-cache:
    driver: local
